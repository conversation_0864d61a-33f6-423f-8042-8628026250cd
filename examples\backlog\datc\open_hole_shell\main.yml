# unit system
# m, N, Pa

version: "0.10"


# --------------------------------------------------------------------
structure:
  name: "shell_open_hole"

  parameter:
    l1v1: 0
    l1v2: 30
    l1v3: -45
    l1v4: 60
    l1v5: 0
    l1v6: 30
    l1v7: -45
    l1v8: 60

  distribution:
    - name: a1
      function: f1
      domain: 'pd1'
      xdim: 2
      data:
        - coordinate: [-1, -1]
          value: l1v1
        - coordinate: [-1, 1]
          value: l1v2
        - coordinate: [1, -1]
          value: l1v3
        - coordinate: [1, 1]
          value: l1v4
        - coordinate: [-0.5, -0.5]
          value: l1v5
        - coordinate: [-0.5, 0.5]
          value: l1v6
        - coordinate: [0.5, -0.5]
          value: l1v7
        - coordinate: [0.5, 0.5]
          value: l1v8

  domain:
    - name: 'pd1'
      dim: 2
      type: 'list'
      data:
        file: 'shell_open_hole_param_ms01.inp'
        format: 'abaqus'
      map:
        method: 'file'
        file: 'node_map.csv'
        format: 'csv'
        # method: 'three_nodes'
        # type: 'id'
        # is_id_label: True
        # list:
        #   - [3, 5]
        #   - [22, 13]
        #   - [23, 29]

  model:
    tool: "abaqus"
    main_file: "shell_open_hole_physc_ms01.inp"
    prop_file: "shellsections.inp"
    config:
      orient_name: 'Ori-1'

  sg_assignment:
    - region: 'all'
      sg: "mainsg"

  sg:
    - name: 'mainsg'
      design: "lv1_layup"
      model:
        type: "pl1"
        tool: "swiftcomp"
        tool_version: "2.1"
        config:
          mesh_size: -1

# --------------------------------------------------------------------
function:
  - name: "f1"
    type: "scatter_interpolation"
    interp_kind: "linear"


# --------------------------------------------------------------------
sg:
  - name: "lv1_layup"
    parameter:
      a1: 0
    dim: 1
    builder: "default"
    design:
      layers:
        - material: "m1"
          ply_thickness: 2.0e-3
          number_of_plies: 1
          in-plane_orientation: a1

  - name: "m1"
    # type: "material"
    model:
      type: "sd1"
      property:
        density: 1.0
        anisotropy: "engineering"
        elasticity:
          [
            181e9, 8.96e9, 8.96e9,
            7.2e9, 7.2e9, 7.2e9,
            0.3, 0.28, 0.28
          ]
        strength:
          [
            2275e6, 64e6, 64e6,  # x1t, x2t, x3t
            1680e6, 186e6, 186e6,  # x1c, x2c, x3c
            127e6, 127e6, 121e6  # x23, x31, x12
          ]
        failure_criterion: 4  # Tsai-Wu



# --------------------------------------------------------------------
analysis:
  steps:
    - step: "homogenization"
      type: "sg"
      analysis: "h"
      # setting:
      #   solver: "swiftcomp"

    - step: "static analysis"
      type: "abaqus"
      setting:
        timeout: 300
      kwargs:
        exec:
          args:
            - "interactive"
          kwargs:
            ask_delete: "OFF"
      post_process:
        - script: "abq_get_result.py"
          args: [
            "shell_open_hole_physc_ms01.odb",
            "-position", "node",
            "-setname", "node_displ",
            "-fieldvar", "u",
            "-filename", "abq_result.dat",
          ]
        - script: "abq_get_result.py"
          args: [
            "shell_open_hole_physc_ms01.odb",
            "-position", "element",
            "-setname", "elem_hole",
            "-fieldvar", "sf,sm",
            "-filename", "macro_states.csv",
            "-fileformat", "csv",
          ]
      step_result_file: "abq_result.dat"

    - step: "failure"
      type: "sg"
      analysis: "fi"
      section_response:
        data_form: "file"
        file_name: "macro_states.csv"
        file_format: "csv"
      output:
        file_name: "strength_ratio"
        file_format: "yml"
        value: ['sr',]

    - step: "post_process"
      type: "script"
      module: "data_proc_funcs"
      function: "postProcess"






# --------------------------------------------------------------------
study:
  method:
    # list_parameter_study:
    #   list_of_points: [0, 30, 60, 90]
    # multidim_parameter_study:
    #   partitions: [1, 1, 1, 1]
    sampling:
      sample_type:
        lhs:
      samples: 10
      seed: 1027

  variables:
    data_form: "explicit"
    list:
      - name: "l1v1"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v2"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v3"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v4"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v5"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v6"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v7"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v8"
        type: "continuous"
        bounds: [-90, 90]

  responses:
    data_form: "explicit"
    response_functions:
      - descriptor: "u1"
      - descriptor: "u2"
      - descriptor: "u3"
      - descriptor: "sr_min"

  interface:
    fork:
      parameters_file: "params.in"
      results_file: "results.out"
      file_save: on
      work_directory:
        named: "evals/eval"
        directory_tag: on
        directory_save: on
    required_files: ["sup_files/*", ]
    # asynchronous:
    #   evaluation_concurrency: 10
    # failure_capture:
    #   recover: [-1e12, -1e12, -1e12, -1e12]
