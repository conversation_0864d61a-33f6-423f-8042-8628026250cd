from __future__ import annotations

import copy
import logging
from typing import Iterable

import numpy as np
import scipy.interpolate as spinterp
# from scipy.interpolate import interp1d

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)


class MSGDFunction():
    def __init__(self, name:str='', vtype:str='float'):
        self.name = name
        self.vtype = vtype

    def __call__(self, x):
        """
        Parameters
        ----------
        x : Array-like, shape: (npoints, ndims)
            Independent variables.

        Returns
        -------
        Array-like, shape: (npoints, ndims)
            Dependent variables.
        """
        ...

    def toDictionary(self):
        _dict = {
            'name': self.name,
            'type': self.vtype
        }

        return _dict

    def copy(self) -> MSGDFunction:
        return copy.deepcopy(self)




class LinearFunction(MSGDFunction):
    """A simple class for 1D linear function

    f(x) = (b - a) * (x + d) / c + a
    """
    def __init__(
        self, name:str,
        a=0, b=1, c=1, d=0, axis=1, n=None,
        vtype:str='float'):
        super().__init__(name=name, vtype=vtype)
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        self.axis = axis
        self.n = n  # rounding number of digits

    def __str__(self):
        # s = f'f(x) = ({self.b} - {self.a}) * (x + {self.d}) / {self.c} + {self.a}'
        s = 'f(x) = ({b} - {a}) * (x + {d}) / {c} + {a}'.format(
            a=self.a, b=self.b, c=self.c, d=self.d
        )
        return s

    def __call__(self, x):
        try:
            xd = [ (x[i] + self.d[i]) for i in range(len(x)) ]
        except:
            xd = x

        f = (self.b - self.a) * xd[self.axis-1] / self.c + self.a

        if self.n:
            f = round(f, self.n)

        return f









class SymLinearFunction(MSGDFunction):
    """A simple class for symmetric linear function

    f(x) = 2 * (b - a) * | (x + d)  / c| + a
    """
    def __init__(self, a, b, c, d=None, axis=1):
        super().__init__()
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        self.axis = axis

    def __str__(self):
        # s = f'f(x) = 2 * ({self.b} - {self.a}) * |x / {self.c}| + {self.a}'
        s = 'f(x) = 2 * ({b} - {a}) * |x / {c}| + {a}'.format(
            a=self.a, b=self.b, c=self.c
        )
        return s

    def __call__(self, x):
        try:
            xd = [ (x[i] + self.d[i]) for i in range(len(x)) ]
        except:
            xd = x

        # print('x =', x)
        # print('xd =', xd)

        return 2.0 * (self.b - self.a) * abs( xd[self.axis-1] / self.c) + self.a









class ScriptFunction(MSGDFunction):
    """
    """
    def __init__(
        self, name:str, vtype:str='float',
        mod_name='', func_name='',
        func=None, coef=None):
        """
        """
        super().__init__(name=name, vtype=vtype)

        self.mod_name = mod_name
        self.func_name = func_name
        self.coef = coef

        self.func = func

    def __repr__(self):
        s = f'{self.mod_name}.{self.func_name}'
        s += '(x, y, z'
        for k, v in self.coef.items():
            s += ', {}={}'.format(k, v)
        s += ')'
        return s

    def toDictionary(self):
        _dict = super().toDictionary()
        _dict['module'] = self.mod_name
        _dict['function'] = self.func_name
        _dict['coefficients'] = self.coef
        return _dict

    def implement(self):
        print(self.mod_name, self.func_name, self.coef)
        from msgd.utils import importFunction
        self.func = importFunction(self.mod_name, self.func_name)

    def __call__(self, x):
        return self.func(x, self.coef)









class ScatterInterpolationFunction(MSGDFunction):
    def __init__(
        self, name:str, vtype:str='float',
        data_indv:list=[], data_depv:list=[],
        kind:str='linear', fill_value=np.nan,
        rescale:bool=False
        ):
        """
        """
        super().__init__(name=name, vtype=vtype)
        self._data_points = data_indv
        self._data_values = data_depv
        self._method = kind

        if not isinstance(fill_value, (int, float)):
            self._fill_value = np.nan
        else:
            self._fill_value = fill_value
        self._rescale = rescale

    def toDictionary(self):
        _dict = super().toDictionary()
        _dict['method'] = self._method
        if self._fill_value != np.nan:
            _dict['fill_value'] = self._fill_value
        _data = []
        for _di, _dd in zip(self._data_points, self._data_values):
            _data.append({
                'coordinate': _di,
                'value': _dd
            })
        _dict['data'] = _data
        return _dict

    def implement(self, data_indv:list=[], data_depv:list=[], **kwargs):
        self._data_points = data_indv
        self._data_values = data_depv

    def __call__(self, eval_points):
        """
        Parameters
        ----------
        eval_points : array-like, shape (npoints, ndims)
            Independent variables.
        """

        logger.debug(f'data_points = {self._data_points}')
        logger.debug(f'data_values = {self._data_values}')
        logger.debug(f'eval_points = {eval_points}')

        eval_values = spinterp.griddata(
            points=self._data_points,
            values=self._data_values,
            xi=eval_points,
            method=self._method,
            fill_value=self._fill_value,
            rescale=self._rescale
        )

        # breakpoint()
        if self.vtype == 'float':
            eval_values = [list(map(float, yi)) for yi in eval_values]

        elif self.vtype == 'int':
            # y = int(round(y))
            eval_values = [list(map(int, yi)) for yi in eval_values]

        logger.debug(f'eval_values = {eval_values}')

        return eval_values









class InterpolationFunction(MSGDFunction):
    def __init__(
        self, name:str, vtype:str='float',
        data_indv:Iterable[Iterable]=[], data_depv:Iterable[Iterable]=[],
        kind:str='linear', fill_value:str='extrapolate'):
        """

        Parameters
        ----------
        data_indv : array-like, shape (npoints, ndims)
            Independent variables.
        data_depv : array-like, shape (npoints, ndims)
            Dependent variables.
        kind : str, optional
            Kind of the interpolation, choose one from 'linear', 'previous', 'next', by default 'linear'
        fill_value : str, optional
            Value to fill the points outside the data range, by default 'extrapolate'
        """
        super().__init__(name=name, vtype=vtype)

        self._data_indv:Iterable[Iterable] = data_indv
        self._data_depv:Iterable[Iterable] = data_depv

        self._kind = kind
        self._fill_value = fill_value

        self._func = None

    @property
    def data_x(self): return self._data_indv
    @data_x.setter
    def data_x(self, data_x:Iterable): self._data_indv = data_x
    @property
    def data_y(self): return self._data_depv
    @data_y.setter
    def data_y(self, data_y:Iterable): self._data_depv = data_y
    @property
    def num_data_points(self): return len(self._data_indv)
    @property
    def dim_x(self): return len(self._data_indv[0])
        # if isinstance(self._data_indv[0], list):
        #     return len(self._data_indv[0])
        # else:
        #     return 1
    @property
    def dim_y(self): return len(self._data_depv[0])
        # if isinstance(self._data_depv[0], list):
        #     return len(self._data_depv[0])
        # else:
        #     return 1
    @property
    def kind(self): return self._kind
    @property
    def fill_value(self): return self._fill_value
    @property
    def func(self): return self._func
    @func.setter
    def func(self, func): self._func = func


    def __repr__(self):
        s = [
            f'{__name__}',
            f'vtype={self.vtype}',
            f'kind={self.kind}',
            f'fill={self.fill_value}'
            # f'xdim={self.dim_x}',
            # f'ydim={self.dim_y}',
        ]
        return ', '.join(s)

    def toDictionary(self):
        _dict = super().toDictionary()
        _dict['kind'] = self.kind
        _dict['fill_value'] = self.fill_value
        return _dict

    def implement(
        self, data_x:Iterable[Iterable]=[], data_y:Iterable[Iterable]=[]):
        """
        Parameters
        ----------
        data_indv : array-like, shape (npoints, ndims)
            Independent variables.
        data_depv : array-like, shape (npoints, ndims)
            Dependent variables.
        """
        if len(np.shape(data_x)) != 2:
            raise ValueError('data_x must be 2D array-like.')
        if len(np.shape(data_y)) != 2:
            raise ValueError('data_y must be 2D array-like.')

        self.data_x = data_x
        self.data_y = data_y

        if self.dim_x == 1:
            _x = [x[0] for x in self.data_x]  # Flatten the data
            if self.vtype != 'str':
                self.func = spinterp.interp1d(
                    _x, self.data_y, axis=0,
                    kind=self.kind, fill_value=self.fill_value
                )
                # If the function value type is string, then the function is a look-up table.
                # This is handled in the __call__ method.

        elif self.dim_x == 2:
            if self.vtype != 'str':
                logger.debug('self.data_indv')
                logger.debug(self.data_x)
                logger.debug('self.data_depv')
                logger.debug(self.data_y)
                self.func = lambda x_eval: spinterp.griddata(
                    self.data_x, self.data_y, x_eval, method=self.kind
                )


    def __call__(self, x):
        if len(np.shape(x)) != 2:
            raise ValueError('x must be 2D array-like.')

        # print(f'self.vtype = {self.vtype}')
        # print(f'self.kind = {self.kind}')
        y = None

        if self.vtype == 'str':

            # If the function value type is string, then the function is a look-up table.
            # This only works for 1D data.
            # print(f'self.dim_x = {self.dim_x}')
            if self.dim_x == 1:
                y = []
                _eval_x = [xi[0] for xi in x]  # Flatten the data
                _data_x = [xi[0] for xi in self.data_x]  # Flatten the data

                for _eval_xj in _eval_x:
                    # print(f'_eval_xj = {_eval_xj}')

                    _eval_yj = None

                    if _eval_xj <= _data_x[0]:
                        _eval_yj = self.data_y[0]

                    elif _eval_xj >= _data_x[-1]:
                        _eval_yj = self.data_y[-1]

                    else:
                        _px_prev = _data_x[0]
                        for _i in range(1, self.num_data_points):
                            _px_curr = _data_x[_i]
                            logger.debug(f'_px_prev={_px_prev}, _px_curr={_px_curr}, _eval_xj={_eval_xj}')
                            # print(f'x = {x[0]}')

                            if self.kind == 'previous' and _eval_xj >= _px_prev and _eval_xj < _px_curr:
                                _eval_yj = self.data_y[_i-1]
                                # y.append(_eval_yj)
                                break
                            elif self.kind == 'next' and _eval_xj > _px_prev and _eval_xj <= _px_curr:
                                _eval_yj = self.data_y[_i]
                                # y.append(_eval_yj)
                                break
                            _px_prev = _px_curr

                    y.append(_eval_yj)

                # if len(y) < len(x):
                #     for _ in range(len(x) - len(y)):
                #         y.append(self.data_y[-1])

            return y

        else:
            # print(self.func.fill_value)
            # if self.dim_x == 2:
            #     x = [x[:2],]
            # logger.info(f'x = {x}')
            if self.dim_x == 1:
                _x = [xi[0] for xi in x]  # Flatten the data
            y = self.func(_x).tolist()
            # logger.info(f'y = {y}')

        # if self.dim_y == 1:
        #     if isinstance(y[0], list):
        #         y = y[0][0]
        #     else:
        #         y = y[0]

        if self.vtype == 'int':
            # y = int(round(y))
            y = [list(map(int, yi)) for yi in y]

        return y









try:
    from sympy import *

    class CustomFunction(MSGDFunction):
        """
        """

        def __init__(
            self, name:str, expr='',
            coef:dict={}, var_strs=['x', 'y', 'z'],
            vtype='float'):
            super().__init__(name=name, vtype=vtype)
            self.var_sym = symbols(' '.join(var_strs))
            self.expr_str = expr
            self._coef = coef
            # self.coef_strs = coef_strs
            # self.coef_vals = coef_vals
            self.coef_sym = []
            for cs in self._coef.keys():
                s = symbols(cs)
                self.coef_sym.append(s)
            self.expr_sym = sympify(self.expr_str)
            self.expr_lmd = None
            # self.return_type = return_type

        def __str__(self):
            return self.expr_sym.__str__()

        def toDictionary(self):
            _dict = super().toDictionary()
            _dict['expression'] = self.expr_str
            _dict['coefficients'] = self._coef

            return _dict

        def implement(self, coef:dict={}):
            # print('implement')
            self._coef.update(coef)
            self.coef_rep = []
            for s in self.coef_sym:
                v = self._coef[s.__str__()]
                self.coef_rep.append((s, v))
            self.expr_sym = self.expr_sym.subs(self.coef_rep)
            self.expr_lmd = lambdify(self.var_sym, self.expr_sym, 'math')

        def __call__(self, x=0, y=0, z=0):
            # print('__call__')
            if not self.expr_lmd:
                return

            if isinstance(x, Iterable):
                if len(np.shape(x)) == 2:
                    _x = x[0]
                else:
                    _x = x
                __x = _x[0]
                try:
                    __y = _x[1]
                except IndexError:
                    __y = 0
                try:
                    __z = _x[2]
                except IndexError:
                    __z = 0
                f = self.expr_lmd(__x, __y, __z)
                # f = self.expr_lmd(_x[0], _x[1], _x[2])
                # f = self.expr_lmd(*_x)

            else:
                f = self.expr_lmd(x, y, z)

            # print(f'f = {f}')

            if self.vtype == 'float':
                f = float(f)
            elif self.vtype == 'int':
                f = int(round(f))

            return f


except ModuleNotFoundError:
    # print('sympy module not found.')
    pass
except:
    # print('other exceptions.')
    pass

