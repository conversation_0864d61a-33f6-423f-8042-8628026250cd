#!/usr/bin/python
"""Main python script to extract blade geometry and properties from RCAS model for iVABS

Description: This Python script extracts blade geometry and properties from a RCAS mode/script for iVABS inputs
"""
# ------------------------------------------------------------------------------
import sys

def readRCASList(src):
    with open(src) as f:
        RCASData = f.read()
    f.close()
    RCASList = RCASData.splitlines()
    return (RCASList)

# ------------------------------------------------------------------------------

def getScreen(RCASList, screenName, screenList):

    sFlag = False
    for i, item in enumerate(RCASList):
        if (item.split()):
            capline = item.upper()
            if (sFlag):
                if ("A" in capline.strip()[0]):
                    screenList.append(item.strip())
                elif ("S" in capline.strip()[0]):
                    sFlag = False
                else:
                    screenList.append(item.strip())
            elif (sFlag == False and capline.strip()[0] != "!" and checkScreen("S" + screenName, capline)):
                sFlag = True
                screenList.append(item.strip())

# ------------------------------------------------------------------------------

def checkScreen(screenName, line):
    if (screenName in line.replace(" ", "")[:len(screenName)]):
        return True
    else:
        return False

# ------------------------------------------------------------------------------

def outputOml(dst, RCASList):
    fo = open(dst, 'w')
    # get screens FENODE AERONODE, AEROSEG and AIRFOILINTERP from the base RCAS script
    screenName = ['FENODE', 'AERONODE', 'AEROSEG', 'AIRFOILINTERP']
    for i in range(len(screenName)):
        screenList = []
        getScreen(RCASList, screenName[i], screenList)
        for j, item in enumerate(screenList):
            fo.write(item)
            fo.write("\n")
    fo.close()

# ------------------------------------------------------------------------------

def outputProp(dst, RCASList):
    fo = open(dst, 'w')
    # get blade structural property table file name
    for i, item in enumerate(RCASList):
        if (item.split()):
            line = item.upper()
            key1 = 'PROPERTY'
            key2 = 'FILENAME'
            if (key1 in line and key2 in line):
                PropTabName = RCASList[i + 1].split()[3]
                break

            if (key1 in line):
                line = RCASList[i + 1].upper()
                if (key2 in line):
                    PropTabName = RCASList[i + 2].split()[3]
                    break

    # output blade property table
    wFlag = False
    for i, item in enumerate(RCASList):
        keyWord = '***begin-RCAS-file: ' + PropTabName + '  ***'
        if (keyWord in item):
            wFlag = True
        keyWord = '*****end-RCAS-file: ' + PropTabName + '  ***'
        if (keyWord in item):
            fo.write(item)
            break
        if (wFlag == True):
            fo.write(item)
            fo.write("\n")
    fo.close()

# --------------------------------------------------------------------------------------------------
# ----Main Code-------------------------------------------------------------------------------------
# --------------------------------------------------------------------------------------------------
if __name__ == "__main__":

    # User sets filenames
    if (len(sys.argv) > 1):
        rcasBase = sys.argv[1]
    else:
        print("Enter the name of the base RCAS script.\n")
        print("e.g. uh60a_base.rcas")
        lineInput = input()
        lineInput = lineInput.replace(',', ' ')
        rcasBase = lineInput.split()[0]

    rcasBase = rcasBase.replace('"', '')
    rcasBase = rcasBase.replace("'", "")
    omlFile = 'oml.dat'
    propFile = 'prop.dat'

    # Read in baseline RCAS script
    RCASList = readRCASList(rcasBase)
    # output oml.dat
    outputOml(omlFile, RCASList)
    # output prop.dat
    outputProp(propFile, RCASList)
