INFO     [2024-06-02 17:02:55] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 17:02:55] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 17:02:55] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 17:02:55] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-06-02 17:02:55] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-06-02 17:02:55] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-06-02 17:02:55] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-06-02 17:02:55] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-02 17:02:55] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 17:02:55] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-06-02 17:02:55] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
WARNING  [2024-06-02 17:02:56] _structure.calcParamsFromDistributions :: Element 1 is not assigned with any CS. 
WARNING  [2024-06-02 17:02:56] _structure.calcParamsFromDistributions :: Element 2 is not assigned with any CS. 
WARNING  [2024-06-02 17:02:56] _structure.calcParamsFromDistributions :: Element 3 is not assigned with any CS. 
WARNING  [2024-06-02 17:02:56] _structure.calcParamsFromDistributions :: Element 4 is not assigned with any CS. 
WARNING  [2024-06-02 17:02:56] _structure.calcParamsFromDistributions :: Element 5 is not assigned with any CS. 
INFO     [2024-06-02 17:02:56] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-06-02 17:02:56] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 17:02:56] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 17:02:56] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 17:02:56] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-02 17:04:35] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 17:04:35] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 17:04:35] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 17:04:35] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-06-02 17:04:35] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-06-02 17:04:35] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-06-02 17:04:35] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-06-02 17:04:35] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-02 17:04:35] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 17:04:35] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-06-02 17:04:35] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
INFO     [2024-06-02 17:04:35] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-06-02 17:04:36] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 17:04:36] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 17:04:36] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 17:04:38] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-02 17:06:18] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 17:06:19] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 17:06:19] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 17:06:19] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-06-02 17:06:19] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-06-02 17:06:19] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-06-02 17:06:19] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-06-02 17:06:19] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-02 17:06:19] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 17:06:19] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-06-02 17:06:19] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
INFO     [2024-06-02 17:06:19] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-06-02 17:06:19] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 17:06:19] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 17:06:19] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 17:06:21] _msgd.writeAnalysisOut :: [main] writing output to file ... 
