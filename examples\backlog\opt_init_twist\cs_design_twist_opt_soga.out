Writing new restart file 'cs_design_twist_opt_soga.rst'.

>>>>> Executing environment.

>>>>> Running soga iterator.

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                      1.0351878414e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 1:
Active set vector = { 1 1 1 }
                      3.5732584713e+02 diff_gj
                      7.8839128156e+01 diff_eiyy
                      1.1177577688e+00 diff_eizz



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                      1.0730307932e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 2:
Active set vector = { 1 1 1 }
                      3.7958036790e+02 diff_gj
                      9.2392936405e+01 diff_eiyy
                      1.1277890314e+00 diff_eizz



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      2.5598925748e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 3:
Active set vector = { 1 1 1 }
                      1.9607683833e+03 diff_gj
                      9.8736082101e+02 diff_eiyy
                      1.8051871878e+00 diff_eizz



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                      5.3901791437e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 4:
Active set vector = { 1 1 1 }
                      8.5766892201e+03 diff_gj
                      4.6986027905e+03 diff_eiyy
                      4.6263597660e+00 diff_eizz



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                      5.4503006073e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 5:
Active set vector = { 1 1 1 }
                      8.7683411541e+03 diff_gj
                      4.8060860492e+03 diff_eiyy
                      4.7080760128e+00 diff_eizz



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                      8.1423383282e-01 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 6:
Active set vector = { 1 1 1 }
                      3.4674985380e+02 diff_gj
                      8.0141389872e+00 diff_eiyy
                      1.0622414758e+00 diff_eizz



---------------------
Begin Evaluation    7
---------------------
Parameters for evaluation 7:
                      9.5446638386e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 7:
Active set vector = { 1 1 1 }
                      2.6822366059e+04 diff_gj
                      1.4930740111e+04 diff_eiyy
                      1.2405716902e+01 diff_eizz



---------------------
Begin Evaluation    8
---------------------
Parameters for evaluation 8:
                      2.1366008484e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 8:
Active set vector = { 1 1 1 }
                      1.3766313931e+03 diff_gj
                      6.5915368915e+02 diff_eiyy
                      1.5559267978e+00 diff_eizz



---------------------
Begin Evaluation    9
---------------------
Parameters for evaluation 9:
                      2.2113711966e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 9:
Active set vector = { 1 1 1 }
                      1.4721291922e+03 diff_gj
                      7.1284680374e+02 diff_eiyy
                      1.5966890590e+00 diff_eizz



---------------------
Begin Evaluation   10
---------------------
Parameters for evaluation 10:
                      2.7716910306e+00 k1

blocking fork: python run.py cs_design_twist_opt_soga.yml 1 input.in output.out

Active response data for evaluation 10:
Active set vector = { 1 1 1 }
                      2.2926783359e+03 diff_gj
                      1.1737025421e+03 diff_eiyy
                      1.9467705184e+00 diff_eizz


<<<<< Function evaluation summary: 10 total (10 new, 0 duplicate)
<<<<< Best parameters          =
                      8.1423383282e-01 k1
<<<<< Best objective functions =
                      3.4674985380e+02
                      8.0141389872e+00
                      1.0622414758e+00
<<<<< Best data captured at function evaluation 6


<<<<< Iterator soga completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =     41.622 [parent =     41.622, child =          0]
  Total wall clock =     41.621
