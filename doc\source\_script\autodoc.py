import os
import yaml
from jinja2 import Environment, FileSystemLoader

KEYWORD_REF = os.path.abspath(os.path.join(
    '..', '..', 'scripts', 'msgd', '_static', 'input_keywords.yml'
    ))
print(f'KEYWORD_REF\n  {KEYWORD_REF}')

TEMPLATE_DIR = os.path.abspath(os.path.join('..', 'source', '_static'))
print(f'TEMPLATE_DIR\n  {TEMPLATE_DIR}')
TEMPLATE_FILE = "rst_template_keyword.j2"
TEMPLATE_SPEC = "kw_spec_template.j2"

OUTPUT_DIR = os.path.abspath(os.path.join(
    '..', 'source', 'generated'
    ))
print(f'OUTPUT_DIR\n  {OUTPUT_DIR}')

if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)


def load_keywords():
    with open(KEYWORD_REF, 'r') as f:
        return yaml.safe_load(f)


def generate_rst_files():
    """Generate .rst files from YAML configuration and Jinja2 template."""
    env = Environment(loader=FileSystemLoader(TEMPLATE_DIR))

    try:
        template = env.get_template(TEMPLATE_FILE)
    except Exception as e:
        raise FileNotFoundError(
            f"Template file '{TEMPLATE_FILE}' not found in '{TEMPLATE_DIR}'."
            ) from e

    keywords = load_keywords()

    for kw, _inputs in keywords.items():
        output_filename = os.path.join(OUTPUT_DIR, f"kw-{kw}.rst")
        with open(output_filename, 'w') as f:

            # Write the reference name
            f.write(f".. _kw-{kw}:\n\n")

            # Write the title
            f.write(f"{kw}\n")
            f.write(f"{'=' * len(kw)}\n\n")

            # For specifications under each parent keyword
            for _input in _inputs:
                rst_content = template.render(keyword=kw, **_input)
                f.write(rst_content)
                f.write("\n\n")

                _specs = _input.get('specifications')

                # If specifications are conditional
                if isinstance(_specs, list):
                    for _spec in _specs:
                        _cond = _spec.get('condition')
                        f.write(f"If {_cond}\n")
                        f.write(f"{'.' * (len(_cond)+5)}\n\n")
                        spec_template = env.get_template(TEMPLATE_SPEC)
                        spec_content = spec_template.render(**_spec)
                        f.write(spec_content)
                        f.write("\n\n")

                elif isinstance(_specs, dict):
                    spec_template = env.get_template(TEMPLATE_SPEC)
                    spec_content = spec_template.render(**_specs)
                    f.write(spec_content)
                    f.write("\n\n")

        print(f"Generated {output_filename}")
