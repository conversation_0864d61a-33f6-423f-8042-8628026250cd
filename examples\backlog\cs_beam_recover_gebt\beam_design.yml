# Main input for GEBT
# More detailed instruction can be found in the GEBT manual.

# This example:
# - straight beam
# - cantilever beam
# - free vibration analysis


# Overall analysis configuration block
# setting:
analysis: 1
# Static analysis.

max_iteration: 1
# Maximum number of iterations for the nonlinear analysis.
# If 1, carry out linear analysis

num_steps: 1
# Number of analysis steps.


# Geometry and mesh block
point:
# List of key points on the beam model.

  - id: 1
    coordinates: [0, 0, 0]
  - id: 2
    coordinates: [26.83, 0, 0]

member:
# List of beam memebrs.

  - id: 1

    points: [1, 2]
    # IDs of the begining and ending points defining the member.

    division: 32
    # Number of divisions (elements) of this member.

set:
# Create some sets for the use below.

  - name: "root"
    type: 'point'
    objects: [1,]
  - name: "tip"
    type: 'point'
    objects: [2,]
  - name: 'segment1'
    type: 'member'
    objects: [1,]


# Boundary condition and load block
condition:
  - region: 'root'
    # Set name defined above.
    # Fix the root.

    dofs: [1, 2, 3, 4, 5, 6]
    # Degree-of-freedom labels
    # - 1, 2, 3: displacements in x, y, z
    # - 4, 5, 6: rotations in x, y, z

    values: [0, 0, 0, 0, 0, 0]
    # Values corresponding to each DoF.

  - region: 'tip'
    # No BCs and loads at the tip.

    dofs: [7, 8, 9, 10, 11, 12]
    # Degree-of-freedom labels
    # - 7, 8, 9: forces in x, y, z
    # - 10, 11, 12: moments in x, y, z

    values: [0, 0, 0, 0, 1, 0]
    # Applied load:
    # Bending moment about y-axis (My) at the tip

