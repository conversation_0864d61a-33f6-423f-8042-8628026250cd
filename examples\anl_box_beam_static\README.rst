
Eigenvalue analysis of a box beam
===============================================================

Caluclate the first ten eigenvalues of a box beam.


Running
----------------------

Example location: `examples/anl_box_beam_eigen`

..  code-block:: shell

    ivabs analyze main.yml


Results
-------

Eigenvalues of the first ten modes are shown below.

..  csv-table::
    :header: "Mode", "Frequency [Hz]"
    :widths: auto

    1, 362.949
    2, 573.312
    3, 1239.729
    4, 1647.460
    5, 1920.559
    6, 3118.625
    7, 4583.196
    8, 4742.627
    9, 5174.042
    10, 6629.734


Input files
-----------

main.yml
    Main input file.

box.xml.tmp
    Box cross-section design template.

beam_design.yml
    Beam model design input.


