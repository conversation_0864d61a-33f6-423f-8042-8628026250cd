.. _section-xml:

Introduction to XML
===================

#. The content of an XML file is composed of elements. Each element is
   marked by a pair of tags ``<tag>...</tag>``.
#. The hierarchical structure of elements is important. At the same level
   of hierarchy, the arrangement order of elements is not important.
#. The tag names are keywords, which should not be changed.
#. Each element can have multiple attributes, which are ``name="value"``
   pairs. The names are also keywords. The values must be surrounded by
   double quotes.
