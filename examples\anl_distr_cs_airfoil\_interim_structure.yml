name: blade
parameter: {}
model:
  type: ''
  tool: ''
  tool_version: ''
  main_file: ''
  prop_file: ''
  config: {}
design:
  name: blade
  parameter:
    length: 30
    chord_root: 2
    chord_tip: 1
    web_f_root: 0.8
    web_f_tip: 0.75
    web_r_root: 0.6
    web_r_tip: 0.65
    ply_spar_root: 20
    ply_spar_r1: 16
    ply_spar_tip: 8
  dim: 1
  builder: default
  design: null
  distribution:
  - name: chord
    type: float
    xscale: 30
    data_form: explicit
    data:
    - coordinate: 0
      value: 2
    - coordinate: 1
      value: 1
  - name:
    - a2p1
    - a2p3
    type: float
    xscale: 30
    data_form: compact
    data: '0,0.8,0.6

      1,0.75,0.65

      '
  - name:
    - ply_spar
    type: int
    xscale: 30
    data_form: compact
    data: '0,20

      0.2,16

      1,8

      '
cs_assignment:
- region: all
  location: node
  cs: main_cs
physics: elastic
