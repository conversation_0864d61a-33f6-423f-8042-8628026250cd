import pytest
import numpy as np
from msgd.core._helpers import mask_selected_elements


class TestMaskSelectedElements:
    """Test suite for the mask_selected_elements function."""

    def test_empty_input(self):
        """Test with empty cell_data_etags and selected_etags."""
        cell_data_etags = []
        selected_etags = []

        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert result == []

    def test_empty_selected_etags(self):
        """Test with empty selected_etags list."""
        cell_data_etags = [
            np.array([1, 2, 3]),
            np.array([101, 102])
        ]
        selected_etags = []

        expected = [
            np.array([False, False, False]),
            np.array([False, False])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_single_cell_block_partial_selection(self):
        """Test with a single cell block and partial element selection."""
        cell_data_etags = [
            np.array([1, 2, 3, 4, 5])
        ]
        selected_etags = [2, 4]

        expected = [
            np.array([False, True, False, True, False])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        np.testing.assert_array_equal(result[0], expected[0])

    def test_single_cell_block_all_selected(self):
        """Test with a single cell block where all elements are selected."""
        cell_data_etags = [
            np.array([1, 2, 3])
        ]
        selected_etags = [1, 2, 3]

        expected = [
            np.array([True, True, True])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        np.testing.assert_array_equal(result[0], expected[0])

    def test_single_cell_block_none_selected(self):
        """Test with a single cell block where no elements are selected."""
        cell_data_etags = [
            np.array([1, 2, 3])
        ]
        selected_etags = [4, 5, 6]  # None of these exist in the cell block

        expected = [
            np.array([False, False, False])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        np.testing.assert_array_equal(result[0], expected[0])

    def test_multiple_cell_blocks(self):
        """Test with multiple cell blocks."""
        cell_data_etags = [
            np.array([1, 2, 3]),
            np.array([101, 102, 103, 104]),
            np.array([201, 202])
        ]
        selected_etags = [1, 102, 104, 201]

        expected = [
            np.array([True, False, False]),      # Only element 1 selected
            np.array([False, True, False, True]), # Elements 102 and 104 selected
            np.array([True, False])              # Only element 201 selected
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_docstring_example(self):
        """Test the exact example from the docstring."""
        cell_data_etags = [
            [1, 2, 3],
            [101, 102, 103]
        ]
        selected_etags = [1, 102]

        expected = [
            np.array([True, False, False]),
            np.array([False, True, False])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_duplicate_selected_etags(self):
        """Test with duplicate element tags in selected_etags."""
        cell_data_etags = [
            np.array([1, 2, 3])
        ]
        selected_etags = [1, 1, 2]  # Duplicate element tag 1

        expected = [
            np.array([True, True, False])  # Should still work correctly
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        np.testing.assert_array_equal(result[0], expected[0])

    def test_large_element_tags(self):
        """Test with large element tag values."""
        cell_data_etags = [
            np.array([10001, 10002, 10003]),
            np.array([20001, 20002])
        ]
        selected_etags = [10002, 20001, 20002]

        expected = [
            np.array([False, True, False]),
            np.array([True, True])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_single_element_per_block(self):
        """Test with single elements in multiple blocks."""
        cell_data_etags = [
            np.array([1]),
            np.array([101]),
            np.array([201])
        ]
        selected_etags = [101, 201]

        expected = [
            np.array([False]),  # Element 1 not selected
            np.array([True]),   # Element 101 selected
            np.array([True])    # Element 201 selected
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_empty_cell_block(self):
        """Test with an empty cell block."""
        cell_data_etags = [
            np.array([]),
            np.array([1, 2, 3])
        ]
        selected_etags = [1, 2]

        expected = [
            np.array([], dtype=bool),        # Empty mask for empty block
            np.array([True, True, False])    # Normal mask for second block
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_different_cell_data_types(self):
        """Test with different types of cell_data_etags (list vs numpy array)."""
        cell_data_etags = [
            [1, 2, 3],          # Regular list
            np.array([101, 102]) # Numpy array
        ]
        selected_etags = [2, 101]

        expected = [
            np.array([False, True, False]),
            np.array([True, False])
        ]
        result = mask_selected_elements(cell_data_etags, selected_etags)

        assert len(result) == len(expected)
        for i, (actual, exp) in enumerate(zip(result, expected)):
            np.testing.assert_array_equal(actual, exp,
                                        err_msg=f"Mismatch in cell block {i}")

    def test_return_type_and_structure(self):
        """Test that the return type and structure match the specification."""
        cell_data_etags = [
            np.array([1, 2]),
            np.array([101])
        ]
        selected_etags = [1, 101]

        result = mask_selected_elements(cell_data_etags, selected_etags)

        # Check that we get a list
        assert isinstance(result, list)
        assert len(result) == 2

        # Check that each item is a numpy array of booleans
        assert all(isinstance(mask, np.ndarray) for mask in result)
        assert all(mask.dtype == bool for mask in result)

        # Check specific content
        np.testing.assert_array_equal(result[0], np.array([True, False]))
        np.testing.assert_array_equal(result[1], np.array([True]))

    def test_order_independence_of_selected_etags(self):
        """Test that the order of selected_etags doesn't affect the result."""
        cell_data_etags = [
            np.array([1, 2, 3, 4])
        ]
        selected_etags_1 = [1, 3, 4]
        selected_etags_2 = [4, 1, 3]  # Different order

        result_1 = mask_selected_elements(cell_data_etags, selected_etags_1)
        result_2 = mask_selected_elements(cell_data_etags, selected_etags_2)

        # Results should be identical regardless of order
        assert len(result_1) == len(result_2)
        for mask1, mask2 in zip(result_1, result_2):
            np.testing.assert_array_equal(mask1, mask2)


if __name__ == "__main__":
    pytest.main([__file__])