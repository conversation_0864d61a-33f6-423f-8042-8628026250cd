version: "0.9"


cs:
  - name: "airfoil"
    parameter:
      mesh_size: 0.005
      mesh_type: 1
    design:
      dim: 2
      tool: "prevabs"
      base_file: "mh104.xml.tmp"
    model:
      md1:
        tool: "vabs"


analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      output:
        - value: ["ea", "gj", "eiyy", "eizz"]


study:
  method:
    # list_parameter_study:
    #   list_of_points: [
    #     0.01, 1,
    #     0.005, 1,
    #     0.0025, 1,
    #     0.01, 2,
    #     0.005, 2,
    #     0.0025, 2,
    #   ]
    multidim_parameter_study:
      partitions: [1, 3]
  variables:
    data_form: "explicit"
    list:
      - name: "mesh_type"
        type: "discrete"
        space_type: "set"
        class: "integer"
        elements: [1, 2]
      - name: "mesh_size"
        type: "discrete"
        space_type: "set"
        class: "real"
        elements: [0.005, 0.01, 0.02, 0.04]
  responses:
    data_form: "explicit"
    response_functions:
      - descriptor: "ea"
      - descriptor: "gj"
      - descriptor: "eiyy"
      - descriptor: "eizz"
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        named: "evals/eval"
        directory_tag: on
        directory_save: on
    required_files:
      - "design/*"
