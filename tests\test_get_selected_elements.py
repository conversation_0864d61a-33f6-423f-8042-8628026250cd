import pytest
from msgd.core._helpers import get_selected_elements


class TestGetSelectedElements:
    """Test suite for the get_selectd_elements function."""

    def test_empty_selected_etags(self):
        """Test with empty selected_etags list."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2}
        selected_etags = []

        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == []

    def test_single_selected_element(self):
        """Test with a single selected element."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2}
        selected_etags = [1]

        expected = [[0, 1, 2]]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_multiple_selected_elements(self):
        """Test with multiple selected elements."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14]),
            ('quad', [15, 16, 17, 18])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2, 102: 3}
        selected_etags = [1, 101, 102]

        expected = [
            [0, 1, 2],
            [11, 12, 13, 14],
            [15, 16, 17, 18]
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_all_elements_selected(self):
        """Test when all elements are selected."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2}
        selected_etags = [1, 2, 101]

        expected = [
            [0, 1, 2],
            [1, 2, 3],
            [11, 12, 13, 14]
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_selected_etags_order_preserved(self):
        """Test that the order of selected_etags is preserved in the result."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2}
        selected_etags = [101, 1, 2]  # Different order

        expected = [
            [11, 12, 13, 14],  # etag 101 first
            [0, 1, 2],         # etag 1 second
            [1, 2, 3]          # etag 2 third
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_duplicate_selected_etags(self):
        """Test with duplicate element tags in selected_etags."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3])
        ]
        etag_to_index = {1: 0, 2: 1}
        selected_etags = [1, 1, 2]  # Duplicate etag 1

        expected = [
            [0, 1, 2],  # etag 1 first occurrence
            [0, 1, 2],  # etag 1 second occurrence (duplicate)
            [1, 2, 3]   # etag 2
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_nonexistent_etag_raises_error(self):
        """Test that selecting a non-existent element tag raises an error."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3])
        ]
        etag_to_index = {1: 0, 2: 1}
        selected_etags = [1, 999]  # etag 999 doesn't exist

        with pytest.raises(KeyError):
            get_selected_elements(element_list, etag_to_index, selected_etags)

    def test_different_element_types(self):
        """Test with different element types."""
        element_list = [
            ('line', [0, 1]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14]),
            ('tetra', [20, 21, 22, 23])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2, 201: 3}
        selected_etags = [2, 201, 1]

        expected = [
            [1, 2, 3],          # triangle
            [20, 21, 22, 23],   # tetra
            [0, 1]              # line
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_large_node_indices(self):
        """Test with large node indices."""
        element_list = [
            ('triangle', [1000, 1001, 1002]),
            ('quad', [2000, 2001, 2002, 2003])
        ]
        etag_to_index = {5001: 0, 5002: 1}
        selected_etags = [5002, 5001]

        expected = [
            [2000, 2001, 2002, 2003],
            [1000, 1001, 1002]
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_docstring_example(self):
        """Test the exact example from the docstring."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14]),
            ('quad', [15, 16, 17, 18])
        ]
        etag_to_index = {1: 0, 2: 1, 101: 2, 102: 3}
        selected_etags = [1, 2, 101, 102]

        expected = [
            [0, 1, 2],
            [1, 2, 3],
            [11, 12, 13, 14],
            [15, 16, 17, 18]
        ]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected

    def test_return_type_and_structure(self):
        """Test that the return type and structure match the specification."""
        element_list = [
            ('triangle', [0, 1, 2]),
            ('quad', [11, 12, 13, 14])
        ]
        etag_to_index = {1: 0, 101: 1}
        selected_etags = [1, 101]

        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        # Check that we get a list
        assert isinstance(result, list)
        assert len(result) == 2

        # Check that each item is a list of integers (node IDs)
        assert all(isinstance(item, list) for item in result)
        assert all(all(isinstance(node_id, int) for node_id in item) for item in result)

        # Check specific content
        assert result[0] == [0, 1, 2]
        assert result[1] == [11, 12, 13, 14]

    def test_empty_element_list(self):
        """Test with empty element_list."""
        element_list = []
        etag_to_index = {}
        selected_etags = []

        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == []

    def test_single_element_list(self):
        """Test with a single element in the list."""
        element_list = [('triangle', [0, 1, 2])]
        etag_to_index = {1: 0}
        selected_etags = [1]

        expected = [[0, 1, 2]]
        result = get_selected_elements(element_list, etag_to_index, selected_etags)

        assert result == expected


if __name__ == "__main__":
    pytest.main([__file__])