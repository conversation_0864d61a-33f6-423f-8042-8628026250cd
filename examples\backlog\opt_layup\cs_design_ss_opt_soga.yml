version: "0.9"

# Settings
# ====================================================================
setting:
  log_level_cmd: "info"
  log_level_file: "debug"
  log_file_name: "eval.log"
  data_process_functions_file: "data_proc_funcs"


# Design parameters/variables of the structure
# mainly the distribution of SG parameters w.r.t. structural coordinates
# ====================================================================

structure:
  name: "uh60_blade_1"
  parameters:
    cs_template: 'airfoil_gbox_uni_ss.xml.tmp'
    airfoil: 'SC1095.dat'
    a2p1: 0.8
    a2p3: 0.6
    airfoil_point_order: -1
    lam_skin: "T300 15k/976_0.0053"
    lam_cap: "Aluminum 8009_0.01"
    lam_spar: "T300 15k/976_0.0053"
    lam_front: "T300 15k/976_0.0053"
    lam_back: "T300 15k/976_0.0053"
    ssc_spar: "[-45/0/45/90]"
  design:
    type: 'discrete'
    dim: 1
    cs_assignment:
      all: 'main_cs'
  # model:
  cs:
    main_cs:
      base: 'cs'
      model: 'md1'


cs:
- name: "cs"
  parameters:
    cs_template: 'airfoil_gbox_uni_ss.xml.tmp'
    mdb_name: "material_database_us_ft"
    rnsm: 0.001
    gms: 0.004
    mat_nsm: "lead"
    mat_fill_front: "Rohacell 70"
    mat_fill_back: "Plascore PN2-3/16OX3.0"
    mat_fill_te: "Plascore PN2-3/16OX3.0"
  design:
    dim: 2
    tool: 'prevabs'
    base_file: cs_template
  model:
    md1:
      tool: 'vabs'


# Analysis process
# ====================================================================
analysis:
  setting:
  steps:
    - step: "cs analysis"
      type: 'sg'
      analysis: 'h'
    - step: "calc diff"
      type: "script"
      file: 'data_proc_funcs'
      function: "dakota_postpro"
      kwargs:
        beam_properties: ["gj", "ei22", "ei33"]
        target: [2.29e3, 3.98e3, 2.44e5]


# Configurations of design study, e.g., parameter study, optimization, etc.
# Mainly for Dakota
# ====================================================================
study:
  method:
    format: "keyword"
    output: "normal"
    list_parameter_study:
      list_of_points: [0.85, 0.55]
    # soga:
    #   max_function_evaluations: 10
    #   population_size: 5
    #   seed: 1027
    #   print_each_pop: true
  variables:
    data_form: "compact"
    data: |
      a2p1, design, continuous, 0.7:0.9
      a2p3, design, continuous, 0.5:0.7
  responses:
    data_form: "compact"
    data: |
      diff_gj,   objective, min, 0.5
      diff_eiyy, objective, min, 0.8
      diff_eizz, objective, min, 0.8
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        directory_tag: on
        directory_save: on
    required_files:
      - "design/*"
      - "scripts/*"
    # asynchronous:
    #   evaluation_concurrency: 20
    # failure_capture:
    #   recover: [1e12, 1e12, 1e12, -1e12]
