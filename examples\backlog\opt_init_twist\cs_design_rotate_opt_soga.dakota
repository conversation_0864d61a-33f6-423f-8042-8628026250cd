# cs_design_rotate_opt_soga.dakota

environment
  output_file = 'cs_design_rotate_opt_soga.out'
  write_restart = 'cs_design_rotate_opt_soga.rst'
  error_file = 'cs_design_rotate_opt_soga.err'
  tabular_data
    tabular_data_file = 'cs_design_rotate_opt_soga_tabular.dat'
  results_output
    results_output_file = 'cs_design_rotate_opt_soga_results'


method
  output  normal
  soga
    max_function_evaluations =  10
    population_size =  5
    seed =  1027
    print_each_pop


model
  single

variables
  active = design

  continuous_design = 1
    descriptors = 'r1'
    upper_bounds = 10
    lower_bounds = 0




interface
  analysis_driver = 'python run.py cs_design_rotate_opt_soga.yml 1'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  'run.py'  'cs_design_rotate_opt_soga.yml'  'design/*'  'scripts/*'


responses
  descriptors =  'diff_gj'  'diff_eiyy'  'diff_eizz'
  objective_functions = 3
    sense =  'min'  'min'  'min'
    weights = 0.5 0.8 0.8
  no_gradients
  no_hessians


