# main.dakota

environment
  output_file = 'main.out'
  write_restart = 'main.rest'
  error_file = 'main.err'
  tabular_data
    tabular_data_file = 'main_tabular.dat'
  results_output
    results_output_file = 'main_results'


method
  multidim_parameter_study
    partitions =  2  1


model
  single

variables
  active = design

  continuous_design = 1
    descriptors = 'lyr_ang'
    upper_bounds = 90
    lower_bounds = -90


  discrete_design_range = 1
    descriptors = 'lyr_ply'
    upper_bounds = 8
    lower_bounds = 1




interface
  analysis_driver = 'ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile {PARAMETERS} --resultfile {RESULTS}'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        link_file =  'main.yml'  'files/*'
      verbatim


responses
  descriptors =  'mu'  'ea'  'gj'  'eiyy'  'eizz'
  response_functions = 5
  no_gradients
  no_hessians


