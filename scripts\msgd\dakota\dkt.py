# Classes for dakota method
import logging
# import os
# import platform
import subprocess as sbp

# import pprint

# from msgd._global import *


logger = logging.getLogger(__name__)

def writeNestedDict(file, d, indent=0):
    for k, v in d.items():
        if v is False:
            continue
        # print('k =', k)
        file.write(' '*indent)
        file.write(k)
        if isinstance(v, dict):
            file.write('\n')
            indent += 2
            writeNestedDict(file, v, indent)
            indent -= 2
        else:
            if (v is None) or (v is True):
                file.write("\n")
                continue

            file.write(" =  ")
            if isinstance(v, list):
                file.write("  ".join(list(map(str, v))))
            elif isinstance(v, str):
                # file.write(f"  '{v}'")
                file.write("'{0}'".format(v))
            elif isinstance(v, int) or isinstance(v, float):
                file.write("{0}".format(v))
            file.write("\n")




# class Dakota():

#     def __init__(self, name):
#         self.name = name
#         # self.keywords = {}
#         # with open('keywords.json', 'r') as fo:
#         #     self.keywords = json.load(fo)
#         self.method_keywords = {
#             'multidim_parameter_study': {
#                 'partitions': []
#             }
#         }

#         self.environment = {}

#         # Same key: value structures as the Dakota input file
#         self.method = {
#             'output': 'normal'
#         }
#         self.model = {}

#         """
#         variables = {
#             'id': '',
#             'active': '',
#             'design': {
#                 'continuous': [
#                     {
#                         'descriptor': 'name',
#                         'upper_bound': 1,
#                         'lower_bound': -1,
#                         'initial': 0
#                     }
#                 ],
#                 'discrete': {
#                     'range': [],
#                     'set': {
#                         'integer': [],
#                         'string': [],
#                         'real': [
#                             {
#                                 'descriptor': 'name',
#                                 'elements': []
#                             }
#                         ]
#                     }
#                 }
#             },
#             'state': {
#                 'continuous': [],
#                 'discrete': {
#                     'range': [],
#                     'set': {
#                         'integer': [],
#                         'string': [],
#                         'real': []
#                     }
#                 }
#             }
#         }
#         """
#         self.variables = {
#             'active': 'design',
#             'design': {
#                 'continuous': [],
#                 'discrete': {
#                     'range': [],
#                     'set': {
#                         'integer': [],
#                         'string': [],
#                         'real': []
#                     }
#                 }
#             },
#             'state': {
#                 'continuous': [],
#                 'discrete': {
#                     'range': [],
#                     'set': {
#                         'integer': [],
#                         'string': [],
#                         'real': []
#                     }
#                 }
#             },

#             # 'continuous_design': {
#             #     'descriptors': [],
#             #     'upper_bounds': [],
#             #     'lower_bounds': []
#             # }
#         }
#         self.interface = {
#             'analysis_driver': 'python',
#             # 'interface_script': 'interface.py',
#             'design_inputs': '',
#             'required_files': [],
#             'concurrency': 1
#         }

#         self.required_files = []

#         """
#         responses = {
#             'id': '',
#             'descriptors': [],
#             'objective_functions': [
#                 {
#                     'descriptor': 'name1',
#                     'sense': 'min'
#                 },
#             ],
#             'response_functions': [],
#             'calibration_functions': [],
#             'inequality_constraints': [
#                 {
#                     'descriptor': 'name2',
#                     'upper_bound': 1e12,
#                     'lower_bound': 1
#                 }
#             ],
#             'gradients': [],
#             'hessians': []
#         }
#         """
#         self.responses = {
#             'descriptors': [],
#             'objective_functions': [],
#             'response_functions': [],
#             'calibration_functions': [],
#             'inequality_constraints': [],
#             'equality_constraints': [],
#             'gradients': [],
#             'hessians': [],
#         }

#         return

#     def summary(self):
#         ppt = pprint.PrettyPrinter()
#         # print('\nkeywords =')
#         # print(self.keywords)
#         print('environment')
#         ppt.pprint(self.environment)
#         print('method')
#         ppt.pprint(self.method)
#         print('model')
#         ppt.pprint(self.model)
#         print('variables')
#         ppt.pprint(self.variables)
#         print('interface')
#         ppt.pprint(self.interface)
#         print('responses')
#         ppt.pprint(self.responses)
#         print()


#     def update(self, input):
#         self.method.update(input['method'])

#         for k, v in input['variables'].items():
#             if v['type'] == 'continuous':
#                 cdv = {
#                     'descriptor': k,
#                     'lower_bound': v['bounds'][0],
#                     'upper_bound': v['bounds'][1]
#                 }
#                 self.variables['design']['continuous'].append(cdv)
#                 # self.variables['continuous_design']['descriptors'].append(k)
#                 # self.variables['continuous_design']['lower_bounds'].append(v['bounds'][0])
#                 # self.variables['continuous_design']['upper_bounds'].append(v['bounds'][1])

#         self.interface.update(input['interface'])
#         # self.interface['design_inputs'] = fn_design_base
#         # dakota.interface['required_files'].append(fn_design)

#         self.responses.update(input['responses'])

#         return


#     def updateResponsesDescriptors(self):
#         descriptors = []
#         try:
#             for f in self.responses['response_functions']:
#                 descriptors.append(f['descriptor'])
#         except KeyError:
#             pass
#         try:
#             for f in self.responses['objective_functions']:
#                 descriptors.append(f['descriptor'])
#         except KeyError:
#             pass
#         try:
#             for f in self.responses['calibration_functions']:
#                 descriptors.append(f['descriptor'])
#         except KeyError:
#             pass
#         try:
#             for f in self.responses['inequality_constraints']:
#                 descriptors.append(f['descriptor'])
#         except KeyError:
#             pass
#         try:
#             for f in self.responses['equality_constraints']:
#                 descriptors.append(f['descriptor'])
#         except KeyError:
#             pass

#         self.responses['descriptors'] = descriptors

#         return


def writeInputEnvironment(msgdstudy, file):
    r"""
    """
    

    # fn_base, fn_ext = os.path.splitext(msgdstudy.fn_mdao)

    file.write("environment\n")
    # file.write(f"  output_file = '{self.name}.out'\n")
    file.write("  output_file = '{}.out'\n".format(msgdstudy.fn_base))
    # file.write(f"  write_restart = '{self.name}.rst'\n")
    file.write("  write_restart = '{}.rest'\n".format(msgdstudy.fn_base))
    # file.write(f"  error_file = '{self.name}.err'\n")
    file.write("  error_file = '{}.err'\n".format(msgdstudy.fn_base))
    file.write("  tabular_data\n")
    file.write("    tabular_data_file = '{}_tabular.dat'\n".format(msgdstudy.fn_base))
    file.write("  results_output\n")
    file.write("    results_output_file = '{}_results'\n".format(msgdstudy.fn_base))
    file.write("\n\n")
    return









def writeInputMethod(msgdstudy, file):
    file.write("method\n")

    # file.write(f"  output  {self.method['output']}\n")
    try:
        file.write("  output  {0}\n".format(msgdstudy.method['output']))
    except KeyError:
        pass

    for k, v in msgdstudy.method.items():
        if k in ['output', 'format']:
            pass
        else:
            file.write("  {0}\n".format(k))
            writeNestedDict(file, v, 4)

    file.write("\n\n")
    return









def writeInputModel(msgdstudy, file):
    file.write("model\n")
    file.write("  single")
    file.write("\n\n")
    return









def writeVariables(msgdstudy, file, vars, var_type, var_domain, var_space_type='', var_class='', write_initial=False):
    r"""Write a specific group of variables to the Dakota input file

    Parameters
    ----------
    file: File object
        Dakota input file
    vars: list
        List of variables
    var_type: {'design', 'state', 'aleatory_uncertain', 'epistemic_uncertain'}
        Type of the variables
    var_domain: {'continuous', 'discrete'}
        Domain of the variables
    var_space_type: {'range', 'set'}
        Type of the variable space
    var_class: {'integer', 'string', 'real'}
        Class of the variables
    """
    nvars = len(vars)

    descriptors = []
    upper_bounds = []
    lower_bounds = []
    elements = []
    nelm_per_var = []
    initials = []

    for var in vars:
        descriptors.append("'{}'".format(var['descriptor']))
        if var_space_type != 'set':
            try:
                upper_bounds.append(var['upper_bound'])
                lower_bounds.append(var['lower_bound'])
            except KeyError:
                pass
        else:
            elements.append(var['elements'])

        if write_initial:
            initials.append(var['initial'])


    if var_space_type != 'set':
        var_type_str = '_'.join([var_domain, var_type])
        if var_domain == 'discrete':
            var_type_str = '_'.join([var_type_str, 'range'])
        file.write("  {} = {}\n".format(var_type_str, nvars))
    else:
        file.write("  {} = {}\n".format(var_class, nvars))

    file.write("    descriptors = {}\n".format('  '.join(descriptors)))

    if var_space_type != 'set':
        if len(upper_bounds) > 0:
            ub_str = '  '.join(list(map(str, upper_bounds)))
            file.write('    upper_bounds = {}\n'.format(ub_str))

        if len(lower_bounds) > 0:
            lb_str = '  '.join(list(map(str, lower_bounds)))
            file.write('    lower_bounds = {}\n'.format(lb_str))

    else:
        file.write('    elements_per_variable =')
        for var_elms in elements:
            file.write('  {}'.format(len(var_elms)))
        file.write('\n')

        file.write('    elements =')
        for var_elms in elements:
            for elm in var_elms:
                if var_class == 'string':
                    file.write("  '{}'".format(elm))
                else:
                    file.write("  {}".format(elm))
        file.write('\n')

    if write_initial:
        if var_type == 'state':
            file.write('    initial_state =')
        else:
            file.write('    initial_point =')

        for init in initials:
            if var_class == 'string':
                file.write("  '{}'".format(init))
            else:
                file.write("  {}".format(init))
    file.write('\n')

    return









def writeInputVariables(msgdstudy, file):
    file.write("variables\n")

    try:
        file.write('  id_variables = {}\n'.format(msgdstudy.variables['id']))
    except KeyError:
        pass

    try:
        file.write('  active = {}\n'.format(msgdstudy.variables['active']))
    except KeyError:
        pass

    file.write('\n')

    for var_type, value1 in msgdstudy.variables.items():
        if var_type in ['active',]:
            continue
        for var_domain, value2 in value1.items():
            if var_domain == 'continuous' and len(value2) > 0:
                writeVariables(msgdstudy, file, value2, var_type, var_domain)
                file.write('\n')
            elif var_domain == 'discrete':
                for var_space_type, value3 in value2.items():
                    if var_space_type == 'range' and len(value3) > 0:
                        writeVariables(msgdstudy, file, value3, var_type, var_domain, var_space_type)
                        file.write('\n')
                    elif var_space_type == 'set' and (sum([len(v) for v in list(value3.values())]) > 0):
                        file.write("  {}_{}_{}\n".format(var_domain, var_type, var_space_type))
                        for var_class, value4 in value3.items():
                            if len(value4) > 0:
                                writeVariables(msgdstudy, file, value4, var_type, var_domain, var_space_type, var_class)
                                file.write('\n')

    # cdv = self.variables['continuous_design']
    # ncdv = len(cdv['descriptors'])
    # if ncdv > 0:
    #     # file.write(f"  continuous_design = {ncdv}\n")
    #     file.write("  continuous_design = {0}\n".format(ncdv))
    #     file.write("    descriptors =  ")
    #     file.write("  ".join(["'{0}'".format(d) for d in cdv['descriptors']]))
    #     file.write("\n")
    #     file.write("    upper_bounds =  ")
    #     file.write("  ".join(list(map(str, cdv['upper_bounds']))))
    #     file.write("\n")
    #     file.write("    lower_bounds =  ")
    #     file.write("  ".join(list(map(str, cdv['lower_bounds']))))
    #     file.write("\n")

    file.write("\n\n")
    return









def writeInputInterface(msgdstudy, file):
    file.write("interface\n")
    # file.write(f"  analysis_driver = '{self.interface['analysis_driver']} {self.interface['interface_script']} {self.interface['design_inputs']}'\n")
    file.write("  analysis_driver = '{}'\n".format(msgdstudy.interface['analysis_driver']))
    if len(msgdstudy.interface['fork']) > 0:
        file.write("    fork\n")
        writeNestedDict(file, msgdstudy.interface['fork'], indent=6)
    # file.write("      parameters_file = 'params.in'\n")
    # file.write("      results_file = 'results.out'\n")
    # file.write("      file_save\n")
    # file.write("      work_directory\n")
    # file.write("        named = 'evals/eval'\n")
    # file.write("        directory_tag\n")
    # file.write("        directory_save\n")

    # if len(msgdstudy.interface['required_files']) > 0:
    # if len(msgdstudy.required_files) > 0:
    #     if platform.system() == 'Windows':
    #         file.write("        copy_file =")
    #     elif platform.system() == 'Linux':
    #         file.write("        link_file =")
    # # file.write(f"  '{self.interface['interface_script']}'")
    # # file.write("  '{0}'".format(self.interface['interface_script']))
    # # file.write(f"  '{self.interface['design_inputs']}'")
    # # file.write("  '{0}'".format(self.interface['design_inputs']))
    #     # for filename in self.interface['required_files']:
    #     for filename in msgdstudy.required_files:
    #         file.write("  '{0}'".format(filename))
    #     file.write("\n")

    try:
        # if msgdstudy.interface['asynchronous']['evaluation_concurrency'] > 1:
        if len(msgdstudy.interface['asynchronous']) > 0:
            file.write('  asynchronous\n')
            writeNestedDict(file, msgdstudy.interface['asynchronous'], indent=4)
    except KeyError:
        pass
    # if self.interface['concurrency'] > 1:
    #     file.write('  asynchronous\n')
    #     file.write('    evaluation_concurrency = {}\n'.format(self.interface['concurrency']))

    if 'failure_capture' in msgdstudy.interface.keys():
        file.write('  failure_capture\n')
        writeNestedDict(file, msgdstudy.interface['failure_capture'], indent=4)

    file.write("\n\n")
    return









def writeInputResponses(msgdstudy, file):
    file.write("responses\n")

    try:
        file.write('  id_responses = {}\n'.format(msgdstudy.responses['id']))
    except KeyError:
        pass

    # Write descriptors
    msgdstudy.updateResponsesDescriptors()
    file.write("  descriptors =")
    for d in msgdstudy.responses['descriptors']:
        file.write("  '{0}'".format(d))
    file.write("\n")

    # Write functions
    # file.write(f"  response_functions = {len(self.responses['descriptors'])}\n")
    rfs = msgdstudy.responses['response_functions']
    if len(rfs) > 0:
        file.write("  response_functions = {0}\n".format(len(rfs)))


    ofs = msgdstudy.responses['objective_functions']
    if len(ofs) > 0:
        file.write("  objective_functions = {0}\n".format(len(ofs)))
        file.write("    sense =")
        for of in ofs:
            file.write("  '{}'".format(of['sense']))
        file.write("\n")

        try:
            _weights = []
            for of in ofs:
                _weights.append(of['weight'])
                # file.write("  {}".format(of['weight']))
            file.write("    weights = ")
            file.write(' '.join(list(map(str, _weights))))
        except KeyError:
            pass

        file.write("\n")


    if len(msgdstudy.responses['calibration_functions']) > 0:
        file.write("  calibration_terms = {0}\n".format(len(msgdstudy.responses['calibration_functions'])))


    ics = msgdstudy.responses['inequality_constraints']
    if len(ics) > 0:
        file.write("    nonlinear_inequality_constraints = {0}\n".format(len(ics)))
        file.write("      upper_bounds =")
        for ic in ics:
            try:
                ub = ic['upper_bound']
            except KeyError:
                ub = ''
            if ub == '':
                ub = '1e12'
            file.write("  {}".format(ub))
        file.write("\n")
        file.write("      lower_bounds =")
        for ic in ics:
            try:
                lb = ic['lower_bound']
            except KeyError:
                lb = ''
            if lb == '':
                lb = '-1e12'
            file.write("  {}".format(lb))
        file.write("\n")


    # Write gradients
    if len(msgdstudy.responses['gradients']) > 0:
        pass
    else:
        file.write("  no_gradients\n")


    # Write hessians
    if len(msgdstudy.responses['hessians']) > 0:
        pass
    else:
        file.write("  no_hessians\n")

    file.write("\n\n")
    return









def writeInput(msgdstudy):
    # if fn_base != '':
    #     input_file = fn_base
    # else:
    #     input_file = msgdstudy.name
    fn = '{}.dakota'.format(msgdstudy.fn_base)
    msgdstudy.fn = fn

    with open(fn, 'w') as fo:
        fo.write("# {}".format(fn))
        fo.write("\n\n")

        writeInputEnvironment(msgdstudy, fo)
        writeInputMethod(msgdstudy, fo)
        writeInputModel(msgdstudy, fo)
        writeInputVariables(msgdstudy, fo)
        writeInputInterface(msgdstudy, fo)
        writeInputResponses(msgdstudy, fo)









def runDakota(fn):
    # print('running dakota...')

    # Run dakota
    dakota_cmd = ['dakota', '-i', fn]
    # print('run: ' + ' '.join(dakota_cmd))
    logger.critical('calling: {}'.format(' '.join(dakota_cmd)))
    # sbp.call(dakota_cmd, shell=True)
    sbp.run(dakota_cmd)

    return

