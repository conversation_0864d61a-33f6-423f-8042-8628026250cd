import argparse
import os

import pandas as pd


def parse_args():
    parser = argparse.ArgumentParser(description='Convert airfoil geometry from .xy to .dat format.')
    parser.add_argument('xyfile', help='Airfoil geometry file in .xy format.')
    parser.add_argument('dest', default=None, help='Optional output file destination. The default is to simply change the extension of xyfile.')
    return parser.parse_args()


def main():

    args = parse_args()
    xyfile = args.xyfile
    dest = args.dest

    base, _ = os.path.splitext(xyfile)

    if dest is not None:
        if os.path.isdir(dest):
            datfile = os.path.join(dest, base) + '.dat'
        else:
            datfile = dest
    else:
        datfile = base + '.dat'

    xy = pd.read_csv(
        xyfile,
        skiprows=[0],
        header=None,
        delim_whitespace=True,
        skipinitialspace=True
    ).drop_duplicates()[::-1]  # drop duplicate point and reverse order

    xy.to_csv(
        datfile,
        sep=' ',
        index=False,
        header=False,
        float_format='%.8f'
    )


if __name__ == '__main__':
    main()
