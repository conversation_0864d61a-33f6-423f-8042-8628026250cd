Analysis of a single cross-section
============================================


The task is to carry out a quick cross-sectional analysis of a specific cross-sectional design.
Specifically, in this example, we want to set values to the locations of spar webs and get the torsional and bending stiffness (|gj|, |eiyy|, |eizz|).


Running
----------------------

Example location: `examples/anl_cs_airfoil_box`

..  code-block:: shell

    ivabs analyze main.yml


Model
-------

This is a very basic airfoil cross-section with a box spar, a front and a back components.
Some basic parameters are listed below.

..  csv-table:: Cross-section parameters
    :header: "Name", "Value"
    :widths: 4, 4
    :align: center

    Airfoil, SC1095
    Coordinate origin, Trailing edge
    Chord length, 1.0
    Pitch angle, 0.0


..  figure:: main.png
    :width: 6in
    :align: center

    Cross-section.


The design of the cross-section is based on a template ``airfoil_simple.xml.tmp``.


Results
---------

Analysis results can be found in the file ``main.out``.

..  list-table:: Results
    :header-rows: 1

    * - Quantity
      - Value
    * - |gj|
      - 2716.1390578
    * - |eiyy|
      - 4077.6188095
    * - |eizz|
      - 246963.3418




.. Specification of the cross-section
.. ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. This block specifies the base design of the cross-section that will be analyzed.

.. ..  code-block:: yaml
..     :linenos:

..     cs:
..       - name: "airfoil"
..         parameter:
..           a2p1: 0.82
..           a2p3: 0.58
..         design:
..           dim: 2
..           tool: "prevabs"
..           base_file: "airfoil_simple.xml.tmp"
..         model:
..           md1:
..             tool: "vabs"

.. ``cs``
..     Root key of the list of cross-section base designs.

.. ``name: "airfoil"``
..     Name of the cross-section.

.. ``parameter:``
..     Root key of the parameter specification.


.. ..  note::

..     For more details on how to prepare the parameterized base design of a cross-section (``airfoil_simple.xml.tmp``), please see :ref:`section-ivabs_parameterization`.




.. Specification of the analysis steps
.. ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. ..  code-block:: yaml
..     :linenos:

..     analysis:
..       steps:
..         - step: "cs analysis"
..           type: "cs"
..           analysis: "h"
..           output:
..             - value: ["gj", "eiyy", "eizz"]


.. ..  note::

..     For the complete list of available keys to get beam properties, see :ref:`section-beam_properties`.



Input files
-----------------------

main.yml
    Main input file.

airfoil_simple.xml.tmp
    Cross-sectional design template.

SC1095.dat
    Airfoil data.

material_database_us_ft.xml
    Material database.

