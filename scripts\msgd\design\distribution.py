from __future__ import annotations

import copy
import logging
# import os

import numpy as np
# import pprint


import msgd.utils as mutils
import msgd.ext as mext
from msgd.core._data_classes import DataBase



logger = logging.getLogger(__name__)


def loadData(
    form, yname=[], ytype='float',
    xdim=1, xscale=1.0,
    raw_data=None,
    file_name='', file_format='', request_data_name=[],
    parameters={},
    **kwargs
    ):
    """
    """

    logger.info(f'loading data ({form})...')

    logger.debug(locals())

    data = {}
    """
    {
        'data1': {
            'x': [...],
            'y': [...],
        },
        ...
    }
    """
    for _yn in yname:
        data[_yn] = {'x': [], 'y': []}

    if not isinstance(ytype, list):
        ytype = [ytype,]*len(yname)

    if not isinstance(xscale, list):
        xscale = [xscale,]*xdim

    if form == 'explicit':
        for _d in raw_data:
            _x = _d['coordinate']
            if not isinstance(_x, list):
                _x = [_x,]
            if not xscale is None:
                _x = [_x[i]*xscale[i] for i in range(xdim)]

            _y = _d['value']
            if not isinstance(_y, list):
                _y = [_y,]

            for _yn, _yv in zip(yname, _y):
                data[_yn]['x'].append(_x)
                data[_yn]['y'].append(_yv)


    elif form == 'compact':
        logger.debug('raw_input =\n{}'.format(raw_data))

        lines = raw_data.splitlines()
        for row in lines:
            temp = row.split(',')
            # print(temp)

            # _x = [float(v.strip()) for v in temp[:xdim]]
            _x = []
            for _i, _v in enumerate(temp[:xdim]):
                try:
                    _v = parameters[_v.strip()]
                except KeyError:
                    pass

                _v = float(_v)

                # pprint.pprint(locals())
                
                if not xscale is None:
                    _v = _v * xscale[_i]

                _x.append(_v)

            # if not xscale is None:
            #     _x = [xi*xscalei for xi, xscalei in zip(_x, xscale)]

            # _y = [eval(t)(v.strip()) for t, v in zip(ytype, temp[xdim:])]
            _y = []
            for _t, _v in zip(ytype, temp[xdim:]):
                try:
                    _v = parameters[_v.strip()]
                except KeyError:
                    pass

                # print(locals())

                _y.append(eval(_t)(_v))


            for _yn, _yv in zip(yname, _y):
                data[_yn]['x'].append(_x)
                data[_yn]['y'].append(_yv)


    elif form == 'file':

        _request_name = request_data_name
        if not isinstance(request_data_name, list):
            _request_name = [request_data_name,]

        _data = mext.loadDataFromFile(
            file_name=file_name,
            file_format=file_format,
            yname=_request_name,
            xscale=xscale,
            **kwargs
            )

        for _yname, _rname in zip(yname, _request_name):
            data[_yname] = _data[_rname]

    # for _n, _d in data.items():
    #     print(f'data {_n}')
    #     print(f'  x = {_d["x"]}')
    #     print(f'  y = {_d["y"]}')

    return data









# ====================================================================

class Distribution():
    """
    A class for handling parameter distributions with numpy vectorization support.
    """

    def __init__(
        self, name:str|list, region:str, domain=None,
        func_base:str|mutils.MSGDFunction=None,
        vtype='float', coefficient:dict={},
        xdim=1, xscale=1,
        interp_data_form='', interp_data=None,
        file_name='', file_format='',
        data_request='',
        configs:dict={}
        ):
        self.name = name
        self.region = region
        self.domain = domain
        self.func_base = func_base
        self._vtype = vtype
        self._coef:dict = coefficient

        self._xdim = xdim
        self.xscale = xscale  # float or 'nondim'

        self._interp_data_form = interp_data_form
        self._interp_data = interp_data
        self._file_name = file_name
        self._file_format = file_format
        self._data_request = data_request
        self._configs = configs

        self._function = []
        self._names = [name] if not isinstance(name, list) else name

    def __call__(self, x, _id=0, return_dict=True, **kwargs):
        """
        Evaluate the distribution function at given points.

        Parameters
        ----------
        x: array-like, shape (npoints, ndims)
            Coordinates of the points.
        _id: int, default=0
            ID for domain transformation.
        return_dict: bool, default=True
            If True, returns a dictionary of results.
            If False, returns a numpy array of shape (npoints, nfunctions).
        **kwargs: dict
            Additional arguments for domain transformation.

        Returns
        -------
        Union[dict, np.ndarray]:
            If return_dict is True, returns a dictionary with function names as keys.
            If return_dict is False, returns a numpy array of shape (npoints, nfunctions).
        """
        # Ensure x is a 2D numpy array
        x = np.asarray(x)
        if x.ndim != 2:
            raise ValueError("Input x must be 2D array")
        # if x.ndim == 1:
        #     x = x.reshape(-1, 1)
        # elif x.ndim > 2:
        #     raise ValueError("Input x must be 1D or 2D array")

        # Transform domain
        x_transformed = self.transform_domain(x, _id=_id, **kwargs)
        
        # Pre-allocate results array
        n_points = x.shape[0]
        n_funcs = len(self._function)
        results = np.empty((n_points, n_funcs))

        # Evaluate all functions
        for i, func in enumerate(self._function):
            y = func(x_transformed)
            # Handle scalar outputs
            if np.isscalar(y):
                results[:, i] = y
            else:
                results[:, i] = y.ravel()

        if return_dict:
            return {name: results[:, i] for i, name in enumerate(self._names)}
        return results

    def transform_domain(self, x, _id=0, **kwargs):
        """
        Transform coordinates to the appropriate domain.

        Parameters
        ----------
        x: np.ndarray, shape (npoints, ndims)
            Input coordinates.
        _id: int, default=0
            ID for domain transformation.
        **kwargs: dict
            Additional arguments for domain transformation.

        Returns
        -------
        np.ndarray:
            Transformed coordinates.
        """
        if isinstance(self.domain, str):
            if self.domain == 'xy':
                return x[:, :2]
            return x
        else:
            # Local import to avoid circular dependency
            from msgd.design.transformation import Domain
            if isinstance(self.domain, Domain):
                return self.domain.transform(x, _id=_id, **kwargs)
        return x

    @property
    def vtype(self): return self._vtype
    @property
    def function(self): return self._function
    @property
    def coef(self): return self._coef
    @property
    def interp_data(self): return self._interp_data
    @property
    def interp_data_form(self): return self._interp_data_form

    def toDictionary(self):
        _dict = {'name': self.name}

        if isinstance(self._function, str):
            _dict['function'] = self._function
        elif isinstance(self._function, mutils.MSGDFunction):
            _dict['function'] = self._function.name

        if self._vtype:
            _dict['type'] = self._vtype

        if self.xscale != 1.0:
            _dict['xscale'] = self.xscale

        if self._coef:
            _dict['coefficient'] = self._coef

        if self._interp_data_form:
            _dict['data_form'] = self._interp_data_form

        if self._interp_data:
            _dict['data'] = self._interp_data

        return _dict

    def updateCoefficient(self, coef:dict):
        # print(f'self._coef = {self._coef}')
        # print(f'coef = {coef}')
        mutils.substituteParams(self._coef, coef)

        # print(self._interp_data)

        if self._interp_data_form == 'compact':
            _rows = self._interp_data.split('\n')
            for _i, _row in enumerate(_rows):
                _items = _row.split(',')
                for _j, _item in enumerate(_items):
                    # print(f'item = {_item}')
                    try:
                        _items[_j] = str(coef[_item.strip()])
                    except KeyError:
                        pass
                # print(f'items = {_items}')
                _rows[_i] = ','.join(_items)
            self._interp_data = '\n'.join(_rows)

        else:
            mutils.substituteParams(self._interp_data, coef)

        # print(self._interp_data)

        try:
            self.xscale = coef[self.xscale]
        except KeyError:
            pass


    def implement(
        self, db_function:DataBase, db_domain, parameters={},
        **kwargs
        ):
        """
        Implement the distribution function.
        """
        logger.info(f'[{self.name}] implementing parameter distribution...')

        # Get domain
        if isinstance(self.domain, str):
            for _d in db_domain:
                if _d.name == self.domain:
                    self.domain = _d
                    break

        # Get function base
        if isinstance(self.func_base, str):
            _func_base = db_function.getItemByName(self.func_base)
        else:
            _func_base = self.func_base

        logger.debug(f'_func_base = {_func_base}')

        # Handle interpolation functions
        if isinstance(_func_base, (mutils.InterpolationFunction, mutils.ScatterInterpolationFunction)):
            _data = loadData(
                form=self._interp_data_form,
                yname=self._names,
                ytype=self._vtype,
                xdim=self._xdim,
                xscale=self.xscale,
                raw_data=self._interp_data,
                file_name=self._file_name,
                file_format=self._file_format,
                request_data_name=self._data_request,
                parameters=parameters,
                **self._configs
            )

            # Update function value types
            _vtypes = [self._vtype] * len(self._names) if not isinstance(self._vtype, list) else self._vtype

            for i, name in enumerate(self._names):
                _func = copy.deepcopy(_func_base)
                _func.vtype = _vtypes[i]

                _x = np.asarray(_data[name]['x'])
                _y = np.asarray(_data[name]['y'])

                # Ensure 2D arrays
                if _x.ndim == 1:
                    _x = _x.reshape(-1, 1)
                if _y.ndim == 1:
                    _y = _y.reshape(-1, 1)

                _func.implement(_x, _y)
                logger.debug(_func.toDictionary())
                self._function.append(_func)

        # Handle script functions
        elif isinstance(_func_base, mutils.ScriptFunction):
            _func = copy.deepcopy(_func_base)
            _func.coef = self._coef
            _func.implement()
            self._function = [_func]

        return self




# def loadDistribution(distr_input, func_lib=None, params={}, **kwargs):
#     """Create the parameter distribution from inputs.

#     Parameters
#     ----------

#     Returns
#     -------
#     obj:`msgd.utils.function.MSGDFunction`
#         Distribution function of a parameter.
#     """

#     # if not logger:
#     #     logger = mul.initLogger(__name__)

#     logger.info('loading parameter distribution...')

#     # dobj = None

#     dname = distr_input.get('name', None)

#     ddomain = distr_input.get('domain', None)

#     function = distr_input.get('function', None)
#     # print('function =', function)

#     logger.debug(f'local variables:\n{mutils.convertDict2Str(locals())}')

#     if isinstance(function, list):
#         pass
#         # for _func in function:
#         #     _fname = _func.get('name')

#     elif function == 'interpolation':
#         # yname = distr_input['yname']
#         try:
#             yname = distr_input['name']
#         except KeyError:
#             try:
#                 yname = distr_input['yname']
#             except KeyError:
#                 try:
#                     yname = distr_input['ynames']
#                 except KeyError:
#                     return
#         if not isinstance(yname, list):
#             yname = [yname, ]


#         try:
#             ytype = distr_input['ytype']
#         except KeyError:
#             try:
#                 ytype = distr_input['ytypes']
#             except KeyError:
#                 ytype = 'float'

#         # ftype = distr_input.get('type', None)

#         # Function defined in the 'distribution'
#         # if ftype.lower().startswith('interp'):

#         interp_data = distr_input.get('data', None)
#         data_form = distr_input.get('data_form', 'explicit')
#         interp_kind = distr_input.get('kind', 'linear')
#         fill_value = distr_input.get('fill_value', 'extrapolate')
#         xndim = distr_input.get('xdimension', 1)

#         try:
#             xscale = distr_input['xscale']
#         except KeyError:
#             try:
#                 xscale = distr_input['xscales']
#             except KeyError:
#                 xscale = 1.0

#         # print('interp_data')
#         # print(interp_data)

#         dfuncs = createInterpolationDistribution(
#             yname=yname, ytype=ytype, interp_data=interp_data,
#             input_form=data_form, interp_kind=interp_kind, fill_value=fill_value,
#             xndim=xndim, xscale=xscale, other_input=distr_input
#         )

#         # print(dfuncs)

#         dict_distr = {}
#         if not isinstance(yname, list):
#             yname = [yname,]
#             dfuncs = [dfuncs,]
#         for _i, _name in enumerate(yname):
#             _distr = Distribution(domain=ddomain, function=dfuncs[_i])
#             dict_distr[_name] = _distr
#             # dict_distr[piname] = dfuncs[i]
#         # else:
#         #     dict_distr[yname] = dfuncs

#         return dict_distr



#     elif isinstance(function, str):
#         # Use the function stored in flib
#         fcoefs = distr_input.get('coefficients', None)
#         finput = func_lib[function]

#         fform = finput['type']

#         if fform == 'expression':
#             expr = finput['expression']
#             coef_names = finput['coefficients']
#             return_type = finput.get('return_type', 'float')
#             dfunc = mutils.CustomFunction(expr, coef_names, return_type=return_type)
#             dfunc.implement(fcoefs)
#             logger.debug('{} = {}'.format(dname, str(dfunc)))

#         elif fform == 'script':
#             file_name = finput['file_name']
#             pyf_name = finput['function_name']
#             import_expr = 'from {} import {}'.format(file_name, pyf_name)
#             exec(import_expr)
#             func = eval(pyf_name)
#             dfunc = mutils.ScriptFunction(func, fcoefs)

#         distr = Distribution(domain=ddomain, function=dfunc)

#         return {dname: distr}









# def calcParamsAtPoint(nid, ncoords, pdistr:Distribution, transformations={}):
#     """
#     """

#     _coords = copy.deepcopy(ncoords)
#     logger.debug(f'_coords = ({_coords[0]}, {_coords[1]}, {_coords[2]})')

#     # Transform the coordinates if necessary
#     _domain_name = pdistr.domain
#     if not _domain_name is None:
#         logger.debug(f'_domain_name = {_domain_name}')
#         _transf = transformations[_domain_name]
#         if _transf.getType() == 'function':
#             _coords = _transf(_coords)
#         elif _transf.getType() == 'list':
#             _coords = _transf(nid, input_type='id')
#         logger.debug(f'-> _coords = ({_coords[0]}, {_coords[1]}, {_coords[2]})')

#     pvalue = pdistr(_coords)

#     return pvalue




# def calcParamsAtPoints(points:list, pdistr:Distribution, transf:Transformation):
#     """Calculate parameters values at given points.

#     Parameters
#     ----------
#     points:list[int, list|tuple]
#         List of points (pid, pcoords) where the parameter is evaluated.
#     pdistr:
#         Distribution of the parameter.
#     transf:
#         Transformation of the domain.

#     Returns
#     -------
#     dict
#         Parameter values at every point.
#     """

#     pvalues = {}

#     for pid, pcoords in points:

#         _coords = copy.deepcopy(pcoords)
#         logger.debug(f'_coords = ({_coords[0]}, {_coords[1]}, {_coords[2]})')

#         # Transform the coordinates if necessary
#         # _domain_name = pdistr.getDomainName()
#         # if not _domain_name is None:
#         if not transf is None:
#             # logger.debug(f'_domain_name = {_domain_name}')
#             # _transf = transformations[_domain_name]
#             if transf.getType() == 'function':
#                 _coords = transf(_coords)
#             elif transf.getType() == 'list':
#                 _coords = transf(pid, input_type='id')
#             logger.debug(f'-> _coords = ({_coords[0]}, {_coords[1]}, {_coords[2]})')

#         pvalue = pdistr(_coords)

#         pvalues[pid] = pvalue

#     return pvalues




# def calcParamsFromDistr(
#     distributions,
#     model_nodes=None, model_elems=None,
#     sg_assignment=None, sg_key='sg',
#     # locations=None,
#     # trans_func=None, nodes_param=None, nodes_id_map=None,
#     # transformations:dict={},
#     rel_tol=1e-9, abs_tol=1e-12
#     ):
#     """Calculate specific values from parameter distributions.

#     Parameters
#     ------------

#     nodes_param:
#         Nodal coordinates in the parametric domain.

#     nodes_id_map:
#         Nodal id mapping from the physical domain to the parametric domain.
#     """

#     logger.info('calculating parameters from distribution...')

#     logger.debug(f'local variables:\n{mutils.convertDict2Str(locals())}')

#     # sets = {}
#     '''Definitation of sets

#     ..  code-block::

#         {
#             'set1_name': {
#                 'type': 'node' or 'element' or 'coordinate',
#                 'entities': []
#             },
#             ...
#         }
#     '''

#     sg_base_sets:list[dict] = []
#     '''Set-SG base design name pairs

#     ..  code-block::

#         [
#             {
#                 'region': set_name,
#                 'sg': sg_base_design_name
#             },
#             ...
#         ]
#     '''

#     param_sets = {}
#     """
#     param_sets = {
#         'set1 name': {'p1': v11, 'p2': v21},
#         'set2 name': {'p1': v12, 'p2': v22},
#         ...
#     }
#     """

#     # elem_sets = {}
#     entity_sets = {}
#     """
#     elem_sets = {
#         'set1 name': [eid1, eid2, ...],
#         'set2 name': [eid1, eid2, ...],
#         ...
#     }
#     """
#     # eid_to_set = {}  # Map from element id to set name

#     node_params = {}
#     """
#     node_params = {
#         n1: {'p1': v11, 'p2': v21},
#         n2: {'p1': v12, 'p2': v22},
#         ...
#     }
#     """

#     # if locations:
#     if not model_elems:
#         # No elements are given
#         # Evaluate distributions using nodal coordinates only

#         _count = 0

#         # for i, loc in enumerate(locations):
#         for _nid, _ncoord in model_nodes.items():
#             _count += 1
#             _params = {'location': _ncoord}

#             for pname, pdistr in distributions.items():
#                 pvalue = pdistr(_ncoord)
#                 _params[pname] = pvalue

#             _setname = 'set{}'.format(_count)
#             entity_sets[_setname] = {'type': 'node', 'entities': [_nid,]}
#             param_sets[_setname] = _params
#             # sets[_setname] = {
#             #     'type': 'coordinate',
#             #     'entities': [_ncoord,]
#             # }

#             for _i, _sg_assign in enumerate(sg_assignment):
#                 # print(_sg_assign)
#                 _region = _sg_assign.region
#                 _sg_base_name = _sg_assign.sg_model

#                 # Check if the location is inside the region
#                 if _region == 'all':
#                     sg_base_sets.append({
#                         'region': _setname,
#                         sg_key: _sg_base_name
#                     })


#     else:
#         # Calculate parameters from node and element data

#         # For each element:
#         # 1. calculate the averaged parameters
#         # 2. group elements into element sets by parameters
#         set_id = 0

#         for eid, enodes in model_elems.items():

#             # Get the point coordinate where plate/shell properties are needed
#             # ec = structure.model.calcElementCentroidT(ei, trans=structure.ft)
#             # print(ec)

#             elem_params = {}  # {'p1': v11, 'p2': v21}

#             for pname in distributions.keys():
#                 elem_params[pname] = 0

#             # For each element node
#             for nid in enodes:

#                 try:
#                     # elayups[nid] = node_layups[nid]
#                     # params = node_params[nid]
#                     for _pn, _pv in node_params[nid].items():
#                         elem_params[_pn] += _pv
#                 except KeyError:
#                     # Get the nodal coordinates
#                     ncoords = model_nodes[nid]

#                     # Get the design at the point
#                     nparams = {}
#                     for pname, pdistr in distributions.items():

#                         pvalue = pdistr(ncoords)
#                         # pvalue = calcParamsAtPoint(nid, ncoords, pdistr, transformations)

#                         nparams[pname] = pvalue
#                         elem_params[pname] += pvalue

#                     # print('nparams =', nparams)

#                     node_params[nid] = nparams
#                 # print(layup_ec.layers[0].material_orient)
#                 # layup_ec.summary()
#                 # elem_params[nid] = copy.deepcopy(params)

#             # Calculate the average parameters
#             for _pn, _pv in elem_params.items():
#                 elem_params[_pn] = _pv / len(enodes)
#             # print(elem_params)



#             # Check if duplicated design parameters
#             found = False
#             for _setname, _params in param_sets.items():
#                 if mutils.compareDictionaries(elem_params, _params, rel_tol=rel_tol, abs_tol=abs_tol):
#                     # Found existing design of layup SG
#                     found = True
#                     entity_sets[_setname]['entities'].append(eid)
#                     break
#             if not found:
#                 # New design of layup SG
#                 set_id += 1
#                 _setname = 'set{}'.format(set_id)
#                 entity_sets[_setname] = {'type': 'element', 'entities': [eid, ]}
#                 param_sets[_setname] = elem_params


#     return sg_base_sets, param_sets, entity_sets, node_params









# def createInterpolationDistribution(
#     name=None, yname=None, ytype='float', input_form='explicit',
#     interp_kind='linear', interp_data=None, fill_value='extrapolate',
#     xndim=1, xscale=1.,
#     other_input=None
#     ):
#     """
#     Parameters
#     ----------
#     interp_data : str or list or dict
#         Parameter distribution input

#     Returns
#     -------
#     """

#     logger.info('reading distribution of parameter {}...'.format(yname))

#     dobjs = []

#     if isinstance(yname, str):
#         yname = [yname, ]
#     yndim = len(yname)

#     if isinstance(ytype, str):
#         ytype = [ytype, ]*yndim

#     if isinstance(fill_value, str):
#         fill_value = [fill_value, ]*yndim

#     logger.debug(f'local variables:\n{mutils.convertDict2Str(locals())}')
#     # mul.logDictionary(locals())
#     # logger.debug('yname = {}'.format(yname))
#     # logger.debug('yndim = {}'.format(yndim))
#     # logger.debug('ytype = {}'.format(ytype))
#     # logger.debug('fill_value = {}'.format(fill_value))
#     # logger.debug('input_form = {}'.format(input_form))

#     if input_form == 'explicit':
#         x, y = [], []
#         for _d in interp_data:
#             _x = _d['coordinate']
#             _y = _d['value']
#             x.append(_x if isinstance(_x, list) else [_x,])
#             y.append(_y if isinstance(_y, list) else [_y,])

#         for j, yjname in enumerate(yname):
#             yj = [rowi[j] for rowi in y]
#             dobj = mutils.InterpolationFunction(x, y, interp_kind)
#             dobjs.append(dobj)
#             logger.debug('{}: {}'.format(yjname, str(dobj)))

#     elif input_form == 'compact':
#         # raw_data = pd['data']

#         logger.debug('interp_data =\n{}'.format(interp_data))

#         x, y = [], []
#         lines = interp_data.splitlines()
#         for row in lines:
#             temp = row.split(',')
#             # print(temp)
#             x.append([float(v.strip()) for v in temp[:xndim]])
#             y.append([eval(t)(v.strip()) for t, v in zip(ytype, temp[xndim:])])

#         if len(x) == 1:
#             if interp_kind == 'previous':
#                 x.append([xi+1 for xi in x[0]])
#                 y.append(y[0])

#         for j, yjname in enumerate(yname):
#             yj = [rowi[j] for rowi in y]
#             # print(x)
#             # print(yj)
#             # pobj = mup.Parameter(name=yjname, type=ytype[j])
#             dobj = mutils.InterpolationFunction(x, yj, interp_kind, fill_value=fill_value[j], ytype=ytype[j])
#             # blade.parameters[yname] = pobj
#             dobjs.append(dobj)
#             logger.debug('{}: {}'.format(yjname, str(dobj)))

#     elif input_form == 'file':

#         file_name = other_input['file_name']
#         file_format = other_input['file_format']

#         xs, ys = mext.loadDataFromFile(file_name, file_format, yname, xscale, other_input)

#         for xj, yj, yjtype in zip(xs, ys, ytype):
#             dobj = mutils.InterpolationFunction(xj, yj, interp_kind, ytype=yjtype)
#             dobjs.append(dobj)

#     return dobjs


