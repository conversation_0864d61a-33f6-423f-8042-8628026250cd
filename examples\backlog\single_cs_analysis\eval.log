CRITICAL [2023-06-27 14:57:28] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 14:57:42] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 14:57:42] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2023-06-27 14:58:05] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 14:58:05] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2023-06-27 14:59:58] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 14:59:58] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2023-06-27 15:03:49] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 15:03:49] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2023-06-27 15:05:37] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 15:05:37] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2023-06-27 15:05:47] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2023-06-27 15:05:47] dkt.runDakota :: calling: dakota -i main.dakota 
