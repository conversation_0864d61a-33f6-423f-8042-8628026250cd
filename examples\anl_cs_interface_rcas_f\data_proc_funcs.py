# import numpy as np
from scipy import interpolate
import pprint

try:
    # import msgpi.utils.params as mup
    import msgpi.utils.function as muf
except ModuleNotFoundError:
    import msgd.utils.function as muf

#import msgd.ms.rcas as mmr
import msgd.ext.rcas as meg

# Pre-processing


def materialId2Name(data, sname, logger, *args, **kwargs):
    processed = {}

    # pprt = pprint.PrettyPrinter(indent=4)
    # pprt.pprint(data)
    mdb = {
        1: 'AS4 12k/E7K8_0.0054',
        2: 'S2/SP381_0.0092',
        3: 'T650-35 12k/976_0.0052',
        4: 'T700 24K/E765_0.0056',
    }
    # data[sname]['lam_spar_1'] = mdb[data[sname]['lamid_spar_1']]
    # data[sname]['lam_front'] = mdb[data[sname]['lamid_front']]
    # data[sname]['lam_back'] = mdb[data[sname]['lamid_back']]
    # data[sname]['mat_fill_te'] = data[sname]['lam_back'].split('_')[0]

    processed['lam_spar_1'] = mdb[data['lamid_spar_1']]
    processed['lam_front'] = mdb[data['lamid_front']]
    processed['lam_back'] = mdb[data['lamid_back']]
    processed['mat_fill_te'] = processed['lam_back'].split('_')[0]

    return processed


# def preProcessGeo(data, sname, logger, *args, **kwargs):
#     # Calculate embedded points
#     data[sname]['pfle1_a2'] = data[sname]['pnsmc_a2'] - data[sname]['nsmr'] - 0.005
#     data[sname]['pfle2_a2'] = data[sname]['wl_a2'] + 0.005
#     data[sname]['pfte1_a2'] = data[sname]['wt_a2'] - 0.01

#     return









# Post-processing

def dakota_postpro(data, sname, logger, *args, **kwargs):
    """ Post-process results from previous analysis steps.

    This is the last step in a Dakota evaluation.
    The goal is to calculate response/objective/constraint function values
    and store them in data['dakota']:

        data['dakota'] = {
            'descriptor1': value1,
            'descriptor2': value2,
            ...
        }
    """

    pprt = pprint.PrettyPrinter(indent=4)

    # cs_names = list(outputs['cs'].keys())
    blade_name = data['structures']['blade'][0]
    cs_names = data['structures']['cs']

    # weights = kwargs['weights']
    bp_names = kwargs['beam_properties']

    # Convert data into interpolation functions
    ref_bp = kwargs['ref_properties']
    ref_bp_func_type = ref_bp['function']
    ref_bp_funcs = {}
    if ref_bp_func_type == 'interpolation':
        intp_kind = ref_bp['kind']
        try:
            ref_bp_form = ref_bp['data_form']
        except KeyError:
            ref_bp_form = 'compact'
        if ref_bp_form == 'file':
            ref_bp_fn = ref_bp['file_name']
            ref_bp_format = ref_bp['data_format']
            if ref_bp_format == 'rcas_prop':
                ref_prop = meg.readRcasProp(ref_bp_fn)
                ref_prop_request = ref_bp['data_request']
                for bpn, bpr in zip(bp_names, ref_prop_request):
                    # print('bpn: {}, bpr: {}'.format(bpn, bpr))
                    # print(ref_prop[bpr.upper()])
                    ref_bp_funcs[bpn] = muf.InterpolationFunction(
                        ref_prop[bpr.upper()]['x'],
                        ref_prop[bpr.upper()]['y'],
                        kind=intp_kind
                    )
            # ndimx = 1
            # ref_bp_funcs = mup.loadDataCSV(ref_bp_fn, ndimx, kind=intp_kind)
    # xscale = kwargs['xscale']

    # diffs = np.zeros(len(cs_names))
    diffs = {'gj': [], 'ei22': [], 'ei33': [], 'mu': [], 'sc2': [], 'mc2': []}
    # mpls = np.zeros(len(cs_names))

    # Radial locations of cross-sections
    # rs = np.zeros(len(cs_names))
    rs = data[blade_name]['stations']
    for i, cs_name in enumerate(cs_names):
        cs_data = data[cs_name]

        # diff = []

        for bp_name in bp_names:
            # fn = pp[0]  # function name
            # rn = pp[1]  # response name
            # cs_args = pp[2]
            rn = bp_name + '_diff'

            # Get calculated value
            bp_cal = cs_data[bp_name]
            # if bp_name == 'mc2':
            #     bp_cal = bp_cal - cs_data['sc2']
            #     bp_name = bp_name + '_sc'

            # Get reference/target value
            bp_ref_name = bp_name + '_ref'
            ref_bp_func = ref_bp_funcs[bp_name]
            # print(rs[i]*xscale)
            bp_ref = float(ref_bp_func(rs[i]))
            # print(type(bp_ref).__name__)
            cs_data[bp_ref_name] = bp_ref

            r = (bp_cal - bp_ref) / bp_cal
            # diff.append(r)
            diffs[bp_name].append(r**2)

            cs_data[rn] = r
            data['dakota']['diff_{}_{}'.format(cs_name, bp_name)] = r


        # weighted_squared_diffs = [diff[i]**2 * weights[i] for i in range(len(diff))]
        # diffs[i] = sum(weighted_squared_diffs)

        # mpls[i] = cs_outputs['mu']

    # diff_obj = sum(diffs)
    # outputs['final'].insert(0, ['diff', diff_obj])
    data['dakota']['diff_gj'] = sum(diffs['gj'])
    data['dakota']['diff_eiyy'] = sum(diffs['ei22'])
    data['dakota']['diff_eizz'] = sum(diffs['ei33'])
    data['dakota']['diff_mu'] = sum(diffs['mu'])
    data['dakota']['diff_sc2'] = sum(diffs['sc2'])
    data['dakota']['diff_mc2'] = sum(diffs['mc2'])
    

    # ttm = 0
    # for i in range(int(len(rs) / 2)):
    #     ttm += (mpls[2*i] + mpls[2*i+1]) / 2.0 * (rs[2*i+1] - rs[2*i])
    # ttm = ttm * inputs['rotor_radius']
    # outputs['final'].insert(1, ['total_mass_c', ttm])


    # Process strength ratio
    sr_min = None

    for i, cs_name in enumerate(cs_names):
        for k, v in data[cs_name].items():
            if k.startswith('sr_case'):
                if not sr_min:
                    sr_min = v
                elif v > 0 and v < sr_min:
                    sr_min = v

    data['dakota']['sr_min'] = sr_min

    return

'''
def QCoffset(data, sname, logger, *args, **kwargs):
    
    oml = mmr.readRcasOml('oml.dat')

    return
'''

def qc2csFrame(data, sname, logger, *args, **kwargs):
    ''' Transform a web location in the quarter cord frame of reference to
        the CS frame of reference:
        
    Quarter Chord: Origin is at the blade quarter chord (global refererance frame)
    CS:            Origin is at the trailing edge of the blade (Cross-section origin)

    This transform will keep the web following the Y-axis of the blade even 
    if the quarter chord moves forward or aft
    '''

    processed = {}

    fileo = 'oml.dat'

    r = []
    QC = []
    # R = data[sname]['R']
    R = data['R']
    #Chord = data[sname]['chord']    
 
    flag = 0
    i = 0
    with open(fileo, 'r') as f:
        oml_log = f.readlines()
        for line in oml_log:
            if line[0] == 'S':
                var = line.split()
                if var[1] == 'AERONODE':
                    flag = 1
                else: 
                    flag = 0
            elif flag == 1: # read in AERONODE
                var = line.split()
                if var[0] == 'a':
                    r.append(float(var[2])/R) # non-dim (r/R)
                    QC.append(float(var[3])) 
                    i += 1
    qc_fn = interpolate.interp1d(r, QC)  # create linear interoplation function 
    # Y = qc_fn(data[sname]['x'])
    Y = qc_fn(data['location'])

    # data[sname]['a2p1'] = data[sname]['web_le'] + 3/4 + Y #/ Chord # data[sname]['Y']
    # data[sname]['a2p3'] = data[sname]['web_te'] + 3/4 + Y #/ Chord # data[sname]['Y']
    # if data[sname]['a2p3'] < 0.205:
    #     data[sname]['a2p3'] = data[sname]['a2p1'] - 0.02
    processed['a2p1'] = data['web_le'] + 3/4 + Y #/ Chord # data[sname]['Y']
    processed['a2p3'] = data['web_te'] + 3/4 + Y #/ Chord # data[sname]['Y']
    if processed['a2p3'] < 0.205:
        processed['a2p3'] = processed['a2p1'] - 0.02
    #print(data[sname]['a2p1'], data[sname]['web_le'])
    #print(data[sname]['a2p3'], data[sname]['web_te'])

    return processed

# def calcRelDiff(response_name, cs_data, *args, **kwargs):
#     bp_name = args[0]
#     target = args[1]

#     value = outputs[bp_name]
#     xo2_le = -1.0 * cs_data['chord'] * cs_data['oa2']

#     if 'sc2_le_diff' in response_name:
#         value += xo2_le
#     elif 'mc2_le_diff' in response_name:
#         value += xo2_le

#     rv = (value - target) / target

#     return rv


