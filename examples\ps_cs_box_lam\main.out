Writing new restart file 'main.rest'.

>>>>> Executing environment.

>>>>> Running multidim_parameter_study iterator.

Multidimensional parameter study variable partitions of
                                     2
                                     1

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                     -9.0000000000e+01 lyr_ang
                                     1 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 1:
Active set vector = { 1 1 1 1 1 }
                      1.9929690000e-06 mu
                      2.0916959001e+04 ea
                      1.4719892030e+03 gj
                      1.0989467554e+03 eiyy
                      2.6833902918e+03 eizz



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                      0.0000000000e+00 lyr_ang
                                     1 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 2:
Active set vector = { 1 1 1 1 1 }
                      1.9929690000e-06 mu
                      3.0329070000e+05 ea
                      1.4730544292e+03 gj
                      1.5934012122e+04 eiyy
                      3.8906806382e+04 eizz



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      9.0000000000e+01 lyr_ang
                                     1 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 3:
Active set vector = { 1 1 1 1 1 }
                      1.9929690000e-06 mu
                      2.0916959001e+04 ea
                      1.4719892030e+03 gj
                      1.0989467554e+03 eiyy
                      2.6833902918e+03 eizz



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                     -9.0000000000e+01 lyr_ang
                                     8 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 4:
Active set vector = { 1 1 1 1 1 }
                      1.5186072000e-05 mu
                      1.5943848239e+05 ea
                      1.0207521757e+04 gj
                      7.3797379379e+03 eiyy
                      1.8871892323e+04 eizz



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                      0.0000000000e+00 lyr_ang
                                     8 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 5:
Active set vector = { 1 1 1 1 1 }
                      1.5186072000e-05 mu
                      2.3110216000e+06 ea
                      1.0228623957e+04 gj
                      1.0694427223e+05 eiyy
                      2.7337538659e+05 eizz



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                      9.0000000000e+01 lyr_ang
                                     8 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 6:
Active set vector = { 1 1 1 1 1 }
                      1.5186072000e-05 mu
                      1.5943848239e+05 ea
                      1.0207521757e+04 gj
                      7.3797379379e+03 eiyy
                      1.8871892323e+04 eizz


<<<<< Function evaluation summary: 6 total (6 new, 0 duplicate)


At least one correlation coefficient is nan or inf. This commonly occurs when
discrete variables (including histogram variables) are present, a response is
completely insensitive to variables (response variance equal to 0), there are
fewer samples than variables, or some samples are approximately collinear.

Simple Correlation Matrix among all inputs and outputs:
                  lyr_ang      lyr_ply           mu           ea           gj         eiyy         eizz 
     lyr_ang  1.00000e+00 
     lyr_ply  0.00000e+00  1.00000e+00 
          mu  0.00000e+00  1.00000e+00  1.00000e+00 
          ea  0.00000e+00  4.65831e-01  4.65831e-01  1.00000e+00 
          gj  0.00000e+00  9.99999e-01  9.99999e-01  4.67251e-01  1.00000e+00 
        eiyy  0.00000e+00  4.57460e-01  4.57460e-01  9.99836e-01  4.58885e-01  1.00000e+00 
        eizz  0.00000e+00  4.60666e-01  4.60666e-01  9.99936e-01  4.62089e-01  9.99977e-01  1.00000e+00 

Partial Correlation Matrix between input and output:
                       mu           ea           gj         eiyy         eizz 
     lyr_ang         -nan  0.00000e+00  0.00000e+00  0.00000e+00  0.00000e+00 
     lyr_ply  1.00000e+00  4.65831e-01  9.99999e-01  4.57460e-01  4.60666e-01 

Simple Rank Correlation Matrix among all inputs and outputs:
                  lyr_ang      lyr_ply           mu           ea           gj         eiyy         eizz 
     lyr_ang  1.00000e+00 
     lyr_ply  0.00000e+00  1.00000e+00 
          mu  0.00000e+00  1.00000e+00  1.00000e+00 
          ea  0.00000e+00  5.02519e-01  5.02519e-01  1.00000e+00 
          gj  0.00000e+00  9.04534e-01  9.04534e-01  8.18182e-01  1.00000e+00 
        eiyy  0.00000e+00  5.02519e-01  5.02519e-01  1.00000e+00  8.18182e-01  1.00000e+00 
        eizz  0.00000e+00  5.02519e-01  5.02519e-01  1.00000e+00  8.18182e-01  1.00000e+00  1.00000e+00 

Partial Rank Correlation Matrix between input and output:
                       mu           ea           gj         eiyy         eizz 
     lyr_ang  0.00000e+00  0.00000e+00  0.00000e+00  0.00000e+00  0.00000e+00 
     lyr_ply  1.00000e+00  5.02519e-01  9.04534e-01  5.02519e-01  5.02519e-01 


<<<<< Iterator multidim_parameter_study completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =       0.01 [parent =    0.01806, child =   -0.00806]
  Total wall clock =      7.628
