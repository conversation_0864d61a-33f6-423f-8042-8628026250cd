from odbAccess import openOdb
from textRepr import *
import sys
import csv
# import math

textReprOptions.setValues(
    maxRecursionDepth=3,
    maxElementsInSequence=10,
)

var_name_map = {
    'sf1': 'n11',
    'sf2': 'n22',
    'sf3': 'n12',
    'sm1': 'm11',
    'sm2': 'm22',
    'sm3': 'm12',
}


def main(odb_name, *args):

    position = ''
    set_name = ''
    step = 'Step-1'
    frame = -1
    fmt_result = ''

    argc = len(args)
    i = 0
    while (i < argc):
        if (args[i] == '-fieldvar'):
            i += 1
            field_name = args[i]
        elif (args[i] == '-position'):
            i += 1
            position = args[i]
        elif (args[i] == '-setname'):
            i += 1
            set_name = args[i]
        elif (args[i] == '-step'):
            i += 1
            step = args[i]
        elif (args[i] == '-frame'):
            i += 1
            frame = int(args[i])
        elif (args[i] == '-filename'):
            i += 1
            fn_result = args[i]
        elif (args[i] == '-fileformat'):
            i += 1
            fmt_result = args[i]
        i += 1

    print 'reading result from odb: ' + odb_name
    odb = openOdb(path=odb_name)

    result = getOutputFromOdb(
        odb, position, set_name, field_name,
        step_name=step, frame_id=frame
        )

    odb.close()

    writeResultToFile(
        result,
        fn_result,
        file_format=fmt_result,
        position=position
        )

    print '\ndone'

    print ''

    return



def getOutputFromOdb(
    odb, position, set_name, field_name,
    step_name='Step-1', frame_id=-1
    ):
    print ''

    print 'position =', position
    print 'set_name =', set_name
    print 'field_name =', field_name

    result = []
    """
    [
        'node/element': label,
        'data': {
            'field_name_1': value_1,
            'field_name_2': value_2,
            ...
        },
        ...
    ]
    """

    # Get field outputs
    # print field.upper()
    # print odb.steps.values()
    step = odb.steps[step_name]
    print '\nstep.frames ='
    # prettyPrint(step.frames)

    frame = step.frames[frame_id]
    field_outputs = frame.fieldOutputs

    field_names = field_name.split(',')

    _result_raw = {}
    """
    {
        label: {
            sub_label: {
                field_name_1: value_1,
                field_name_2: value_2,
                ...
            },
            ...
        },
        ...
    }
    """

    for _field_name in field_names:
        field = field_outputs[_field_name.upper()]
        # print '\nfield ='
        # prettyPrint(field)

        if position == 'node':
            _set = odb.rootAssembly.nodeSets[set_name.upper()]
            field_set = field.getSubset(region=_set)
            labels = field_set.componentLabels
            for _i, _value in enumerate(field_set.values):
                _result = {
                    'node': _value.nodeLabel,
                    'data': {}
                }
                for label, value in zip(labels, _value.data):
                    _result['data'][label.lower()] = value
                    # try:
                    # except KeyError:
                    #     result[_value.nodeLabel] = {label.lower(): value}
                result.append(_result)

        elif position == 'element':
            _set = odb.rootAssembly.elementSets[set_name.upper()]
            field_set = field.getSubset(region=_set)
            labels = field_set.componentLabels
            for _i, _value in enumerate(field_set.values):
                # _result['element'] = _value.elementLabel
                if not _value.elementLabel in _result_raw:
                    _result_raw[_value.elementLabel] = {}
                if not _value.nodeLabel in _result_raw[_value.elementLabel]:
                    _result_raw[_value.elementLabel][_value.nodeLabel] = {}

                for label, value in zip(labels, _value.data):
                    _result_raw[_value.elementLabel][_value.nodeLabel][label.lower()] = value
                    # try:
                    #     result[_value.elementLabel][label.lower()] += value
                    # except KeyError:
                    #     result[_value.elementLabel] = {label.lower(): value}

            # print '\n_result_raw ='
            # prettyPrint(_result_raw)


    if position == 'element':
        # Calculate average values
        for _element_label, _node_data in _result_raw.items():
            _nnode = len(_node_data)

            _result = {
                'element': _element_label,
                'data': {}
            }

            _index = -1
            for _i, _r in enumerate(result):
                if _r['element'] == _element_label:
                    _result = _r
                    _index = _i
                    break

            for _node_label, _data in _node_data.items():
                # if not _element_label in result:
                #     result[_element_label] = {}
                for _label, _value in _data.items():
                    try:
                        _result['data'][_label] += _value / _nnode
                    except KeyError:
                        _result['data'][_label] = _value / _nnode
            # result[_element_label] = {k: v/len(_node_data) for k, v in result[_element_label].items()}

            if _index == -1:
                result.append(_result)
            else:
                result[_index] = _result

        # result.append(_result_raw)

        # print '\nfield_set ='
        # prettyPrint(field_set)
        # print '\nfield_set.locations ='
        # prettyPrint(field_set.locations)
        # print '\nfield_set.locations[0] ='
        # prettyPrint(field_set.locations[0])
        # print '\nfield_set.values ='
        # prettyPrint(field_set.values)

        # data_set = []
        # for _i, _value in enumerate(field_set.values):
        #     # print '\nfield_set.values[', _i, '] ='
        #     # prettyPrint(_value)
        #     _result = {}

        #     for label, value in zip(labels, _value.data):
        #         result[label.lower()] = value

        #     if position == 'node':
        #         _result['node'] = _value.nodeLabel
        #     elif position == 'element':
        #         _result['element'] = _value.elementLabel

            # result.append(_result)
            # if data_set is None:
            #     data_set = _value.data
            # else:
            #     data_set += _value.data

        # data_set /= len(field_set.values)
        # data_set = field_set.values[0].data
        # print data_u_set


    print '\nresult =', result

    return result


def writeResultToFile(result, file_name, file_format='', position=''):
    """
    Parameters
    ----------
    result : dict
        result dictionary
    file_name : str
        file name to write the result
    """
    print 'writing result to file: ' + file_name

    if file_format == 'csv':
        with open(file_name, 'wb') as file:
            writer = csv.writer(file)
            # Write header
            _col_names = [position,]
            for k, v in result[0]['data'].items():
                try:
                    _col_names.append(var_name_map[k])
                except KeyError:
                    # _col_names.append(k)
                    pass
                # _col_names.append(k)
            writer.writerow(_col_names)

            # Write data
            for _i, _result in enumerate(result):
                _entry = [_result[position],]
                for k, v in _result['data'].items():
                    if k in var_name_map.keys():
                        _entry.append(v)
                writer.writerow(_entry)

    else:
        with open(file_name, 'a') as file:
            for k, v in result[0]['data'].items():
                file.write('{} = {}\n'.format(k, v))


    return


if __name__ == '__main__':

    main(sys.argv[1], *sys.argv[2:])
    # print sys.argv
    # main('Job-1.odb', 'abq_result.dat', 'abq_section_loads.csv')

