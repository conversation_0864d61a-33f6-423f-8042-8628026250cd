import sys
import yaml
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QPushButton, QTextEdit, QLineEdit, QLabel, 
    QFormLayout, QDoubleSpinBox, QComboBox)
from PySide6.QtCore import Signal, Qt
# import pyqtgraph as pg

class FormWindow(QWidget):
    yaml_updated = Signal(str)
    
    def __init__(self, yaml_data, yaml_editor):
        super().__init__()
        self.yaml_data = yaml_data
        self.yaml_editor = yaml_editor
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("Form Editor")
        layout = QVBoxLayout()
        self.form_layout = QFormLayout()
        self.fields = {}

        if 'cs' in self.yaml_data:
            cs = self.yaml_data['cs'][0]
            params = cs.get('parameter', {})
            
            self.fields['a2p1'] = QDoubleSpinBox()
            self.fields['a2p1'].setRange(0.0, 10.0)
            self.fields['a2p1'].setValue(params.get('a2p1', 0.0))
            self.form_layout.addRow(QLabel("a2p1"), self.fields['a2p1'])
            
            self.fields['a2p3'] = QDoubleSpinBox()
            self.fields['a2p3'].setRange(0.0, 10.0)
            self.fields['a2p3'].setValue(params.get('a2p3', 0.0))
            self.form_layout.addRow(QLabel("a2p3"), self.fields['a2p3'])
            
            self.fields['gms'] = QDoubleSpinBox()
            self.fields['gms'].setRange(0.0, 1.0)
            self.fields['gms'].setDecimals(6)
            self.fields['gms'].setValue(params.get('gms', 0.0))
            self.form_layout.addRow(QLabel("Global Mesh Size (gms)"), self.fields['gms'])
            
            self.fields['fill_mat_front'] = QComboBox()
            self.fields['fill_mat_front'].addItems(["foam", "honeycomb", "metal"])
            self.fields['fill_mat_front'].setCurrentText(params.get('fill_mat_front', 'foam'))
            self.form_layout.addRow(QLabel("Fill Material Front"), self.fields['fill_mat_front'])
            
            self.fields['fill_mat_back'] = QComboBox()
            self.fields['fill_mat_back'].addItems(["foam", "honeycomb", "metal"])
            self.fields['fill_mat_back'].setCurrentText(params.get('fill_mat_back', 'honeycomb'))
            self.form_layout.addRow(QLabel("Fill Material Back"), self.fields['fill_mat_back'])
        
        self.update_form_btn = QPushButton("Update YAML")
        self.update_form_btn.clicked.connect(self.update_yaml_from_form)
        self.form_layout.addWidget(self.update_form_btn)
        
        layout.addLayout(self.form_layout)
        self.setLayout(layout)
    
    def update_yaml_from_form(self):
        if 'cs' in self.yaml_data:
            cs = self.yaml_data['cs'][0]
            cs['parameter']['a2p1'] = self.fields['a2p1'].value()
            cs['parameter']['a2p3'] = self.fields['a2p3'].value()
            cs['parameter']['gms'] = self.fields['gms'].value()
            cs['parameter']['fill_mat_front'] = self.fields['fill_mat_front'].currentText()
            cs['parameter']['fill_mat_back'] = self.fields['fill_mat_back'].currentText()
        self.yaml_editor.setText(yaml.dump(self.yaml_data, default_flow_style=False))

class EditorWindow(QWidget):
    def __init__(self, yaml_data):
        super().__init__()
        self.yaml_data = yaml_data
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("YAML Editor")
        layout = QVBoxLayout()
        self.yaml_editor = QTextEdit()
        self.yaml_editor.setText(yaml.dump(self.yaml_data, default_flow_style=False))
        layout.addWidget(self.yaml_editor)
        self.setLayout(layout)

class ConsoleWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("Command Line")
        layout = QVBoxLayout()
        self.console_output = QTextEdit()
        self.console_output.setReadOnly(True)
        self.console_input = QLineEdit()
        self.console_button = QPushButton("Run Command")
        self.console_button.clicked.connect(self.run_command)
        layout.addWidget(self.console_output)
        layout.addWidget(self.console_input)
        layout.addWidget(self.console_button)
        self.setLayout(layout)
    
    def run_command(self):
        command = self.console_input.text()
        self.console_output.append(f"> {command}")
        self.console_input.clear()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    yaml_data = yaml.safe_load(open("main.yml"))
    editor_window = EditorWindow(yaml_data)
    form_window = FormWindow(yaml_data, editor_window.yaml_editor)
    console_window = ConsoleWindow()
    
    form_window.show()
    editor_window.show()
    console_window.show()
    
    sys.exit(app.exec())
