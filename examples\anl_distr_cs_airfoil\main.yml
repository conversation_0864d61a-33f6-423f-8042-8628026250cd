version: "0.10"

structure:
  name: "blade"

  parameter:
    length: 30
    chord_root: 2
    chord_tip: 1
    web_f_root: 0.8
    web_f_tip: 0.75
    web_r_root: 0.6
    web_r_tip: 0.65
    ply_spar_root: 20
    ply_spar_r1: 16
    ply_spar_tip: 8

  distribution:
    # Chord length
    - name: "chord"
      function: "f_interp_linear"
      xscale: length
      data:
        - coordinate: 0
          value: chord_root
        - coordinate: 1
          value: chord_tip

    # Horizontal locations of the shear webs
    - name: ["a2p1", "a2p3"]
      function: "f_interp_linear"
      xscale: length
      data_form: 'compact'
      data: |
        0, web_f_root, web_r_root
        1, web_f_tip,  web_r_tip

    # Main spar thickness
    - name: ['ply_spar',]
      function: 'f_interp_linear'
      type: 'int'
      xscale: length
      data_form: 'compact'
      data: |
        0,   ply_spar_root
        0.2, ply_spar_r1
        1,   ply_spar_tip

  model:
    section_locations:
      type: 'curvilinear'
      coordinates: [0, 6, 15, 27, 30]

  cs_assignment:
    - region: 'all'
      location: 'node'
      cs: 'main_cs'

  cs:
    - name: 'main_cs'
      design: "airfoil"
      model:
        type: "bm1"
        solver: 'vabs'
        tool_version: '4.0'


# ====================================================================
function:
  - name: 'f_interp_prev'
    type: 'interpolation'
    interp_kind: 'previous'
  - name: 'f_interp_linear'
    type: 'interpolation'
    interp_kind: 'linear'


# ====================================================================
cs:
  - name: "airfoil"
    builder: "prevabs"
    parameter:
      mdb_name: "material_database_us_ft"
      airfoil: 'SC1095.dat'
      airfoil_point_order: -1
      chord: 1
      a2p1: 0.8  # Horizontal location of the front shear web
      a2p3: 0.6  # Horizontal location of the rear shear web
      lam_spar: "T300 15k/976_0.0053"
      lam_cap: "Aluminum 8009_0.01"
      lam_front: "T300 15k/976_0.0053"
      lam_back: "T300 15k/976_0.0053"
      ang_spar: 0
      ang_front: 0
      ang_back: 0
      ply_spar: 1
      ply_front: 1
      ply_back: 1
      mat_nsm: "lead"
      rnsm: 0.001 # Radius of nonstructural mass
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "AS4 12k/E7K8"
      gms: 0.002  # Global Mesh Size
      fms: 0.04   # Filling Component Mesh Size
    design:
      base_file: "airfoil_gbox_uni.xml.tmp"


# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      output:
        value: [
          'mu', 'gyry', 'gyrz',
          'ea', "gj", "eiyy", "eizz",
          'mcy', 'mcz', 'tcy', 'tcz',
        ]

