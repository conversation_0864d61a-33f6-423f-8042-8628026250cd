---
description: 
globs: 
alwaysApply: true
---
# Coding Style Guide

This document outlines the coding style conventions used in the MSG Design framework.

## Function Naming

- Use snake_case (lowercase with underscores) for function names
- Example:
  ```python
  def calculate_stress():
      pass

  def update_material_properties():
      pass
  ```

## Documentation Style

Use NumPy style docstrings for all functions, classes, and modules. The format is:

```python
def function_name(param1, param2):
    """Short description of the function.

    Longer description of the function if needed.

    Parameters
    ----------
    param1 : type
        Description of param1
    param2 : type
        Description of param2

    Returns
    -------
    return_type
        Description of the return value

    Notes
    -----
    Additional notes about the function if needed.

    Examples
    --------
    >>> function_name(value1, value2)
    result
    """
    pass
```

### Class Documentation

```python
class MyClass:
    """Short description of the class.

    Longer description of the class if needed.

    Parameters
    ----------
    param1 : type
        Description of param1
    param2 : type
        Description of param2

    Attributes
    ----------
    attr1 : type
        Description of attr1
    attr2 : type
        Description of attr2

    Notes
    -----
    Additional notes about the class if needed.

    Examples
    --------
    >>> instance = MyClass(value1, value2)
    >>> instance.method()
    result
    """
```

### Module Documentation

```python
"""
Short description of the module.

Longer description of the module if needed.

Notes
-----
Additional notes about the module if needed.

Examples
--------
>>> from module import function
>>> function()
result
"""
```

## Examples from the Codebase

### Function Example
From [_msgd.py](mdc:scripts/msgd/_msgd.py):
```python
def evaluate(self):
    """Evaluate the study.

    This method runs the analysis workflow for the current study.

    Returns
    -------
    bool
        True if the evaluation was successful, False otherwise.

    Notes
    -----
    This method is called by Dakota during optimization.
    """
```

### Class Example
From [_structure.py](mdc:scripts/msgd/core/_structure.py):
```python
class StructureModel:
    """Structure model for MSG analysis.

    This class represents a structural model for MSG analysis.

    Parameters
    ----------
    name : str
        Name of the structure model
    config : dict
        Configuration parameters

    Attributes
    ----------
    name : str
        Name of the structure model
    config : dict
        Configuration parameters
    """
```

## Best Practices

1. Always include type hints in function parameters and return values
2. Document all public functions, classes, and modules
3. Include examples in docstrings where appropriate
4. Keep docstrings up to date with code changes
5. Use clear and descriptive names for functions and variables
6. Follow PEP 8 for general Python style guidelines

