from __future__ import annotations

# import typing
import math
# import msgd.model.beam as mmb
# import msgd.model.io as sgio
import sgio
# from msgd.core import StructureModel
# import msgd.design.blade as mbld
import msgd.utils as mutils
import pprint


# class GEBTModel():
#     pass


# class GEBTAnalysis():
#     pass


# class GEBTBeamMember():
#     r"""Class for a GEBT beam memebr.
#     """

#     def __init__(self):
#         #: list of ints: Point labels.
#         #: `[beginning point, ending point]`
#         self._points = []

#         # #: list of ints: Cross-section labels.
#         # #: `[beginning, ending]`
#         # self.css = []

#         #: int: Frame id
#         self.frame_id: int = 0
#         #: int: Curvature id
#         self.curv_id: int = 0

#         # #: int: Number of division of the segment.
#         # self.num_divisions: int = 30

#     @property
#     def points(self): return self._points

#     def __str__(self):
#         # s = f'{self.member_id:4d}'
#         s = '{:4d}'.format(self.member_id)
#         # s += f'{self.points[0]:4d}{self.points[1]:4d}'
#         s += '{:4d}{:4d}'.format(self.points[0], self.points[1])
#         # # s += f'{self.css[0]:4d}{self.css[1]:4d}'
#         # s += '{:4d}{:4d}'.format(self.css[0], self.css[1])
#         # s += f'{self.frame_id:4d}{self.num_divisions:4d}{self.curv_id:4d}'
#         s += '{:4d}{:4d}{:4d}'.format(self.frame_id, self.num_divisions, self.curv_id)
#         return s


#     def summary(self):
#         print('member:', self.member_id)
#         print('points:', self.points)
#         print('sectional properties:', self.css)
#         print('frame:', self.frame_id)
#         print('divisions:', self.num_divisions)
#         print('initial curvature:', self.curv_id)




class GEBTCondition():
    def __init__(self):
        self.type = None  # 'point' or 'member'

        #: int: Point or member id
        self.object_id = None

        #: list of int: Constrianed degrees of freedom (point condition only)
        self.dofs = [0, 0, 0, 0, 0, 0]

        #: list of int: ID of distributed function to each dof (member condition only)
        self.dfids = [0, 0, 0, 0, 0, 0]

        #: list of float: Constrained values corresponding to each dof
        self.values = [0., 0., 0., 0., 0., 0.]

        #: list of int: ID of time function corresponding to each dof
        self.time_funcs = [0, 0, 0, 0, 0, 0]

        #: list of int: Whether constrained condition is a follower (1) or not (0)
        self.followers = [0, 0, 0, 0, 0, 0]

    def summary(self):
        print('object id:', self.object_id)
        if self.type == 'point':
            print('dofs:', self.dofs)
        elif self.type == 'member':
            print('dfids:', self.dfids)
        print('values:', self.values)
        print('time functions:', self.time_funcs)
        print('follower loads:', self.followers)

    def writeGEBTInput(self, file, write_comment=False):
        # file.write(f'{self.object_id}\n')
        file.write('{:4d}'.format(self.object_id))
        if write_comment:
            if self.type == 'point':
                file.write('  # Point boundary and loading conditions')
            elif self.type == 'member':
                file.write('  # Member boundary and loading conditions')
        file.write('\n')

        if self.type == 'point':
            mutils.writeFormatIntegers(file, self.dofs, '16d')
        elif self.type == 'member':
            mutils.writeFormatIntegers(file, self.dfids, '16d')

        mutils.writeFormatFloats(file, self.values, '16.6E')
        mutils.writeFormatIntegers(file, self.time_funcs, '16d')
        mutils.writeFormatIntegers(file, self.followers, '16d')

        file.write('\n')




class GEBTTimeFunction():
    def __init__(self, fid, ftype=0, time_range=[0, 1], entries=[]):
        self.id = fid
        self.type = ftype
        self.time_range = time_range
        self.entries = entries

        return

    def writeGEBTInput(self, file):
        mutils.writeFormatIntegers(file, [self.id,], '4d', newline=False)
        if self.id == 1:
            file.write('  # Time functions')
        file.write('\n')
        mutils.writeFormatIntegers(file, [self.type,], '8d')
        mutils.writeFormatFloats(file, self.time_range, '16.6E')
        mutils.writeFormatIntegers(file, [len(self.entries),], '8d')
        for _entry in self.entries:
            mutils.writeFormatFloats(file, _entry, '16.6E')

        return




class GEBTNodeResult():
    """One set of result of a keypoint or element mid point.
    """

    def __init__(self):
        #: str: Type of the node ('point' or 'element')
        self.type = None

        #: int: ID of the node
        self.id = 0

        #: list of floats: Coordinates of the node
        self.coord = [0, 0, 0]

        #: list of floats: Global displacement at the node
        self.u = [0, 0, 0]

        #: list of floats: Global rotation at the node
        self.r = [0, 0, 0]

        #: list of floats: Sectional force at the node
        self.f = [0, 0, 0]

        #: list of floats: Sectional moment at the node
        self.m = [0, 0, 0]

        #: list of floats: Linear momenta at the node
        self.p = [0, 0, 0]

        #: list of floats: Angular momenta at the node
        self.h = [0, 0, 0]


    def get(self, name:str):
        """
        """
        _name = name.lower()
        _comp = int(_name[-1]) - 1

        if _name.startswith('x'):
            return self.coord[_comp]
        elif _name.startswith('u'):
            return self.u[_comp]
        elif _name.startswith('r'):
            return self.r[_comp]
        elif _name.startswith('f'):
            return self.f[_comp]
        elif _name.startswith('m'):
            return self.m[_comp]
        elif _name.startswith('p'):
            return self.p[_comp]
        elif _name.startswith('h'):
            return self.h[_comp]


    def getResultFlattened(self):
        """Get the result in a flattened list.

        Returns
        -------
        list of numbers
            Flattened result of the node.
            Arrangement of data::

                [ID, X1, X2, X3, U1, U2, U3, R1, R2, R3, F1, F2, F3, M1, M2, M3, P1, P2, P3, H1, H2, H3]

        """
        v = [self.id,]
        for _v in [self.coord, self.u, self.r, self.f, self.m, self.p, self.h]:
            v.extend(list(_v))

        return v


    def outputRecordInOneLine(self, analysis):
        record = ''

        record += '{:8s}'.format(self.type)
        record += '{:8d}'.format(self.id)

        record += mutils.listToString(self.coord, fmt='16.7E')
        record += mutils.listToString(self.u, fmt='16.7E')
        record += mutils.listToString(self.r, fmt='16.7E')
        record += mutils.listToString(self.f, fmt='16.7E')
        record += mutils.listToString(self.m, fmt='16.7E')

        if analysis > 0:
            record += mutils.listToString(self.p, fmt='16.7E')
            record += mutils.listToString(self.h, fmt='16.7E')

        return record




class GEBTBeamResult():
    """Results of the whole beam for a single step or eigenvalue
    """

    def __init__(self):
        #: list of :obj:`GEBTNodeResult`: Key point results
        self.points = []

        #: list of list of :obj:`GEBTNodeResult`: Member results
        self.members = []

    def printAll(self, analysis):
        head = '{0:8s}{1:>8s}'.format('type', 'id')
        for name in ['x', 'u', 'r', 'f', 'm']:
            for i in ['1', '2', '3']:
                head += '{0:>16s}'.format(name+i)
        if analysis > 0:
            for name in ['p', 'm']:
                for i in ['1', '2', '3']:
                    head += '{0:>16s}'.format(name+i)
        print(head)

        for p in self.points:
            print(p.outputRecordInOneLine(analysis))

        for i, m in enumerate(self.members):
            print('member', i+1)
            for e in m:
                print(e.outputRecordInOneLine(analysis))

    def getResultById(self, which:str, id:int):
        """
        Get the result of a node by its ID.

        Parameters
        ----------
        which : str
            Type of the node ('point' or 'member')
        id : int
            ID of the node

        Returns
        -------
        :obj:`GEBTNodeResult`
            Result of the node.
        """
        if which.startswith('p'):
            for _p in self.points:
                if _p.id == id:
                    return _p

        elif which.startswith('m'):
            for _i, _m in enumerate(self.members):
                if _i+1 == id:
                    return _m

        return

    def getOutputPointFlattened(self):
        output = []
        for _p in self.points:
            output.append(_p.getResultFlattened())
        return output

    def getOutputMemberFlattened(self):
        output = []
        for _m in self.members:
            for _e in _m:
                output.append(_e.getResultFlattened())
        return output

    def getResultAllSortedFlattened(self):
        """
        Get all results of the beam in an array
        with nodes sorted.

        Parameters
        ----------

        Returns
        -------
        list of list of float
            Flattened results of the beam.
        """
        results = []

        for _i in range(len(self.members)):
            _pt_result = self.points[_i]
            results.append(_pt_result.getResultFlattened())

            _mb_result = self.members[_i]
            for _nd_result in _mb_result:
                results.append(_nd_result.getResultFlattened())

        results.append(self.points[-1].getResultFlattened())

        return results



class GEBTBeam():
    """Class for the GEBT beam model.

    The followings should be specified for a valid model:

    - structure design

        - geometry (beam reference line)
        - sectional properties

    - meshing settings

        - global mesh size
        - element type

    - analysis settings
    - boundary conditions
    - loading conditions
    """

    def __init__(self, name:str=''):
        #: str: Name of the beam.
        self.name = name

        #: str: Name of the GEBT input file
        self.input_file = ''

        # Geometry
        # --------
        #: dict of {int, list of floats}: Key point id and coordinates.
        #: `{ptid: [x1, x2, x3], ...}`
        self._points:dict[int, list[float]] = {}
        #: dict of {int, msgpi.ms.beam.GEBTBeamMember}: Beam members
        #: `{id: [beginning point id, ending point id], ...}`
        self._members:dict[int, list[int]] = {}

        self._init_curvatures = []  #: Initial curvatures
        #: Assignments of initial curvatures to members
        #: `{member_id: curv_id, ...}`
        self._init_curv_assignments:dict[int, int] = {}

        #: dict: Point sets
        self.point_sets = {}
        #: dict: Member sets
        self.member_sets = {}


        # Sectional properties
        # --------------------
        #: dict of {int, msgpi.sg.BeamProperty}: Effective properties of cross-sections.
        #: `{section_id: BeamProperty object, ...}`
        self._sections:dict[int, sgio.Model] = {}

        self._section_id_to_name = {}

        #: Section assignments
        #: `{member_id: [section_id0, section_id1], ...}`
        self._section_assignments = {}

        #: dict of {int, list} Local frames
        self._local_frames = {}

        #: Assignements of local frames to members
        #: `{member_id: frame_id, ...}`
        self._local_frame_assignments:dict[int, int] = {}

        # self.pt_name_id = {}
        self.bp_name_id = {}

        self.sg_names = []

        # Boundary and loading conditions
        # -------------------------------
        #: list: Point conditions (B.C. and loads).
        self.point_conditions: list[GEBTCondition] = []
        #: list: Member conditions (B.C. and loads).
        self.member_conditions: list[GEBTCondition] = []
        self._distr_loads = {}  #: Distribution loads `{id: coefs, ...}`

        # Meshing settings
        # ----------------
        #: int: Global mesh size
        self._global_mesh_size = 0
        #: Divisions of members
        #: `{member_id: num_divisions, ...}`
        self._member_divisions:dict = {}

        # Analysis
        # --------
        #: int: Analysis type (GEBT).
        self._analysis = 0
        #: int: Max iteration.
        self._max_iteration = 1
        #: int: Number of analysis steps.
        self._num_steps = 1
        #: int: Number of eigen analysis resutls.
        self._num_eigens = 0
        #: list of floats: Angular velocity of the rotating beam.
        #: `[wa1, wa2, wa3]`
        self._angular_velocity = [0, 0, 0]
        #: list of int: Time function of angular velocity.
        #: `[tf_wa1, tf_wa2, tf_wa3]`
        self._av_tf = [0, 0, 0]
        #: list of floats: Linear velocity of the first key point.
        #: `[va1, va2, va3]`
        self._linear_velocity = [0, 0, 0]
        #: list of int: Time function of linear velocity.
        #: `[tf_va1, tf_va2, tf_va3]`
        self._lv_tf = [0, 0, 0]

        self._sim_range = [0, 1]
        self._time_functions = []  #: Time functions


        # Results
        # self.results = None  #: Results of GEBT analysis
        self.results_steps = []
        '''Analysis result for each step.

        ..  code-block::

            [
                beam_result_step_1:GEBTBeamResult,
                beam_result_step_2:GEBTBeamResult,
                beam_result_step_3:GEBTBeamResult,
                ...
            ]
        '''

        self.results_eigen = []

    # @property
    # def name(self): return self._name
    @property
    def points(self): return self._points
    @points.setter
    def points(self, pts:dict[int, list[float]]): self._points = pts
    @property
    def members(self): return self._members
    @members.setter
    def members(self, mbs:dict[int, list[int]]): self._members = mbs
    @property
    def member_divisions(self): return self._member_divisions
    @property
    def parts(self): return self._members
    @parts.setter
    def parts(self, mbs:dict[int, list[int]]): self._members = mbs
    @property
    def cell_sets(self): return self.member_sets
    @property
    def init_curvatures(self): return self._init_curvatures
    @init_curvatures.setter
    def init_curvatures(self, curvs:list): self._init_curvatures = curvs
    # @property
    # def elements(self):
    #     elms = {}
    #     for _mid, _member in self.members.items():
    #         elms[_mid] = _member.points
    #     return elms
    @property
    def sections(self): return self._sections
    @sections.setter
    def sections(self, secs:dict[int, sgio.Model]): self._sections = secs
    @property
    def section_assignments(self): return self._section_assignments
    @property
    def local_frames(self): return self._local_frames
    @local_frames.setter
    def local_frames(self, lfs:dict[int, list]): self._local_frames = lfs
    @property
    def local_frame_assignments(self): return self._local_frame_assignments

    @property
    def analysis(self): return self._analysis
    @analysis.setter
    def analysis(self, a:int): self._analysis = a
    @property
    def max_iteration(self): return self._max_iteration
    @max_iteration.setter
    def max_iteration(self, mi:int): self._max_iteration = mi
    @property
    def num_steps(self): return self._num_steps
    @num_steps.setter
    def num_steps(self, ns:int): self._num_steps = ns
    @property
    def num_eigens(self): return self._num_eigens
    @num_eigens.setter
    def num_eigens(self, ne:int): self._num_eigens = ne
    @property
    def angular_velocity(self): return self._angular_velocity
    @angular_velocity.setter
    def angular_velocity(self, av:list): self._angular_velocity = av
    @property
    def av_tf(self): return self._av_tf
    @av_tf.setter
    def av_tf(self, avtf:list): self._av_tf = avtf
    @property
    def linear_velocity(self): return self._linear_velocity
    @linear_velocity.setter
    def linear_velocity(self, lv:list): self._linear_velocity = lv
    @property
    def lv_tf(self): return self._lv_tf
    @lv_tf.setter
    def lv_tf(self, lvtf:list): self._lv_tf = lvtf

    @property
    def distr_loads(self): return self._distr_loads
    @property
    def time_functions(self): return self._time_functions

    @property
    def num_points(self): return len(self.points)
    @property
    def num_members(self): return len(self.members)
    @property
    def num_local_frames(self): return len(self._local_frames)
    @property
    def num_init_curvatures(self): return len(self._init_curvatures)
    @property
    def num_sections(self): return len(self.sections)
    @property
    def num_point_conditions(self): return len(self.point_conditions)
    @property
    def num_member_conditions(self): return len(self.member_conditions)
    @property
    def num_distr_loads(self): return len(self._distr_loads)
    @property
    def num_time_functions(self): return len(self._time_functions)

    @property
    def mass(self):
        mass = 0

        for _i in range(self.num_members):
            _mid = _i + 1
            _sid0, _sid1 = self._section_assignments[_mid]
            _bp0 = self._sections[_sid0]
            _bp1 = self._sections[_sid1]
            _mu0 = _bp0.get('mu')
            _mu1 = _bp1.get('mu')
            _mu_avg = (_mu0 + _mu1) / 2
            _mlen = self.getMemberSize(_mid)
            _mass = _mu_avg * _mlen
            mass += _mass

        return mass

    def addPoint(self, pid:int, coords:list[float]):
        self._points[pid] = coords

    def addMember(self, mid:int, points:list[int]):
        self._members[mid] = points

    def addDistributedFunction(self, dfid:int, coefs:list[float]):
        self._distr_loads[dfid] = coefs

    def addSection(self, sid:int, bp:sgio.Model):
        self._sections[sid] = bp

    def assignMemberSections(self, mid:int, sid0:int, sid1:int):
        self._section_assignments[mid] = [sid0, sid1]

    def getSectionIdByName(self, name:str):
        for sid, sname in self._section_id_to_name.items():
            if sname == name:
                return sid
        return None

    def addSectionIdToName(self, sid:int, name:str):
        self._section_id_to_name[sid] = name

    def assignMemberLocalFrame(self, mid:int, fid:int):
        self._local_frame_assignments[mid] = fid

    def assignMemberInitCurvature(self, mid:int, cid:int):
        self._init_curv_assignments[mid] = cid

    def setMemberDivisions(self, mid:int, nd:int):
        self._member_divisions[mid] = nd

    def getMemberSize(self, mid:int, scale=1):
        p1, p2 = self._members[mid]
        len2 = mutils.ss(self._points[p1], self._points[p2])
        length = math.sqrt(len2) * scale

        return length

    def getEntitySetByName(self, ent_type:str, name:str):
        """
        Get the set of entities by its type and name.

        Parameters
        ----------
        ent_type : str
            Type of the entity ('point' or 'member')
        name : str
            Name of the entity

        Returns
        -------
        list of int
            IDs of the entities
        """

        if ent_type.startswith('p'):
            return self.point_sets.get(name, [])
        elif ent_type.startswith('m'):
            return self.member_sets.get(name, [])

        return []

    def summary(self):
        """Print a summary of the beam model
        """
        pp = pprint.PrettyPrinter(indent=4, compact=False)

        print()
        print('Analysis')
        print('--------')
        print('analysis type:', self.analysis)
        print('max iteration:', self.max_iteration)
        print('number of steps:', self.num_steps)
        if self.analysis != 0:
            print('angular velocity:', self.angular_velocity)
            print('time function:', self.av_tf)
            print('linear velocity:', self.linear_velocity)
            print('time function:', self.lv_tf)
        if self.analysis == 3:
            print('number of eigen results:', self.num_eigens)

        print()
        print('Points')
        print('------')
        # pp.pprint(self.points)
        pp.pprint(self.bp_name_id)
        for pid, coords in self.points.items():
            # print(f'{pid}: ( {coords[0]} , {coords[1]} , {coords[2]} )')
            print('{}: ( {} , {} , {} )'.format(pid, coords[0], coords[1], coords[2]))

        # print()
        # print('Members')
        # print('-------')
        # for k, v in self.members.items():
        #     v.summary()

        print()
        print('Point sets')
        print('----------')
        for _name, _ids in self.point_sets.items():
            print(_name, _ids)

        print()
        print('Member sets')
        print('-----------')
        for _name, _ids in self.member_sets.items():
            print(_name, _ids)

        print()
        print('Point conditions')
        print('----------------')
        for pc in self.point_conditions:
            pc.summary()

        # print('\nbp_name_id')
        # print(self.bp_name_id)
        print()
        print('Sectional properties')
        print('--------------------')
        for sid, bp in self.sections.items():
            print('Section', sid)
            print('Compliance')
            pp.pprint(bp.cmpl)
            if self.analysis != 0:
                print('Mass')
                pp.pprint(bp.mass)
            print('')

        print()
        print('Member conditions')
        print('-----------------')
        for mc in self.member_conditions:
            mc.summary()

        print()
        print('Distributed functions')
        print('---------------------')
        pp.pprint(self._distr_loads)

        print()
        print('Local frames')
        print('------------')
        for id, c in self.local_frames.items():
            print('\nframe', id)
            print(c)

        print()
        print('Time functions')
        print('--------------')
        print('Simulation range:', self._sim_range)
        for tf in self.time_functions:
            print('\nfunction:', tf.id)
            print('type:', tf.type)
            print('entries:')
            print(tf.entries)
            print('\n')


    def printResults(self):
        """Print GEBT analysis results
        """
        for i, step in enumerate(self.results_steps):
            print('step', i+1)
            step.printAll(self.analysis)

        for i, eigen in enumerate(self.results_eigen):
            _eva, _eve = eigen
            print(f'eigenvalue {i+1}: {_eva}')
            _eve.printAll(self.analysis)


    def getResultStep(self, step=-1) -> GEBTBeamResult:
        return self.results_steps[step]


    def getResult(self, name:str):
        if name.startswith('eig'):
            evas = []
            for eigout in self.results_eigen:
                _eva_im, _eva_re = eigout[0]
                if _eva_re > 0:
                    evas.append(_eva_re)
            # print(evas)
            evas.sort()
            _i = int(name[3:]) - 1
            value = evas[_i]

        return value


    def getResultEigenVector(self, eig:int):
        """Get the eigenvector of the specified eigenvalue
        """
        eigvec = []

        _i = 0
        _result = None
        for _eigval, _eigvec in self.results_eigen:
            _eva_im, _eva_re = _eigval
            if _eva_re > 0:
                _i += 1
                if _i == eig:
                    _result = _eigvec
                    break

        for _mi in range(len(self.members)):
            _mid = _mi + 1
            # _member = self.members[_mid]
            # _pid1, _pid2 = _member.points
            _pid1, _pid2 = self.members[_mid]

            # Add the first point if the member is the first one
            if len(eigvec) == 0:
                _node_result = _result.getResultById('point', _pid1)
                eigvec.append(_node_result.getResultFlattened())
                # eigvec.append(_node_result)

            # Add all node results of member elements
            _memb_result = _result.getResultById('memebr', _mid)
            for _node_result in _memb_result:
                eigvec.append(_node_result.getResultFlattened())
                # eigvec.append(_node_result)

            # Add the last point of the member
            _node_result = _result.getResultById('point', _pid2)
            eigvec.append(_node_result.getResultFlattened())
            # eigvec.append(_node_result)

        return eigvec


    def addSectionalProperty(self, bp):
        """Add sectional properties to the model

        Parameters
        ----------
        bp : :obj:`BeamProperty`
            A BeamProperty object that will be added
        """
        bpid = self.bp_name_id[bp.name]
        self.sections[bpid] = bp


    def addSectionalProperties(self):
        """Add all sectional properties whose names stored in the beam model
        """
        for cs_name, cs_id in self.bp_name_id.items():
            bp = sgio.readVABSOut(cs_name+'.sg')
            bp.name = cs_name
            self.addSectionalProperty(bp)


    def addCondition(self, loc_type, loc_id, cond_input):
        """Add a codition

        Parameters
        ----------
        loc_type : {'point', 'memebr'}
            Type of the conditioned location
        loc_id : int
            ID of the conditioned location
        cond_input : dict
            Specifications of the condition

            - ``dofs``: A list of six integers indicating the degrees of freedom to be constrained. Requried.
            - ``values``: A list of six float numbers indicating the values of the corresponding constraint. Optional.
            - ``time_funcs``: A list of six integers indicating the IDs of the time functions used for the constraints. Optional.
            - ``followers``: A list of six integers (0 or 1) indicating if the corresponding load is a follower force/moment or not. Optional.
        """
        cond = GEBTCondition()
        cond.object_id = loc_id
        cond.type = loc_type

        if loc_type == 'point':
            cond.dofs = cond_input['dofs']
        if loc_type == 'member':
            cond.dfids = cond_input['dfids']

        cond.values = list(map(float, cond_input.get('values', [0, 0, 0, 0, 0, 0])))
        cond.time_funcs = cond_input.get('time_funcs', [0, 0, 0, 0, 0, 0])
        cond.followers = cond_input.get('followers', [0, 0, 0, 0, 0, 0])

        if loc_type == 'point':
            self.point_conditions.append(cond)
        if loc_type == 'member':
            self.member_conditions.append(cond)

        return


    def addPointConditions(self, conds):
        """Add multiple point conditions at one time

        Parameters
        ----------
        conds : dict
            Pairs of point name and condition specifications.
        """
        for pname, pcond in conds.items():
            obj_id = self.pt_name_id[pname]
            self.addPointCondition(obj_id, pcond)


    def mesh(self):
        """Generate the discretization of the beam
        """
        for i, mb in self.members.items():
            if self.global_mesh_size > 0:
                p1coords = self.points[mb.points[0]]
                p2coords = self.points[mb.points[1]]
                mb_len = mutils.ss(p1coords, p2coords)
                mb_len = math.sqrt(mb_len)
                mb.num_divisions = int(math.ceil(mb_len / self.global_mesh_size))


    def writeGmshMsh(self):
        """ Write the Gmsh mesh file for the visualization of the whole blade
        """
        # Write meshing data
        num_nodes_total = len(self.stations)
        num_elements_total = len(self.stations) - 1
        for st in self.stations:
            num_nodes_total += st['cross_section'].num_nodes
            num_elements_total += st['cross_section'].num_elements
        
        self.fn_gmsh_msh = self.name + '.msh'
        with open(self.fn_gmsh_msh, 'w') as fout:
            # Mesh format
            fout.write('$MeshFormat\n')
            fout.write('2.2  0  8\n')  # version-number  file-type  data-size
            fout.write('$EndMeshFormat\n')
            fout.write('\n')
            
            # Nodes
            fout.write('$Nodes\n')
            fout.write(str(num_nodes_total)+'\n')
            # Reference line
            for i, st in enumerate(self.stations):
                fout.write('{0:8d}'.format(i+1))
                mutils.writeFormatFloats(fout, list(st['coordinates']))
            # Cross sections
            nnodes = len(self.stations)
            for st in self.stations:
                loc = list(st['coordinates'])
                cs = st['cross_section']
                for nid, coords in cs.nodes.items():
                    fout.write('{0:8d}'.format(nid+nnodes))
                    mutils.writeFormatFloats(fout, list(coords)+loc)
                nnodes += cs.num_nodes
            fout.write('$EndNodes\n')
            fout.write('\n')
            
            # Elements
            fout.write('$Elements\n')
            fout.write(str(num_elements_total)+'\n')
            # Reference line
            for i in range(len(self.stations) - 1):
                line = [i+1, 1, 2, 1, 1, i+1, i+2]
                mutils.writeFormatIntegers(fout, line)
            # Cross sections
            nnodes = len(self.stations)
            nelems = len(self.stations) - 1
            for st in self.stations:
                cs = st['cross_section']
                for eid, nodes in cs.elements.items():
                    line = list([eid+nelems, 2, 2, 1, 1])
                    nid_tmp = [nid+nnodes for nid in nodes]
                    # line = np.concatenate([line, np.array(nodes)+nnodes])
                    line = line + nid_tmp
                    mutils.writeFormatIntegers(fout, line)
                nnodes += cs.num_nodes
                nelems += cs.num_elements
            fout.write('$EndElements\n')
            fout.write('\n')

        # Write options
        self.fn_gmsh_msh_opt = self.fn_gmsh_msh + '.opt'
        with open(self.fn_gmsh_msh_opt, 'w') as fout:
            fout.write('General.Axes = ' + str(3) + ';\n')
            fout.write('General.Orthographic = ' + str(0) + ';\n')
            fout.write('Mesh.Lines = ' + str(1) + ';\n')
            fout.write('Mesh.LineWidth = ' + str(1) + ';\n')




    def _writeGEBTHeader(self, file, sfi='8d', sff='16.6e'):
        # file.write('{:4d}'.format(self.analysis))
        mutils.writeFormatIntegers(
            file,
            [self.analysis, self.max_iteration, self.num_steps],
            fmt=sfi, newline=False
        )
        file.write('  # Analysis type, max number of iteraions, number of steps\n')

        if self.analysis != 0:
            mutils.writeFormatFloats(file, self.angular_velocity, fmt=sff)
            mutils.writeFormatIntegers(file, self.av_tf, fmt=sfi)
            mutils.writeFormatFloats(file, self.linear_velocity, fmt=sff)
            mutils.writeFormatIntegers(file, self.lv_tf, fmt=sfi)

        if self.analysis == 3:
            # mutils.writeFormatIntegers(
            #     file, [self.num_eigens,], fmt=sfi, newline=False)
            file.write(f'{self.num_eigens:{sfi}}  # Number of eigenvalues\n')

        file.write('\n')

        line = [
            self.num_points, self.num_members, self.num_point_conditions,
            self.num_sections, self.num_local_frames, self.num_member_conditions,
            self.num_distr_loads, self.num_time_functions, self.num_init_curvatures
        ]
        mutils.writeFormatIntegers(file, line, fmt=sfi, newline=False)
        file.write('  # Number of (key points, memebrs, point conditions')
        file.write(', sectional properties, frames, memebr conditions')
        file.write(', distribmutilsn loads, time functions, initial curvatures)\n')

        file.write('\n')




    def _writeGEBTPoints(self, file, sfi='8d', sff='16.6e'):
        """
        """
        for _pid, _coords in self.points.items():
            file.write(f'{_pid:{sfi}}')
            mutils.writeFormatFloats(file, _coords, fmt=sff, newline=False)
            if _pid == 1:
                file.write('  # Coordinates of each key point')
            file.write('\n')
        file.write('\n')




    def _writeGEBTMembers(self, file, sfi='8d'):
        """
        """
        for _mid, _points in self.members.items():
            # print(f'_mid: {_mid}')
            _nums = [_mid,]
            _nums.extend(_points)
            # print(self._section_assignments)
            _nums.extend(self._section_assignments[_mid])
            _nums.append(self._local_frame_assignments.get(_mid, 0))
            _nums.append(self._member_divisions[_mid])
            _nums.append(self._init_curv_assignments.get(_mid, 0))
            # print(f'_nums: {_nums}')
            mutils.writeFormatIntegers(file, _nums, fmt=sfi, newline=False)
            if _mid == 1:
                _comment = [
                    '  # Member definition: id',
                    'beginning key point id',
                    'ending key point id',
                    'beginning section id',
                    'ending section id',
                    'local frame id',
                    'number of divisions',
                    'initial curvature id'
                ]
                file.write(', '.join(_comment))
            file.write('\n')
        file.write('\n')




    def _writeGEBTSections(self, file, sfi='8d', sff='16.6e'):
        """
        """
        for sid in range(1, len(self.sections)+1):
            bp = self.sections[sid]
            # bp = ms.constitutive
            file.write(f'{sid:{sfi}}')
            # file.write('{0:4d}'.format(sid))
            if sid == 1:
                file.write('  # Sectional properties')
            file.write('\n')
            if len(bp.cmpl) > 0:
                for row in bp.cmpl:
                    mutils.writeFormatFloats(file, row, fmt=sff)
            else:
                for i, row in enumerate(bp.cmpl_c):
                    mutils.writeFormatFloats(
                        file, (row[0], 0, 0, row[1], row[2], row[3]), fmt=sff)
                    if i == 0:
                        mutils.writeFormatFloats(file, [0,]*6, fmt=sff)
                        mutils.writeFormatFloats(file, [0,]*6, fmt=sff)
            file.write('\n')
            if self.analysis != 0:
                for row in bp.mass:
                    mutils.writeFormatFloats(file, row, fmt=sff)
                file.write('\n')
        file.write('\n')




    def writeGEBTIn(self, fn_gebt_in='', sfi='8d', sff='16.6e'):
        """Write data to the GEBT input.

        Parameters
        ----------
        fn_gebt_in : str
            File name of the GEBT input.
            If left blank, the file name will be the same as the beam name,
            i.e., `[beam.name].dat`.

        """

        if fn_gebt_in == '':
            fn_gebt_in = self.name + '.dat'

        self.input_file = fn_gebt_in

        with open(fn_gebt_in, 'w') as fout:
            self._writeGEBTHeader(fout, sfi=sfi)

            self._writeGEBTPoints(fout, sfi=sfi, sff=sff)

            self._writeGEBTMembers(fout, sfi=sfi)

            # Write the block of point conditions
            for i, pc in enumerate(self.point_conditions):
                if i == 0:
                    pc.writeGEBTInput(fout, write_comment=True)
                else:
                    pc.writeGEBTInput(fout)

            fout.write('\n')

            self._writeGEBTSections(fout, sfi=sfi, sff=sff)

            # Write the block of frames
            for i, cij in self.local_frames.items():
                fout.write('{0:4d}'.format(i))
                if i == 1:
                    fout.write('  # Local coordinate frames')
                fout.write('\n')
                for row in cij:
                    mutils.writeFormatFloats(fout, row, sff)
                fout.write('\n')

            # Write the block of member conditions
            for i, mc in enumerate(self.member_conditions):
                if i == 0:
                    mc.writeGEBTInput(fout, write_comment=True)
                else:
                    mc.writeGEBTInput(fout)

            # print(self.distr_loads)
            for i in range(self.num_distr_loads):
                fout.write('{:4d}'.format(i+1))
                if i == 0:
                    fout.write('  # Distributed load functions')
                fout.write('\n')
                mutils.writeFormatFloats(fout, self.distr_loads[i+1], sff)
                fout.write('\n')

            # Write the block of time functions
            if self.num_time_functions > 0 or self.analysis == 2:
                mutils.writeFormatFloats(fout, self.sim_range, sff, newline=False)
                fout.write('  # Simulation range\n\n')

                for tf in self.time_functions:
                    tf.writeGEBTInput(fout)

                fout.write('\n')

        return fn_gebt_in


    # def findPtCoordByName(self, name):
    #     """Find key point coordinates by point id.

    #     Parameters
    #     ----------
    #     name : int
    #         Point id.

    #     Returns
    #     -------
    #     list of floats
    #         Point coordinates.
    #     """
    #     for i, c in self.points.items():
    #         if i == name:
    #             return c
    #     return None




    # def findSectionByName(self, name):
    #     """Find sectional properties by section id.

    #     Parameters
    #     ----------
    #     name : int
    #         Section id.

    #     Returns
    #     -------
    #     msgpi.sg.MaterialSection
    #         Sectional properties.
    #     """
    #     for i, s in self.sections.items():
    #         if s.name == name:
    #             return i
    #     return 0
