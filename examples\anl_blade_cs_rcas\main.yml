# iVABS main input
version: "0.10"


# Design parameters/variables of the structure
# mainly the distribution of SG parameters w.r.t. structural coordinates
# ====================================================================
structure:
  name: "blade"

  parameter:
    radius: 8.178
    ang_front_r0: 0
    ang_front_r1: 90
    ang_back_r0: 0
    ang_back_r1: 90
    r_ply: 0.5
    ply_spar_l1_root: 8
    ply_spar_l1_r1: 6
    ply_spar_l1_tip: 2
    ply_front_root: 4
    ply_front_r1: 3
    ply_front_tip: 1
    ply_back_root: 4
    ply_back_r1: 3
    ply_back_tip: 1

  distribution:
    - name: "airfoil"
      function: "f_interp_prev"
      type: "str"
      data_form: "file"
      file_name: "rcas_input.dat"
      file_format: "rcas"
      data_request: "airfoilinterp"
      config:
        airfoil_file_ext: "dat"

    - name: "chord"
      function: "f_interp_prev"
      data_form: "file"
      file_name: "rcas_input.dat"
      file_format: "rcas"
      data_request: "chord_structure"

    - name: ["ply_spar_1", "ply_front", "ply_back"]
      function: "f_interp_linear"
      type: "int"
      xscale: radius
      data_form: 'compact'
      data: |
        0.1, ply_spar_l1_root: 8, ply_front_root, ply_back_root
        r_ply, ply_spar_l1_r1, ply_front_r1, ply_back_r1
        1.0, ply_spar_l1_tip, ply_front_tip, ply_back_tip

    - name: ["ang_front", "ang_back"]
      function: "f_interp_linear"
      type: "int"
      xscale: radius
      data_form: 'compact'
      data: |
        0, ang_front_r0, ang_back_r0
        1, ang_front_r1, ang_back_r1

  design:

  model:
    main_file: 'rcas_input.dat'
    tool: 'rcas'  # FENODE > Blade nodes
    config:
      node_id_start: 2

  cs_assignment:
    - region: 'all'
      location: 'node'
      cs: 'main_cs'
  cs:
    - name: 'main_cs'
      design: 'cs_airfoil'
      model:
        type: 'bm2'
        solver: 'vabs'


function:
  - name: 'f_interp_prev'
    type: 'interpolation'
    interp_kind: 'previous'
  - name: 'f_interp_linear'
    type: 'interpolation'
    interp_kind: 'linear'


# Settings for SG/CS analysis
# ====================================================================
cs:
  - name: "cs_airfoil"
    builder: "prevabs"
    parameter:
      cs_template: 'airfoil_gbox_uni.xml.tmp'
      mdb_name: "material_database_si"
      airfoil: 'sc1095.dat'
      airfoil_point_order: -1
      chord: 0.5273
      lam_spar_1: "T300 15k/976_0.0053"
      lam_cap: "Aluminum 8009_0.01"
      lam_front: "AS4 12k/E7K8" # takes the place of the spar (front to back)
      lam_back: "AS4 12k/E7K8" # takes the place of the spar (front to back)
      ply_spar_1: 0
      ang_front: 0
      ang_back: 0
      ply_front: 1
      ply_back: 1
      mat_nsm: "lead"
      rnsm: 0.001 # Radius of nonstructural mass
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "AS4 12k/E7K8"
      gms: 0.002  # Global Mesh Size
      fms: 0.04   # Filling Component Mesh Size
    design:
      base_file: cs_template


# Analysis process
# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: 'h'
      output:
        file_name: "prop_calc.dat"
        file_format: "rcas"
        value: [
          'mu', 'gyry', 'gyrz',
          'ea', "gj", "eiyy", "eizz",
          'mcy', 'mcz', 'tcy', 'tcz',
        ]
        # name: [
        #   'CMPL', 'CKMZZ', 'CKMYY',
        #   'CEA', 'CGJ', 'CEIYY', 'CEIZZ',
        #   'CCGOFF', 'CCGOFFZ', 'CTOFFY', 'CTOFFZ'
        # ]

    # - step: "rcas"

    - step: "cs recovery"
      type: "cs"
      analysis: 'fi'  # initial failure analysis
      section_response:
        data_form: "file"
        file_name: "global_force_moment.csv"
        file_format: "csv"
      output:
        file_name: "strength_ratio.yml"
        file_format: "yml"
        value: ['sr',]
