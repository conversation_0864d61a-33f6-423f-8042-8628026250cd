import pprint
import msgd.dakota.dkt as mdd


class MSGDStudy():
    """
    """
    def __init__(self, name=''):
        self.name = name
        # self.configs = {}

        self.data = {}
        self.eval_in = None
        self.eval_out = None
        self.eval_id = 0

        # self.mdao_tool = 'dakota'
        self.fn = ''
        self.dir_work = ''
        self.fn_base = ''

        self.environment = {}

        # Same key: value structures as the Dakota input file
        self.method = {
            'output': 'normal'
        }
        self.model = {}

        #: dict: Design variables
        #:
        #: ..  code-block:: python
        #:
        #:     variables = {
        #:         'id': '',
        #:         'active': '',
        #:         'design': {
        #:             'continuous': [
        #:                 {
        #:                     'descriptor': 'name',
        #:                     'upper_bound': 1,
        #:                     'lower_bound': -1,
        #:                     'initial': 0
        #:                 }
        #:             ],
        #:             'discrete': {
        #:                 'range': [
        #:                     {
        #:                         'descriptor': 'name',
        #:                         'upper_bound': 1,
        #:                         'lower_bound': -1,
        #:                         'initial': 0
        #:                     }
        #:                 ],
        #:                 'set': {
        #:                     'integer': [],
        #:                     'string': [],
        #:                     'real': [
        #:                         {
        #:                             'descriptor': 'name',
        #:                             'elements': [],
        #:                             'initial': 0
        #:                         }
        #:                     ]
        #:                 }
        #:             }
        #:         },
        #:         'state': {
        #:             'continuous': [
        #:                 {
        #:                     'descriptor': 'name',
        #:                     'upper_bound': 1,
        #:                     'lower_bound': -1,
        #:                     'initial': 0
        #:                 }
        #:             ],
        #:             'discrete': {
        #:                 'range': [],
        #:                 'set': {
        #:                     'integer': [],
        #:                     'string': [],
        #:                     'real': []
        #:                 }
        #:             }
        #:         }
        #:     }
        self.variables = {
            'active': 'design',
            'design': {
                'continuous': [],
                'discrete': {
                    'range': [],
                    'set': {
                        'integer': [],
                        'string': [],
                        'real': []
                    }
                }
            },
            'state': {
                'continuous': [],
                'discrete': {
                    'range': [],
                    'set': {
                        'integer': [],
                        'string': [],
                        'real': []
                    }
                }
            },
        }

        #: dict: Responses
        #:
        #: ..  code-block:: python
        #:
        #:     interface = {
        #:         'analysis_driver': 'python',
        #:         'design_inputs': '',
        #:         'required_files': [],
        #:         'concurrency': 1
        #:     }
        self.interface = {
            'analysis_driver': 'python',
            # 'interface_script': 'interface.py',
            'design_inputs': '',
            'required_files': [],
            'concurrency': 1
        }

        self.required_files = []

        #: dict: Responses
        #:
        #: ..  code-block:: python
        #:
        #:     responses = {
        #:         'id': '',
        #:         'descriptors': [],
        #:         'objective_functions': [
        #:             {
        #:                 'descriptor': 'name1',
        #:                 'sense': 'min'
        #:             },
        #:         ],
        #:         'response_functions': [],
        #:         'calibration_functions': [],
        #:         'inequality_constraints': [
        #:             {
        #:                 'descriptor': 'name2',
        #:                 'upper_bound': 1e12,
        #:                 'lower_bound': 1
        #:             }
        #:         ],
        #:         'gradients': [],
        #:         'hessians': []
        #:     }
        self.responses = {
            'descriptors': [],
            'objective_functions': [],
            'response_functions': [],
            'calibration_functions': [],
            'inequality_constraints': [],
            'equality_constraints': [],
            'gradients': [],
            'hessians': [],
        }

        return


    def summary(self):
        ppt = pprint.PrettyPrinter()
        # print('\nkeywords =')
        # print(self.keywords)
        print('environment')
        ppt.pprint(self.environment)
        print('method')
        ppt.pprint(self.method)
        print('model')
        ppt.pprint(self.model)
        print('variables')
        ppt.pprint(self.variables)
        print('interface')
        ppt.pprint(self.interface)
        print('responses')
        ppt.pprint(self.responses)
        print()


    def update(self, input):
        self.method.update(input['method'])

        for k, v in input['variables'].items():
            if v['type'] == 'continuous':
                cdv = {
                    'descriptor': k,
                    'lower_bound': v['bounds'][0],
                    'upper_bound': v['bounds'][1]
                }
                self.variables['design']['continuous'].append(cdv)
                # self.variables['continuous_design']['descriptors'].append(k)
                # self.variables['continuous_design']['lower_bounds'].append(v['bounds'][0])
                # self.variables['continuous_design']['upper_bounds'].append(v['bounds'][1])

        self.interface.update(input['interface'])
        # self.interface['design_inputs'] = fn_design_base
        # dakota.interface['required_files'].append(fn_design)

        self.responses.update(input['responses'])

        return


    def updateResponsesDescriptors(self):
        descriptors = []
        try:
            for f in self.responses['response_functions']:
                descriptors.append(f['descriptor'])
        except KeyError:
            pass
        try:
            for f in self.responses['objective_functions']:
                descriptors.append(f['descriptor'])
        except KeyError:
            pass
        try:
            for f in self.responses['calibration_functions']:
                descriptors.append(f['descriptor'])
        except KeyError:
            pass
        try:
            for f in self.responses['inequality_constraints']:
                descriptors.append(f['descriptor'])
        except KeyError:
            pass
        try:
            for f in self.responses['equality_constraints']:
                descriptors.append(f['descriptor'])
        except KeyError:
            pass

        self.responses['descriptors'] = descriptors

        return


    def writeMDAOInput(self, writer='dakota'):

        if writer == 'dakota':
            mdd.writeInput(self)
        else:
            writer(self)

        return


    # def readMDAOEvalIn(self, fn_eval_params, fn_eval_results):
    #     self.eval_in, self.eval_out = di.read_parameters_file(
    #         fn_eval_params, fn_eval_results
    #     )
    #     self.eval_id = int(self.eval_in.eval_id)
    #     for param_name in self.eval_in.descriptors:
    #         self.data[param_name] = self.eval_in[param_name]

    #     return


    # def writeMDAOEvalOut(self, status, fn_eval_results=''):
    #     r"""
    #     """
    #     self.logger.info('writing output files...')
    #     if status == 0:
    #         # Success
    #         for desc in self.eval_out.descriptors:
    #             val = self.data[desc]
    #             self.eval_out[desc].function = val
    #         self.eval_out.write()

    #         with open('interim.out', 'w') as fo:
    #             json.dump(self.data, fo, indent=4)

    #         self.logger.critical('{} finish'.format(self.log_prompt))
    #     elif status == 1:
    #         # Fail
    #         self.logger.info('{} failed'.format(self.log_prompt))
    #         with open(fn_eval_results, 'w') as fo:
    #             fo.write('FAIL')

    #         e = tb.format_exc()
    #         print(e)
    #     return


