INFO     [2024-05-31 14:36:33] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-31 14:36:33] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-31 14:36:33] _msgd.updateData :: updating current design... 
INFO     [2024-05-31 14:36:33] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
CRITICAL [2024-05-31 14:36:33] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 456, in updateData
    self._global_structure.loadStructureMesh()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 992, in loadStructureMesh
    for _set_name, _items in self._model.point_sets.items():
                             ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'point_sets'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 456, in updateData
    self._global_structure.loadStructureMesh()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 992, in loadStructureMesh
    for _set_name, _items in self._model.point_sets.items():
                             ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'point_sets'
INFO     [2024-05-31 14:36:33] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-31 14:38:28] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-31 14:38:28] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-31 14:38:28] _msgd.updateData :: updating current design... 
INFO     [2024-05-31 14:38:28] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
CRITICAL [2024-05-31 14:38:28] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 456, in updateData
    self._global_structure.loadStructureMesh()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 992, in loadStructureMesh
    for _set_name, _items in self._model.point_sets.items():
                             ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'point_sets'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 456, in updateData
    self._global_structure.loadStructureMesh()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 992, in loadStructureMesh
    for _set_name, _items in self._model.point_sets.items():
                             ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'point_sets'
INFO     [2024-05-31 14:38:28] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-31 14:41:29] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-31 14:41:29] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-31 14:41:29] _msgd.updateData :: updating current design... 
INFO     [2024-05-31 14:41:29] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-05-31 14:41:29] io.readGEBTIn :: reading the GEBT beam file beam_design.yml... 
INFO     [2024-05-31 14:41:29] io.readGEBTInYml :: loading file beam_design.yml... 
INFO     [2024-05-31 14:41:29] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-05-31 14:41:29] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-05-31 14:41:29] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-05-31 14:41:29] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-31 14:41:29] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-31 14:41:29] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-05-31 14:41:29] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
INFO     [2024-05-31 14:41:29] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-05-31 14:41:29] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-31 14:41:29] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-05-31 14:41:29] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-31 14:41:30] main.run :: [step: beam analysis] running gebt analysis... 
INFO     [2024-05-31 14:41:30] io.readGEBTOut :: reading gebt output beam_design.dat.out... 
INFO     [2024-05-31 14:41:30] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-02 16:10:24] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 16:10:24] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 16:10:24] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 16:10:24] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-06-02 16:10:24] io.readGEBTIn :: reading the GEBT beam file beam_design.yml... 
INFO     [2024-06-02 16:10:24] io.readGEBTInYml :: loading file beam_design.yml... 
INFO     [2024-06-02 16:10:24] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-06-02 16:10:24] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-06-02 16:10:24] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-06-02 16:10:24] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-02 16:10:24] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 16:10:24] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-06-02 16:10:24] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
INFO     [2024-06-02 16:10:24] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-06-02 16:10:24] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 16:10:24] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 16:10:24] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 16:10:26] main.run :: [step: beam analysis] running gebt analysis... 
INFO     [2024-06-02 16:10:27] io.readGEBTOut :: reading gebt output beam_design.dat.out... 
INFO     [2024-06-02 16:10:27] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-02 16:11:37] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 16:11:37] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 16:11:37] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 16:11:37] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-06-02 16:11:37] io.readGEBTIn :: reading the GEBT beam file beam_design.yml... 
INFO     [2024-06-02 16:11:38] io.readGEBTInYml :: loading file beam_design.yml... 
INFO     [2024-06-02 16:11:38] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-06-02 16:11:38] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-06-02 16:11:38] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-06-02 16:11:38] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-02 16:11:38] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 16:11:38] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-06-02 16:11:38] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
INFO     [2024-06-02 16:11:38] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-06-02 16:11:38] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 16:11:38] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 16:11:38] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 16:11:39] main.run :: [step: beam analysis] running gebt analysis... 
INFO     [2024-06-02 16:11:40] io.readGEBTOut :: reading gebt output beam_design.dat.out... 
INFO     [2024-06-02 16:11:40] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-02 16:15:12] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 16:15:12] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 16:15:12] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 16:15:12] _structure.loadStructureMesh :: [blade1] loading structural mesh data... 
INFO     [2024-06-02 16:15:12] io.readGEBTIn :: reading the GEBT beam file beam_design.yml... 
INFO     [2024-06-02 16:15:12] io.readGEBTInYml :: loading file beam_design.yml... 
INFO     [2024-06-02 16:15:12] _structure.implementDomainTransformations :: [blade1] implementing domain transformations... 
INFO     [2024-06-02 16:15:12] _structure.implementDistributionFunctions :: [blade1] implementing distribution functions... 
INFO     [2024-06-02 16:15:12] distribution.implement :: [w] implementing parameter distribution... 
INFO     [2024-06-02 16:15:12] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-02 16:15:12] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 16:15:12] _structure.discretizeDesign :: [blade1] discretizing the design... 
INFO     [2024-06-02 16:15:12] _structure.calcParamsFromDistributions :: [blade1] calculating parameters from distributions... 
INFO     [2024-06-02 16:15:12] _structure.writeMeshData :: writing mesh data to file blade1_mesh.msh... 
INFO     [2024-06-02 16:15:12] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 16:15:12] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 16:15:12] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 16:15:13] main.run :: [step: beam analysis] running gebt analysis... 
INFO     [2024-06-02 16:15:13] io.readGEBTOut :: reading gebt output beam_design.dat.out... 
INFO     [2024-06-02 16:15:13] _msgd.writeAnalysisOut :: [main] writing output to file ... 
