Writing new restart file 'cs_design_rotate_opt_soga.rst'.

>>>>> Executing environment.

>>>>> Running soga iterator.

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                      1.0351878414e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 1:
Active set vector = { 1 1 1 }
                      1.5942876419e-03 diff_gj
                      3.7399959799e-04 diff_eiyy
                      6.3598684426e-04 diff_eizz



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                      1.0730307932e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 2:
Active set vector = { 1 1 1 }
                      1.5962123144e-03 diff_gj
                      3.7366567839e-04 diff_eiyy
                      6.3450872951e-04 diff_eizz



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      2.5598925748e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 3:
Active set vector = { 1 1 1 }
                      1.5912395633e-03 diff_gj
                      3.7284527638e-04 diff_eiyy
                      6.3461454918e-04 diff_eizz



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                      5.3901791437e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 4:
Active set vector = { 1 1 1 }
                      1.5839750655e-03 diff_gj
                      3.7691924623e-04 diff_eiyy
                      6.3780463115e-04 diff_eizz



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                      5.4503006073e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 5:
Active set vector = { 1 1 1 }
                      1.5795103493e-03 diff_gj
                      3.7318180905e-04 diff_eiyy
                      6.3423307377e-04 diff_eizz



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                      8.1423383282e-01 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 6:
Active set vector = { 1 1 1 }
                      1.5964516157e-03 diff_gj
                      3.7381613065e-04 diff_eiyy
                      6.3529922131e-04 diff_eizz



---------------------
Begin Evaluation    7
---------------------
Parameters for evaluation 7:
                      9.5446638386e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 7:
Active set vector = { 1 1 1 }
                      1.5455452402e-03 diff_gj
                      3.7679100503e-04 diff_eiyy
                      6.3860352459e-04 diff_eizz



---------------------
Begin Evaluation    8
---------------------
Parameters for evaluation 8:
                      2.1366008484e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 8:
Active set vector = { 1 1 1 }
                      1.5892451528e-03 diff_gj
                      3.7193384422e-04 diff_eiyy
                      6.3454229508e-04 diff_eizz



---------------------
Begin Evaluation    9
---------------------
Parameters for evaluation 9:
                      2.2113711966e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 9:
Active set vector = { 1 1 1 }
                      1.5916322707e-03 diff_gj
                      3.7420796482e-04 diff_eiyy
                      6.3522467213e-04 diff_eizz



---------------------
Begin Evaluation   10
---------------------
Parameters for evaluation 10:
                      2.7716910306e+00 r1

blocking fork: python run.py cs_design_rotate_opt_soga.yml 1 input.in output.out

Active response data for evaluation 10:
Active set vector = { 1 1 1 }
                      1.5914720087e-03 diff_gj
                      3.7245472362e-04 diff_eiyy
                      6.3569483607e-04 diff_eizz


<<<<< Function evaluation summary: 10 total (10 new, 0 duplicate)
<<<<< Best parameters          =
                      9.5446638386e+00 r1
<<<<< Best objective functions =
                      1.5455452402e-03
                      3.7679100503e-04
                      6.3860352459e-04
<<<<< Best data captured at function evaluation 7


<<<<< Iterator soga completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =     37.726 [parent =     37.726, child =          0]
  Total wall clock =     37.725
