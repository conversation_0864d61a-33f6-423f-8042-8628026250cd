version: "0.10"

# Design parameters/variables of the structure
# ====================================================================
structure:
  name: "beam"
  parameter:
    lyr_ang: 0
    lyr_ply: 6
  cs:
    - name: 'main_cs'
      design: "box"
      model:
        type: "bm2"
        solver: 'vabs'

# CS base design
# ====================================================================
cs:
  - name: "box"
    builder: "prevabs"
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 0
      lyr_ply: 6
    design:
      base_file: "box.xml.tmp"

# Analysis process
# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      # setting:
      #   timeout: 60
      output:
        value: [
          'mu',
          'ea', "gj", "eiyy", "eizz",
        ]

# Configurations of design study, e.g., parameter study, optimization, etc.
# Mainly for Dakota
# ====================================================================
study:
  method:
    format: "keyword"
    soga:  # Single objective genetic algorithm
      max_function_evaluations: 10
      population_size: 5
      seed: 1027
      print_each_pop: true
  variables:
    data_form: "explicit"
    list:
      - name: "lyr_ang"
        type: "continuous"
        bounds: [-90, 90]
      - name: "lyr_ply"
        type: "discrete"
        space_type: "range"
        bounds: [1, 8]
  responses:
    data_form: "explicit"
    objective_functions:
      - descriptor: 'ea'
        sense: 'maximize'
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        directory_tag: on
        directory_save: on
    required_files:
      - "files/*"
