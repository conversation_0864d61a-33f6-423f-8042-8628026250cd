import numpy as np
from meshio import Cell<PERSON>lock


def get_element_list(cells: list[CellBlock], cell_data_etags) -> list:
    """Create an array of element data from a list of cell blocks.

    Parameters
    ----------
    cells : list of CellBlock
        List of cell blocks.
        Structure:

        ..  code-block::

            [
                CellBlock('triangle', [[0, 1, 2], [1, 2, 3], ...]),
                CellBlock('quad', [[11, 12, 13, 14], [15, 16, 17, 18], ...]),
                ...
            ]

    cell_data_etags : list of ndarray
        List of element IDs for each cell block.
        Structure:

        ..  code-block::

            [
                [1, 2, 3, ...],
                [101, 102, 103, ...],
                ...
            ]

    Returns
    -------
    list of tuples (element type, list of node IDs)
        2D list of element data.
        Each row contains data of one element.
        Each row is a tuple of (element type, list of node IDs).

        ..  code-block::

            [
                ('triangle', [0, 1, 2]),
                ('triangle', [1, 2, 3]),
                ...,
                ('quad', [11, 12, 13, 14]),
                ('quad', [15, 16, 17, 18]),
                ...
            ]
    dict
        Map from element tag to index in the list.
        Structure:

        ..  code-block::

            {
                1: 0,
                2: 1,
                3: 2,
                ...,
                101: 3,
                102: 4,
                103: 5,
                ...
            }

    Examples
    --------

    >>> cells = [
    ...     CellBlock('triangle', [[0, 1, 2], [1, 2, 3]]),
    ...     CellBlock('quad', [[11, 12, 13, 14], [15, 16, 17, 18]])
    ... ]
    >>> cell_data_etags = [
    ...     [1, 2],
    ...     [101, 102]
    ... ]
    >>> get_element_list(cells, cell_data_etags)
    [
        ('triangle', [0, 1, 2]),
        ('triangle', [1, 2, 3]),
        ('quad', [11, 12, 13, 14]),
        ('quad', [15, 16, 17, 18]),
    ],
    {
        1: 0,
        2: 1,
        101: 2,
        102: 3
    }

    """
    element_list = []
    etag_to_index = {}

    # Iterate through each cell block
    for cell_block_idx, cell_block in enumerate(cells):
        element_type = cell_block.type
        element_data = cell_block.data

        # Check if we have corresponding element tags for this cell block
        if cell_block_idx >= len(cell_data_etags):
            # If no element tags provided for this block, skip it
            continue

        # Get element tags for this cell block
        etags = cell_data_etags[cell_block_idx]
        if len(etags) != len(element_data):
            raise ValueError(f"Mismatch between number of elements ({len(element_data)}) "
                           f"and element tags ({len(etags)}) in cell block {cell_block_idx}")

        # Process each element in the cell block
        for etag, node_ids in zip(etags, element_data):
            # Convert node IDs to a list of integers
            node_list = [int(node_id) for node_id in node_ids]

            # Create tuple of (element_type, node_list)
            element_tuple = (element_type, node_list)

            # Add to sequential list and record mapping
            current_index = len(element_list)
            element_list.append(element_tuple)
            etag_to_index[int(etag)] = current_index

    return element_list, etag_to_index




def get_selected_elements(element_list, etag_to_index, selected_etags):
    """Get a list of selected elements based on their element tags.

    Parameters
    ----------
    element_list : list of tuples
        List of all elements.
        Structure:

        ..  code-block::

            [
                ('triangle', [0, 1, 2]),
                ('triangle', [1, 2, 3]),
                ...,
                ('quad', [11, 12, 13, 14]),
                ('quad', [15, 16, 17, 18]),
                ...
            ]

    etag_to_index : dict
        Mapping from element tag to index in the element list.
        Structure:

        ..  code-block::

            {
                1: 0,
                2: 1,
                3: 2,
                ...,
                101: 3,
                102: 4,
                103: 5,
                ...
            }

    selected_etags : list of int
        List of selected element tags.
        Structure:

        ..  code-block::

            [1, 2, 101, 102, ...]

    Returns
    -------
    list
        List of selected elements grouped by types.

        ..  code-block::

            [
                [  # triangle
                    [0, 1, 2],
                    [1, 2, 3],
                ],
                [  # quad
                    [11, 12, 13, 14],
                    [15, 16, 17, 18],
                ],
                ...
            ]

    dict
        Mapping from element id to index in the selected elements list.
        Structure:

        ..  code-block::

            {
                1: (0, 0),
                2: (0, 1),
                101: (1, 0),
                102: (1, 1),
                ...
            }
    """





def mask_selected_elements(cell_data_etags, selected_etags):
    """Create a mask for selected elements.

    Parameters
    ----------
    cell_data_etags : list of ndarray
        List of element IDs for each cell block.
        Structure:

        ..  code-block::

            [
                [1, 2, 3, ...],
                [101, 102, 103, ...],
                ...
            ]

    selected_etags : list of int
        List of selected element tags.

    Returns
    -------
    list of ndarray
        List of masks for each cell block.
        Structure:

        ..  code-block::

            [
                [True, True, False, ...],
                [False, True, True, ...],
                ...
            ]

    Examples
    --------

    >>> cell_data_etags = [
    ...     [1, 2, 3],
    ...     [101, 102, 103]
    ... ]
    >>> selected_etags = [1, 102]
    >>> mask_selected_elements(cell_data_etags, selected_etags)
    [
        [True, False, False],
        [False, True, False]
    ]
    """
    # Convert selected_etags to a set for efficient lookup
    selected_set = set(selected_etags)

    # Create list to store masks for each cell block
    masks = []

    # Iterate through each cell block
    for cell_etags in cell_data_etags:
        # Convert cell_etags to numpy array if it's not already
        cell_etags_array = np.asarray(cell_etags)

        # Create boolean mask: True where element tag is in selected_etags
        mask = np.isin(cell_etags_array, list(selected_set))

        # Add mask to results
        masks.append(mask)

    return masks




def get_unique_ntags_of_selected_elements(elements:np.ndarray, mask:np.ndarray) -> np.ndarray:
    """Get unique node tags of selected elements.

    Parameters
    ----------
    elements : ndarray
        List of elements of one type.
        Structure:

        ..  code-block::

            [
                [0, 1, 2],
                [1, 2, 3],
                [2, 3, 4],
                ...
            ]

    mask : ndarray
        Mask for selected elements.
        Structure:

        ..  code-block::

            [False, True, True, ...]

    Returns
    -------
    ndarray
        Unique node tags of selected elements.
        Structure:

        ..  code-block::

            [1, 2, 3, 4, ...]

    """
    # Handle empty input cases
    if elements.size == 0 or mask.size == 0:
        return np.array([], dtype=int)

    # Ensure elements is a 2D array
    elements = np.asarray(elements)
    if elements.ndim == 1:
        elements = elements.reshape(1, -1)

    # Ensure mask is a 1D boolean array
    mask = np.asarray(mask, dtype=bool)

    # Check that mask length matches number of elements
    if len(mask) != len(elements):
        raise ValueError(f"Mask length ({len(mask)}) does not match number of elements ({len(elements)})")

    # If no elements are selected, return empty array
    if not np.any(mask):
        return np.array([], dtype=int)

    # Select elements based on mask
    selected_elements = elements[mask]

    # Flatten all node IDs from selected elements and get unique values
    all_node_ids = selected_elements.flatten()
    unique_node_ids = np.unique(all_node_ids)

    return unique_node_ids




def mask_selected_nodes(nodes:np.ndarray, selected_node_ids:np.ndarray) -> np.ndarray:
    """Create a mask for selected nodes.

    Parameters
    ----------
    nodes : ndarray
        List of all nodes.
        Structure:

        ..  code-block::

            [
                [0.0, 0.0, 0.0],
                [1.0, 0.0, 0.0],
                ...
            ]

    selected_node_ids : ndarray
        List of selected node IDs.
        Structure:

        ..  code-block::

            [1, 2, 3, ...]

    Returns
    -------
    ndarray
        Mask for selected nodes.
        Structure:

        ..  code-block::

            [False, True, True, ...]
    """
    # Convert inputs to numpy arrays
    nodes = np.asarray(nodes)
    selected_node_ids = np.asarray(selected_node_ids)

    # Handle empty inputs
    if nodes.size == 0:
        return np.array([], dtype=bool)

    # Ensure nodes is a 2D array
    if nodes.ndim == 1:
        nodes = nodes.reshape(1, -1)

    # Get the number of nodes
    num_nodes = len(nodes)

    # Initialize mask with all False
    mask = np.zeros(num_nodes, dtype=bool)

    # Handle empty selected_node_ids
    if selected_node_ids.size == 0:
        return mask

    # Filter out invalid indices (negative or out of range)
    valid_indices = selected_node_ids[
        (selected_node_ids >= 0) & (selected_node_ids < num_nodes)
    ]

    # Set mask to True for valid selected indices
    if len(valid_indices) > 0:
        mask[valid_indices] = True

    return mask




def get_masks_intersection(mask_1, mask_2):
    """Get the intersection of two masks.

    Parameters
    ----------
    mask_1 : ndarray
        First mask.
    mask_2 : ndarray
        Second mask.

    Returns
    -------
    ndarray
        Intersection of the two masks.
    """
    return np.logical_and(mask_1, mask_2)


def get_masks_union(mask_1, mask_2):
    """Get the union of two masks.

    Parameters
    ----------
    mask_1 : ndarray
        First mask.
    mask_2 : ndarray
        Second mask.

    Returns
    -------
    ndarray
        Union of the two masks.
    """
    return np.logical_or(mask_1, mask_2)
