INFO     [2024-05-22 17:03:13] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-22 17:03:13] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-22 17:03:13] _msgd.updateData :: updating current design... 
INFO     [2024-05-22 17:03:13] _structure.updateParameters :: [square_plate] updating parameters... 
INFO     [2024-05-22 17:03:13] _structure.substituteParameters :: [square_plate] substituting parameters... 
INFO     [2024-05-22 17:03:13] _structure.loadStructureMesh :: [square_plate] loading structural mesh data... 
INFO     [2024-05-22 17:03:13] abaqus.readAbaqusInput :: reading abaqus input file plate_sq2_ss_nfx_bck_s4r_40x40_si.inp... 
INFO     [2024-05-22 17:03:13] _structure.implementDomainTransformations :: [square_plate] implementing domain transformations... 
INFO     [2024-05-22 17:03:13] _structure.implementDistributionFunctions :: [square_plate] implementing distribution functions... 
INFO     [2024-05-22 17:03:13] distribution.implement :: [a1] implementing parameter distribution... 
INFO     [2024-05-22 17:03:13] execu.importFunction :: import users_function 
INFO     [2024-05-22 17:03:13] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-22 17:03:13] _structure.discretizeDesign :: [square_plate] discretizing the design... 
INFO     [2024-05-22 17:03:13] _structure.calcParamsFromDistributions :: [square_plate] calculating parameters from distributions... 
INFO     [2024-05-22 17:03:13] _structure.writeMeshData :: writing mesh data to file square_plate_mesh.msh... 
INFO     [2024-05-22 17:03:14] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-22 17:03:14] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-05-22 17:03:14] sg.runH :: [step: homogenization] running sg analysis (h)... 
INFO     [2024-05-22 17:03:14] _structure.updateParameters :: [mainsg_set1] updating parameters... 
INFO     [2024-05-22 17:03:14] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:14] sg.runH :: [sg: mainsg_set1] running sg analysis... 
INFO     [2024-05-22 17:03:14] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:14] main.buildSGModel :: building 1D SG (default): mainsg_set1... 
INFO     [2024-05-22 17:03:14] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:14] builder.buildSG1D :: building 1D SG: mainsg_set1... 
INFO     [2024-05-22 17:03:14] main.write :: writting sg data to sg\mainsg_set1.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:14] execu.run :: SwiftComp sg\mainsg_set1.sg 2D H 
INFO     [2024-05-22 17:03:14] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:14] _structure.updateParameters :: [mainsg_set2] updating parameters... 
INFO     [2024-05-22 17:03:14] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:14] sg.runH :: [sg: mainsg_set2] running sg analysis... 
INFO     [2024-05-22 17:03:14] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:14] main.buildSGModel :: building 1D SG (default): mainsg_set2... 
INFO     [2024-05-22 17:03:14] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:14] builder.buildSG1D :: building 1D SG: mainsg_set2... 
INFO     [2024-05-22 17:03:14] main.write :: writting sg data to sg\mainsg_set2.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:14] execu.run :: SwiftComp sg\mainsg_set2.sg 2D H 
INFO     [2024-05-22 17:03:14] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:14] _structure.updateParameters :: [mainsg_set3] updating parameters... 
INFO     [2024-05-22 17:03:14] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:14] sg.runH :: [sg: mainsg_set3] running sg analysis... 
INFO     [2024-05-22 17:03:14] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:14] main.buildSGModel :: building 1D SG (default): mainsg_set3... 
INFO     [2024-05-22 17:03:14] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:14] builder.buildSG1D :: building 1D SG: mainsg_set3... 
INFO     [2024-05-22 17:03:14] main.write :: writting sg data to sg\mainsg_set3.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:14] execu.run :: SwiftComp sg\mainsg_set3.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set4] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set4] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set4... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set4... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set4.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set4.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set5] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set5] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set5... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set5... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set5.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set5.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set6] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set6] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set6... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set6... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set6.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set6.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set7] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set7] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set7... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set7... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set7.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set7.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set8] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set8] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set8... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set8... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set8.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set8.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set9] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set9] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set9... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set9... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set9.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set9.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [mainsg_set10] updating parameters... 
INFO     [2024-05-22 17:03:15] _structure.updateParameters :: [lv1_layup] updating parameters... 
INFO     [2024-05-22 17:03:15] sg.runH :: [sg: mainsg_set10] running sg analysis... 
INFO     [2024-05-22 17:03:15] _structure.substituteParameters :: [lv1_layup] substituting parameters... 
INFO     [2024-05-22 17:03:15] main.buildSGModel :: building 1D SG (default): mainsg_set10... 
INFO     [2024-05-22 17:03:15] main.buildSGFromDefault :: building SG using default... 
INFO     [2024-05-22 17:03:15] builder.buildSG1D :: building 1D SG: mainsg_set10... 
INFO     [2024-05-22 17:03:15] main.write :: writting sg data to sg\mainsg_set10.sg (format: swiftcomp)... 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp sg\mainsg_set10.sg 2D H 
INFO     [2024-05-22 17:03:15] execu.run :: SwiftComp finished successfully! 
INFO     [2024-05-22 17:03:15] abaqus.run :: [step: buckling analysis] running abaqus analysis step... 
INFO     [2024-05-22 17:03:16] abaqus.writeAbaqusSectionsInp :: writing classical ABD matrix entries to shellsections.inp... 
CRITICAL [2024-05-22 17:03:16] abaqus.run :: [eval 1] abaqus job=plate_sq2_ss_nfx_bck_s4r_40x40_si.inp interactive ask_delete=OFF 
CRITICAL [2024-05-22 17:03:30] abaqus.postProcess :: [eval 1] abaqus python abq_get_result.py plate_sq2_ss_nfx_bck_s4r_40x40_si.odb abq_result.dat 
INFO     [2024-05-22 17:03:31] _msgd.writeAnalysisOut :: [main] writing output to file ... 
