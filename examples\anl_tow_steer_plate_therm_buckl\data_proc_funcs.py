import pprint

def trans(vin, coef, **kwargs):
    """Coordinate transformation

    vin : input Cartesian coordinates [X, Y, Z]
    vout : output transformed coordinates [x, y, z]
    """
    wx = coef['wx']
    wy = coef['wy']

    x = vin[0] / wx
    y = vin[1] / wy
    z = vin[2]

    vout = [x, y, z]

    return vout




def dakota_postpro(data, sname, logger, *args, **kwargs):
    # pprint.pprint(data)

    density = 0

    for sg in data['sgdb']['lv1_layup']:
        density += sg['properties']['md2']['mpa']

    density = density / len(data['sgdb']['lv1_layup'])

    data['main']['density'] = density

    return

