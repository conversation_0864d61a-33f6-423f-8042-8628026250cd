version: '0.10'
structure:
  name: beam
  parameter: {}
  model:
    type: ''
    tool: ''
    tool_version: ''
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: beam
    parameter:
      lyr_ang_sta1: 0
      lyr_ang_sta2: 90
      lyr_ply_sta1: 1
      lyr_ply_sta2: 9
    dim: 1
    builder: default
    design: null
    distribution:
    - name: lyr_ang
      type: float
      data_form: explicit
      data:
      - coordinate: 0
        value: 0
      - coordinate: 10
        value: 90
    - name: lyr_ply
      type: float
      data_form: explicit
      data:
      - coordinate: 0
        value: 1
      - coordinate: 10
        value: 9
  cs_assignment:
  - region: all
    location: node
    cs: main_cs
  physics: elastic
functions:
- name: f_interp
  type: float
  kind: linear
  fill_value: extrapolate
cs:
- name: box
  parameter:
    w: 0.953
    h: 0.53
    lyr_ang: 0
    lyr_ply: 6
  dim: 2
  builder: prevabs
  design:
    base_file: box.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    output:
      value:
      - ea
      - gayy
      - gazz
      - gj
      - eiyy
      - eizz
    type: sg
    analysis: h
    work_dir: cs
