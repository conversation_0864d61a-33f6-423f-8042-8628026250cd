# # import os
# # import json
# # from types import NoneType
# # import yaml
# # import pprint
# # import traceback as tb
# # from typing import KeysView

# from msgd.core import MSGD
# import msgd.utils as mutils

# import msgd.analysis._sg as mas
# import msgd.analysis.custom as mac

# import msgd.ext.abaqus as mea
# import msgd.ext.gebt as meb

# import msgd.core.io as mci


# import msgd._global as GLOBAL

# import logging
# logger = logging.getLogger(GLOBAL.LOGGER_NAME)



# def runStep(step_type:str, step_input:dict, msgd:MSGD, step_name=''):
#     """Run a single analysis step.

#     Parameters
#     ----------
#     step_type
#         Type of the analysis step.
#         Choose one from 'sg', 'cs', 'script', and other 3rd party tools
#     """

#     print()
#     logger.critical('='*20)
#     logger.critical(f'[eval {msgd.eval_id}] running {step_type} step: {step_name}')


#     msgd.global_structure.writeInterim()


#     if step_type == 'sg' or step_type == 'cs':

#         logger.debug(f'msgd.structure_data =\n{mutils.convertDict2Str(msgd.structure_data)}')

#         step_analysis = step_input['analysis']
#         if step_analysis[0] in ['d', 'l', 'r', 'f']:
#             # Read global loads
#             input_load = mutils.getValueByKey(step_input['input'], 'load_case', match='start')
#             struct_resp_cases_sets = mci.readStructuralGlobalResponses(msgd, input_load)

#             # Based on the 'location' provided in the load cases,
#             # find the corresponding 'set' names

#             # logger.debug(f'struct_resp_cases_sets:\n{mutils.convertToPrettyString(struct_resp_cases_sets)}')
#             mutils.dumpData(struct_resp_cases_sets, 'interim_structure_response_cases_set')

#             msgd.structure_response_cases_sets = struct_resp_cases_sets

#         mas.run(msgd, step_input)



#     elif step_type == 'script':

#         module_name = step_input['file']
#         func_name = step_input['function']

#         # cwd = os.getcwd()
#         # msgd.logger.info('cwd: {}'.format(cwd))

#         try:
#             import_str = 'import {} as user_mod'.format(module_name)
#             logger.info(import_str)
#             exec(import_str)
#             func_str = 'user_mod.{}'.format(func_name)
#             logger.info('evaluating user function: {}'.format(func_str))
#             func_obj = eval(func_str)
#         except ImportError:
#             try:
#                 import_str = 'from {} import {}'.format(module_name, func_name)
#                 logger.info(import_str)
#                 exec(import_str)
#                 logger.info('evaluating user function: {}'.format(func_name))
#                 func_obj = eval(func_str)
#             except ImportError:
#                 print('something wrong when importing module:', module_name)

#         design_analysis = mac.ScriptDesignAnalysis()

#         design_analysis.data = {
#             'main': msgd.data,
#             'structure': msgd.structure_data,
#             '{}db'.format(msgd.SG_KEY): msgd.sg_db,
#             'elemsets': msgd.elem_sets,
#             'paramsets': msgd.param_sets,
#             'propsets': msgd.prop_sets
#         }
#         logger.debug("design_analysis.data['main'] = {}".format(str(msgd.data)))
#         # msgd.logger.debug("design_analysis.data['structure'] = {}".format(str(msgd.structure_data)))
#         # msgd.logger.debug("design_analysis.data['sgdb'] = {}".format(str(msgd.sg_db)))

#         design_analysis.function = func_obj

#         design_analysis.args = step_input.get('args', [])
#         design_analysis.kwargs = step_input.get('kwargs', {})

#         # design_analysis.summary('summary before run')
#         design_analysis.run()
#         # design_analysis.summary('summary after run')

#         msgd.data.update(design_analysis.data['main'])


#     elif step_type == 'abaqus':

#         abq_job = mea.AbaqusJob()

#         structure = msgd.getStructure()

#         abq_job.mdb = structure
#         # abq_job.mdb = msgd.structure['model']

#         try:
#             abq_job.postpros = step_input['post_process']
#         except KeyError:
#             pass

#         try:
#             timeout = step_input['setting']['timeout']
#         except KeyError:
#             timeout = 600

#         if len(msgd.orient_sets) == 0:
#             msgd.orient_sets['default'] = list(structure.orient.keys())[0]
#             # msgd.orient_sets['default'] = list(msgd.structure['model'].orient.keys())[0]

#         # Write section properties to a .inp file
#         # print(msgd.elem_sets)
#         # print(msgd.prop_sets)
#         mea.writeAbaqusSectionsInp(
#             structure.fn_section, msgd.elem_sets, msgd.prop_sets,
#             # msgd.structure['model'].fn_section, msgd.elem_sets, msgd.prop_sets,
#             orient_sets=msgd.orient_sets, physics=msgd.physics,
#         )

#         # Run structural analysis
#         abq_job.run(timeout=timeout)
#         abq_job.postProcess(timeout=timeout)

#         try:
#             fn_result = step_input['step_result_file']
#             # print(fn_result)
#             with open(fn_result, 'r') as file:
#                 for line in file.readlines():
#                     line = line.strip()
#                     if line == '':
#                         continue
#                     line = line.split('=')
#                     msgd.data[line[0].strip()] = float(line[1].strip())
#         except KeyError:
#             pass


#     elif step_type == 'gebt':
#         meb.run(msgd, step_input)


#     return

