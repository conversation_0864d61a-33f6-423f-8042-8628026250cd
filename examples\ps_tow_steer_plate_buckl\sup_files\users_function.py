import math
# import yaml
from scipy.interpolate import interp1d



# --------------------------------------------------------------------

def svFiberAngle(v, coef, *args, **kwargs):
    v = trans(v, None)
    phi = svFiberAngleR(v, coef)
    return phi




def svFiberAngleR(v, coef, *args, **kwargs):
    """
    v : cylindrical coordinates [theta, r, z]
    theta : circumferential coordinate (radians)
    theta range : [0, pi]
    phi : fiber angle (degrees)
    maxk : maximum curvature (1/in)
    """

    try:
        tol = kwargs['tol']
    except KeyError:
        tol = 1e-6

    theta = v[0]
    # print('theta =', theta)

    intp_theta = coef['theta'] + [225, 270, 315, 360]
    intp_theta = [math.radians(theta) for theta in intp_theta]
    intp_phi = coef['phi'] + coef['phi'][::-1][1:]
    intp_cos_phi = [math.cos(math.radians(phi)) for phi in intp_phi]

    # Calculate fiber angle
    for i, th in enumerate(intp_theta):
        if (abs(theta - th) <= tol):
            return intp_phi[i]

    f_cos_phi = interp1d(intp_theta, intp_cos_phi)
    cos_phi = f_cos_phi(theta)
    try:
        m = coef['m']
    except KeyError:
        m = 1.0
    phi = m * math.degrees(math.acos(cos_phi))

    return phi




def svNumPly(v, coef, *args, **kwargs):
    v = trans(v, None)
    nply = svNumPlyR(v, coef)
    return nply


def svNumPlyR(v, coef, *args, **kwargs):
    """
    v : cylindrical coordinates [theta, r, z]
    theta : circumferential coordinate (radians)
    theta range : [0, pi]
    phi : fiber angle (degrees)
    maxk : maximum curvature (1/in)
    """

    try:
        tol = kwargs['tol']
    except KeyError:
        tol = 1e-6

    theta = v[0]
    # print('theta =', theta)

    intp_theta = coef['theta'] + [225, 270, 315, 360]
    intp_theta = [math.radians(theta) for theta in intp_theta]
    intp_nply = coef['nply'] + coef['nply'][::-1][1:]
    # intp_cos_nply = [math.cos(math.radians(phi)) for phi in intp_phi]

    f_nply = interp1d(intp_theta, intp_nply)
    nply = f_nply(theta).tolist()
    nply = int(round(nply))

    return nply









# --------------------------------------------------------------------

def postProcess(data, sname, *args, **kwargs):
    calcDensity(data)
    calcMaxCurvature(data, kwargs['theta'], kwargs['R'])
    return

def postProcess2(data, sname, *args, **kwargs):
    calcDensity(data)
    return









def calcDensity(data):
    name_sg = 'mainsg'

    density = 0
    nelem = 0

    for _setname, _eids in data['elemsets'].items():
        nelem_set = len(_eids)
        mpa_sg = data['structure']['sgs_data']['{}_{}'.format(name_sg, _setname)]['property']['md2']['mpa']
        density += (mpa_sg * nelem_set)
        nelem += nelem_set

    density = density / nelem

    data['main']['density'] = density
    return









def calcMaxCurvature(data, thetas, R):
    """
    theta : circumferential coordinate (radians)
    theta range : [0, pi]
    phi : fiber angle (degrees)
    maxk : maximum curvature (1/in)
    """
    dd = data['main']

    theta = [math.radians(t) for t in thetas]
    phi = [
        [dd['_phi10'], dd['_phi11'], dd['_phi12'], dd['_phi13'], dd['_phi14']],
        [dd['_phi20'], dd['_phi21'], dd['_phi22'], dd['_phi23'], dd['_phi24']],
        # [dd['_phi50'], dd['_phi51'], dd['_phi52'], dd['_phi53'], dd['_phi54']]
    ]
    for i in range(len(phi)):
        for j in range(len(phi[i])):
            phi[i][j] = math.radians(phi[i][j])
    # intp_cos_phi = [math.cos(math.radians(phi)) for phi in intp_phi]

    # Calculate curvature
    # R = data['structure']['parameter']['R']
    ks = []
    for i in range(len(phi)):
        for j in range(len(theta) - 1):
            ks.append(
                math.fabs((math.cos(phi[i][j])-math.cos(phi[i][j+1])) / (R*(theta[j+1]-theta[j])))
            )

    maxk = max(ks)

    data['main']['curv_max'] = maxk

    return {'curv_max': maxk}









def calcSafetyMargin(data, *args, **kwargs):
    srs = data['dakota']['srs']  # Strength ratios
    sr_min = min(srs)
    applied_load = kwargs['applied_load']
    mf = sr_min * applied_load

    # mf = data['dakota']['mf']
    mcr = data['dakota']['mcr']
    sfm = mf - mcr

    return {'safe_m': sfm}









def trans(vin, coef, *args, **kwargs):
    """Convert coordinates from Cartesion to cylindrical

    vin : input Cartesian coordinates [X, Y, Z]
    vout : output cylindrical coordinates [theta, r, z]
    theta : circumferential coordinates (radians)
    """
    theta = math.atan2(vin[1], vin[0])  # radians, (-pi, pi)
    theta = -theta + math.pi / 2
    if theta < 0:
        theta += 2 * math.pi
    r = 12.0
    z = vin[2]

    vout = [theta, r, z]

    return vout

