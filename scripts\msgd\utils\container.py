import copy
import logging
import math
import collections.abc

import numpy as np

import msgd._global as GLOBAL

logger = logging.getLogger(__name__)

def updateDictRecur(d, u, ignore=[]):
    for k, v in u.items():
        if k in ignore:
            continue
        else:
            if isinstance(v, collections.abc.Mapping):
                d[k] = updateDictRecur(d.get(k, {}), v, ignore=ignore)
            else:
                d[k] = v
    return d


# def updateListRecur(list1:list, list2:list):
#     """Update list 1 using list 2.
#     """
    


# def updateDict(source:dict, target:dict, ignore:list=[]):
def updateDict(source, target, ignore=[]):

    for k, v in source.items():
        if not k in ignore:
            target[k] = v

    return



def getValueByKey(dictionary:dict, key:str, default=None, match:str='start', recursive=False):
    r"""
    """
    value = default

    # if not isinstance(key, list):
    #     key = [key,]

    for _k, _v in dictionary.items():
        # for k in key:
        if match == 'start' and _k.startswith(key):
            value = _v
            break
        elif match == 'exact' and _k == key:
            value = _v
            break
        elif match == 'in' and _k in key:
            value = _v
            break


    return value



def compareValues(v1, v2, rel_tol:float=1e-9, abs_tol:float=1e-12):
    """
    """
    # print(type(v1), type(v2))

    if type(v1) != type(v2):
        return False

    if v1 == np.nan and v2 == np.nan:
        return True

    elif isinstance(v1, int) or isinstance(v1, str):
        if not (v1 == v2):
            return False

    elif isinstance(v1, float):
        # print(f'v1: {v1}, v2: {v2}')
        # print(math.isclose(v1, v2, rel_tol=rel_tol, abs_tol=abs_tol))
        if not math.isclose(v1, v2, rel_tol=rel_tol, abs_tol=abs_tol):
            return False

    return True



def compareDictionaries(
    d1:dict, d2:dict, rel_tol:float=1e-9, abs_tol:float=1e-12
    ):
    """
    """
    # print(f'd1: {d1}')
    # print(f'd2: {d2}')

    for k, v1 in d1.items():
        # print(f'k: {k}, v1: {v1}')

        try:
            v2 = d2[k]
            # print(f'v2: {v2}')
        except KeyError:
            return False

        if isinstance(v1, dict):
            if not compareDictionaries(v1, v2, rel_tol, abs_tol):
                return False
        elif isinstance(v1, list):
            if not compareLists(v1, v2, rel_tol, abs_tol):
                return False

        else:
            is_same = compareValues(v1, v2, rel_tol, abs_tol)
            if not is_same:
                return False
            else:
                continue

        # elif isinstance(v1, int) or isinstance(v1, str):
        #     if not (v1 == v2):
        #         return False
        # else:
        #     if not math.isclose(v1, v2, rel_tol=rel_tol, abs_tol=abs_tol):
        #         return False

    return True


def compareLists(l1:list, l2:list, rel_tol:float=1e-9, abs_tol:float=1e-12):
    for v1, v2 in zip(l1, l2):
        if isinstance(v1, dict):
            if not compareDictionaries(v1, v2, rel_tol, abs_tol):
                return False
        elif isinstance(v1, list):
            if not compareLists(v1, v2, rel_tol, abs_tol):
                return False

        else:
            return compareValues(v1, v2, rel_tol, abs_tol)

        # elif isinstance(v1, int) or isinstance(v1, str):
        #     if not (v1 == v2):
        #         return False
        # else:
        #     if not math.isclose(v1, v2, rel_tol=rel_tol, abs_tol=abs_tol):
        #         return False

    return True




def substituteParams(inputs, params):
    """
    """
    logger.debug('subsituting parameters...')
    logger.debug('before substitution')
    logger.debug(f'inputs: {inputs}')
    logger.debug(f'params: {params}')

    substituteParamsRecur(inputs, params)

    logger.debug('after substitution')
    logger.debug(f'inputs: {inputs}')



def substituteParamsRecur(inputs, params):
    """Substitute quantities in inputs using values given in params.

    inputs can have a nested structure of {} and []
    - Go through every key:value pairs
    - If value is a string, then check if key exists
      in params as well. If yes, then do the substitution
    """
    if isinstance(inputs, dict):
        for k, v in inputs.items():
            if isinstance(v, dict) or isinstance(v, list):
                substituteParamsRecur(v, params)
            elif isinstance(v, str):
                inputs[k] = substituteParamString(v, params)
                # try:
                #     inputs[k] = params[v]
                # except KeyError:
                #     pass

    elif isinstance(inputs, list):
        for i, v in enumerate(inputs):
            if isinstance(v, dict) or isinstance(v, list):
                substituteParamsRecur(v, params)
            elif isinstance(v, str):
                inputs[i] = substituteParamString(v, params)
                # try:
                #     inputs[i] = params[v]
                # except KeyError:
                #     pass

    return


def substituteParamString(string, params):
    """
    """

    value = copy.deepcopy(string)

    # Direct substitution
    if string in params.keys():
        value = params[string]
        logger.debug(f'substituting {string} = {value}')

    # Evaluate string expressions
    else:
        _str = string.split(':')
        if len(_str) > 1 and _str[0] == 'f':
            _params = dict((k, getattr(math, k)) for k in GLOBAL.SAFE_MATH_FUNCS)
            _params.update(params)
            value = eval(_str[1], _params)
            logger.debug(f'substituting {string} = {value}')

    return value
