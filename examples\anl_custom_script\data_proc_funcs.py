import math

def materialId2Name(data, **kwargs):

    # print(data)

    out = {}

    mdb = {
        1: 'AS4 12k/E7K8_0.0054',
        2: 'S2/SP381_0.0092',
        3: 'T650-35 12k/976_0.0052',
        4: 'T700 24K/E765_0.0056',
    }
    out['lam_spar'] = mdb[data['ilam_spar']]
    # data[sname]['lam_front'] = mdb[data[sname]['lamid_front']]
    # data[sname]['lam_back'] = mdb[data[sname]['lamid_back']]

    # data[sname]['mat_fill_te'] = data[sname]['lam_back'].split('_')[0]

    return out


def postProcess(data, **kwargs):

    # print(data)

    out = {}

    _target = kwargs['target']

    _diff = 0
    for _name, _value_tar in _target.items():
        _value_cal = data[_name]
        _diff += (_value_cal - _value_tar) ** 2

    out['diff'] = math.sqrt(_diff) / len(_target)

    return out
