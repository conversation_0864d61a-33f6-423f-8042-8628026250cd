# Main input file

version: '0.10'


# Design parameters/variables of the structure
# ====================================================================
structure:
  name: 'blade1'

  parameter:
    lyr_ang_sta1: 0
    lyr_ang_sta2: 90
    lyr_ply: 6

  distribution:
    - name: 'lyr_ang'
      function: 'f_interp'
      # domain: ''
      data:
        - coordinate: 0
          value: lyr_ang_sta1
        - coordinate: 10
          value: lyr_ang_sta2

  # parametric_domain:
  #   - name: 'pd1'

  model:
    main_file: 'beam_design.yml'
    tool: 'gebt'

  cs_assignment:
    - region: 'segment1'
      cs: 'main_cs'
      location: 'element'
  cs:
    - name: 'main_cs'
      design: "box"
      model:
        type: "bm2"
        solver: 'vabs'
        tool_version: '4.0'


function:
  - name: 'f_interp'
    type: 'interpolation'
    kind: 'linear'


# CS base design
# ====================================================================
cs:
  - name: "box"
    builder: "prevabs"
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 0
      lyr_ply: 6
    #   lma_thk: 0.005
    #   gms: 0.01
    #   elem_type: 'linear'
    design:
      base_file: "box.xml.tmp"



# Analysis process
# ====================================================================
analysis:
  steps:
    - step: 'cs analysis'
      type: 'cs'
      analysis: 'h'

    - step: 'beam analysis'
      type: 'gebt'
      analysis: 1
      output:
        - value: "all"
          to: 'file'
          file_name: 'global_force_moment.csv'
        - value: ['u1', 'u2', 'u3']
          name: ['u1_tip', 'u2_tip', 'u3_tip']
          to: 'main'
          entity: 'point'
          entity_name: 'tip'
        - value: 'mass'

    - step: "cs recovery"
      type: "cs"
      analysis: 'fi'  # initial failure analysis
      section_response:
        data_form: "file"
        file_name: "global_force_moment.csv"
        file_format: "csv"
      output:
        file_name: "strength_ratio.yml"
        file_format: "yml"
        value: ['sr_min',]
