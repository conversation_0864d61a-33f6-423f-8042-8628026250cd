Architecture
============

Overview
--------

The MSG-based multiscale structural design tool is built around several key components:

* Structure Gene (SG) design and analysis
* Global structural analysis
* Multi-level analysis capabilities
* Integration with various analysis tools

Class Structure
--------------

The system is organized around several key classes:

SGAssignment
~~~~~~~~~~~~
* Attributes:
    * structure region (set name)
    * sg model name
    * discrete map

SGData
~~~~~~
* A specific design for analysis
* Attributes:
    * id
    * sg model name
    * specific parameters
    * sg object from sgio

SGLoad
~~~~~~
* Global deformation and load for this sg

SGModel
~~~~~~~
* SG design + model configs
* Attributes:
    * name
    * sg design name
    * model configuration
    * meshing data

SGDesign
~~~~~~~~
* SG microstructural design
* Attributes:
    * name
    * base design
    * parameters (with default values)

Relations
---------

* One global structure can have N regions
* One SG model can belong to N SG assignments
* One SG assignment has only one SG model
* One SG design can be used in N SG models
* One SG design can generate N SG designs by substituting parameters

Workflow
--------

1. Define design inputs (from input file)
2. Read global structure model (if any)
3. Discretize (mesh) the global structure
4. Calculate parameters at nodes in assigned regions
5. Generate SG data
6. Run SG analysis (homogenization)
7. Finalize the global structure analysis input
8. Run global structural analysis

File Organization
----------------

The system uses a specific file organization for dehomogenization and failure analysis:

.. code-block:: text

   global_structure/
   sg_set1.sg
   sg_set1.sg.k
   sg_set2.sg
   sg_set2.sg.k
   sg_set3.sg
   sg_set3.sg.k
   local/
       global_structure/
           loc1/
               sg_set1/
                   case1/
                       sg_set1.sg.glb
                       sg_set1.sg.u
                       sg_set1.sg.s
                       sg_set1.sg.e
                       ...
                   case2/
                   ...
               loc1/
                   sg_set3/
                       case1/
                       ...
           loc2/
               sg_set2/
                   case1/
                   ...
                   case2/
                   ... 