<cross_section name="uh60a_section" format="1">
    <include>
        <material>{mdb_name}</material>
    </include>
    <analysis>
        <model>1</model>
    </analysis>
    <general>
        <scale>{chord=1}</scale>
        <mesh_size>{gms}</mesh_size>
        <element_type>{elm_type="linear"}</element_type>
        <track_interface>0</track_interface>
    </general>



    <baselines>

        <line name="ln_af" type="airfoil">
            <points data="file" format="{airfoil_format=1}" direction="{airfoil_point_order=-1}" header="{airfoil_file_head=0}">{airfoil}</points>
            <flip>{airfoil_flip=1}</flip>
            <reverse>{airfoil_point_reverse=0}</reverse>
        </line>

        <point name="p1" on="ln_af" by="x2" which="top">{a2p1=0.8}</point>
        <point name="p2" on="ln_af" by="x2" which="bottom">{a2p2=a2p1}</point>
        <point name="p3" on="ln_af" by="x2" which="top">{a2p3=0.6}</point>
        <point name="p4" on="ln_af" by="x2" which="bottom">{a2p4=a2p3}</point>

        <point name="pfle1">0.1 0</point>
        <point name="pfle2">0.9 0</point>

        <line name="line_top">
            <points>p1:p3</points>
        </line>
        <line name="line_bottom">
            <points>p4:p2</points>
        </line>
        <line name="line_le">
            <points>p2:p1</points>
        </line>
        <line name="line_te">
            <points>p3:p4</points>
        </line>
    </baselines>



    <layups>
        <layup name="lyp_le_cap">
            <layer lamina="{lam_cap}">{ang_cap=0}:{ply_cap=1}</layer>
        </layup>
        <layup name="lyp_skin">
            <layer lamina="{lam_skin}">{ang_skin=0}:{ply_skin=1}</layer>
        </layup>

    </layups>



    <component name="skin">
        <segment>
            <baseline>line_top</baseline>
            <layup>lyp_skin</layup>
        </segment>
        <segment>
            <baseline>line_bottom</baseline>
            <layup>lyp_skin</layup>
        </segment>
        <segment>
            <baseline>line_le</baseline>
            <layup>lyp_skin</layup>
        </segment>
        <segment>
            <baseline>line_te</baseline>
            <layup>lyp_skin</layup>
        </segment>
    </component>


    <component name="fill_front" type="fill" depend="skin">
        <location>pfle1</location>
        <material>{mat_fill_front}</material>
        <mesh_size at="pfle1,pfle2">{fms=10*gms}</mesh_size>
    </component>



</cross_section>
