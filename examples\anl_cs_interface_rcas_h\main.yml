# iVABS main input
version: "0.10"


# Design parameters/variables of the structure
# mainly the distribution of SG parameters w.r.t. structural coordinates
# ====================================================================
structure:
  name: "uh60_blade_1"

  parameter:
    radius: 26.8333
    ang_front_r0: 0
    ang_front_r1: 90
    ang_back_r0: 0
    ang_back_r1: 90
    r_ply: 0.5
    ply_spar_l1_root: 8
    ply_spar_l1_r1: 6
    ply_spar_l1_tip: 2
    ply_front_root: 4
    ply_front_r1: 3
    ply_front_tip: 1
    ply_back_root: 4
    ply_back_r1: 3
    ply_back_tip: 1

  distribution:
    - name: "airfoil"
      function: "f_interp_prev"
      type: "str"
      data_form: "file"
      file_name: "oml.dat"
      file_format: "rcas"
      data_request: "airfoilinterp"
      config:
        airfoil_file_ext: "dat"

    - name: "chord"
      function: "f_interp_prev"
      data_form: "file"
      file_name: "oml.dat"
      file_format: "rcas"
      data_request: "chord_structure"

    # - name: ["web_le","web_te","a2nsm"]
    #   function: "f_interp_linear"
    #   # kind: "linear"
    #   # xnames: "r/R"
    #   # ynames: ["web_le","web_te","a2nsm"] # ?????
    #   # web_le = Position of point 1 in QC coordniate frame
    #   # web_te = Position of point 1 in QC coordniate frame
    #   # a2p1 = Position of point 1 in Y dir (cross section coordinate frame)
    #   # a2p3 = Position of point 3 in Y
    #   # a2nsm = Position of nonstructural mass center in Y
    #   data_form: 'compact'
    #   data: |
    #     0.130, wl_a2_sta1: 0.12, web_te_in: -0.4,  pnsmc_a2_sta1: 0.95
    #     0.844, wl_a2_sta2: 0.12, web_te_in: -0.4,  pnsmc_a2_sta2: 0.95
    #     0.919, wl_a2_sta3: 0.12, wt_a2_sta3: -0.05, pnsmc_a2_sta3: 0.95
    #     1.000, wl_a2_sta4: 0.12, wt_a2_sta4: -0.05, pnsmc_a2_sta4: 0.95

    # - name: ["ply_spar_1", "ply_spar_2", "ply_spar_3", "ply_spar_4"]
    #   function: "f_interp_linear"
    #   # kind: "linear"
    #   # xnames: "r/R"
    #   # ynames: ["ply_spar_1", "ply_spar_2", "ply_spar_3", "ply_spar_4"]
    #   ytypes: "int"
    #   data_form: 'compact'
    #   data: |
    #     0.130,            np_spar_l1_rt: 4, np_spar_l2_rt: 4, np_spar_l3_rt: 4, np_spar_l4_rt: 4
    #     r1_np_spar:0.487, np_spar_l1_s1: 4, np_spar_l2_s1: 4, np_spar_l3_s1: 4, np_spar_l4_s1: 4
    #     1,                np_spar_l1_tp: 4, np_spar_l2_tp: 4, np_spar_l3_tp: 4, np_spar_l4_tp: 4
#        - name: "fiber angles (spar)"
#          function: "interpolation"
#          kind: "previous"
#          xnames: "r/R"
#          ynames: ["ang_spar_1", "ang_spar_2", "ang_spar_3", "ang_spar_4"]
#          ytypes: "int"
#          data: |
#            0.130, fo_spar_1_seg1: 0, fo_spar_2_seg1: 0, fo_spar_3_seg1: 0, fo_spar_4_seg1: 0
#            0.487, fo_spar_1_seg2: 0, fo_spar_2_seg2: 0, fo_spar_3_seg2: 0, fo_spar_4_seg2: 0
#            0.726, fo_spar_1_seg3: 0, fo_spar_2_seg3: 0, fo_spar_3_seg3: 0, fo_spar_4_seg3: 0
#            0.844, fo_spar_1_seg4: 0, fo_spar_2_seg4: 0, fo_spar_3_seg4: 0, fo_spar_4_seg4: 0
#            0.919, fo_spar_1_seg5: 0, fo_spar_2_seg5: 0, fo_spar_3_seg5: 0, fo_spar_4_seg5: 0
    - name: ["ply_spar_1", "ply_front", "ply_back"]
      function: "f_interp_linear"
      type: "int"
      xscale: radius
      data_form: 'compact'
      data: |
        0.1, ply_spar_l1_root, ply_front_root, ply_back_root
        r_ply, ply_spar_l1_r1, ply_front_r1, ply_back_r1
        1.0, ply_spar_l1_tip, ply_front_tip, ply_back_tip

    - name: ["ang_front", "ang_back"]
      function: "f_interp_linear"
      type: "int"
      xscale: radius
      data_form: 'compact'
      data: |
        0, ang_front_r0, ang_back_r0
        1, ang_front_r1, ang_back_r1
    # - name: "lamina"
    #   function: "interpolation"
    #   kind: "previous"
    #   xnames: "r/R"
    #   ynames: ["lamid_spar_1", "lamid_front", "lamid_back"]
    #   ytypes: ["int", "int", "int"]
    #   data: |
    #     0.0, mi_spar_1: 1, mi_le: 1, mi_te: 1

  design:

  model:
    main_file: 'oml.dat'
    tool: 'rcas'  # FENODE > Blade nodes
    config:
      node_id_start: 12

  cs_assignment:
    - region: 'all'
      location: 'node'
      cs: 'main_cs'
  cs:
    - name: 'main_cs'
      design: 'cs_airfoil'
      model:
        type: 'bm2'
        solver: 'vabs'


function:
  - name: 'f_interp_prev'
    type: 'interpolation'
    interp_kind: 'previous'
  - name: 'f_interp_linear'
    type: 'interpolation'
    interp_kind: 'linear'


# Settings for SG/CS analysis
# ====================================================================
cs:
  - name: "cs_airfoil"
    builder: "prevabs"
    parameter:
      cs_template: 'airfoil_gbox_uni.xml.tmp'
      mdb_name: "material_database_us_ft"
      airfoil: 'sc1095.dat'
      airfoil_point_order: -1
      # airfoil_point_order
      #
      #  1 = TE to upper surface to LE  to lower surface to TE (orignally .xy)
      # -1 = TE to lower surface to LE to upper surface to TE (.dat)
      # Airfoil names come from RCAS script file screen AIRFOILINTERP.
      # There needs to be a airfoil file for each airfoil in AIRFOILINTERP.
      # If .xy files are provided airfoil_file_ext needs to be xy, 
      # any header removed, and airfoil_point_order = 1.
      # The order needs to be consistent across all airfoil files used in the simulation.
      chord: 1.73
      lam_spar_1: "T300 15k/976_0.0053"
      lam_cap: "Aluminum 8009_0.01"
      lam_front: "AS4 12k/E7K8" # takes the place of the spar (front to back)
      lam_back: "AS4 12k/E7K8" # takes the place of the spar (front to back)
      ply_spar_1: 0
      ang_front: 0
      ang_back: 0
      ply_front: 1
      ply_back: 1
      mat_nsm: "lead"
      rnsm: 0.001 # Radius of nonstructural mass
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "AS4 12k/E7K8"
      gms: 0.002  # Global Mesh Size
      fms: 0.04   # Filling Component Mesh Size
      # interface_output: 1
      # interface_min_theta3_diff: 30
      # interface_min_theta1_diff: 30
    design:
      base_file: cs_template


# Analysis process
# ====================================================================
analysis:
  steps:
    # - step: 'preprocess'
    #   type: 'script'
    #   module: data_proc_funcs
    #   function: materialId2Name

    - step: "cs analysis"
      type: "cs"
      analysis: 'h'
      output:
        file_name: "prop_calc.dat"
        file_format: "rcas"
        value: [
          'mu', 'gyry', 'gyrz',
          'ea', "gj", "eiyy", "eizz",
          'mcy', 'mcz', 'tcy', 'tcz',
        ]

