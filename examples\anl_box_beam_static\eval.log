[14:29:29] INFO     reading main input main.yml... [io.readMSGDInput]
[14:29:29] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[14:29:29] INFO     updating current design... [_msgd.updateData]
[14:29:29] INFO     [blade1] loading structural mesh data... [_structure.loadStructureMesh]
[14:29:29] INFO     reading the GEBT beam file beam_design.yml... [io.readGEBTIn]
[14:29:29] INFO     loading file beam_design.yml... [io.readGEBTInYml]
[14:29:29] INFO     [blade1] implementing domain transformations... [_structure.implementDomainTransformations]
[14:29:29] INFO     [blade1] implementing distribution functions... [_structure.implementDistributionFunctions]
[14:29:29] INFO     [lyr_ang] implementing parameter distribution... [distribution.implement]
[14:29:29] INFO     loading data (explicit)... [distribution.loadData]
[14:29:29] INFO     [main] discretizing the structure... [_msgd.discretize]
[14:29:29] INFO     [blade1] discretizing the design... [_structure.discretizeDesign]
[14:29:29] INFO     [blade1] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[14:29:29] INFO     writing mesh data to file blade1_mesh.msh... [_structure.writeMeshData]
[14:29:29] INFO     [main] going through steps... [_msgd.analyze]
[14:29:29] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[14:29:29] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[14:29:30] INFO     [step: beam analysis] running gebt analysis... [main.run]
[14:29:30] INFO     reading gebt output beam_design.dat.out... [io.readGEBTOut]
[14:29:30] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[14:29:30] INFO     [step: cs recovery] running cs analysis (fi)... [sg.runDF]
[14:29:30] INFO     reading sectional responses... [sg.readSectionResponse]
[14:29:30] INFO     reading structural response file global_force_moment.csv... [sg.readMacroStateFileCsv]
[14:29:31] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[16:31:41] INFO     reading main input main.yml... [io.readMSGDInput]
[16:31:41] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[16:31:41] INFO     updating current design... [_msgd.updateData]
[16:31:41] INFO     [blade1] loading structural mesh data... [_structure.loadStructureMesh]
[16:31:41] INFO     reading the GEBT beam file beam_design.yml... [io.readGEBTIn]
[16:31:41] INFO     loading file beam_design.yml... [io.readGEBTInYml]
[16:31:41] INFO     [blade1] implementing domain transformations... [_structure.implementDomainTransformations]
[16:31:41] INFO     [blade1] implementing distribution functions... [_structure.implementDistributionFunctions]
[16:31:41] INFO     [lyr_ang] implementing parameter distribution... [distribution.implement]
[16:31:41] INFO     loading data (explicit)... [distribution.loadData]
[16:31:41] INFO     [main] discretizing the structure... [_msgd.discretize]
[16:31:41] INFO     [blade1] discretizing the design... [_structure.discretizeDesign]
[16:31:41] INFO     [blade1] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[16:31:41] INFO     writing mesh data to file blade1_mesh.msh... [_structure.writeMeshData]
[16:31:41] INFO     [main] going through steps... [_msgd.analyze]
[16:31:41] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[16:31:41] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[16:31:42] INFO     [step: beam analysis] running gebt analysis... [main.run]
[16:31:42] INFO     reading gebt output beam_design.dat.out... [io.readGEBTOut]
[16:31:42] CRITICAL Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\ext\gebt\main.py", line 176, in run
    _result_step_last = beam.getResultsStep(_step)
AttributeError: 'GEBTBeam' object has no attribute 'getResultsStep'
 [__main__.main]
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\ext\gebt\main.py", line 176, in run
    _result_step_last = beam.getResultsStep(_step)
AttributeError: 'GEBTBeam' object has no attribute 'getResultsStep'
[16:31:42] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[16:33:03] INFO     reading main input main.yml... [io.readMSGDInput]
[16:33:03] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[16:33:03] INFO     updating current design... [_msgd.updateData]
[16:33:03] INFO     [blade1] loading structural mesh data... [_structure.loadStructureMesh]
[16:33:03] INFO     reading the GEBT beam file beam_design.yml... [io.readGEBTIn]
[16:33:03] INFO     loading file beam_design.yml... [io.readGEBTInYml]
[16:33:03] INFO     [blade1] implementing domain transformations... [_structure.implementDomainTransformations]
[16:33:03] INFO     [blade1] implementing distribution functions... [_structure.implementDistributionFunctions]
[16:33:03] INFO     [lyr_ang] implementing parameter distribution... [distribution.implement]
[16:33:03] INFO     loading data (explicit)... [distribution.loadData]
[16:33:03] INFO     [main] discretizing the structure... [_msgd.discretize]
[16:33:03] INFO     [blade1] discretizing the design... [_structure.discretizeDesign]
[16:33:03] INFO     [blade1] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[16:33:03] INFO     writing mesh data to file blade1_mesh.msh... [_structure.writeMeshData]
[16:33:03] INFO     [main] going through steps... [_msgd.analyze]
[16:33:03] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[16:33:03] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[16:33:04] INFO     [step: beam analysis] running gebt analysis... [main.run]
[16:33:04] INFO     reading gebt output beam_design.dat.out... [io.readGEBTOut]
[16:33:04] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[16:33:04] INFO     [step: cs recovery] running cs analysis (fi)... [sg.runDF]
[16:33:04] INFO     reading sectional responses... [sg.readSectionResponse]
[16:33:04] INFO     reading structural response file global_force_moment.csv... [sg.readMacroStateFileCsv]
[16:33:05] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[17:46:03] INFO     reading main input main.yml... [io.readMSGDInput]
[17:46:03] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[17:46:03] INFO     updating current design... [_msgd.updateData]
[17:46:03] INFO     [blade1] loading structural mesh data... [_structure.loadStructureMesh]
[17:46:03] INFO     reading the GEBT beam file beam_design.yml... [io.readGEBTIn]
[17:46:03] INFO     loading file beam_design.yml... [io.readGEBTInYml]
[17:46:03] INFO     [blade1] implementing domain transformations... [_structure.implementDomainTransformations]
[17:46:03] INFO     [blade1] implementing distribution functions... [_structure.implementDistributionFunctions]
[17:46:03] INFO     [lyr_ang] implementing parameter distribution... [distribution.implement]
[17:46:03] INFO     loading data (explicit)... [distribution.loadData]
[17:46:03] INFO     [main] discretizing the structure... [_msgd.discretize]
[17:46:03] INFO     [blade1] discretizing the design... [_structure.discretizeDesign]
[17:46:03] INFO     [blade1] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[17:46:03] INFO     writing mesh data to file blade1_mesh.msh... [_structure.writeMeshData]
[17:46:03] INFO     [main] going through steps... [_msgd.analyze]
[17:46:03] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[17:46:03] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[17:46:04] INFO     [step: beam analysis] running gebt analysis... [main.run]
[17:46:04] INFO     reading gebt output beam_design.dat.out... [io.readGEBTOut]
[17:46:04] CRITICAL Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\ext\gebt\main.py", line 200, in run
    _node_id = beam.getEntitySetByName(_entity_request, _entity_name)[0]
IndexError: list index out of range
 [__main__.main]
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\ext\gebt\main.py", line 200, in run
    _node_id = beam.getEntitySetByName(_entity_request, _entity_name)[0]
IndexError: list index out of range
[17:46:04] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[17:51:25] INFO     reading main input main.yml... [io.readMSGDInput]
[17:51:25] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[17:51:25] INFO     updating current design... [_msgd.updateData]
[17:51:25] INFO     [blade1] loading structural mesh data... [_structure.loadStructureMesh]
[17:51:25] INFO     reading the GEBT beam file beam_design.yml... [io.readGEBTIn]
[17:51:25] INFO     loading file beam_design.yml... [io.readGEBTInYml]
[17:51:25] INFO     [blade1] implementing domain transformations... [_structure.implementDomainTransformations]
[17:51:25] INFO     [blade1] implementing distribution functions... [_structure.implementDistributionFunctions]
[17:51:25] INFO     [lyr_ang] implementing parameter distribution... [distribution.implement]
[17:51:25] INFO     loading data (explicit)... [distribution.loadData]
[17:51:25] INFO     [main] discretizing the structure... [_msgd.discretize]
[17:51:25] INFO     [blade1] discretizing the design... [_structure.discretizeDesign]
[17:51:25] INFO     [blade1] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[17:51:25] INFO     writing mesh data to file blade1_mesh.msh... [_structure.writeMeshData]
[17:51:25] INFO     [main] going through steps... [_msgd.analyze]
[17:51:25] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[17:51:25] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[17:51:26] INFO     [step: beam analysis] running gebt analysis... [main.run]
[17:51:26] INFO     reading gebt output beam_design.dat.out... [io.readGEBTOut]
[17:51:26] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[17:51:26] INFO     [step: cs recovery] running cs analysis (fi)... [sg.runDF]
[17:51:26] INFO     reading sectional responses... [sg.readSectionResponse]
[17:51:26] INFO     reading structural response file global_force_moment.csv... [sg.readMacroStateFileCsv]
[17:51:27] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
