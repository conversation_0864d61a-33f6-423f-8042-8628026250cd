# iVABS master input
name: "uh60_blade_design"

# Settings
# ====================================================================
setting:
  data_process_functions_file: "data_proc_funcs"


# Design parameters/variables of the structure
# mainly the distribution of SG parameters w.r.t. structural coordinates
# ====================================================================
design:
  - name: "uh60_blade_1"
    structure_class: "blade"
    cross_section_base_design: |
      0, airfoil_gbox_uni.xml.tmp
    parameters:
      list:
        rnsm: 0.001
        gms: 0.004
        airfoil_point_order: -1
        R: 26.8333
        lam_skin: "T300 15k/976_0.0053"
        lam_cap: "Aluminum 8009_0.01"
        mat_nsm: "lead"
        mat_fill_front: "Rohacell 70"
        mat_fill_back: "Plascore PN2-3/16OX3.0"
        mat_fill_te: "Plascore PN2-3/16OX3.0"
        mdb_name: "material_database_us_ft"
      distributions:
        - name: "oml"
          function: "interpolation"
          kind: "previous"
          xnames: "r/R"
          ynames: ["airfoil", "chord"]
          ytypes: ["str", "float"]
          xscales: "nondim"
          data_form: "file"
          file_name: "oml.dat"
          data_format: "rcas_oml"
          data_request: ["airfoil", "chord"]
          airfoil_file_ext: "dat"
        - name: "geo"
          function: "interpolation"
          kind: "linear"
          xnames: "r/R"
          ynames: ["a2p1", "a2p3", "a2nsm"]
          data: |
            0.130, wl_a2_sta1: 0.9, wt_a2_sta1: 0.7, pnsmc_a2_sta1: 0.95
            0.487, wl_a2_sta2: 0.9, wt_a2_sta2: 0.7, pnsmc_a2_sta2: 0.95
            0.726, wl_a2_sta3: 0.9, wt_a2_sta3: 0.7, pnsmc_a2_sta3: 0.95
            0.844, wl_a2_sta4: 0.9, wt_a2_sta4: 0.7, pnsmc_a2_sta4: 0.95
            0.919, wl_a2_sta5: 0.9, wt_a2_sta5: 0.7, pnsmc_a2_sta5: 0.95
            1.000, wl_a2_sta6: 0.9, wt_a2_sta6: 0.7, pnsmc_a2_sta6: 0.95
        - name: "layup (spar)"
          function: "interpolation"
          kind: "previous"
          xnames: "r/R"
          ynames: ["ply_spar_1", "ang_spar_1", "ang_spar_2", "ang_spar_3", "ang_spar_4"]
          ytypes: "int"
          data: |
            0.130, np_spar_seg1: 14, fo_spar_1_seg1: 0, fo_spar_2_seg1: 0, fo_spar_3_seg1: 0, fo_spar_4_seg1: 0
            0.487, np_spar_seg2: 17, fo_spar_1_seg2: 0, fo_spar_2_seg2: 0, fo_spar_3_seg2: 0, fo_spar_4_seg2: 0
            0.726, np_spar_seg3: 17, fo_spar_1_seg3: 0, fo_spar_2_seg3: 0, fo_spar_3_seg3: 0, fo_spar_4_seg3: 0
            0.844, np_spar_seg4: 17, fo_spar_1_seg4: 0, fo_spar_2_seg4: 0, fo_spar_3_seg4: 0, fo_spar_4_seg4: 0
            0.919, np_spar_seg5: 17, fo_spar_1_seg5: 0, fo_spar_2_seg5: 0, fo_spar_3_seg5: 0, fo_spar_4_seg5: 0
        - name: "layup (others)"
          function: "interpolation"
          kind: "previous"
          xnames: "r/R"
          ynames: ["ply_front", "ply_back", "ang_front", "ang_back"]
          ytpes: "int"
          data: |
            0, np_le: 17, np_te: 5, fo_le: 0, fo_te: 0
        - name: "lamina"
          function: "interpolation"
          kind: "previous"
          xnames: "r/R"
          ynames: ["lamid_spar_1", "lamid_front", "lamid_back"]
          ytypes: ["int", "int", "int"]
          data: |
            0.0, mi_spar_1: 1, mi_le: 1, mi_te: 1


# Analysis process
# ====================================================================
analysis:
  setting:
  steps:
    - step: "generate cs design"
      structure_class: "blade"
      uh60_blade_1:
        settings:
          analysis: 1
        inputs:
          stations: [0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.9371, 1.0]
    - step: "cs analysis"
      structure_class: "cs"
      all:
        settings:
          analysis: "h"
          timeout: 60
        preprocess:
          - function: "materialId2Name"
    - step: "calc diff"
      type: "script"
      function: "dakota_postpro"
      kwargs:
        weights: [0.5, 0.8, 0.8]
        beam_properties: ["gj", "ei22", "ei33"]
        ref_properties:
          function: "interpolation"
          kind: "linear"
          data_form: "file"
          file_name: "prop.dat"
          data_format: "rcas_prop"
          data_request: ["bgj", "beiyy", "beizz"]


# Configurations of design study, e.g., parameter study, optimization, etc.
# Mainly for Dakota
# ====================================================================
study:
  method:
    format: "keyword"
    moga:
      max_iterations: 100
      max_function_evaluations: 20000
      population_size: 200
      seed: 1027
      print_each_pop: true
  variables:
    data_form: "compact"
    data: |
      wl_a2_sta1,    design, continuous,      0.8:0.9
      wl_a2_sta2,    design, continuous,      0.8:0.9
      wl_a2_sta3,    design, continuous,      0.8:0.9
      wl_a2_sta4,    design, continuous,      0.8:0.9
      wl_a2_sta5,    design, continuous,      0.8:0.9
      wl_a2_sta6,    design, continuous,      0.8:0.9
      wt_a2_sta1,    design, continuous,      0.5:0.7
      wt_a2_sta2,    design, continuous,      0.5:0.7
      wt_a2_sta3,    design, continuous,      0.5:0.7
      wt_a2_sta4,    design, continuous,      0.5:0.7
      wt_a2_sta5,    design, continuous,      0.5:0.7
      wt_a2_sta6,    design, continuous,      0.5:0.7
      pnsmc_a2_sta1, design, continuous,      0.95:0.985
      pnsmc_a2_sta2, design, continuous,      0.95:0.985
      pnsmc_a2_sta3, design, continuous,      0.95:0.985
      pnsmc_a2_sta4, design, continuous,      0.95:0.985
      pnsmc_a2_sta5, design, continuous,      0.95:0.985
      pnsmc_a2_sta6, design, continuous,      0.95:0.985
      nsmr,          design, continuous,      0.001:0.01
      r_stam,        design, continuous,      0.3:0.8
      mi_spar_1,     design, discrete, range, 1:4
      mi_le,         design, discrete, range, 1:4
      mi_te,         design, discrete, range, 1:4
      np_spar_seg1,  design, discrete, range, 1:20
      np_spar_seg2,  design, discrete, range, 1:20
      np_spar_seg3,  design, discrete, range, 1:20
      np_spar_seg4,  design, discrete, range, 1:20
      np_spar_seg5,  design, discrete, range, 1:20
      np_le,    design, discrete, range, 1:20
      np_te,    design, discrete, range, 1:20
      fo_spar_1_seg1,     design, discrete, range, -90:90
      fo_spar_1_seg2,     design, discrete, range, -90:90
      fo_spar_1_seg3,     design, discrete, range, -90:90
      fo_spar_1_seg4,     design, discrete, range, -90:90
      fo_spar_1_seg5,     design, discrete, range, -90:90
      fo_spar_2_seg1,     design, discrete, range, -90:90
      fo_spar_2_seg2,     design, discrete, range, -90:90
      fo_spar_2_seg3,     design, discrete, range, -90:90
      fo_spar_2_seg4,     design, discrete, range, -90:90
      fo_spar_2_seg5,     design, discrete, range, -90:90
      fo_spar_3_seg1,     design, discrete, range, -90:90
      fo_spar_3_seg2,     design, discrete, range, -90:90
      fo_spar_3_seg3,     design, discrete, range, -90:90
      fo_spar_3_seg4,     design, discrete, range, -90:90
      fo_spar_3_seg5,     design, discrete, range, -90:90
      fo_spar_4_seg1,     design, discrete, range, -90:90
      fo_spar_4_seg2,     design, discrete, range, -90:90
      fo_spar_4_seg3,     design, discrete, range, -90:90
      fo_spar_4_seg4,     design, discrete, range, -90:90
      fo_spar_4_seg5,     design, discrete, range, -90:90
      fo_le,         design, discrete, range, -90:90
      fo_te,         design, discrete, range, -90:90
  responses:
    data_form: "compact"
    data: |
      diff_gj, objective, min, 0.5
      diff_eiyy, objective, min, 0.8
      diff_eizz, objective, min, 0.8
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: off
      work_directory:
        directory_tag: on
        directory_save: off
    required_files:
      - "data/*"
      - "design/*"
      - "scripts/*"
    asynchronous:
      evaluation_concurrency: 4
    failure_capture:
      recover: [1e12, 1e12, 1e12]
