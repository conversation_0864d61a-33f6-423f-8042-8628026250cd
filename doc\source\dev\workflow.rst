.. highlight:: python

Overall MSGD workflow
======================

In ``msgd.main.main()``:

#. Get running arguments/options such as main input file name ``fn`` and running mode ``mode``.
#. Load the main input file::

    msgd = msgd.io.readMSGDInput(fn, mode, ...)

#. Run analysis with a specific mode::

    msgd.run(mode)


In ``msgd.msgd.MSGD.run()``:

* If ``mode`` is '1', run a single analysis.

  #. Read specific parameter values generated by MDAO::

        msgd.readMDAOEvalIn()

  #. Update the structural design data::

        msgd.updateData()

  #. Analyze::

        msgd.analyze_v2()

  #. Write the final output file to pass result back to MDAO::

        msgd.writeMDAOEvalOut()

* Otherwise, write the MDAO (Dakota) input file::

    msgd.study.writeMDAOInput(...)

  If ``mode`` is '0', run MDAO::

    msgd.runMDAO()



Data exchange between MDAO (Dakota) and the analysis
----------------------------------------------------------------------

This is done through the dictionary-type object ``MSGD.data``.

For each MDAO iteration, it generate a set of specific values for design variables.

For Dakota:

#. Read the parameter file and store the values in an intermediate object::

    msgd.eval_in, msgd.eval_out = dakota.interfacing.read_parameters_file()

#. Send all parameters to ``msgd.data``.
#. Run analysis.
#. Store all results in ``msgd.data``.
#. Send all results to another intermediate object ``msgd.eval_out``.
#. Write the result file which can be recognized by Dakota.



Process of the single analysis function ``msgd.analyze_v2()``
----------------------------------------------------------------------

Load data
^^^^^^^^^^^^^^^^^^^


Generate location depending designs from the distributions (if needed)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Designs are stored in a dict-type object ``param_sets``::

    param_sets = {
        'set_name_1': params_1,
        'set_name_2': params_2,
        ...
    }


Go for each analysis step
^^^^^^^^^^^^^^^^^^^^^^^^^^


