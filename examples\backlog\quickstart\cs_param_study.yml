version: "0.8"


# Design parameters/variables of the structure
# ====================================================================
structure:
  name: "uh60_blade_1"
  parameter:
    a2p1: 0.8
    a2p3: 0.6
  design:
    type: 'discrete'
    dim: 1
    cs_assignment:
      all: 'main_cs'
  cs:
    main_cs:
      base: 'cs_1'
      model: 'md1'


cs:
  - name: "cs_1"
    parameter:
      mdb_name: "material_database_us_ft"
      airfoil: "SC1095.dat"
      airfoil_point_direction: -1
      lam_spar_1: "T300 15k/976_0.0053"
      lam_front: "T300 15k/976_0.0053"
      lam_back: "T300 15k/976_0.0053"
      lam_skin: "T300 15k/976_0.0053"
      lam_cap: "Aluminum 8009_0.01"
      mat_nsm: "lead"
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "Plascore PN2-3/16OX3.0"
      gms: 0.004
      rnsm: 0.001
    design:
      dim: 2
      tool: 'prevabs'
      base_file: "airfoil_gbox_uni.xml.tmp"
    model:
      md1:
        tool: 'vabs'

# Analysis process
# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      output:
        - value: ["gj", "eiyy", "eizz"]


# Configurations of design study, e.g., parameter study, optimization, etc.
# Mainly for Dakota
# ====================================================================
study:
  method:
    multidim_parameter_study:
      partitions: [2, 2]
  variables:
    data_form: "compact"
    data: |
      a2p1,    design, continuous,      0.8:0.9
      a2p3,    design, continuous,      0.5:0.6
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        directory_tag: on
        directory_save: on
    required_files:
      - "airfoil_gbox_uni.xml.tmp"
      - "material_database_us_ft.xml"
      - "SC1095.dat"
  responses:
    data_form: "compact"
    data: |
      gj, response
      eiyy, response
      eizz, response
