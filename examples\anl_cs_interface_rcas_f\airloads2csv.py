import argparse
import json
import os
import re

import pandas as pd


def parse_args():
    parser = argparse.ArgumentParser(description='A tool to convert RCAS internal airloads .rep files into CSV format for iVABS.')
    parser.add_argument('repfile', help='RCAS .rep file to convert.')
    parser.add_argument('dest', help='Destination CSV file name.')
    parser.add_argument('--use-legacy-format', '-l', help='Output to legacy file format (<PERSON>)', action='store_true')
    return parser.parse_args()


class RepFile:
    def __init__(self, file):
        self.file = file
        self.metadata = {}
        self.data = None
        with open(file, 'r') as f:
            self.raw_txt = f.readlines()

    def parse(self):
        new_page = re.compile('page +[0-9]+')
        cursor = 0
        tmp_data_inner = None
        tmp_data_outer = None
        caseno = None
        coord = None
        while cursor < len(self.raw_txt):
            page_id = new_page.findall(self.raw_txt[cursor])
            if page_id:
                current_data, current_meta = self._parse_page(cursor)
                self.metadata.update({page_id[0]: current_meta})
                caseno = caseno or current_meta['caseno']
                coord = coord or current_meta['description'][0]
                new_caseno = current_meta['caseno']
                new_coord = current_meta['description'][0]
                if new_coord == coord and new_caseno == caseno:
                    if tmp_data_inner is None:
                        tmp_data_inner = current_data
                    else:
                        tmp_data_inner = tmp_data_inner.join(current_data, how='outer')

                elif new_caseno == caseno:
                    if tmp_data_outer is None:
                        tmp_data_outer = tmp_data_inner
                    else:
                        tmp_data_outer = pd.concat((tmp_data_outer, tmp_data_inner))
                    tmp_data_inner = current_data

                else:
                    if self.data is None:
                        self.data = tmp_data_outer
                    else:
                        self.data = pd.concat((self.data, tmp_data_outer))
                    tmp_data_outer = tmp_data_inner
                    tmp_data_inner = current_data
                cursor = current_meta['end_line']
                caseno = new_caseno
                coord = new_coord
            else:
                cursor += 1
        self.data = pd.concat((self.data, pd.concat((tmp_data_outer, tmp_data_inner))))

    def save(self, dest, legacy_format=False, save_meta=False):
        if legacy_format:
            ncol = self.data.values.shape[1]
            lines = []
            for case in self.data.index.unique(level='Case'):
                for coord in self.data.index.unique(level='Coord'):
                    lines.append(coord.lower() + ',' * ncol + '\n')
                    lines.append(str(case) + ',' * ncol + '\n')
                    for az, row in self.data.loc[(case, coord), :].iterrows():
                        lines.append(f'{az},' + ','.join([str(x) for x in row]) + '\n')
                    lines.append(',' * ncol + '\n')
            lines = lines[:-1]
            with open(dest, 'w') as f:
                f.writelines(lines)
        else:
            self.data.to_csv(dest)
        if save_meta:
            with open(os.path.splitext(dest)[0] + '.json', 'w') as f:
                json.dump(self.metadata, f, indent=4)

    def _parse_page(self, idx):
        page_metadata = {
            'start_line': idx,
            'caseno': int(self.raw_txt[idx + 3].strip(' \n').split('.')[1]),
            'description': self.raw_txt[idx + 4].strip(' \n')
        }
        ihead = idx + 6
        hoffset = 1
        header_entry = re.compile(' [ -]([a-zA-Z0-9() _]{11}|[a-zA-Z0-9() _]+[^\n])')
        h1 = [s.strip() for s in header_entry.findall(self.raw_txt[ihead][1:])]
        h2 = [s.strip() for s in header_entry.findall(self.raw_txt[ihead + 1][1:])]
        try:
            _ = float(h2[0])
        except ValueError:
            labels = [l1 + l2 for l1, l2 in zip(h1, h2)]
            hoffset += 1
        else:
            labels = h1
        cursor = ihead + hoffset
        values = []
        intable = True
        while intable:
            try:
                line = self.raw_txt[cursor].strip('\n')
                if len(line.strip()) > 0:
                    values.append([float(x) for x in re.findall(' [ -][0-9E+-.]+', line[1:])])
                    cursor += 1
                else:
                    intable = False
            except IndexError:
                intable = False
        page_metadata.update({'end_line': cursor})
        data = pd.DataFrame(values, columns=labels)
        data['Case'] = [page_metadata['caseno']] * len(data)
        data['Coord'] = [page_metadata['description'][0]] * len(data)
        data = data.set_index(['Case', 'Coord', labels[0]])
        return data, page_metadata


def call(src, dest, legacy_format=False):
    """Wrapper function for calling from blade optimization workflow."""
    repfile = RepFile(src)
    repfile.parse()
    repfile.save(dest, legacy_format=legacy_format)


if __name__ == '__main__':
    args = parse_args()
    rep = args.repfile
    dst = args.dest
    use_legacy = args.use_legacy_format
    call(rep, dst, legacy_format=use_legacy)
