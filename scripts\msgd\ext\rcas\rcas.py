import csv
import logging
import math

from scipy.interpolate import interp2d
# import unicodedata as ucd
import sgio

# import msgd.utils as mutils

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)

planform_data_name = [
    'airfoil',
    'chord',
    'twist',
    'shear',
]

prop_name_map_default = {
    'mu': 'BMPL',
    'mcy': 'BCGOFF',
    'mcz': 'BCGOFFZ',
    'tcy': 'BTOFFY',
    'tcz': 'BTOFFZ',
    'gyry': 'BKMZZ',
    'gyrz': 'BKMYY',
}


# class GalaxyBladeData():
class RCASModel():
    def __init__(self, name:str=''):
        self.name = name

        self.length = 1

        self.fe_nodes = {}
        self.aero_nodes = {}
        self.aero_segs = {}
        self.airfoil_interp = {}

        self.oml = {}
        self.prop = {}
        """
        prop = {
            'PROP_NAME': {
                'x': [],
                'y': []
            },
            ...
        }
        """

        self.load = {
            'spanwise': [],
            'spanwise_type': 'blade_node_id',
            'azimuth': [],
            'azimuth_unit': 'degree',
            'force': {},
            'moment': {}
        }

        self.mesh:sgio.Mesh = None


    @property
    def num_fenodes(self): return len(self.fe_nodes['id'])
    @property
    def num_aeronodes(self): return len(self.aero_nodes['id'])
    @property
    def num_aerosegs(self): return len(self.aero_segs['id'])

    @property
    def points(self):
        _points = {}
        for _coord, _nid in zip(self.mesh.points, self.mesh.point_data['node_id']):
            _points[_nid] = _coord
        return _points

    @property
    def parts(self):
        elements = {}
        _nid_data = self.mesh.point_data['node_id']
        _eid_data = self.mesh.cell_data['element_id']
        # print(f'_eid_data = {_eid_data}')
        for _cbi, _cb in enumerate(self.mesh.cells):
            _eid_cbi = _eid_data[_cbi]
            for _eid, _enodes in zip(_eid_cbi, _cb.data):
                elements[int(_eid)] = [int(_nid_data[_ni]) for _ni in _enodes]
        return elements

    def getFENodeCoordById(self, node_id):
        for _i, _id in enumerate(self.fe_nodes['id']):
            if _id == node_id:
                return [
                    self.fe_nodes['x'][_i],
                    self.fe_nodes['y'][_i],
                    self.fe_nodes['z'][_i]
                ]


    def getAeroNodeCoordById(self, node_id):
        for _i, _id in enumerate(self.aero_nodes['id']):
            if _id == node_id:
                return [
                    self.aero_nodes['x'][_i],
                    self.aero_nodes['y'][_i],
                    self.aero_nodes['z'][_i]
                ]


    def getAirfoilInterpNames(self, file_ext='xy'):
        x = self.airfoil_interp['loc']
        y = self.airfoil_interp['airfoil']
        y = ['.'.join([fn, file_ext]) for fn in y]

        return {'x': x, 'y': y}


    def getFENodesBlade(self, xmin=None, xmax=None):
        fenodes_list = []
        for v in self.oml['fenode'].values():
            fenodes_list.append(v)
        fenodes_list.sort()

        fenodes = {'x': [], 'y': []}
        for c in fenodes_list:
            if xmin and c[0] < xmin:
                continue
            if xmax and c[0] > xmax:
                continue
            fenodes['x'].append(c[0])
            fenodes['y'].append(c[1])

        return fenodes


    def getChordAirflow(self):
        chord_airfoil = {'x': [], 'y': []}

        _pnodes = self.aero_segs['pnode_id']
        _cnodes = self.aero_segs['cnode_id']
        _chords = self.aero_segs['chord']
        for _i in range(self.num_aerosegs):
            # seg = self.oml['aeroseg'][i+1]
            # anodes = [self.oml['aeronode'][j] for j in seg['aero_nodes']]
            _pcoord = self.getAeroNodeCoordById(_pnodes[_i])
            _ccoord = self.getAeroNodeCoordById(_cnodes[_i])
            _chord = _chords[_i]

            chord_airfoil['x'].extend([_pcoord[0], _ccoord[0]])
            chord_airfoil['y'].extend([_chord, _chord])

            # chord_airfoil['x'].append(anodes[0][0])
            # chord_airfoil['y'].append(seg['chord'])
            # chord_airfoil['x'].append(anodes[1][0])
            # chord_airfoil['y'].append(seg['chord'])

        return chord_airfoil


    def getChordStruct(self):
        chord_struct = {'x': [], 'y': []}

        _pnodes = self.aero_segs['pnode_id']
        _cnodes = self.aero_segs['cnode_id']
        _chords = self.aero_segs['chord']
        _shears = self.aero_segs['shear']
        for _i in range(self.num_aerosegs):
            # seg = self.oml['aeroseg'][i+1]
            # anodes = [self.oml['aeronode'][j] for j in seg['aero_nodes']]
            _pcoord = self.getAeroNodeCoordById(_pnodes[_i])
            _ccoord = self.getAeroNodeCoordById(_cnodes[_i])
            _chord = _chords[_i] * math.cos(_shears[_i])

            chord_struct['x'].extend([_pcoord[0], _ccoord[0]])
            chord_struct['y'].extend([_chord, _chord])

            # chord_struct['x'].append(_pcoord[0])
            # chord_struct['y'].append(seg['chord']*math.cos(seg['shear']))
            # chord_struct['x'].append(_ccoord[0])
            # chord_struct['y'].append(seg['chord']*math.cos(seg['shear']))

        return chord_struct


    def getAeroSegTwist(self, which='all'):
        r"""
        Parameters
        ----------

        which : ['all', 'in', 'out']
            Get data of which point: all, inboard or outboard.
        """
        twist = {'x': [], 'y': []}
        for i in range(len(self.oml['aeroseg'])):
            seg = self.oml['aeroseg'][i+1]
            anodes = [self.oml['aeronode'][j] for j in seg['aero_nodes']]
            if which == 'all' or which == 'in':
                twist['x'].append(anodes[0][0])
                twist['y'].append(seg['twist'])
            if which == 'all' or which == 'out':
                twist['x'].append(anodes[1][0])
                twist['y'].append(seg['twist'])
        return twist


    def getAeroSegShear(self, which='all'):
        r"""
        Parameters
        ----------

        which : ['all', 'in', 'out']
            Get data of which point: all, inboard or outboard.
        """
        shear = {'x': [], 'y': []}
        for i in range(len(self.oml['aeroseg'])):
            seg = self.oml['aeroseg'][i+1]
            anodes = [self.oml['aeronode'][j] for j in seg['aero_nodes']]
            if which == 'all' or which == 'in':
                shear['x'].append(anodes[0][0])
                shear['y'].append(seg['shear'])
            if which == 'all' or which == 'out':
                shear['x'].append(anodes[1][0])
                shear['y'].append(seg['shear'])
        return shear


    def getProp(self, prop):
        R = self.prop['REFLENGTH']
        output = self.prop[prop]
        output['x'] = [x*R for x in output['x']]
        # output = {
        #     'x': [x*R for x in self.prop[prop]['x']],
        #     'y': self.prop[prop]['y']
        # }
        return output


    def getLoadCase(self, condition, azimuth, span):
        """
        Get a single load vector given the flight condition, azimuth
        andgle, and spanwise location
        """
        load = {'force': {}, 'moment': {}}

        i_angle = self.load['azimuth'].index(azimuth)
        j_span = self.load['spanwise'].index(span)

        load['force']['x'] = self.load['force'][condition]['x'][i_angle][j_span]
        load['force']['y'] = self.load['force'][condition]['y'][i_angle][j_span]
        load['force']['z'] = self.load['force'][condition]['z'][i_angle][j_span]
        load['moment']['x'] = self.load['moment'][condition]['x'][i_angle][j_span]
        load['moment']['y'] = self.load['moment'][condition]['y'][i_angle][j_span]
        load['moment']['z'] = self.load['moment'][condition]['z'][i_angle][j_span]

        return load


    def getLoadDataGrid(
        self, load, condition, component,
        azimuth_scale=1, spanloc_scale=1
        ):
        """
        Get the load data in the grid form for interpolation.

        Parameters
        ----------

        load: 'f' or 'm'
        condition: str
            Condition name
        component: 'x', 'y', or 'z'
            Component name

        x = []: spanwise location
        y = []: azimuth
        z = [[],[],...]: grid data
        """
        d = self.load[load]

        # a = self.load['azimuth']
        a = []
        z = []
        for row in d[condition][component]:
            a.append(row[0]*azimuth_scale)
            z.append(row[1:])

        b = [self.oml['fenode'][i][0]*spanloc_scale for i in d['node_id']]


        return {'a': a, 'b': b, 'z': z}


    def getRcasLoadCaseInterpFunctions(
        self, fn_force, fn_moment, fn_oml, flight_condition,
        azimuth_scale=1, spanloc_scale=1, load_file_delimiter=','
        ):

        load_functions = {
            'f': {},
            'm': {}
        }

        self.oml = readRcasOml(fn_oml)

        # self.load['azimuth'], self.load['force'], self.load['moment'] = readRcasLoad(fn_force, fn_moment, delimiter=load_file_delimiter)
        # self.load['spanwise'] = load_location
        self.load['f'], self.load['m'] = readRcasLoad(fn_force, fn_moment, delimiter=load_file_delimiter)
        # self.load['spanwise'] = self.load['f']['node_id']

        flight_condition = str(flight_condition)

        # create functions

        for fm in ['f', 'm']:
            for cp in ['x', 'y', 'z']:
                ld = self.getLoadDataGrid(
                    fm, flight_condition, cp, azimuth_scale, spanloc_scale)
                # print(ld)
                load_functions[fm][cp] = interp2d(ld['b'], ld['a'], ld['z'])
                if len(self.load['azimuth']) == 0:
                    self.load['azimuth'] = ld['a']


        self.load_functions = load_functions

        return

    def writePropsToFile(
        self, file_name, cs_model_sets, cs_assigns,
        props_to_write:list, coord='norm',
        prop_name_map:dict={},
        sff='12.4E',
        **kwargs):
        """
        """

        if not prop_name_map:
            prop_name_map = prop_name_map_default

        # Rearrange data
        prop = {}
        """
        {
            'BPROP': {
                'x': [],
                'y': []
            },
            ...
        }
        """

        # print(f'cs_model_sets = {cs_model_sets}')
        print(f'props_to_write = {props_to_write}')

        _entity_cs = {}  # Map from entity id to cs name
        for _cs_assign in cs_assigns:
            for _discreate_assign in _cs_assign.discrete_sg_assigns:
                _id = _discreate_assign['region']
                _cs_name = _discreate_assign['sg_model']
                _entity_cs[_id] = _cs_name

        for _pid, _pcoord in self.points.items():
            _cs_name = _entity_cs[_pid]

            for _set_name, _model in cs_model_sets.items():
                if _model.name == _cs_name:
                    _cs = _model
                    break

            _x = _pcoord[0]
            if coord == 'norm':
                _x = _x / self.length

            for _prop_name in props_to_write:
                _y = _cs.constitutive.get(_prop_name)

                try:
                    _prop_name_rcas = prop_name_map[_prop_name]
                except KeyError:
                    _prop_name_rcas = f'B{_prop_name.upper()}'

                if _prop_name_rcas not in prop:
                    prop[_prop_name_rcas] = {'x': [], 'y': []}

                prop[_prop_name_rcas]['x'].append(_x)
                prop[_prop_name_rcas]['y'].append(_y)

        # Write
        with open(file_name, 'w') as file:
            writePropToFile(prop, file, sff=sff, **kwargs)

        return




def readRcasInput(
    fn_oml, fn_prop='',
    create_mesh=True, node_id_start=1, node_id_end=-1,
    **kwargs
    ) -> RCASModel:
    """
    """

    logger.info(f'reading rcas input file {fn_oml}...')

    rcas_model = RCASModel()

    # rcas_model.oml = readRcasOml(fn_oml)
    _oml = readRcasOml(fn_oml)
    rcas_model.fe_nodes = _oml['fenode']
    rcas_model.aero_nodes = _oml['aeronode']
    rcas_model.aero_segs = _oml['aeroseg']
    rcas_model.airfoil_interp = _oml['airfoilinterp']

    rcas_model.length = rcas_model.fe_nodes['x'][-1]

    if fn_prop:
        rcas_model.prop = readRcasProp(fn_prop)

    # Create mesh
    if create_mesh:
        _nodes = []
        _node_data = {
            'node_id': [],
        }
        _elements = []
        _element_data = {
            'element_id': [],
        }

        # Store nodes
        _store = False
        for _i, _id in enumerate(rcas_model.fe_nodes['id']):
            if node_id_start > 0 and _id == node_id_start:
                _store = True
            if _store:
                _nodes.append([
                    rcas_model.fe_nodes['x'][_i],
                    rcas_model.fe_nodes['y'][_i],
                    rcas_model.fe_nodes['z'][_i]
                ])
                _node_data['node_id'].append(_id)
            if node_id_end > 0 and _id == node_id_end:
                _store = False

        _elem_data = []
        for _i in range(len(_nodes) - 1):
            _elements.append([_i, _i+1])
            _elem_data.append(_i+1)
        _element_data['element_id'].append(_elem_data)

        _elements = {
            'line': _elements,
        }

        rcas_model.mesh = sgio.meshio.Mesh(
            points=_nodes,
            cells=_elements,
            point_data=_node_data,
            cell_data=_element_data,
        )


    return rcas_model









def getRcasLoadVectorAt(functions, azimuth, spanwise):

    load = [
        functions['f']['x'](spanwise, azimuth)[0],
        functions['f']['y'](spanwise, azimuth)[0],
        functions['f']['z'](spanwise, azimuth)[0],
        functions['m']['x'](spanwise, azimuth)[0],
        functions['m']['y'](spanwise, azimuth)[0],
        functions['m']['z'](spanwise, azimuth)[0]
    ]

    return load









def readRcasOml(fn:str) -> dict:
    """A function reading RCAS OML data file.
    Data formatting rules:
    1. Key tags:
    - 'S': keyword line (space needed after 'S' and before keyword)
    - '!': comment line
    - 'A' or 'a': data line
    2. Keywords:
    - 'FENODE': finite element node (required)
    - 'AERONODE': aerodynamic node
    - 'AEROSEG': aerodynamic segment
    - 'AIRFOILINTERP': airfoil interpolation
    3. Comments:
    - Anything after '!' is a comment and should be omitted.
    4. Data lines:
    - Each keyword can contain multiple data lines and columns.
    - For each data line, data columns are separated by space(s).
    - The first column is the tag (drop), and the rest are data columns (keep).
    6. 'FENODE':
    - Columns: id, x, y, z
    - Types: int, float, float, float
    7. 'AERONODE':
        - Columns: id, x, y, z
        - Types: int, float, float, float
    8. 'AEROSEG':
        - Columns: id, pnode, cnode, chord, airfoil, elem_id, twist, shear
        - Types: int, int, int, float, str, int, float, float
    9. 'AIRFOILINTERP':
        - Columns: r, airfoil
        - Types: float, str

    Parameters
    ----------
    fn : str
        File name

    Returns
    -------
    dict
        Parsed data        
    """
    fe_nodes = {
        'id': [], 
        'x': [],
        'y': [],
        'z': []
    }

    aero_nodes = {
        'id': [],
        'x': [],
        'y': [],
        'z': []
    }

    aero_segs = {
        'id': [],
        'pnode_id': [],
        'cnode_id': [],
        'chord': [],
        'airfoil': [],
        'elem_id': [],
        'twist': [],
        'shear': []
    }

    airfoil_interp = {
        'loc': [],
        'airfoil': []
    }


    with open(fn, 'r') as file:
        for line in file.readlines():
            line = line.strip().split('!')[0]
            if line == '':
                continue

            line = line.split()
            if line[0].lower() == 's':
                kwd = line[1].lower()
                continue

            if kwd == 'fenode':
                fe_nodes['id'].append(int(line[1]))
                fe_nodes['x'].append(float(line[2]))
                fe_nodes['y'].append(float(line[3]))
                fe_nodes['z'].append(float(line[4]))

            elif kwd == 'aeronode':
                aero_nodes['id'].append(int(line[1]))
                aero_nodes['x'].append(float(line[2]))
                aero_nodes['y'].append(float(line[3]))
                aero_nodes['z'].append(float(line[4]))

            elif kwd == 'aeroseg':
                aero_segs['id'].append(int(line[1]))
                aero_segs['pnode_id'].append(int(line[2]))
                aero_segs['cnode_id'].append(int(line[3]))
                aero_segs['chord'].append(float(line[4]))
                aero_segs['airfoil'].append(line[5])
                aero_segs['elem_id'].append(int(line[6]))
                aero_segs['twist'].append(float(line[7]))
                aero_segs['shear'].append(float(line[8]))

            elif kwd == 'airfoilinterp':
                airfoil_interp['loc'].append(float(line[1]))
                airfoil_interp['airfoil'].append(line[2])

    oml = {
        'fenode': fe_nodes,
        'aeronode': aero_nodes,
        'aeroseg': aero_segs,
        'airfoilinterp': airfoil_interp
    }

    return oml









def readRcasProp(fn):
    """
    prop = {
        'BGJ': {
            'x': [],
            'y': []
        },
        ...
    }

    "!M PropName" is an identifier telling RCAS that
    the subsequent data set belongs to the category "PropName".

    Property names:
    - 'REFLENGTH': Reference length
    - 'BSTRUCTW': Structural twist about blade spanwise axis (rad)
    - 'BMPL': Mass per unit length
    - 'BEA': Extension stiffness
    - 'BGJ': Torsional stiffness
    - 'BEIYY': Bending stiffness about local Y-axis
    - 'BEIZZ': Bending stiffness about local Z-axis
    - 'BEIYZ': Coupled bending stiffness
    - 'BKMYY': Radius of gyration about local Y-axis
    - 'BKMZZ': Radius of gyration about local Z-axis
    - 'BKMYZ'
    - 'BCGOFF': Center of gravity offset along local Y-axis
    - 'BCGOFFZ': Center of gravity offset along local Z-axis
    - 'BTOFFY': Elastic/tension offset along local Y-axis
    - 'BTOFFZ': Elastic/tension offset along local Z-axis
    """
    prop = {}
    kwd_scalar_data = ['REFLENGTH',]
    kwd_non_xydata = ['BMISC',]
    data_type = 'xy'

    with open(fn, 'r') as file:
        for line in file.readlines():
            if line.startswith('*') or line.strip() == '':
                continue

            if line.startswith('!'):
                kwd = line.strip().split()[1]
                if kwd in kwd_scalar_data:
                    data_type = 'scalar'
                elif kwd in kwd_non_xydata:
                    data_type = 'other'
                    prop[kwd] = []
                else:
                    data_type = 'xy'
                    prop[kwd] = {'x': [], 'y': []}
                continue

            line = line.strip().split()
            if data_type == 'xy':
                prop[kwd]['x'].append(float(line[0]))
                prop[kwd]['y'].append(float(line[1]))
            elif data_type == 'scalar':
                prop[kwd] = float(line[0])
            elif data_type == 'other':
                data = list(map(float, line))
                prop[kwd].append(data)

    return prop




def writePropToFile(prop, file, sff='12.4E', **kwargs):
    """
    Parameters
    ----------
    prop : dict
        Property data

        {
            'BPROP': {
                'x': [],
                'y': []
            },
            ...
        }
    file : file
        File object
    sff : str
        Scientific format string
    """
    for _prop_name, _xy in prop.items():
        file.write(f'!M {_prop_name.upper()}\n')
        for _x, _y in zip(_xy['x'], _xy['y']):
            file.write(f'{_x:7.5f} {_y:{sff}}\n')
        file.write('\n')
    return




# def writeRcasPropsFile(
#     file_name, cs_model_sets, cs_assigns,
#     props_to_write:list, coord='norm',
#     sff='12.4E',
#     **kwargs):
#     # Rearrange data
#     prop = {}
#     """
#     {
#         'BPROP': {
#             'x': [],
#             'y': []
#         },
#         ...
#     }
#     """

#     # print(f'cs_model_sets = {cs_model_sets}')
#     print(f'props_to_write = {props_to_write}')

#     _entity_cs = {}  # Map from entity id to cs name
#     for _cs_assign in cs_assigns:
#         for _discreate_assign in _cs_assign.discrete_sg_assigns:
#             _id = _discreate_assign['region']
#             _cs_name = _discreate_assign['sg_model']
#             _entity_cs[_id] = _cs_name

#     for _pid, _pcoord in points.items():
#         _cs_name = _entity_cs[_pid]

#         for _set_name, _model in cs_model_sets.items():
#             if _model.name == _cs_name:
#                 _cs = _model
#                 break

#         _x = _pcoord[0]
#         if coord == 'norm':
#             _x = _x / length

#         for _prop_name in props_to_write:
#             _y = _cs.constitutive.get(_prop_name)

#             try:
#                 _prop_name_rcas = prop_name_map[_prop_name]
#             except KeyError:
#                 _prop_name_rcas = f'B{_prop_name.upper()}'

#             if _prop_name_rcas not in prop:
#                 prop[_prop_name_rcas] = {'x': [], 'y': []}

#             prop[_prop_name_rcas]['x'].append(_x)
#             prop[_prop_name_rcas]['y'].append(_y)

#     # Write
#     with open(file_name, 'w') as file:
#         writePropToFile(prop, file, sff=sff, **kwargs)

#     return









def readRcasLoad(
        fn_force, fn_moment,
        delimiter=',', nhead=1, encoding='utf-8-sig',
        loc_tags=['node',], cond_tags=['case', 'azimuth'],
        load_type=0
    ):
    '''
    '''
    logger.info(f'reading structural response file {fn_force} and {fn_moment}...')

    from msgd.core import StateLocCase
    state_locase = StateLocCase(locs=[])

    # struct_resp_cases = sgio.StructureResponseCases()
    # struct_resp_cases.loc_tags = loc_tags
    # struct_resp_cases.cond_tags = cond_tags

    node_id, force = readRcasLoadCsv(fn_force, delimiter, nhead, encoding)
    _, moment = readRcasLoadCsv(fn_moment, delimiter, nhead, encoding)

    # print(f'force =\n{mutils.convertToPrettyString(force)}')

    # load_tags = ['fx', 'fy', 'fz', 'mx', 'my', 'mz']

    for _case_f, _case_m in zip(force, moment):
        # condition = [_case_f['case'], _case_f['azimuth']]
        _case = {
            'case': _case_f['case'],
            'azimuth': _case_f['azimuth']
        }

        _states = {}

        _load_field = {}  # Load field data

        for _i, _node in enumerate(node_id):

            location = [_node,]

            # sect_resp = sgio.SectionResponse()
            # sect_resp.load_type = load_type
            # sect_resp.load_tags = load_tags

            _load = [
                _case_f['x'][_i], _case_f['y'][_i], _case_f['z'][_i],
                _case_m['x'][_i], _case_m['y'][_i], _case_m['z'][_i]
            ]

            # sect_resp.load = _load
            _load_field[_node] = _load

            # struct_resp_cases.addResponseCase(location, condition, sect_resp)

        _states['load'] = sgio.State(name='load', data=_load_field)

        _state_case = sgio.StateCase(case=_case, states=_states)
        # print(_state_case)
        state_locase.addStateCase(_state_case)

    return state_locase




# def readRcasLoadOld(fn_force, fn_moment, delimiter=',', nhead=1, encoding='utf-8-sig'):

#     force = readRcasLoadCsv(fn_force, delimiter, nhead, encoding)
#     moment = readRcasLoadCsv(fn_moment, delimiter, nhead, encoding)

#     return force, moment









def readRcasLoadCsv(fn, delimiter=',', nhead=1, encoding='utf-8-sig'):
    """
    load = [
        {
            'case': 1,
            'azimuth': 0,
            'x': [xn1, xn2, xn3, ...],
            'y': [],
            'z': []
        },
        {
            'case': 2,
            ...
        },
        ...
    ]
    """

    node_id = []
    load = []

    with open(fn, 'r', encoding=encoding) as file:
        cr = csv.reader(file, delimiter=delimiter)

        for i, row in enumerate(cr):
            row = [s.strip() for s in row]
            if row[0] == '':
                continue

            if i < nhead:
                # Read head
                for label in row:
                    if label.lower().startswith('rotor'):
                        nid = int(label.lower().split('node')[1])
                        node_id.append(nid)

            else:
                case = int(row[0])
                component = row[1].lower()
                azimuth = float(row[2])

                i_case = None
                for _i, _load in enumerate(load):
                    if case == _load['case'] and azimuth == _load['azimuth']:
                        i_case = _i
                        break
                if i_case is None:
                    load.append({
                        'case': case,
                        'azimuth': azimuth
                    })
                    i_case = -1

                # if not condition in load.keys():
                #     load[condition] = {}
                # if not component in load[condition].keys():
                #     load[condition][component] = []

                v = list(map(float, row[3:]))
                load[i_case][component] = v

    return node_id, load








# def readRcasLoadOld(fn_force, fn_moment, delimiter=',', encoding='utf-8-sig'):
#     """
#     force/moment = {
#         flight_condition_1: {
#             'x': [
#                 [xn1, xn2, xn3, ...],
#                 [xn1, xn2, xn3, ...],
#                 ...
#             ],
#             'y': [],
#             'z': []
#         },
#         flight_condition_2: {},
#         ...
#     }
#     """
#     azimuth = []
#     force = {}
#     moment = {}

#     with open(fn_force, 'r', encoding=encoding) as file:
#         cr = csv.reader(file, delimiter=delimiter)
#         condition = '1'
#         component = 'x'
#         cond_count = 1
#         comp_count = 1
#         block = 0
#         for row in cr:
#             # row = list(map(ucd.normalize, ['NFKD']*len(row), row))
#             row = [s.strip() for s in row]
#             if row[0] == '':
#                 continue
#             # print(row)

#             if row[1] == '':
#                 if row[0] in ['x', 'y', 'z']:
#                     component = row[0]
#                     block += 1
#                 else:
#                     condition = row[0]
#                     if not condition in force.keys():
#                         force[condition] = {}
#                     if not component in force[condition].keys():
#                         force[condition][component] = []
#             else:
#                 a = float(row[0])
#                 v = list(map(float, row[1:]))
#                 if block == 1:
#                     azimuth.append(a)
#                 force[condition][component].append(v)

#     with open(fn_moment, 'r', encoding=encoding) as file:
#         cr = csv.reader(file, delimiter=delimiter)
#         condition = '1'
#         component = 'x'
#         cond_count = 1
#         comp_count = 1
#         block = 0
#         for row in cr:
#             # row = list(map(ucd.normalize, ['NFKD']*len(row), row))
#             row = [s.strip() for s in row]
#             if row[0] == '':
#                 continue
#             # print(row)

#             if row[1] == '':
#                 if row[0] in ['x', 'y', 'z']:
#                     component = row[0]
#                     block += 1
#                 else:
#                     condition = row[0]
#                     if not condition in moment.keys():
#                         moment[condition] = {}
#                     if not component in moment[condition].keys():
#                         moment[condition][component] = []
#             else:
#                 # a = float(row[0])
#                 v = list(map(float, row[1:]))
#                 # if block == 1:
#                 #     azimuth.append(a)
#                 moment[condition][component].append(v)

#     return azimuth, force, moment


