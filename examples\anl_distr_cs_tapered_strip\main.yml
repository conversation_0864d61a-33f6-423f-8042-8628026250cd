# Main input file

version: '0.10'  # Required.


# Design parameters/variables of the structure
# ====================================================================
structure:
  name: 'blade1'

  parameter:
    L: 10
    w_root: 1.5
    w_tip: 0.5

  distribution:
    - name: 'w'
      function: 'f_interp_linear'
      xscale: L
      data_form: 'compact'
      data: |
        0, w_root
        1, w_tip

  # Basic design inputs of the blade
  model:
    section_locations:
      type: 'curvilinear'
      coordinates: [0, 2, 5, 7, 9, 10]  # Locations to create cross-sections

  cs_assignment:
    - region: 'all'
      location: 'node'
      cs: 'main_cs'

  cs:
    - name: 'main_cs'
      design: 'cs1'
      model:
        type: 'bm2'
        solver: 'vabs'


function:
  - name: 'f_interp_linear'
    type: 'interpolation'
    interp_kind: 'linear'


# Base (default/template) design of the cross-section
# ====================================================================
cs:
  - name: 'cs1'
    builder: 'prevabs'
    parameter:
      w: 1
      t: 0.1
      lam_lyr_1: 'la_mat_1'
      ang_lyr_1: 0
      gms: 0.01

    design:
      base_file: 'rect.xml.tmp'


# Analysis process
# ====================================================================
analysis:
  steps:
    - step: 'cs analysis'
      type: 'cs'
      analysis: 'h'
      output:
        value: [
          'cmp11', 'cmp12', 'cmp13', 'cmp14', 'cmp15', 'cmp16',
          'cmp22', 'cmp23', 'cmp24', 'cmp25', 'cmp26',
          'cmp33', 'cmp34', 'cmp35', 'cmp36',
          'cmp44', 'cmp45', 'cmp46',
          'cmp55', 'cmp56',
          'cmp66',
          'ms11', 'ms12', 'ms13', 'ms14', 'ms15', 'ms16',
          'ms22', 'ms23', 'ms24', 'ms25', 'ms26',
          'ms33', 'ms34', 'ms35', 'ms36',
          'ms44', 'ms45', 'ms46',
          'ms55', 'ms56',
          'ms66',
        ]

