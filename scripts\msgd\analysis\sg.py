import copy
import csv
import logging
import os
import shutil
import xml.etree.ElementTree as et

# import numpy as np
import pprint
import sgio
import tqdm
# import yaml
from icecream import ic


# from msgd.core._structure import StructureModel
# from msgd.core._database import DataBase
# from msgd.core._analysis import AnalysisStep
import msgd._global as GLOBAL
import msgd.builder.main as mbp
import msgd.utils as mutils
import msgd.ext as mext
from msgd._global import Pretty
from msgd.core import *


# from msgd.analysis._sg.core import SGAnalysis


# logger = logging.getLogger()
logger = logging.getLogger(__name__)

pp = pprint.PrettyPrinter(
    indent=2, width=80, compact=True)



class SGenomeAnalysisStep(AnalysisStep):
    """Structure genome analysis step.

    Parameters
    ----------
    name : str, optional
        Name of the step. Default is ''.
    analysis : str, optional
        Type of analysis. Default is ''.
    output_config : list, optional
        Output configuration. Default is [].
    activate : bool, optional
        Activate the step. Default is True.
    parameters : dict, optional
        Parameters for the step. Default is {}.
    work_dir : str, optional
        Work directory. Default is 'sgs'.
    setting : dict, optional
        Setting for the step. Default is {}.
    """

    step_type = GLOBAL.SG_KEY

    def __init__(
        self, name='', analysis:str='',
        macro_states_input:dict={},
        output_config:list=[],
        activate:bool=True, parameters:dict={},
        step_args:list=[], step_kwargs:dict={},
        prepros=[], postpros=[],
        work_dir:str=f'{GLOBAL.SG_KEY}s',
        setting:dict={}
        ):
        super().__init__(
            name=name,
            activate=activate,
            parameters=parameters,
            step_args=step_args,
            step_kwargs=step_kwargs,
            prepros=prepros,
            postpros=postpros,
            setting=setting,
            output_config=output_config
            )

        self._analysis:str = analysis
        # self._step_output:list = output_quantity
        self._dir:str = work_dir

        self._macro_states_input = macro_states_input

    def __repr__(self) -> str:
        _str = [
            f'SG Analysis Step: {self._name}',
            f'  - activate: {self._activate}',
            f'  - type: {type(self).step_type}',
            f'  - analysis: {self._analysis}',
            f'  - work directory: {self._dir}',
            f'  - output quantity:',
        ]

        for _q in self._step_output:
            _str.append(f'    - value: {_q.get("value")}')
            if 'name' in _q.keys():
                _str.append(f'      name: {_q.get("name")}')

        return '\n'.join(_str)

    def toDictionary(self):
        """Convert the object to a dictionary.
        """
        _dict = super().toDictionary()
        _dict['type'] = type(self).step_type
        _dict['analysis'] = self._analysis
        _dict['work_dir'] = self._dir
        # _dict['output'] = self._step_output

        return _dict

    @property
    def analysis(self): return self._analysis
    @property
    def work_dir(self): return self._dir
    @property
    def step_output(self): return self._step_output




    # ----------------------------------------------------------------

    def runH(
        self, structure_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        db_sg_model_sets:DataBase=None,
        **kwargs
        ) -> None:
        """Run homogenization of the structure genome.

        Parameters
        ----------
        structure_model : StructureModel
            Structure model containing the SGs.
        db_sg_model : DataBase, optional
            Database of SG models. Default is None.
        db_sg_design : DataBase, optional
            Database of SG designs. Default is None.
        """

        ic(logger)
        for handler in logger.handlers:
            print(handler)
            if isinstance(handler, logging.StreamHandler):
                print(f"Console log level: {handler.level}")
            elif isinstance(handler, logging.FileHandler):
                print(f"File log level: {handler.level}")

        logger.info(f'[step: {self._name}] running {GLOBAL.SG_KEY} analysis ({self._analysis})...')

        logger.debug(f'structure_model.sg_model_sets')
        logger.debug(structure_model.sg_model_sets)

        # Do SG analysis for each set
        # for _set_name, _sg_model in tqdm.tqdm(structure_model.sg_model_sets.items(), desc=GLOBAL.SG_KEY):
        for _set_name, _sg_model in structure_model.sg_model_sets.items():

            logger.debug(f'running sg analysis _set_name = {_set_name}, _sg_model = {_sg_model.name}')

            # Create SG analysis object
            _sg_analysis = SGAnalysis(
                name=_set_name,
                analysis=self._analysis,
                output_quantity=self._step_output,
                work_dir=self._dir,
                prepros=self._prepro_funcs,
                postpros=self._postpro_funcs,
                setting=self.setting,
                global_parameters=structure_model.design.parameters
                # upper_model_name=structure_model.name
                )

            logger.debug(f'updating parameters')
            _sg_model.updateParameters(structure_model.design.parameters)

            # Run SG analysis
            _sg_analysis.run(
                _sg_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                db_sg_model_sets=db_sg_model_sets,
                # macro_state_locases=_macro_state_locases,
                **kwargs
                )

        # Write output to file
        if self._output_config:
            self._writeOutput(structure_model)


    def _writeOutput(self, structure_model:StructureModel):
        _output_values = self._output_config.get('value', [])
        _output_names = self._output_config.get('name', [])

        _prop_name_map = {}

        for _i, _name in enumerate(_output_values):
            for _set_name, _sg_model in structure_model.sg_model_sets.items():
                _value = _sg_model.constitutive.get(_name)

                if len(structure_model.sg_model_sets) == 1:
                    self._step_output[_name] = _value
                else:
                    if not _name in self._step_output.keys():
                        self._step_output[_name] = {}
                    self._step_output[_name][_sg_model.name] = _value

            if _output_names:
                _prop_name_map[_name] = _output_names[_i]

        _fn = self._output_config.get('file_name', '')
        if _fn:
            _ff = self._output_config.get('file_format', 'csv')
            structure_model.writeSGPropertyFile(
                props_to_write=_output_values,
                prop_name_map=_prop_name_map,
                file_name=_fn
                )





    # ----------------------------------------------------------------

    def runDF(
        self, structure_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        db_sg_model_sets:DataBase=None,
        **kwargs
        ):
        """Run dehomogenization and failure analysis of the structure genome.

        Parameters
        ----------
        structure_model : StructureModel
            Structure model containing the SGs.
        db_sg_model : DataBase, optional
            Database of SG models. Default is None.
        db_sg_design : DataBase, optional
            Database of SG designs. Default is None.
        """

        logger.info(f'[step: {self._name}] running {GLOBAL.SG_KEY} analysis ({self._analysis})...')

        readSectionResponse(self._macro_states_input, structure_model)

        # Do SG analysis for each location
        loc_id = 1
        # for _i, _sg_assign in enumerate(structure_model.discrete_sg_assigns):
        # for _sg_assign in tqdm.tqdm(structure_model.discrete_sg_assigns, desc='location'):
        for _sg_assign in structure_model.discrete_sg_assigns:

            # loc_id = _i + 1
            _entity_id = _sg_assign['region']
            _sg_model_name = _sg_assign['sg_model']
            logger.debug(f'entity_id = {_entity_id}, sg_model_name = {_sg_model_name}')

            # Get set name
            # logger.debug(f'sg_model_sets = {structure_model.sg_model_sets}')
            for __set_name, _sg_model in structure_model.sg_model_sets.items():
                logger.debug(f'__set_name = {__set_name}, _sg_model.name = {_sg_model.name}')
                if _sg_model.name == _sg_model_name:
                    _set_name = __set_name
                    break

            # Get the macro state cases
            # ~~~~~~~~~~~~~~~~~~~~~~~~~

            macro_state_locases:list[StateLocCase] = []

            for _local_state_locase in structure_model.local_state_locases:
                # Write cases to file
                _fn_cases = 'local_cases'
                _cases = _local_state_locase.getCaseList()
                mutils.dumpData(
                    _cases, _fn_cases
                )

                _locs_this_level = _local_state_locase.locs
                # print(f'locs_this_level = {_locs_this_level}')

                # Get all state cases at given locations (e.g., elements)
                # where the lower level SG model is assigned
                _state_cases_at_loc = _local_state_locase.getStateCasesAtLocs(
                    locs=_entity_id,
                    state_name=['load',]
                )
                # print(f'state_cases_at_locs = {_state_cases_at_loc}')

                # Create the list of macro state loc cases
                # for the lower level SG model

                if not _state_cases_at_loc is None:
                    # Create the new list of locs
                    _loc_new = {
                        'structure': structure_model.name,
                        'loc_id': loc_id,
                        'locs': _state_cases_at_loc['locs']
                    }
                    _locs_next_level = copy.deepcopy(_locs_this_level)
                    # print(_locs_next_level)
                    _locs_next_level.append(_loc_new)
                    # print(f'_locs_next_level = {_locs_next_level}')

                    _state_locase_next_level = StateLocCase(
                        # model_track=_model_track,
                        locs=_locs_next_level,
                        state_cases=_state_cases_at_loc['state_cases']
                    )
                    # print(f'_state_locase_next_level = {_state_locase_next_level}')
                    macro_state_locases.append(_state_locase_next_level)


            if len(macro_state_locases) > 0:

                # print('\n')
                logger.debug('-'*40)
                logger.debug(f'loc_id = {loc_id}, entity_id = {_entity_id}, sg_model_name = {_sg_model_name}')

                # Create SG analysis object
                _sg_analysis = SGAnalysis(
                    name=_set_name,
                    analysis=self._analysis,
                    output_quantity=self._step_output,
                    work_dir=self._dir,
                    prepros=self._prepro_funcs,
                    postpros=self._postpro_funcs,
                    setting=self.setting
                    # upper_model_name=structure_model.name
                    )

                # Run SG analysis
                _output = _sg_analysis.run(
                    _sg_model,
                    db_sg_model=db_sg_model,
                    db_sg_design=db_sg_design,
                    db_sg_model_sets=db_sg_model_sets,
                    macro_state_locases=macro_state_locases,
                    **kwargs
                    )

                # For initial failure analysis
                # store the sg output to the upper level model state
                if self._analysis == 'fi':
                    for _this_slc, _sg_slc in zip(structure_model.local_state_locases, macro_state_locases):
                        # breakpoint()
                        _this_slc.updateStateCase(
                            entity_id=_entity_id,
                            state_name='strength_ratio',
                            use_state_cases=_sg_slc.state_cases,
                            use_state_name='strength_ratio_min'
                        )
                        # print('\n(after) local', _this_slc)

                # print()
                # print('local_state_locases')
                # print(yaml.dump(structure_model.local_state_locases, default_flow_style=False))
                # for _slc in structure_model.local_state_locases:
                #     pp.pprint(_slc.toDictionary())

                loc_id += 1

        # breakpoint()

        _data = [_slc.toDictionary() for _slc in structure_model.local_state_locases]
        mutils.dumpData(
            _data,
            f'_{structure_model.name}_local_state_locases'
        )

        # Write strength ratio to file
        if self._output_config:

            _output_values = self._output_config.get('value', [])

            if self._analysis == 'fi':
                # print(structure_model.local_state_locases[0])
                _data = structure_model.local_state_locases[0].getStateCases('strength_ratio')
                # breakpoint()
                if 'sr' in _output_values:
                    self._step_output['sr'] = _data

                if 'sr_min' in _output_values:
                    sr_all_case0 = _data[0]['state']

                    _sr_min = -1
                    for _i, _sr in sr_all_case0.items():
                        if _sr_min == -1:
                            _sr_min = _sr
                        elif _sr < _sr_min:
                            _sr_min = _sr

                    self._step_output['sr_min'] = _sr_min

                _fn = self._output_config.get('file_name', '')
                if _fn:
                    _ff = self._output_config.get('file_format', 'csv')

                mutils.dumpData(
                    data=_data,
                    file_name=_fn,
                    file_format=_ff
                    )

        return




    # ----------------------------------------------------------------

    def run(
        self, structure_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        **kwargs
        ) -> None:
        """Run analysis of the structure genome.

        Parameters
        ----------
        structure_model : StructureModel
            Structure model containing the SGs.
        db_sg_model : DataBase, optional
            Database of SG models. Default is None.
        db_sg_design : DataBase, optional
            Database of SG designs. Default is None.
        """

        # logger.info(f'[step: {self._name}] running {GLOBAL.SG_KEY} analysis ({self._analysis})...')

        # Create subdir
        if not os.path.exists(self.work_dir):
            os.makedirs(self.work_dir)

        # _sg_model_done = []

        self.importPrePostProcFuncs()


        # Homogenization
        # --------------

        if self._analysis == 'h':

            self.runH(
                structure_model=structure_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                **kwargs
            )


        # Dehomogenization and failure analysis
        # --------------------------------------

        elif self._analysis in ['d', 'f', 'fi']:

            self.runDF(
                structure_model=structure_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                **kwargs
            )


        # Store output
        # ------------

        # if self._output_config:
        #     _output_values = self._output_config.get('value', [])
        #     for _name in _output_values:
        #         for _set_name, _sg_model in structure_model.sg_model_sets.items():
        #             _value = _sg_model.constitutive.get(_name)

        #             if len(structure_model.sg_model_sets) == 1:
        #                 self._step_output[_name] = _value
        #             else:
        #                 if not _name in self._step_output.keys():
        #                     self._step_output[_name] = {}
        #                 self._step_output[_name][_sg_model.name] = _value

            # # Write output to file
            # _fn = self._output_config.get('file_name', '')
            # if _fn:
            #     _ff = self._output_config.get('file_format', 'csv')
            #     structure_model.writeSGPropertyFile(
            #         props_to_write=_output_values,
            #         file_name=_fn
            #         )

        return









# ====================================================================

class SGAnalysis():
    """Analysis of a single SG.

    Parameters
    ----------
    name : str, optional
        Name of the analysis step. Default is ''.
    analysis : str, optional
        Type of analysis. Default is ''.
    output_quantity : list, optional
        List of output quantities. Default is [].
    work_dir : str, optional
        Work directory. Default is 'sgs'.
    upper_model_name : str, optional
        Name of the upper model. Default is ''.
    """

    def __init__(
        self, name:str='', analysis:str='',
        output_quantity:list=[],
        work_dir:str=f'{GLOBAL.SG_KEY}s',
        upper_model_name:str='',
        prepros=[], postpros=[],
        setting={}, global_parameters={}
        ):
        # super().__init__(name=name)
        self._name:str = name
        self._analysis:str = analysis
        self._step_output:list = output_quantity
        self._dir:str = work_dir
        self._upper_model_name:str = upper_model_name

        self._prepros = prepros  # list of functions
        self._postpros = postpros

        self.setting = setting
        self._run_try_max = 3  # maximum number of tries to run the analysis

        self._global_parameters = global_parameters

    def __repr__(self) -> str:
        _str = [
            f'SG Analysis Step: {self._name}',
            f'  - analysis: {self._analysis}',
            f'  - work directory: {self._dir}',
            f'  - output quantity:',
        ]

        for _q in self._step_output:
            _str.append(f'    - value: {_q.get("value")}')
            if 'name' in _q.keys():
                _str.append(f'      name: {_q.get("name")}')

        return '\n'.join(_str)

    def toDictionary(self):
        """Convert the object to a dictionary.
        """
        _dict = {}
        _dict['name'] = self._name
        _dict['analysis'] = self._analysis
        _dict['work_dir'] = self._dir
        _dict['output'] = self._step_output

        return _dict

    @property
    def name(self): return self._name
    @property
    def analysis(self): return self._analysis
    @property
    def output_quantity(self): return self._step_output




    # ----------------------------------------------------------------

    def build(
        self, sg_model:StructureModel,
        db_sg_model:DataBase=None,
        db_sg_design:DataBase=None,
        substitute:bool=True,
        ) -> None:
        """Build the SG model.

        Parameters
        ----------
        sg_model : StructureModel
            SG model object.
        db_sg_model : DataBase, optional
            Database of SG models. Default is None.
        db_sg_design : DataBase, optional
            Database of SG designs. Default is None.
        substitute : bool, optional
            Substitute parameters. Default is True.
        """
        # logger.debug(Pretty(locals()))
        logger.debug('\n---\n'.join(f'{k}:\n{v}' for k, v in locals().items()))

        if self.analysis == 'h':
            if isinstance(sg_model.design, str):
                _design = db_sg_design.getItemByName(sg_model.design)
                sg_model.design = _design

            # Substitute parameters
            sg_model.design.substituteParameters()

        # print(f'self._dir = {self._dir}')

        mbp.buildSGModel(
            sg_model,
            analysis=self._analysis,
            db_sg_model=db_sg_model,
            substitute=substitute,
            work_dir=self._dir,
            sgdb=sg_model.sg_model_sets
        )

        # print(sg_model.sg_data)

        return


    def updateGlobalData(method, **kwargs):
        return




    # ----------------------------------------------------------------

    def runH(
        self, sg_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        db_sg_model_sets:DataBase=None,
        **kwargs):
        """
        """

        # logger.debug(f'[{GLOBAL.SG_KEY}: {sg_model.name}] running {GLOBAL.SG_KEY} analysis...')

        output = None

        # Check lower level SGs
        logger.debug(f'sg_model.sg_model_sets')
        logger.debug(sg_model.sg_model_sets)

        # Run lower level SGs
        for _set_name, _sg_model in sg_model.sg_model_sets.items():

            logger.debug(f'running sg analysis _set_name = {_set_name}, _sg_model = {_sg_model.name}')

            _sub_sg_analysis = SGAnalysis(
                name=_set_name,
                analysis=self._analysis,
                output_quantity=self._step_output,
                work_dir=self._dir,
                prepros=self._prepros,
                postpros=self._postpros,
                setting=self.setting,
                global_parameters=self._global_parameters
            )

            _sg_model.updateParameters(self._global_parameters)

            _sub_sg_analysis.run(
                sg_model=_sg_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                db_sg_model_sets=db_sg_model_sets,
                **kwargs
            )

            logger.debug(f'_sg_model: {_sg_model}')

            # Update sg model in db_sg_model
            if _set_name == _sg_model.name.split('_')[-1]:
                _sg_model.name = '_'.join(_sg_model.name.split('_')[:-1])
            for _i in range(len(db_sg_model.data)):
                if db_sg_model.data[_i].name == _sg_model.name:
                    db_sg_model.data[_i] = _sg_model

        logger.debug(f'db_sg_model: {db_sg_model}')
        logger.debug(f'db_sg_design: {db_sg_design}')
        logger.debug(f'db_sg_model_sets: {db_sg_model_sets}')

        # Build
        if sg_model.fn_model == '':
            self.build(
                sg_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design
                )


        # Temporary solution to the issue:
        # SwiftComp/VABS can run successfully
        # but output file is not complete
        _try = 0

        while _try < self._run_try_max:

            if _try > 0:
                logger.info(f'[{GLOBAL.SG_KEY}: {sg_model.name}] trying again...')

            try:

                # Run
                logger.debug(f'sg_model.solver = {sg_model.solver}')
                sgio.run(
                    solver=sg_model.solver,
                    input_name=sg_model.fn_model,
                    analysis=self._analysis,
                    smdim=sg_model.smdim,
                    timeout=self.setting.get('timeout', GLOBAL.DEFAULT_SG_ANALYSIS_TIMEOUT)
                    )

                # Get output
                if sg_model.solver.lower().startswith('v'):
                    fn_out = f'{sg_model.fn_model}.K'
                elif sg_model.solver.lower().startswith('s'):
                    fn_out = f'{sg_model.fn_model}.k'

                logger.debug(f'sg_model.data_format = {sg_model.data_format}')
                _model_func = sgio.readOutputModel(
                    filename=fn_out,
                    file_format=sg_model.data_format,
                    # analysis=self._analysis,
                    model_type=sg_model.model_type,
                    sg=sg_model.sg_data
                    )

                logger.debug(f'_model_func = {_model_func}')

            except sgio._global.OutputFileError as e:
                logger.error(f'[{GLOBAL.SG_KEY}: {sg_model.name}] {e}')
                _try += 1
                continue

            else:
                break


        sg_model.constitutive = _model_func
        logger.debug(f'sg_model: {sg_model}')

        return output




    # ----------------------------------------------------------------

    def runDF(
        self, sg_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        macro_state_locases:list[StateLocCase]=[],
        **kwargs):
        """
        """

        # logger.debug(f'[{GLOBAL.SG_KEY}: {sg_model.name}] running {GLOBAL.SG_KEY} analysis...')

        output = None

        # ffmt = '.9e'
        # delm = '  '

        if sg_model.solver.lower().startswith('v'):
            _exts = [
                'ech', 'K', 'opt', 'glb',
                'v0', 'v1S', 'v22'
            ]
        elif sg_model.solver.lower().startswith('s'):
            _exts = [
                'ech', 'k', 'opt', 'glb',
            ]


        nlocs = len(macro_state_locases)
        ncases = len(macro_state_locases[0].state_cases)

        output = [[None for _ in range(ncases)] for _ in range(nlocs)]

        # Iterate over the macro state cases
        # ----------------------------------
        for _i, _state_locase in enumerate(macro_state_locases):
            # print('\n')
            logger.debug('-'*40)

            # loc_name = f'loc{_i+1}'
            logger.debug(f'loc: {_state_locase.locs}')

            for _j, _state_case in enumerate(_state_locase.state_cases):
                logger.debug('.'*20)

                case_name = f'case{_j+1}'
                logger.debug(f'case_name = {case_name}')

                logger.debug(f'_state_case = {_state_case}')

                self.writeMacroStateToFile(
                    sg_model=sg_model, state_case=_state_case,
                    db_sg_model=db_sg_model, db_sg_design=db_sg_design
                )


                # Create a sub-directory for the case
                _dst = _state_locase.getDirList()
                _dst.extend([sg_model.name, case_name])
                _dst = os.path.join(self._dir, 'local', *_dst)

                logger.debug(f'copying sg files to {_dst}...')

                if not os.path.exists(_dst):
                    os.makedirs(_dst)

                # Copy sg files
                shutil.copy(f'{sg_model.fn_model}', _dst)
                for _ext in _exts:
                    _src = f'{sg_model.fn_model}.{_ext}'
                    # _dst = os.path.join(_dst, f'{loc_name}_{case_name}.{_ext}')
                    shutil.copy(_src, _dst)
                    # os.remove(_src)


                # Run
                # fn_sg = os.path.join(_dst, f'{sg_model.fn_model}')
                fn_sg = os.path.join(_dst, f'{sg_model.name}.sg')
                sgio.run(
                    solver=sg_model.solver,
                    input_name=fn_sg,
                    analysis=self.analysis,
                    smdim=sg_model.smdim
                    )

                # Get output
                if self.analysis == 'fi':
                    _output = sgio.readOutput(
                        fn=fn_sg,
                        file_format=sg_model.data_format,
                        analysis=self.analysis,
                        sg=sg_model.sg_data,
                        tool_ver=sg_model.solver_version,
                        # smdim=sg_model.smdim
                    )

                    # logger.debug(f'_output = {_output}')

                    # try:
                    # _eid_sr_min = _output['elems_sr_min'][0]
                    # _sr_min = _output['strength_ratio'][_eid_sr_min]
                    _eid_sr_min = _output[2][0]
                    _sr_min = _output[1][_eid_sr_min]
                    # except IndexError:
                    #     _sr_min = min(_output['strength_ratio'])
                    #     _eid_sr_min = _output['strength_ratio'].index(_sr_min)

                    # breakpoint()
                    output[_i][_j] = (_eid_sr_min, _sr_min)

                    # Create a new state storing strength ratio
                    _state_sr_min = sgio.State(
                        name='strength_ratio_min',
                        data=[_sr_min,]
                    )
                    _state_case.addState(
                        name='strength_ratio_min',
                        state=_state_sr_min
                    )

        return output




    # ----------------------------------------------------------------

    def run(
        self, sg_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        db_sg_model_sets:DataBase=None,
        macro_state_locases:list[StateLocCase]=[],
        **kwargs):
        """
        """

        logger.debug(f'[{GLOBAL.SG_KEY}: {sg_model.name}] running {GLOBAL.SG_KEY} analysis...')


        for _prepro in self._prepros:
            _params = _prepro(sg_model.design.parameters)
            sg_model.design.parameters.update(_params)

        # print(f'sg_model.design.parameters = {sg_model.design.parameters}')

        if self._analysis == 'h':

            output = self.runH(
                sg_model=sg_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                **kwargs
            )


        elif self._analysis in ['d', 'f', 'fi']:

            output = self.runDF(
                sg_model=sg_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                macro_state_locases=macro_state_locases,
                **kwargs
            )

            # logger.debug(f'output = {output}')


        for _postpro in self._postpros:
            _output = _postpro(output)
            output.update(_output)


        return output




    def writeMacroStateToFile(
        self, sg_model, state_case, load_type=0,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        ffmt='18.9e', delm='  '
        ):
        """
        """

        try:
            _disp = state_case.displacement.data
        except AttributeError:
            _disp = [0, 0, 0]
        try:
            _rotn = state_case.rotation.data
        except AttributeError:
            _rotn = [1, 0, 0, 0, 1, 0, 0, 0, 1]

        _load = state_case.load.data

        if sg_model.design.builder.lower().startswith('prevabs'):

            _fn_xml = os.path.join(self._dir, f'{sg_model.name}.xml')
            # _fn_xml = sg_model.fn_model
            # print(f'_fn_xml = {_fn_xml}')

            _cs_xml_tree, _cs_xml_root = mutils.io.parseXML(_fn_xml)
            # _cs_xml_tree = et.parse(_fn_xml)
            # _cs_xml_root = _cs_xml_tree.getroot()
            _cs_xml_global = _cs_xml_root.find('global')
            if not _cs_xml_global:
                _cs_xml_global = et.SubElement(_cs_xml_root, 'global')
            # if _resp.load_type == 0:
            _cs_xml_global.set('measure', 'stress')
            # elif _resp.load_type == 1:
            #     _cs_xml_global.set('measure', 'strain')
            # _str_load = delm.join([f'{_l:{ffmt}}' for _l in _load])
            _str_disp = mutils.listToString(_disp, delimiter=delm, fmt=ffmt)
            _str_rotn = mutils.listToString(_rotn, delimiter=delm, fmt=ffmt)
            _str_load = mutils.listToString(_load, delimiter=delm, fmt=ffmt)
            logger.debug(f'_str_load = {_str_load}')
            mutils.updateXMLElement(_cs_xml_global, 'displacements', _str_disp)
            mutils.updateXMLElement(_cs_xml_global, 'rotations', _str_rotn)
            mutils.updateXMLElement(_cs_xml_global, 'loads', _str_load)

            # _xe_global = _cs_xml_root.findall('.//global/*')
            # for _xe in _xe_global:
            #     print(f'{_xe.tag}: {_xe.text}')
            # _fn_xml_new = f'{sg_model.name}_new.xml'

            _cs_xml_tree.write(_fn_xml)

            # Build
            self.build(
                sg_model,
                db_sg_model=db_sg_model,
                db_sg_design=db_sg_design,
                substitute=False
                )

        else:
            # default
            _fn = os.path.join(self._dir, f'{sg_model.name}.sg.glb')
            sgio.write(
                sg=sg_model.sg_data,
                fn=_fn,
                file_format=sg_model.solver,
                format_version=sg_model.solver_version,
                analysis=self._analysis,
                macro_responses=[state_case,],
                model_type=sg_model.model_type,
                load_type=load_type,
                sff=ffmt
            )

        return










def readSectionResponse(raw_input:dict, structure:StructureModel):
    """Read and store section response.
    """

    logger.info(f'reading sectional responses...')

    _data_form = raw_input.get('data_form', 'list')

    _state_locase = StateLocCase(locs=[])

    if _data_form == 'list':
        _cases = raw_input.get('cases', [])

        for _i, _case in enumerate(_cases):

            # substitute parameters
            mutils.substituteParams(
                _case, structure.design.parameters
            )

            _spec = _case.get('case', {})
            _loc = _case.get('loc', 1)
            _loc_type = _case.get('loc_type', 'element')
            # _states = {}

            _disp = _case.get('displacement', [])
            if _disp:
                _state_locase.addStateToCase(
                    case=_spec,
                    state_name='displacement',
                    state_data=_disp,
                    state_loc=_loc,
                    loc_type=_loc_type
                )

            _rot = _case.get('rotation', [])
            if _rot:
                _state_locase.addStateToCase(
                    case=_spec,
                    state_name='rotation',
                    state_data=_rot,
                    state_loc=_loc,
                    loc_type=_loc_type
                )

            _load = _case.get('load', [])
            if _load:
                _state_locase.addStateToCase(
                    case=_spec,
                    state_name='load',
                    state_data=_load,
                    state_loc=_loc,
                    loc_type=_loc_type
                )


    elif _data_form == 'file':
        _file_name = raw_input.get('file_name', '')
        _file_format = raw_input.get('file_format', 'csv')

        if _file_format == 'csv':
            _delimiter = raw_input.get('delimiter', ',')
            _state_locase = readMacroStateFileCsv(
                fn=_file_name,
                delimiter=_delimiter
            )

        elif _file_format == 'rcas':
            _fn_force = _file_name['force']
            _fn_moment = _file_name['moment']

            _delimiter = raw_input.get('delimiter', ',')

            _state_locase = mext.readRcasLoad(
                fn_force=_fn_force,
                fn_moment=_fn_moment,
                delimiter=_delimiter
            )

    logger.debug(f'\n_state_locase = {_state_locase}')
    structure.addLocalStateLocCase(_state_locase)

    return









def readMacroStateFileCsv(
    fn:str, load_type=0,
    coord_tags=['x1', 'x2', 'x3'],
    disp_tags=['u1', 'u2', 'u3'],
    rot_tags=['r1', 'r2', 'r3'],
    dir_cos_tags = ['c11', 'c12', 'c13', 'c21', 'c22', 'c23', 'c31', 'c32', 'c33'],
    cond_tags=[], cond_vtypes=[],
    delimiter=',', nhead=1, encoding='utf-8-sig',
    **kwargs
    ) -> StateLocCase:
    """
    """
    # print(locals())

    logger.info('reading structural response file {}...'.format(fn))

    state_locase = StateLocCase(locs=[], state_cases=[])

    load_tags = [
        's11', 's22', 's33', 's23', 's13', 's12',
        'n11', 'n22', 'n12', 'm11', 'm22', 'm12', 'n13', 'n23',
        'f1', 'f2', 'f3', 'm1', 'm2', 'm3'
    ]

    # if model.lower() == 'sd1':
    #     load_tags = ['s11', 's22', 's33', 's23', 's13', 's12']
    # elif model.lower() == 'pl1':
    #     load_tags = ['n11', 'n22', 'n12', 'm11', 'm22', 'm12']
    # elif model.lower() == 'pl2':
    #     load_tags = ['n11', 'n22', 'n12', 'm11', 'm22', 'm12', 'n13', 'n23']
    # elif model.lower() == 'bm1':
    #     load_tags = ['f1', 'm1', 'm2', 'm3']
    # elif model.lower() == 'bm2':
    #     load_tags = ['f1', 'f2', 'f3', 'm1', 'm2', 'm3']

    # if isinstance(loc_tags, str):
    #     loc_tags = [loc_tags,]
    if isinstance(cond_tags, str):
        cond_tags = [cond_tags,]

    # if isinstance(loc_vtypes, str):
    #     loc_vtypes = [loc_vtypes,]
    if isinstance(cond_vtypes, str):
        cond_vtypes = [cond_vtypes,]

    # if len(loc_vtypes) < len(loc_tags):
    #     if len(loc_vtypes) == 0:
    #         loc_vtypes = ['int',] * len(loc_tags)
    #     elif len(loc_vtypes) == 1:
    #         loc_vtypes = loc_vtypes * len(loc_tags)
    if len(cond_vtypes) < len(cond_tags):
        if len(cond_vtypes) == 0:
            cond_vtypes = ['int',] * len(cond_tags)
        elif len(cond_vtypes) == 1:
            cond_vtypes = cond_vtypes * len(cond_tags)

    # struct_resp_cases = sgmodel.StructureResponseCases()
    # struct_resp_cases.loc_tags = loc_tags
    # struct_resp_cases.cond_tags = cond_tags

    # load = {}

    # _raw_data = []
    with open(fn, 'r', encoding=encoding) as file:
        cr = csv.reader(file, delimiter=delimiter)

        _col_names = []
        for i, row in enumerate(cr):
            row = [s.strip() for s in row]
            if row[0] == '':
                continue

            if i < nhead:
                _col_names = copy.deepcopy(row)

            else:
                # _entry = {}
                # print(f'row = {row}')
                _loc_type = ''
                _loc_id = None
                _case = {}
                _state_data = {}
                for _name, _val in zip(_col_names, row):
                    # print(f'_name = {_name}, _val = {_val}')
                    # _entry[_name] = _val
                    if _name in ['node', 'element']:
                        _loc_type = _name
                        _loc_id = int(_val)

                    elif _name in coord_tags:
                        pass

                    elif _name in disp_tags:
                        if not 'displacement' in _state_data.keys():
                            _state_data['displacement'] = []
                        _state_data['displacement'].append(float(_val))

                    elif _name in rot_tags:
                        pass

                    elif _name in dir_cos_tags:
                        if not 'rotation' in _state_data.keys():
                            _state_data['rotation'] = []
                        _state_data['rotation'].append(float(_val))

                    elif _name in load_tags:
                        if not 'load' in _state_data.keys():
                            _state_data['load'] = []
                        _state_data['load'].append(float(_val))

                    else:
                        _case[_name] = _val

                # print(f'_loc_type = {_loc_type}, _loc_id = {_loc_id}')
                # print(f'_case = {_case}')
                # print(f'_state_data = {_state_data}')

                # _raw_data.append(_entry)
                for _name, _data in _state_data.items():
                    state_locase.addStateToCase(
                        case=_case,
                        state_name=_name,
                        state_data=_data,
                        state_loc=_loc_id,
                        loc_type=_loc_type
                    )

    # print(f'_raw_data = {_raw_data}')

    return state_locase




