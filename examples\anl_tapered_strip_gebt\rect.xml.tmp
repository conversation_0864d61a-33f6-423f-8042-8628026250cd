<cross_section name="rect" format="1">
  <include>
    <!-- <baseline>baselines</baseline> -->
    <!-- <material>materials</material> -->
    <!-- <layup>layups</layup> -->
  </include>

  <analysis>
    <model>1</model>
  </analysis>

  <general>
    <translate>0.0 0.0</translate>
    <scale>1.0</scale>
    <rotate>0.0</rotate>
    <mesh_size>{gms}</mesh_size>
    <element_type>linear</element_type>
  </general>

  <baselines>
    <point name="pt_right">{w/2}  0</point>
    <point name="pt_left">{-1*w/2}  0</point>
    <line name="ln_mid">
      <points>pt_left,pt_right</points>
    </line>
  </baselines>

  <materials>
    <material name="mat_1" type="orthotropic">
      <density>1.353e-04</density> <!-- lb * sec^2 / inch^4 -->
      <elastic>
        <e1>20.59e+06</e1> <!-- psi -->
        <e2>1.42e+06</e2>
        <e3>1.42e+06</e3>
        <g12>0.87e+06</g12>
        <g13>0.87e+06</g13>
        <g23>0.696e+06</g23>
        <nu12>0.30</nu12>
        <nu13>0.30</nu13>
        <nu23>0.34</nu23>
      </elastic>
    </material>
    <lamina name="la_mat_1">
      <material>mat_1</material>
      <thickness>{t/2}</thickness> <!-- inch -->
    </lamina>
  </materials>

  <layups>
    <layup name="lyp_half">
      <layer lamina="{lam_lyr_1}">{ang_lyr_1=0}:1</layer>
    </layup>
  </layups>

  <component name="top">
    <segment>
      <baseline>ln_mid</baseline>
      <layup>lyp_half</layup>
    </segment>
  </component>
  <component name="bottom">
    <segment>
      <baseline>ln_mid</baseline>
      <layup direction="right">lyp_half</layup>
    </segment>
  </component>
  <!-- <dehomo>
    <displacements>0 0 0</displacements>
    <rotations>1 0 0 0 1 0 0 0 1</rotations>
    <strains>1 1 1 1</strains>
  </dehomo>
  <failure output="stress">
    <axis1>e11</axis1>
    <axis2>k11</axis2>
    <divisions>10</divisions>
    <loads>1 1 1 1</loads>
  </failure> -->
</cross_section>
