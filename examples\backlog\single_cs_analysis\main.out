Writing new restart file 'main.rst'.

>>>>> Executing environment.

>>>>> Running multidim_parameter_study iterator.

Multidimensional parameter study variable partitions of
                                     1
                                     3

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                                     1 mesh_type
                      5.0000000000e-03 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 1:
Active set vector = { 1 1 1 1 }
                      2.3472989825e+09 ea
                      2.1046584961e+07 gj
                      1.8766311879e+07 eiyy
                      3.5363171667e+08 eizz



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                                     2 mesh_type
                      5.0000000000e-03 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 2:
Active set vector = { 1 1 1 1 }
                      2.3467502531e+09 ea
                      2.1017086232e+07 gj
                      1.8760842395e+07 eiyy
                      3.5353796822e+08 eizz



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                                     1 mesh_type
                      1.0000000000e-02 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 3:
Active set vector = { 1 1 1 1 }
                      2.3481435064e+09 ea
                      2.1093726606e+07 gj
                      1.8774399641e+07 eiyy
                      3.5379455811e+08 eizz



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                                     2 mesh_type
                      1.0000000000e-02 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 4:
Active set vector = { 1 1 1 1 }
                      2.3468356944e+09 ea
                      2.1026637151e+07 gj
                      1.8761448606e+07 eiyy
                      3.5355993609e+08 eizz



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                                     1 mesh_type
                      2.0000000000e-02 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 5:
Active set vector = { 1 1 1 1 }
                      2.3493597668e+09 ea
                      2.1167348865e+07 gj
                      1.8786993577e+07 eiyy
                      3.5404210223e+08 eizz



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                                     2 mesh_type
                      2.0000000000e-02 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 6:
Active set vector = { 1 1 1 1 }
                      2.3470029589e+09 ea
                      2.1042881602e+07 gj
                      1.8762536642e+07 eiyy
                      3.5360452229e+08 eizz



---------------------
Begin Evaluation    7
---------------------
Parameters for evaluation 7:
                                     1 mesh_type
                      4.0000000000e-02 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 7:
Active set vector = { 1 1 1 1 }
                      2.3506557891e+09 ea
                      2.1281476588e+07 gj
                      1.8804376718e+07 eiyy
                      3.5434574563e+08 eizz



---------------------
Begin Evaluation    8
---------------------
Parameters for evaluation 8:
                                     2 mesh_type
                      4.0000000000e-02 mesh_size

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 8:
Active set vector = { 1 1 1 1 }
                      2.3472230152e+09 ea
                      2.1071923296e+07 gj
                      1.8764383031e+07 eiyy
                      3.5367885384e+08 eizz


<<<<< Function evaluation summary: 8 total (8 new, 0 duplicate)

Simple Correlation Matrix among all inputs and outputs:
                mesh_type    mesh_size           ea           gj         eiyy         eizz 
   mesh_type  1.00000e+00 
   mesh_size  0.00000e+00  1.00000e+00 
          ea -7.26056e-01  5.39315e-01  1.00000e+00 
          gj -6.41472e-01  6.49437e-01  9.88063e-01  1.00000e+00 
        eiyy -7.12488e-01  5.36203e-01  9.97779e-01  9.90022e-01  1.00000e+00 
        eizz -6.77713e-01  6.04995e-01  9.96400e-01  9.97344e-01  9.95749e-01  1.00000e+00 

Partial Correlation Matrix between input and output:
                       ea           gj         eiyy         eizz 
   mesh_type -8.62193e-01 -8.43582e-01 -8.44091e-01 -8.51153e-01 
   mesh_size  7.84304e-01  8.46563e-01  7.64166e-01  8.22757e-01 

Simple Rank Correlation Matrix among all inputs and outputs:
                mesh_type    mesh_size           ea           gj         eiyy         eizz 
   mesh_type  1.00000e+00 
   mesh_size  0.00000e+00  1.00000e+00 
          ea -8.72872e-01  4.87950e-01  1.00000e+00 
          gj -7.63763e-01  6.34335e-01  9.76190e-01  1.00000e+00 
        eiyy -8.72872e-01  4.87950e-01  1.00000e+00  9.76190e-01  1.00000e+00 
        eizz -7.63763e-01  6.34335e-01  9.76190e-01  1.00000e+00  9.76190e-01  1.00000e+00 

Partial Rank Correlation Matrix between input and output:
                       ea           gj         eiyy         eizz 
   mesh_type -1.00000e+00 -9.87976e-01 -1.00000e+00 -9.87976e-01 
   mesh_size  1.00000e+00  9.82708e-01  1.00000e+00  9.82708e-01 


<<<<< Iterator multidim_parameter_study completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =     44.349 [parent =     44.349, child = -7.10543e-15]
  Total wall clock =     44.349
