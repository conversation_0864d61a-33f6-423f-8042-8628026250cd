DEBUG    [2023-07-12 11:08:47] msgd.main :: {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} 
CRITICAL [2023-07-12 11:08:47] io.readMSGDInput :: reading main input main.yml... 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: local variables:
{'fn': 'main.yml',
 'fn_run': 'run.py',
 'mode': '1',
 'msgd': <msgd.core._msgd.MSGD object at 0x000001FF6E1F0970>,
 'py_cmd': 'python',
 'variant': 'ivabs'} 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: currect working directory:  
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: input file name: main 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: input file extension: .yml 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: output file name: main.out 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: variant: ivabs 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: version: 0.9 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {},
 'design': {'cs_assignment': [{'cs': 'cs1', 'model': 'b2', 'region': 'all'}],
            'dim': 1,
            'section_locations': [0.2, 0.9]},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'ytype': 'float'}],
 'name': 'blade1'} 
DEBUG    [2023-07-12 11:08:47] io.readMSGDInput :: msgd.analysis =
{'steps': [{'analysis': 'h',
            'output': [{'value': ['gj', 'eiyy', 'eizz']}],
            'setting': {'timeout': 60},
            'step': 'cs analysis',
            'type': 'cs'},
           {'analysis': 'd,fi',
            'input': {'load_case': {'condition_tags': 'cond1',
                                    'condition_value_types': 'float',
                                    'data_form': 'file',
                                    'file_name': 'struct_resp.csv',
                                    'location_tags': 'coord',
                                    'location_value_types': 'float'}},
            'step': 'recovery',
            'type': 'cs'}]} 
INFO     [2023-07-12 11:08:47] _msgd.readMDAOEvalIn :: reading mdao input... 
DEBUG    [2023-07-12 11:08:47] _msgd.readMDAOEvalIn :: mdao_tool: dakota 
DEBUG    [2023-07-12 11:08:47] _msgd.readMDAOEvalIn :: fn_dakota_params:  
DEBUG    [2023-07-12 11:08:47] _msgd.readMDAOEvalIn :: fn_dakota_results:  
INFO     [2023-07-12 11:08:47] _msgd.updateData :: updating current design... 
DEBUG    [2023-07-12 11:08:47] _msgd.updateData :: cs1 
DEBUG    [2023-07-12 11:08:47] _msgd.updateData :: step 1 
DEBUG    [2023-07-12 11:08:47] _msgd.updateData :: step 2 
DEBUG    [2023-07-12 11:08:47] _msgd.writeInput :: writing input file: curr_design.yml... 
CRITICAL [2023-07-12 11:08:47] analysis.analyze :: [eval 0] analysis start 
INFO     [2023-07-12 11:08:47] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:08:47] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                         '0.8, airfoil_solid.xml.tmp\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'topology',
                 'ytype': 'str'},
 'dname': 'topology',
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: reading distribution of parameter ['topology']... 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.0, airfoil_gbox_uni.xml.tmp\n0.8, airfoil_solid.xml.tmp\n',
 'interp_kind': 'previous',
 'name': None,
 'other_input': {'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                         '0.8, airfoil_solid.xml.tmp\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'topology',
                 'ytype': 'str'},
 'xndim': 1,
 'xscale': 1.0,
 'yname': ['topology'],
 'yndim': 1,
 'ytype': ['str']} 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: interp_data =
0.0, airfoil_gbox_uni.xml.tmp
0.8, airfoil_solid.xml.tmp
 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: topology: msgd.utils.function, xdim=1, ydim=24, ytype=str, kind=previous, fill=extrapolate 
INFO     [2023-07-12 11:08:47] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:08:47] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'airfoil',
                 'ytype': 'str'},
 'dname': 'airfoil',
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: reading distribution of parameter ['airfoil']... 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
 'interp_kind': 'previous',
 'name': None,
 'other_input': {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'airfoil',
                 'ytype': 'str'},
 'xndim': 1,
 'xscale': 1.0,
 'yname': ['airfoil'],
 'yndim': 1,
 'ytype': ['str']} 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: interp_data =
0.0, sc1095.txt
0.5, sc1094r8.txt
 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: airfoil: msgd.utils.function, xdim=1, ydim=10, ytype=str, kind=previous, fill=extrapolate 
INFO     [2023-07-12 11:08:47] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:08:47] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.1, 46, -45\n'
                         '0.3, -3, 0\n'
                         '0.7, -44, 90\n'
                         '1.0, 47, 45\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'linear',
                 'name': ['ang_spar_1', 'ang_spar_2'],
                 'ytype': 'float'},
 'dname': ['ang_spar_1', 'ang_spar_2'],
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: reading distribution of parameter ['ang_spar_1', 'ang_spar_2']... 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate', 'extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.1, 46, -45\n0.3, -3, 0\n0.7, -44, 90\n1.0, 47, 45\n',
 'interp_kind': 'linear',
 'name': None,
 'other_input': {'data': '0.1, 46, -45\n'
                         '0.3, -3, 0\n'
                         '0.7, -44, 90\n'
                         '1.0, 47, 45\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'linear',
                 'name': ['ang_spar_1', 'ang_spar_2'],
                 'ytype': 'float'},
 'xndim': 1,
 'xscale': 1.0,
 'yname': ['ang_spar_1', 'ang_spar_2'],
 'yndim': 2,
 'ytype': ['float', 'float']} 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: interp_data =
0.1, 46, -45
0.3, -3, 0
0.7, -44, 90
1.0, 47, 45
 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: ang_spar_1: msgd.utils.function, xdim=1, ydim=1, ytype=float, kind=linear, fill=extrapolate 
DEBUG    [2023-07-12 11:08:47] distribution.createInterpolationDistribution :: ang_spar_2: msgd.utils.function, xdim=1, ydim=1, ytype=float, kind=linear, fill=extrapolate 
INFO     [2023-07-12 11:08:47] _msgd.discretize :: discretizing the design... 
DEBUG    [2023-07-12 11:08:47] _msgd.discretize :: structure model:
None 
DEBUG    [2023-07-12 11:08:47] _msgd.discretize :: parameter distributions:
{'airfoil': <msgd.utils.function.InterpolationFunction object at 0x000001FF6A8C37C0>,
 'ang_spar_1': <msgd.utils.function.InterpolationFunction object at 0x000001FF6A8C3670>,
 'ang_spar_2': <msgd.utils.function.InterpolationFunction object at 0x000001FF67AC7D90>,
 'topology': <msgd.utils.function.InterpolationFunction object at 0x000001FF67794250>} 
INFO     [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: local variables:
{'abs_tol': 1e-12,
 'distributions': {'airfoil': <msgd.utils.function.InterpolationFunction object at 0x000001FF6A8C37C0>,
                   'ang_spar_1': <msgd.utils.function.InterpolationFunction object at 0x000001FF6A8C3670>,
                   'ang_spar_2': <msgd.utils.function.InterpolationFunction object at 0x000001FF67AC7D90>,
                   'topology': <msgd.utils.function.InterpolationFunction object at 0x000001FF67794250>},
 'locations': [0.2, 0.9],
 'model_elems': None,
 'model_nodes': None,
 'rel_tol': 1e-09,
 'sg_assignment': [{'cs': 'cs1', 'model': 'b2', 'region': 'all'}],
 'sg_key': 'cs',
 'trans_func': None} 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction topology (str) at 0.2... 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = airfoil_gbox_uni.xml.tmp (str) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction airfoil (str) at 0.2... 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = sc1095.txt (str) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_1 (float) at 0.2... 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: 0.2 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = 21.499999999999996 (float) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_2 (float) at 0.2... 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: 0.2 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = -22.499999999999996 (float) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction topology (str) at 0.9... 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = airfoil_solid.xml.tmp (str) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction airfoil (str) at 0.9... 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = sc1094r8.txt (str) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_1 (float) at 0.9... 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: 0.9 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = 16.666666666666686 (float) 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_2 (float) at 0.9... 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:47] function.__call__ :: 0.9 
DEBUG    [2023-07-12 11:08:47] distribution.calcParamsFromDistr :: result = 60.0 (float) 
INFO     [2023-07-12 11:08:47] analysis.analyze :: going through steps... 
CRITICAL [2023-07-12 11:08:47] analysis.analyze :: ==================== 
CRITICAL [2023-07-12 11:08:47] analysis.analyze :: [eval 0] running cs step:  
DEBUG    [2023-07-12 11:08:47] analysis.analyze :: step config:
{'analysis': 'h',
 'output': [{'value': ['gj', 'eiyy', 'eizz']}],
 'setting': {'timeout': 60},
 'step': 'cs analysis',
 'type': 'cs'} 
DEBUG    [2023-07-12 11:08:47] analysis.analyze :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {},
 'design': {'cs_assignment': [{'cs': 'cs1', 'model': 'b2', 'region': 'all'}],
            'dim': 1,
            'section_locations': [0.2, 0.9]},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'ytype': 'float'}],
 'name': 'blade1'} 
INFO     [2023-07-12 11:08:47] core.runSGenomeDesignAnalysis :: running cs design h analysis... 
DEBUG    [2023-07-12 11:08:47] core.runSGenomeDesignAnalysis :: cs: cs1, model: md1 
INFO     [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: ---------------- 
INFO     [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: running cs design analysis: cs1_set1... 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: local variables:
{'analysis': {'analysis': 'h',
              'output': [{'value': ['gj', 'eiyy', 'eizz']}],
              'setting': {'timeout': 60},
              'step': 'cs analysis',
              'type': 'cs'},
 'calculate_strength': False,
 'design_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'design_name': 'cs1',
 'dir_list': ['.'],
 'mdao_data': {},
 'model_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set1',
 'params': {'a2p1': 0.8,
            'a2p3': 0.6,
            'airfoil': 'sc1095.txt',
            'airfoil_file_head': 1,
            'airfoil_point_order': 1,
            'airfoil_point_reverse': 1,
            'ang_spar_1': 21.499999999999996,
            'ang_spar_2': -22.499999999999996,
            'chord': 1.73,
            'gms': 0.004,
            'lam_back': 'T300 15k/976_0.0053',
            'lam_cap': 'Aluminum 8009_0.01',
            'lam_front': 'T300 15k/976_0.0053',
            'lam_skin': 'T300 15k/976_0.0053',
            'lam_spar_1': 'T300 15k/976_0.0053',
            'location': 0.2,
            'mat_fill_back': 'Plascore PN2-3/16OX3.0',
            'mat_fill_front': 'Rohacell 70',
            'mat_fill_te': 'Plascore PN2-3/16OX3.0',
            'mat_nsm': 'lead',
            'mdb_name': 'material_database_us_ft',
            'ply_spar_1': 10,
            'rnsm': 0.001,
            'topology': 'airfoil_gbox_uni.xml.tmp'},
 'physics': 'elastic',
 'returns': None,
 'sg_id': 'set1',
 'sg_key': 'cs',
 'sgdb': {},
 'sgdb_map': {},
 'sglib': {'cs1': {'design': {'base_file': 'topology',
                              'dim': 2,
                              'tool': 'prevabs'},
                   'model': {'md1': {'tool': 'vabs'}},
                   'name': 'cs1',
                   'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 21.499999999999996,
                                 'ang_spar_2': -22.499999999999996,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.2,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'}}},
 'sgs_data': {},
 'sgs_temp': {}} 
INFO     [2023-07-12 11:08:47] core.findSGPropByParam :: - finding cs cs1 properties by parameters... 
INFO     [2023-07-12 11:08:47] core.findSGPropByParam :: - not found 
INFO     [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: [cs: cs1_set1] updating current design inputs... 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: design before substitution:
{'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: model before substitution:
{'tool': 'vabs'} 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: design after substitution:
{'base_file': 'airfoil_gbox_uni.xml.tmp', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: model after substitution:
{'tool': 'vabs'} 
INFO     [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: [cs: cs1_set1] checking if lower level cs properties exist... 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: parameters before preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1095.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 21.499999999999996,
 'ang_spar_2': -22.499999999999996,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.2,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_gbox_uni.xml.tmp'} 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: prepros =
[] 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: _fn_base = airfoil_gbox_uni.xml.tmp 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: _fn_spec = cs1_set1.xml 
DEBUG    [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: parameters after preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1095.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 21.499999999999996,
 'ang_spar_2': -22.499999999999996,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.2,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_gbox_uni.xml.tmp'} 
INFO     [2023-07-12 11:08:47] core.runSGDesignAnalysisH :: [cs: cs1_set1] creating cs... 
INFO     [2023-07-12 11:08:47] main.buildSG :: building 2D SG: cs1_set1... 
CRITICAL [2023-07-12 11:08:47] execu.run :: prevabs -i cs1_set1.xml -vabs -ver 4.0 -h 
INFO     [2023-07-12 11:08:48] core.runSGDesignAnalysisH :: [cs: cs1_set1] writing cs input file... 
INFO     [2023-07-12 11:08:48] core.runSGDesignAnalysisH :: [cs: cs1_set1] running cs analysis... 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: [cs: cs1_set1] reading cs analysis output file... 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: [cs: cs1_set1] adding cs outputs to the database... 
INFO     [2023-07-12 11:08:49] core.runSGenomeDesignAnalysis :: running cs design h analysis... 
DEBUG    [2023-07-12 11:08:49] core.runSGenomeDesignAnalysis :: cs: cs1, model: md1 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: ---------------- 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: running cs design analysis: cs1_set2... 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: local variables:
{'analysis': {'analysis': 'h',
              'output': [{'value': ['gj', 'eiyy', 'eizz']}],
              'setting': {'timeout': 60},
              'step': 'cs analysis',
              'type': 'cs'},
 'calculate_strength': False,
 'design_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'design_name': 'cs1',
 'dir_list': ['.'],
 'mdao_data': {'cs1_set1': {'eiyy': 182531.84636,
                            'eizz': 2808826.6659,
                            'gj': 56848.707959},
               'eiyy': 182531.84636,
               'eizz': 2808826.6659,
               'gj': 56848.707959},
 'model_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set2',
 'params': {'a2p1': 0.8,
            'a2p3': 0.6,
            'airfoil': 'sc1094r8.txt',
            'airfoil_file_head': 1,
            'airfoil_point_order': 1,
            'airfoil_point_reverse': 1,
            'ang_spar_1': 16.666666666666686,
            'ang_spar_2': 60.0,
            'chord': 1.73,
            'cs1_set1': {'eiyy': 182531.84636,
                         'eizz': 2808826.6659,
                         'gj': 56848.707959},
            'eiyy': 182531.84636,
            'eizz': 2808826.6659,
            'gj': 56848.707959,
            'gms': 0.004,
            'lam_back': 'T300 15k/976_0.0053',
            'lam_cap': 'Aluminum 8009_0.01',
            'lam_front': 'T300 15k/976_0.0053',
            'lam_skin': 'T300 15k/976_0.0053',
            'lam_spar_1': 'T300 15k/976_0.0053',
            'location': 0.9,
            'mat_fill_back': 'Plascore PN2-3/16OX3.0',
            'mat_fill_front': 'Rohacell 70',
            'mat_fill_te': 'Plascore PN2-3/16OX3.0',
            'mat_nsm': 'lead',
            'mdb_name': 'material_database_us_ft',
            'ply_spar_1': 10,
            'rnsm': 0.001,
            'topology': 'airfoil_solid.xml.tmp'},
 'physics': 'elastic',
 'returns': None,
 'sg_id': 'set2',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 21.499999999999996,
                                 'ang_spar_2': -22.499999999999996,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.2,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                        'cmp11r': 5.0878057051e-07,
                                        'cmp12c': -1.5401286717e-09,
                                        'cmp12r': -7.530958881e-11,
                                        'cmp13c': -4.3595365276e-08,
                                        'cmp13r': 2.8556613473e-09,
                                        'cmp14c': 4.1606368247e-07,
                                        'cmp14r': -1.5401286717e-09,
                                        'cmp15r': -4.3595365276e-08,
                                        'cmp16r': 4.1606368247e-07,
                                        'cmp21c': -1.5401286717e-09,
                                        'cmp21r': -7.530958881e-11,
                                        'cmp22c': 1.75905493e-05,
                                        'cmp22r': 1.5539551764e-06,
                                        'cmp23c': -1.0894250155e-09,
                                        'cmp23r': -2.5828925164e-07,
                                        'cmp24c': 5.9281787424e-09,
                                        'cmp24r': 2.7187260737e-07,
                                        'cmp25r': 5.9677090988e-09,
                                        'cmp26r': 1.1403413773e-10,
                                        'cmp31c': -4.3595365276e-08,
                                        'cmp31r': 2.8556613473e-09,
                                        'cmp32c': -1.0894250155e-09,
                                        'cmp32r': -2.5828925164e-07,
                                        'cmp33c': 5.47838635e-06,
                                        'cmp33r': 2.6105890461e-05,
                                        'cmp34c': 2.3705357301e-08,
                                        'cmp34r': -2.1004911352e-05,
                                        'cmp35r': 1.1050021026e-09,
                                        'cmp36r': -6.2115945295e-09,
                                        'cmp41c': 4.1606368247e-07,
                                        'cmp41r': -1.5401286717e-09,
                                        'cmp42c': 5.9281787424e-09,
                                        'cmp42r': 2.7187260737e-07,
                                        'cmp43c': 2.3705357301e-08,
                                        'cmp43r': -2.1004911352e-05,
                                        'cmp44c': 3.5613024885e-07,
                                        'cmp44r': 1.75905493e-05,
                                        'cmp45r': -1.0894250155e-09,
                                        'cmp46r': 5.9281787424e-09,
                                        'cmp51r': -4.3595365276e-08,
                                        'cmp52r': 5.9677090988e-09,
                                        'cmp53r': 1.1050021026e-09,
                                        'cmp54r': -1.0894250155e-09,
                                        'cmp55r': 5.47838635e-06,
                                        'cmp56r': 2.3705357301e-08,
                                        'cmp61r': 4.1606368247e-07,
                                        'cmp62r': 1.1403413773e-10,
                                        'cmp63r': -6.2115945295e-09,
                                        'cmp64r': 5.9281787424e-09,
                                        'cmp65r': 2.3705357301e-08,
                                        'cmp66r': 3.5613024885e-07,
                                        'ea': 45936361.817,
                                        'ei22': 182531.84636,
                                        'ei33': 2808826.6659,
                                        'ga22': 641849.92559,
                                        'ga33': 984601.87622,
                                        'gj': 56848.707959,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1941020712,
                                        'sc3': 0.015455606458,
                                        'stf11c': 45945000.559,
                                        'stf11r': 45945000.559,
                                        'stf12c': 22162.951903,
                                        'stf12r': -5.5750261835,
                                        'stf13c': 598059.49438,
                                        'stf13r': -3.0120584186,
                                        'stf14c': -53717298.054,
                                        'stf14r': 22159.441361,
                                        'stf15r': 598059.50033,
                                        'stf16r': -53717298.047,
                                        'stf21c': 22162.951903,
                                        'stf21r': -5.5750261835,
                                        'stf22c': 56859.719142,
                                        'stf22r': 647062.21773,
                                        'stf23c': 303.89610305,
                                        'stf23r': -41934.168158,
                                        'stf24c': -26859.493003,
                                        'stf24r': -60074.499223,
                                        'stf25r': -708.88756699,
                                        'stf26r': 115.10153036,
                                        'stf31c': 598059.49438,
                                        'stf31r': -3.0120584186,
                                        'stf32c': 303.89610305,
                                        'stf32r': -41934.168158,
                                        'stf33c': 190372.95062,
                                        'stf33r': 979394.40393,
                                        'stf34c': -711384.37902,
                                        'stf34r': 1170145.8133,
                                        'stf35r': 91.123412413,
                                        'stf36r': -2384.9633539,
                                        'stf41c': -53717298.054,
                                        'stf41r': 22159.441361,
                                        'stf42c': -26859.493003,
                                        'stf42r': -60074.499223,
                                        'stf43c': -711384.37902,
                                        'stf43r': 1170145.8133,
                                        'stf44c': 65613184.403,
                                        'stf44r': 1455062.7137,
                                        'stf45r': 423.66317411,
                                        'stf46r': -29709.163618,
                                        'stf51r': 598059.50033,
                                        'stf52r': -708.88756699,
                                        'stf53r': 91.123412413,
                                        'stf54r': 423.66317411,
                                        'stf55r': 190373.72933,
                                        'stf56r': -711384.61511,
                                        'stf61r': -53717298.047,
                                        'stf62r': 115.10153036,
                                        'stf63r': -2384.9633539,
                                        'stf64r': -29709.163618,
                                        'stf65r': -711384.61511,
                                        'stf66r': 65613190.211,
                                        'tc2': 1.1691572113,
                                        'tc3': 0.013016726123}}}]},
 'sgdb_map': {},
 'sglib': {'cs1': {'design': {'base_file': 'topology',
                              'dim': 2,
                              'tool': 'prevabs'},
                   'model': {'md1': {'tool': 'vabs'}},
                   'name': 'cs1',
                   'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 16.666666666666686,
                                 'ang_spar_2': 60.0,
                                 'chord': 1.73,
                                 'cs1_set1': {'eiyy': 182531.84636,
                                              'eizz': 2808826.6659,
                                              'gj': 56848.707959},
                                 'eiyy': 182531.84636,
                                 'eizz': 2808826.6659,
                                 'gj': 56848.707959,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.9,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'}}},
 'sgs_data': {},
 'sgs_temp': {}} 
INFO     [2023-07-12 11:08:49] core.findSGPropByParam :: - finding cs cs1 properties by parameters... 
INFO     [2023-07-12 11:08:49] core.findSGPropByParam :: - not found 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: [cs: cs1_set2] updating current design inputs... 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: design before substitution:
{'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: model before substitution:
{'tool': 'vabs'} 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: design after substitution:
{'base_file': 'airfoil_solid.xml.tmp', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: model after substitution:
{'tool': 'vabs'} 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: [cs: cs1_set2] checking if lower level cs properties exist... 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: parameters before preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1094r8.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 16.666666666666686,
 'ang_spar_2': 60.0,
 'chord': 1.73,
 'cs1_set1': {'eiyy': 182531.84636, 'eizz': 2808826.6659, 'gj': 56848.707959},
 'eiyy': 182531.84636,
 'eizz': 2808826.6659,
 'gj': 56848.707959,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.9,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_solid.xml.tmp'} 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: prepros =
[] 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: _fn_base = airfoil_solid.xml.tmp 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: _fn_spec = cs1_set2.xml 
DEBUG    [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: parameters after preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1094r8.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 16.666666666666686,
 'ang_spar_2': 60.0,
 'chord': 1.73,
 'cs1_set1': {'eiyy': 182531.84636, 'eizz': 2808826.6659, 'gj': 56848.707959},
 'eiyy': 182531.84636,
 'eizz': 2808826.6659,
 'gj': 56848.707959,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.9,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_solid.xml.tmp'} 
INFO     [2023-07-12 11:08:49] core.runSGDesignAnalysisH :: [cs: cs1_set2] creating cs... 
INFO     [2023-07-12 11:08:49] main.buildSG :: building 2D SG: cs1_set2... 
CRITICAL [2023-07-12 11:08:49] execu.run :: prevabs -i cs1_set2.xml -vabs -ver 4.0 -h 
INFO     [2023-07-12 11:08:50] core.runSGDesignAnalysisH :: [cs: cs1_set2] writing cs input file... 
INFO     [2023-07-12 11:08:50] core.runSGDesignAnalysisH :: [cs: cs1_set2] running cs analysis... 
INFO     [2023-07-12 11:08:51] core.runSGDesignAnalysisH :: [cs: cs1_set2] reading cs analysis output file... 
INFO     [2023-07-12 11:08:51] core.runSGDesignAnalysisH :: [cs: cs1_set2] adding cs outputs to the database... 
CRITICAL [2023-07-12 11:08:51] analysis.analyze :: ==================== 
CRITICAL [2023-07-12 11:08:51] analysis.analyze :: [eval 0] running cs step:  
DEBUG    [2023-07-12 11:08:51] analysis.analyze :: step config:
{'analysis': 'd,fi',
 'input': {'load_case': {'condition_tags': 'cond1',
                         'condition_value_types': 'float',
                         'data_form': 'file',
                         'file_name': 'struct_resp.csv',
                         'location_tags': 'coord',
                         'location_value_types': 'float'}},
 'step': 'recovery',
 'type': 'cs'} 
DEBUG    [2023-07-12 11:08:51] analysis.analyze :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {'cs1_set1': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1095.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 21.499999999999996,
                                         'ang_spar_2': -22.499999999999996,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0.2,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_gbox_uni.xml.tmp'},
                           'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                                'cmp11r': 5.0878057051e-07,
                                                'cmp12c': -1.5401286717e-09,
                                                'cmp12r': -7.530958881e-11,
                                                'cmp13c': -4.3595365276e-08,
                                                'cmp13r': 2.8556613473e-09,
                                                'cmp14c': 4.1606368247e-07,
                                                'cmp14r': -1.5401286717e-09,
                                                'cmp15r': -4.3595365276e-08,
                                                'cmp16r': 4.1606368247e-07,
                                                'cmp21c': -1.5401286717e-09,
                                                'cmp21r': -7.530958881e-11,
                                                'cmp22c': 1.75905493e-05,
                                                'cmp22r': 1.5539551764e-06,
                                                'cmp23c': -1.0894250155e-09,
                                                'cmp23r': -2.5828925164e-07,
                                                'cmp24c': 5.9281787424e-09,
                                                'cmp24r': 2.7187260737e-07,
                                                'cmp25r': 5.9677090988e-09,
                                                'cmp26r': 1.1403413773e-10,
                                                'cmp31c': -4.3595365276e-08,
                                                'cmp31r': 2.8556613473e-09,
                                                'cmp32c': -1.0894250155e-09,
                                                'cmp32r': -2.5828925164e-07,
                                                'cmp33c': 5.47838635e-06,
                                                'cmp33r': 2.6105890461e-05,
                                                'cmp34c': 2.3705357301e-08,
                                                'cmp34r': -2.1004911352e-05,
                                                'cmp35r': 1.1050021026e-09,
                                                'cmp36r': -6.2115945295e-09,
                                                'cmp41c': 4.1606368247e-07,
                                                'cmp41r': -1.5401286717e-09,
                                                'cmp42c': 5.9281787424e-09,
                                                'cmp42r': 2.7187260737e-07,
                                                'cmp43c': 2.3705357301e-08,
                                                'cmp43r': -2.1004911352e-05,
                                                'cmp44c': 3.5613024885e-07,
                                                'cmp44r': 1.75905493e-05,
                                                'cmp45r': -1.0894250155e-09,
                                                'cmp46r': 5.9281787424e-09,
                                                'cmp51r': -4.3595365276e-08,
                                                'cmp52r': 5.9677090988e-09,
                                                'cmp53r': 1.1050021026e-09,
                                                'cmp54r': -1.0894250155e-09,
                                                'cmp55r': 5.47838635e-06,
                                                'cmp56r': 2.3705357301e-08,
                                                'cmp61r': 4.1606368247e-07,
                                                'cmp62r': 1.1403413773e-10,
                                                'cmp63r': -6.2115945295e-09,
                                                'cmp64r': 5.9281787424e-09,
                                                'cmp65r': 2.3705357301e-08,
                                                'cmp66r': 3.5613024885e-07,
                                                'ea': 45936361.817,
                                                'ei22': 182531.84636,
                                                'ei33': 2808826.6659,
                                                'ga22': 641849.92559,
                                                'ga33': 984601.87622,
                                                'gj': 56848.707959,
                                                'mc2': 1.1494335098,
                                                'mc3': 0.012439183019,
                                                'mmoi1': 0.0072632486361,
                                                'mmoi2': 0.00026525481038,
                                                'mmoi3': 0.0069979938258,
                                                'ms11': 0.075463162134,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00093870008494,
                                                'ms16': -0.086739887313,
                                                'ms21': 0.0,
                                                'ms22': 0.075463162134,
                                                'ms23': 0.0,
                                                'ms24': -0.00093870008494,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.075463162134,
                                                'ms34': 0.086739887313,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00093870008494,
                                                'ms43': 0.086739887313,
                                                'ms44': 0.10697665841,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00093870008494,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 0.00027700892854,
                                                'ms56': -0.00110180938,
                                                'ms61': -0.086739887313,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00110180938,
                                                'ms66': 0.10669964949,
                                                'mu': 0.075463162134,
                                                'sc2': 1.1941020712,
                                                'sc3': 0.015455606458,
                                                'stf11c': 45945000.559,
                                                'stf11r': 45945000.559,
                                                'stf12c': 22162.951903,
                                                'stf12r': -5.5750261835,
                                                'stf13c': 598059.49438,
                                                'stf13r': -3.0120584186,
                                                'stf14c': -53717298.054,
                                                'stf14r': 22159.441361,
                                                'stf15r': 598059.50033,
                                                'stf16r': -53717298.047,
                                                'stf21c': 22162.951903,
                                                'stf21r': -5.5750261835,
                                                'stf22c': 56859.719142,
                                                'stf22r': 647062.21773,
                                                'stf23c': 303.89610305,
                                                'stf23r': -41934.168158,
                                                'stf24c': -26859.493003,
                                                'stf24r': -60074.499223,
                                                'stf25r': -708.88756699,
                                                'stf26r': 115.10153036,
                                                'stf31c': 598059.49438,
                                                'stf31r': -3.0120584186,
                                                'stf32c': 303.89610305,
                                                'stf32r': -41934.168158,
                                                'stf33c': 190372.95062,
                                                'stf33r': 979394.40393,
                                                'stf34c': -711384.37902,
                                                'stf34r': 1170145.8133,
                                                'stf35r': 91.123412413,
                                                'stf36r': -2384.9633539,
                                                'stf41c': -53717298.054,
                                                'stf41r': 22159.441361,
                                                'stf42c': -26859.493003,
                                                'stf42r': -60074.499223,
                                                'stf43c': -711384.37902,
                                                'stf43r': 1170145.8133,
                                                'stf44c': 65613184.403,
                                                'stf44r': 1455062.7137,
                                                'stf45r': 423.66317411,
                                                'stf46r': -29709.163618,
                                                'stf51r': 598059.50033,
                                                'stf52r': -708.88756699,
                                                'stf53r': 91.123412413,
                                                'stf54r': 423.66317411,
                                                'stf55r': 190373.72933,
                                                'stf56r': -711384.61511,
                                                'stf61r': -53717298.047,
                                                'stf62r': 115.10153036,
                                                'stf63r': -2384.9633539,
                                                'stf64r': -29709.163618,
                                                'stf65r': -711384.61511,
                                                'stf66r': 65613190.211,
                                                'tc2': 1.1691572113,
                                                'tc3': 0.013016726123}}},
              'cs1_set2': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1094r8.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 16.666666666666686,
                                         'ang_spar_2': 60.0,
                                         'chord': 1.73,
                                         'cs1_set1': {'eiyy': 182531.84636,
                                                      'eizz': 2808826.6659,
                                                      'gj': 56848.707959},
                                         'eiyy': 182531.84636,
                                         'eizz': 2808826.6659,
                                         'gj': 56848.707959,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0.9,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_solid.xml.tmp'},
                           'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                                'cmp11r': 8.6878954609e-07,
                                                'cmp12c': 0.0,
                                                'cmp12r': 0.0,
                                                'cmp13c': -4.9180730583e-07,
                                                'cmp13r': 0.0,
                                                'cmp14c': 7.2917599121e-07,
                                                'cmp14r': 0.0,
                                                'cmp15r': -4.9180730583e-07,
                                                'cmp16r': 7.2917599121e-07,
                                                'cmp21c': 0.0,
                                                'cmp21r': 0.0,
                                                'cmp22c': 0.00027162192945,
                                                'cmp22r': 3.9379621974e-06,
                                                'cmp23c': 0.0,
                                                'cmp23r': -1.0747935643e-05,
                                                'cmp24c': 0.0,
                                                'cmp24r': 9.9217524276e-06,
                                                'cmp25r': 0.0,
                                                'cmp26r': 0.0,
                                                'cmp31c': -4.9180730583e-07,
                                                'cmp31r': 0.0,
                                                'cmp32c': 0.0,
                                                'cmp32r': -1.0747935643e-05,
                                                'cmp33c': 5.7758156681e-05,
                                                'cmp33r': 0.0003372421267,
                                                'cmp34c': 9.5286858883e-07,
                                                'cmp34r': -0.00029847292879,
                                                'cmp35r': 0.0,
                                                'cmp36r': 0.0,
                                                'cmp41c': 7.2917599121e-07,
                                                'cmp41r': 0.0,
                                                'cmp42c': 0.0,
                                                'cmp42r': 9.9217524276e-06,
                                                'cmp43c': 9.5286858883e-07,
                                                'cmp43r': -0.00029847292879,
                                                'cmp44c': 8.4546245969e-07,
                                                'cmp44r': 0.00027162192945,
                                                'cmp45r': 0.0,
                                                'cmp46r': 0.0,
                                                'cmp51r': -4.9180730583e-07,
                                                'cmp52r': 0.0,
                                                'cmp53r': 0.0,
                                                'cmp54r': 0.0,
                                                'cmp55r': 5.7758156681e-05,
                                                'cmp56r': 9.5286858883e-07,
                                                'cmp61r': 7.2917599121e-07,
                                                'cmp62r': 0.0,
                                                'cmp63r': 0.0,
                                                'cmp64r': 0.0,
                                                'cmp65r': 9.5286858883e-07,
                                                'cmp66r': 8.4546245969e-07,
                                                'ea': 4774098.1646,
                                                'ei22': 17308.792358,
                                                'ei33': 1205526.0405,
                                                'ga22': 107897.42799,
                                                'ga33': 280006.76136,
                                                'gj': 3681.5878674,
                                                'mc2': 1.0027721065,
                                                'mc3': 0.026707103412,
                                                'mmoi1': 0.006197578454,
                                                'mmoi2': 6.4318592849e-05,
                                                'mmoi3': 0.0061332598612,
                                                'ms11': 0.033471831002,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00089393565197,
                                                'ms16': -0.033564618483,
                                                'ms21': 0.0,
                                                'ms22': 0.033471831002,
                                                'ms23': 0.0,
                                                'ms24': -0.00089393565197,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.033471831002,
                                                'ms34': 0.033564618483,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00089393565197,
                                                'ms43': 0.033564618483,
                                                'ms44': 0.039879116067,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00089393565197,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 8.9734363542e-05,
                                                'ms56': -0.00099311905661,
                                                'ms61': -0.033564618483,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00099311905661,
                                                'ms66': 0.039789381703,
                                                'mu': 0.033471831002,
                                                'sc2': 1.0988543134,
                                                'sc3': 0.036527803361,
                                                'stf11c': 4774098.1646,
                                                'stf11r': 4774098.1646,
                                                'stf12c': 0.0,
                                                'stf12r': 0.0,
                                                'stf13c': 110636.30469,
                                                'stf13r': 0.0,
                                                'stf14c': -4242151.2389,
                                                'stf14r': 0.0,
                                                'stf15r': 110636.30469,
                                                'stf16r': -4242151.2389,
                                                'stf21c': 0.0,
                                                'stf21r': 0.0,
                                                'stf22c': 3681.5878674,
                                                'stf22r': 279879.86959,
                                                'stf23c': 0.0,
                                                'stf23r': -4671.5261463,
                                                'stf24c': 0.0,
                                                'stf24r': -15356.723497,
                                                'stf25r': 0.0,
                                                'stf26r': 0.0,
                                                'stf31c': 110636.30469,
                                                'stf31r': 0.0,
                                                'stf32c': 0.0,
                                                'stf32r': -4671.5261463,
                                                'stf33c': 20205.505805,
                                                'stf33r': 108024.31976,
                                                'stf34c': -118191.56225,
                                                'stf34r': 118873.63031,
                                                'stf35r': 0.0,
                                                'stf36r': 0.0,
                                                'stf41c': -4242151.2389,
                                                'stf41r': 0.0,
                                                'stf42c': 0.0,
                                                'stf42r': -15356.723497,
                                                'stf43c': -118191.56225,
                                                'stf43r': 118873.63031,
                                                'stf44c': 4974668.9678,
                                                'stf44r': 134867.33666,
                                                'stf45r': 0.0,
                                                'stf46r': 0.0,
                                                'stf51r': 110636.30469,
                                                'stf52r': 0.0,
                                                'stf53r': 0.0,
                                                'stf54r': 0.0,
                                                'stf55r': 20205.505805,
                                                'stf56r': -118191.56225,
                                                'stf61r': -4242151.2389,
                                                'stf62r': 0.0,
                                                'stf63r': 0.0,
                                                'stf64r': 0.0,
                                                'stf65r': -118191.56225,
                                                'stf66r': 4974668.9678,
                                                'tc2': 0.88857645835,
                                                'tc3': 0.023174283576}}}},
 'design': {'cs_assignment': [{'cs': 'cs1', 'model': 'b2', 'region': 'all'}],
            'dim': 1,
            'section_locations': [0.2, 0.9]},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'ytype': 'float'}],
 'name': 'blade1'} 
INFO     [2023-07-12 11:08:51] io.readStructuralGlobalResponses :: reading global structural responses... 
INFO     [2023-07-12 11:08:51] core.runSGenomeDesignAnalysis :: running cs design d,fi analysis... 
DEBUG    [2023-07-12 11:08:51] core.runSGenomeDesignAnalysis :: set_name = set1 
INFO     [2023-07-12 11:08:51] core.runSGDesignAnalysisDF :: running cs dehomogenization/failure analysis: cs1_set1... 
DEBUG    [2023-07-12 11:08:51] core.runSGDesignAnalysisDF :: local variables:
{'analysis': {'analysis': 'd,fi',
              'input': {'load_case': {'condition_tags': 'cond1',
                                      'condition_value_types': 'float',
                                      'data_form': 'file',
                                      'file_name': 'struct_resp.csv',
                                      'location_tags': 'coord',
                                      'location_value_types': 'float'}},
              'step': 'recovery',
              'type': 'cs'},
 'case_name_config_index': {},
 'case_select': [],
 'design_config_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'glb_resp_cases': --------------------
Location:
  coord = 0.2
Condition:
  cond1 = 2.0
Displacement
  u1 =     1.234684e-07
  u2 =     4.674299e-08
  u3 =     5.279585e-05
Rotation (directional cosine)
  c11 =     9.999973e-01, c12 =     1.191997e-06, c13 =     2.338594e-03
  c21 =    -1.403700e-06, c22 =     1.000000e+00, c23 =     9.051800e-05
  c31 =    -2.338594e-03, c32 =    -9.052075e-05, c33 =     9.999973e-01
Load
  f1 =    -5.731561e+00
  f2 =     2.184032e+01
  f3 =     9.755541e+02
  m1 =     1.706671e+01
  m2 =     5.973511e-01
  m3 =    -1.422416e-01
--------------------
Location:
  coord = 0.2
Condition:
  cond1 = 5.0
Displacement
  u1 =     7.901295e-07
  u2 =     2.966777e-07
  u3 =     1.335076e-04
Rotation (directional cosine)
  c11 =     9.999825e-01, c12 =     7.664019e-06, c13 =     5.918115e-03
  c21 =    -8.948300e-06, c22 =     1.000000e+00, c23 =     2.169700e-04
  c31 =    -5.918115e-03, c32 =    -2.170205e-04, c33 =     9.999825e-01
Load
  f1 =    -1.624783e+01
  f2 =     1.324988e+02
  f3 =     2.436210e+03
  m1 =     4.220395e+01
  m2 =     3.700510e+00
  m3 =    -8.149391e-01
--------------------
Location:
  coord = 0.2
Condition:
  cond1 = 8.0
Displacement
  u1 =     1.048698e-06
  u2 =     7.651169e-07
  u3 =     2.149095e-04
Rotation (directional cosine)
  c11 =     9.999546e-01, c12 =     2.004809e-05, c13 =     9.532544e-03
  c21 =    -2.312800e-05, c22 =     9.999999e-01, c23 =     3.229900e-04
  c31 =    -9.532543e-03, c32 =    -3.232015e-04, c33 =     9.999545e-01
Load
  f1 =    -3.329002e+01
  f2 =     3.269003e+02
  f3 =     3.866488e+03
  m1 =     6.618203e+01
  m2 =     9.320603e+00
  m3 =    -2.432339e+00
--------------------,
 'mdao_data': {'cs1_set1': {'eiyy': 182531.84636,
                            'eizz': 2808826.6659,
                            'gj': 56848.707959},
               'cs1_set2': {'eiyy': 17308.792358,
                            'eizz': 1205526.0405,
                            'gj': 3681.5878674},
               'eiyy': 17308.792358,
               'eizz': 1205526.0405,
               'gj': 3681.5878674},
 'model_config_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set1',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 21.499999999999996,
                                 'ang_spar_2': -22.499999999999996,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.2,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                        'cmp11r': 5.0878057051e-07,
                                        'cmp12c': -1.5401286717e-09,
                                        'cmp12r': -7.530958881e-11,
                                        'cmp13c': -4.3595365276e-08,
                                        'cmp13r': 2.8556613473e-09,
                                        'cmp14c': 4.1606368247e-07,
                                        'cmp14r': -1.5401286717e-09,
                                        'cmp15r': -4.3595365276e-08,
                                        'cmp16r': 4.1606368247e-07,
                                        'cmp21c': -1.5401286717e-09,
                                        'cmp21r': -7.530958881e-11,
                                        'cmp22c': 1.75905493e-05,
                                        'cmp22r': 1.5539551764e-06,
                                        'cmp23c': -1.0894250155e-09,
                                        'cmp23r': -2.5828925164e-07,
                                        'cmp24c': 5.9281787424e-09,
                                        'cmp24r': 2.7187260737e-07,
                                        'cmp25r': 5.9677090988e-09,
                                        'cmp26r': 1.1403413773e-10,
                                        'cmp31c': -4.3595365276e-08,
                                        'cmp31r': 2.8556613473e-09,
                                        'cmp32c': -1.0894250155e-09,
                                        'cmp32r': -2.5828925164e-07,
                                        'cmp33c': 5.47838635e-06,
                                        'cmp33r': 2.6105890461e-05,
                                        'cmp34c': 2.3705357301e-08,
                                        'cmp34r': -2.1004911352e-05,
                                        'cmp35r': 1.1050021026e-09,
                                        'cmp36r': -6.2115945295e-09,
                                        'cmp41c': 4.1606368247e-07,
                                        'cmp41r': -1.5401286717e-09,
                                        'cmp42c': 5.9281787424e-09,
                                        'cmp42r': 2.7187260737e-07,
                                        'cmp43c': 2.3705357301e-08,
                                        'cmp43r': -2.1004911352e-05,
                                        'cmp44c': 3.5613024885e-07,
                                        'cmp44r': 1.75905493e-05,
                                        'cmp45r': -1.0894250155e-09,
                                        'cmp46r': 5.9281787424e-09,
                                        'cmp51r': -4.3595365276e-08,
                                        'cmp52r': 5.9677090988e-09,
                                        'cmp53r': 1.1050021026e-09,
                                        'cmp54r': -1.0894250155e-09,
                                        'cmp55r': 5.47838635e-06,
                                        'cmp56r': 2.3705357301e-08,
                                        'cmp61r': 4.1606368247e-07,
                                        'cmp62r': 1.1403413773e-10,
                                        'cmp63r': -6.2115945295e-09,
                                        'cmp64r': 5.9281787424e-09,
                                        'cmp65r': 2.3705357301e-08,
                                        'cmp66r': 3.5613024885e-07,
                                        'ea': 45936361.817,
                                        'ei22': 182531.84636,
                                        'ei33': 2808826.6659,
                                        'ga22': 641849.92559,
                                        'ga33': 984601.87622,
                                        'gj': 56848.707959,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1941020712,
                                        'sc3': 0.015455606458,
                                        'stf11c': 45945000.559,
                                        'stf11r': 45945000.559,
                                        'stf12c': 22162.951903,
                                        'stf12r': -5.5750261835,
                                        'stf13c': 598059.49438,
                                        'stf13r': -3.0120584186,
                                        'stf14c': -53717298.054,
                                        'stf14r': 22159.441361,
                                        'stf15r': 598059.50033,
                                        'stf16r': -53717298.047,
                                        'stf21c': 22162.951903,
                                        'stf21r': -5.5750261835,
                                        'stf22c': 56859.719142,
                                        'stf22r': 647062.21773,
                                        'stf23c': 303.89610305,
                                        'stf23r': -41934.168158,
                                        'stf24c': -26859.493003,
                                        'stf24r': -60074.499223,
                                        'stf25r': -708.88756699,
                                        'stf26r': 115.10153036,
                                        'stf31c': 598059.49438,
                                        'stf31r': -3.0120584186,
                                        'stf32c': 303.89610305,
                                        'stf32r': -41934.168158,
                                        'stf33c': 190372.95062,
                                        'stf33r': 979394.40393,
                                        'stf34c': -711384.37902,
                                        'stf34r': 1170145.8133,
                                        'stf35r': 91.123412413,
                                        'stf36r': -2384.9633539,
                                        'stf41c': -53717298.054,
                                        'stf41r': 22159.441361,
                                        'stf42c': -26859.493003,
                                        'stf42r': -60074.499223,
                                        'stf43c': -711384.37902,
                                        'stf43r': 1170145.8133,
                                        'stf44c': 65613184.403,
                                        'stf44r': 1455062.7137,
                                        'stf45r': 423.66317411,
                                        'stf46r': -29709.163618,
                                        'stf51r': 598059.50033,
                                        'stf52r': -708.88756699,
                                        'stf53r': 91.123412413,
                                        'stf54r': 423.66317411,
                                        'stf55r': 190373.72933,
                                        'stf56r': -711384.61511,
                                        'stf61r': -53717298.047,
                                        'stf62r': 115.10153036,
                                        'stf63r': -2384.9633539,
                                        'stf64r': -29709.163618,
                                        'stf65r': -711384.61511,
                                        'stf66r': 65613190.211,
                                        'tc2': 1.1691572113,
                                        'tc3': 0.013016726123}}},
                  {'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 16.666666666666686,
                                 'ang_spar_2': 60.0,
                                 'chord': 1.73,
                                 'cs1_set1': {'eiyy': 182531.84636,
                                              'eizz': 2808826.6659,
                                              'gj': 56848.707959},
                                 'eiyy': 182531.84636,
                                 'eizz': 2808826.6659,
                                 'gj': 56848.707959,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.9,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'},
                   'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                        'cmp11r': 8.6878954609e-07,
                                        'cmp12c': 0.0,
                                        'cmp12r': 0.0,
                                        'cmp13c': -4.9180730583e-07,
                                        'cmp13r': 0.0,
                                        'cmp14c': 7.2917599121e-07,
                                        'cmp14r': 0.0,
                                        'cmp15r': -4.9180730583e-07,
                                        'cmp16r': 7.2917599121e-07,
                                        'cmp21c': 0.0,
                                        'cmp21r': 0.0,
                                        'cmp22c': 0.00027162192945,
                                        'cmp22r': 3.9379621974e-06,
                                        'cmp23c': 0.0,
                                        'cmp23r': -1.0747935643e-05,
                                        'cmp24c': 0.0,
                                        'cmp24r': 9.9217524276e-06,
                                        'cmp25r': 0.0,
                                        'cmp26r': 0.0,
                                        'cmp31c': -4.9180730583e-07,
                                        'cmp31r': 0.0,
                                        'cmp32c': 0.0,
                                        'cmp32r': -1.0747935643e-05,
                                        'cmp33c': 5.7758156681e-05,
                                        'cmp33r': 0.0003372421267,
                                        'cmp34c': 9.5286858883e-07,
                                        'cmp34r': -0.00029847292879,
                                        'cmp35r': 0.0,
                                        'cmp36r': 0.0,
                                        'cmp41c': 7.2917599121e-07,
                                        'cmp41r': 0.0,
                                        'cmp42c': 0.0,
                                        'cmp42r': 9.9217524276e-06,
                                        'cmp43c': 9.5286858883e-07,
                                        'cmp43r': -0.00029847292879,
                                        'cmp44c': 8.4546245969e-07,
                                        'cmp44r': 0.00027162192945,
                                        'cmp45r': 0.0,
                                        'cmp46r': 0.0,
                                        'cmp51r': -4.9180730583e-07,
                                        'cmp52r': 0.0,
                                        'cmp53r': 0.0,
                                        'cmp54r': 0.0,
                                        'cmp55r': 5.7758156681e-05,
                                        'cmp56r': 9.5286858883e-07,
                                        'cmp61r': 7.2917599121e-07,
                                        'cmp62r': 0.0,
                                        'cmp63r': 0.0,
                                        'cmp64r': 0.0,
                                        'cmp65r': 9.5286858883e-07,
                                        'cmp66r': 8.4546245969e-07,
                                        'ea': 4774098.1646,
                                        'ei22': 17308.792358,
                                        'ei33': 1205526.0405,
                                        'ga22': 107897.42799,
                                        'ga33': 280006.76136,
                                        'gj': 3681.5878674,
                                        'mc2': 1.0027721065,
                                        'mc3': 0.026707103412,
                                        'mmoi1': 0.006197578454,
                                        'mmoi2': 6.4318592849e-05,
                                        'mmoi3': 0.0061332598612,
                                        'ms11': 0.033471831002,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00089393565197,
                                        'ms16': -0.033564618483,
                                        'ms21': 0.0,
                                        'ms22': 0.033471831002,
                                        'ms23': 0.0,
                                        'ms24': -0.00089393565197,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.033471831002,
                                        'ms34': 0.033564618483,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00089393565197,
                                        'ms43': 0.033564618483,
                                        'ms44': 0.039879116067,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00089393565197,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 8.9734363542e-05,
                                        'ms56': -0.00099311905661,
                                        'ms61': -0.033564618483,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00099311905661,
                                        'ms66': 0.039789381703,
                                        'mu': 0.033471831002,
                                        'sc2': 1.0988543134,
                                        'sc3': 0.036527803361,
                                        'stf11c': 4774098.1646,
                                        'stf11r': 4774098.1646,
                                        'stf12c': 0.0,
                                        'stf12r': 0.0,
                                        'stf13c': 110636.30469,
                                        'stf13r': 0.0,
                                        'stf14c': -4242151.2389,
                                        'stf14r': 0.0,
                                        'stf15r': 110636.30469,
                                        'stf16r': -4242151.2389,
                                        'stf21c': 0.0,
                                        'stf21r': 0.0,
                                        'stf22c': 3681.5878674,
                                        'stf22r': 279879.86959,
                                        'stf23c': 0.0,
                                        'stf23r': -4671.5261463,
                                        'stf24c': 0.0,
                                        'stf24r': -15356.723497,
                                        'stf25r': 0.0,
                                        'stf26r': 0.0,
                                        'stf31c': 110636.30469,
                                        'stf31r': 0.0,
                                        'stf32c': 0.0,
                                        'stf32r': -4671.5261463,
                                        'stf33c': 20205.505805,
                                        'stf33r': 108024.31976,
                                        'stf34c': -118191.56225,
                                        'stf34r': 118873.63031,
                                        'stf35r': 0.0,
                                        'stf36r': 0.0,
                                        'stf41c': -4242151.2389,
                                        'stf41r': 0.0,
                                        'stf42c': 0.0,
                                        'stf42r': -15356.723497,
                                        'stf43c': -118191.56225,
                                        'stf43r': 118873.63031,
                                        'stf44c': 4974668.9678,
                                        'stf44r': 134867.33666,
                                        'stf45r': 0.0,
                                        'stf46r': 0.0,
                                        'stf51r': 110636.30469,
                                        'stf52r': 0.0,
                                        'stf53r': 0.0,
                                        'stf54r': 0.0,
                                        'stf55r': 20205.505805,
                                        'stf56r': -118191.56225,
                                        'stf61r': -4242151.2389,
                                        'stf62r': 0.0,
                                        'stf63r': 0.0,
                                        'stf64r': 0.0,
                                        'stf65r': -118191.56225,
                                        'stf66r': 4974668.9678,
                                        'tc2': 0.88857645835,
                                        'tc3': 0.023174283576}}}]},
 'sgs_data': {'cs1_set1': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1095.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 21.499999999999996,
                                         'ang_spar_2': -22.499999999999996,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0.2,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_gbox_uni.xml.tmp'},
                           'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                                'cmp11r': 5.0878057051e-07,
                                                'cmp12c': -1.5401286717e-09,
                                                'cmp12r': -7.530958881e-11,
                                                'cmp13c': -4.3595365276e-08,
                                                'cmp13r': 2.8556613473e-09,
                                                'cmp14c': 4.1606368247e-07,
                                                'cmp14r': -1.5401286717e-09,
                                                'cmp15r': -4.3595365276e-08,
                                                'cmp16r': 4.1606368247e-07,
                                                'cmp21c': -1.5401286717e-09,
                                                'cmp21r': -7.530958881e-11,
                                                'cmp22c': 1.75905493e-05,
                                                'cmp22r': 1.5539551764e-06,
                                                'cmp23c': -1.0894250155e-09,
                                                'cmp23r': -2.5828925164e-07,
                                                'cmp24c': 5.9281787424e-09,
                                                'cmp24r': 2.7187260737e-07,
                                                'cmp25r': 5.9677090988e-09,
                                                'cmp26r': 1.1403413773e-10,
                                                'cmp31c': -4.3595365276e-08,
                                                'cmp31r': 2.8556613473e-09,
                                                'cmp32c': -1.0894250155e-09,
                                                'cmp32r': -2.5828925164e-07,
                                                'cmp33c': 5.47838635e-06,
                                                'cmp33r': 2.6105890461e-05,
                                                'cmp34c': 2.3705357301e-08,
                                                'cmp34r': -2.1004911352e-05,
                                                'cmp35r': 1.1050021026e-09,
                                                'cmp36r': -6.2115945295e-09,
                                                'cmp41c': 4.1606368247e-07,
                                                'cmp41r': -1.5401286717e-09,
                                                'cmp42c': 5.9281787424e-09,
                                                'cmp42r': 2.7187260737e-07,
                                                'cmp43c': 2.3705357301e-08,
                                                'cmp43r': -2.1004911352e-05,
                                                'cmp44c': 3.5613024885e-07,
                                                'cmp44r': 1.75905493e-05,
                                                'cmp45r': -1.0894250155e-09,
                                                'cmp46r': 5.9281787424e-09,
                                                'cmp51r': -4.3595365276e-08,
                                                'cmp52r': 5.9677090988e-09,
                                                'cmp53r': 1.1050021026e-09,
                                                'cmp54r': -1.0894250155e-09,
                                                'cmp55r': 5.47838635e-06,
                                                'cmp56r': 2.3705357301e-08,
                                                'cmp61r': 4.1606368247e-07,
                                                'cmp62r': 1.1403413773e-10,
                                                'cmp63r': -6.2115945295e-09,
                                                'cmp64r': 5.9281787424e-09,
                                                'cmp65r': 2.3705357301e-08,
                                                'cmp66r': 3.5613024885e-07,
                                                'ea': 45936361.817,
                                                'ei22': 182531.84636,
                                                'ei33': 2808826.6659,
                                                'ga22': 641849.92559,
                                                'ga33': 984601.87622,
                                                'gj': 56848.707959,
                                                'mc2': 1.1494335098,
                                                'mc3': 0.012439183019,
                                                'mmoi1': 0.0072632486361,
                                                'mmoi2': 0.00026525481038,
                                                'mmoi3': 0.0069979938258,
                                                'ms11': 0.075463162134,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00093870008494,
                                                'ms16': -0.086739887313,
                                                'ms21': 0.0,
                                                'ms22': 0.075463162134,
                                                'ms23': 0.0,
                                                'ms24': -0.00093870008494,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.075463162134,
                                                'ms34': 0.086739887313,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00093870008494,
                                                'ms43': 0.086739887313,
                                                'ms44': 0.10697665841,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00093870008494,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 0.00027700892854,
                                                'ms56': -0.00110180938,
                                                'ms61': -0.086739887313,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00110180938,
                                                'ms66': 0.10669964949,
                                                'mu': 0.075463162134,
                                                'sc2': 1.1941020712,
                                                'sc3': 0.015455606458,
                                                'stf11c': 45945000.559,
                                                'stf11r': 45945000.559,
                                                'stf12c': 22162.951903,
                                                'stf12r': -5.5750261835,
                                                'stf13c': 598059.49438,
                                                'stf13r': -3.0120584186,
                                                'stf14c': -53717298.054,
                                                'stf14r': 22159.441361,
                                                'stf15r': 598059.50033,
                                                'stf16r': -53717298.047,
                                                'stf21c': 22162.951903,
                                                'stf21r': -5.5750261835,
                                                'stf22c': 56859.719142,
                                                'stf22r': 647062.21773,
                                                'stf23c': 303.89610305,
                                                'stf23r': -41934.168158,
                                                'stf24c': -26859.493003,
                                                'stf24r': -60074.499223,
                                                'stf25r': -708.88756699,
                                                'stf26r': 115.10153036,
                                                'stf31c': 598059.49438,
                                                'stf31r': -3.0120584186,
                                                'stf32c': 303.89610305,
                                                'stf32r': -41934.168158,
                                                'stf33c': 190372.95062,
                                                'stf33r': 979394.40393,
                                                'stf34c': -711384.37902,
                                                'stf34r': 1170145.8133,
                                                'stf35r': 91.123412413,
                                                'stf36r': -2384.9633539,
                                                'stf41c': -53717298.054,
                                                'stf41r': 22159.441361,
                                                'stf42c': -26859.493003,
                                                'stf42r': -60074.499223,
                                                'stf43c': -711384.37902,
                                                'stf43r': 1170145.8133,
                                                'stf44c': 65613184.403,
                                                'stf44r': 1455062.7137,
                                                'stf45r': 423.66317411,
                                                'stf46r': -29709.163618,
                                                'stf51r': 598059.50033,
                                                'stf52r': -708.88756699,
                                                'stf53r': 91.123412413,
                                                'stf54r': 423.66317411,
                                                'stf55r': 190373.72933,
                                                'stf56r': -711384.61511,
                                                'stf61r': -53717298.047,
                                                'stf62r': 115.10153036,
                                                'stf63r': -2384.9633539,
                                                'stf64r': -29709.163618,
                                                'stf65r': -711384.61511,
                                                'stf66r': 65613190.211,
                                                'tc2': 1.1691572113,
                                                'tc3': 0.013016726123}}},
              'cs1_set2': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1094r8.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 16.666666666666686,
                                         'ang_spar_2': 60.0,
                                         'chord': 1.73,
                                         'cs1_set1': {'eiyy': 182531.84636,
                                                      'eizz': 2808826.6659,
                                                      'gj': 56848.707959},
                                         'eiyy': 182531.84636,
                                         'eizz': 2808826.6659,
                                         'gj': 56848.707959,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0.9,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_solid.xml.tmp'},
                           'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                                'cmp11r': 8.6878954609e-07,
                                                'cmp12c': 0.0,
                                                'cmp12r': 0.0,
                                                'cmp13c': -4.9180730583e-07,
                                                'cmp13r': 0.0,
                                                'cmp14c': 7.2917599121e-07,
                                                'cmp14r': 0.0,
                                                'cmp15r': -4.9180730583e-07,
                                                'cmp16r': 7.2917599121e-07,
                                                'cmp21c': 0.0,
                                                'cmp21r': 0.0,
                                                'cmp22c': 0.00027162192945,
                                                'cmp22r': 3.9379621974e-06,
                                                'cmp23c': 0.0,
                                                'cmp23r': -1.0747935643e-05,
                                                'cmp24c': 0.0,
                                                'cmp24r': 9.9217524276e-06,
                                                'cmp25r': 0.0,
                                                'cmp26r': 0.0,
                                                'cmp31c': -4.9180730583e-07,
                                                'cmp31r': 0.0,
                                                'cmp32c': 0.0,
                                                'cmp32r': -1.0747935643e-05,
                                                'cmp33c': 5.7758156681e-05,
                                                'cmp33r': 0.0003372421267,
                                                'cmp34c': 9.5286858883e-07,
                                                'cmp34r': -0.00029847292879,
                                                'cmp35r': 0.0,
                                                'cmp36r': 0.0,
                                                'cmp41c': 7.2917599121e-07,
                                                'cmp41r': 0.0,
                                                'cmp42c': 0.0,
                                                'cmp42r': 9.9217524276e-06,
                                                'cmp43c': 9.5286858883e-07,
                                                'cmp43r': -0.00029847292879,
                                                'cmp44c': 8.4546245969e-07,
                                                'cmp44r': 0.00027162192945,
                                                'cmp45r': 0.0,
                                                'cmp46r': 0.0,
                                                'cmp51r': -4.9180730583e-07,
                                                'cmp52r': 0.0,
                                                'cmp53r': 0.0,
                                                'cmp54r': 0.0,
                                                'cmp55r': 5.7758156681e-05,
                                                'cmp56r': 9.5286858883e-07,
                                                'cmp61r': 7.2917599121e-07,
                                                'cmp62r': 0.0,
                                                'cmp63r': 0.0,
                                                'cmp64r': 0.0,
                                                'cmp65r': 9.5286858883e-07,
                                                'cmp66r': 8.4546245969e-07,
                                                'ea': 4774098.1646,
                                                'ei22': 17308.792358,
                                                'ei33': 1205526.0405,
                                                'ga22': 107897.42799,
                                                'ga33': 280006.76136,
                                                'gj': 3681.5878674,
                                                'mc2': 1.0027721065,
                                                'mc3': 0.026707103412,
                                                'mmoi1': 0.006197578454,
                                                'mmoi2': 6.4318592849e-05,
                                                'mmoi3': 0.0061332598612,
                                                'ms11': 0.033471831002,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00089393565197,
                                                'ms16': -0.033564618483,
                                                'ms21': 0.0,
                                                'ms22': 0.033471831002,
                                                'ms23': 0.0,
                                                'ms24': -0.00089393565197,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.033471831002,
                                                'ms34': 0.033564618483,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00089393565197,
                                                'ms43': 0.033564618483,
                                                'ms44': 0.039879116067,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00089393565197,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 8.9734363542e-05,
                                                'ms56': -0.00099311905661,
                                                'ms61': -0.033564618483,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00099311905661,
                                                'ms66': 0.039789381703,
                                                'mu': 0.033471831002,
                                                'sc2': 1.0988543134,
                                                'sc3': 0.036527803361,
                                                'stf11c': 4774098.1646,
                                                'stf11r': 4774098.1646,
                                                'stf12c': 0.0,
                                                'stf12r': 0.0,
                                                'stf13c': 110636.30469,
                                                'stf13r': 0.0,
                                                'stf14c': -4242151.2389,
                                                'stf14r': 0.0,
                                                'stf15r': 110636.30469,
                                                'stf16r': -4242151.2389,
                                                'stf21c': 0.0,
                                                'stf21r': 0.0,
                                                'stf22c': 3681.5878674,
                                                'stf22r': 279879.86959,
                                                'stf23c': 0.0,
                                                'stf23r': -4671.5261463,
                                                'stf24c': 0.0,
                                                'stf24r': -15356.723497,
                                                'stf25r': 0.0,
                                                'stf26r': 0.0,
                                                'stf31c': 110636.30469,
                                                'stf31r': 0.0,
                                                'stf32c': 0.0,
                                                'stf32r': -4671.5261463,
                                                'stf33c': 20205.505805,
                                                'stf33r': 108024.31976,
                                                'stf34c': -118191.56225,
                                                'stf34r': 118873.63031,
                                                'stf35r': 0.0,
                                                'stf36r': 0.0,
                                                'stf41c': -4242151.2389,
                                                'stf41r': 0.0,
                                                'stf42c': 0.0,
                                                'stf42r': -15356.723497,
                                                'stf43c': -118191.56225,
                                                'stf43r': 118873.63031,
                                                'stf44c': 4974668.9678,
                                                'stf44r': 134867.33666,
                                                'stf45r': 0.0,
                                                'stf46r': 0.0,
                                                'stf51r': 110636.30469,
                                                'stf52r': 0.0,
                                                'stf53r': 0.0,
                                                'stf54r': 0.0,
                                                'stf55r': 20205.505805,
                                                'stf56r': -118191.56225,
                                                'stf61r': -4242151.2389,
                                                'stf62r': 0.0,
                                                'stf63r': 0.0,
                                                'stf64r': 0.0,
                                                'stf65r': -118191.56225,
                                                'stf66r': 4974668.9678,
                                                'tc2': 0.88857645835,
                                                'tc3': 0.023174283576}}}}} 
DEBUG    [2023-07-12 11:08:51] core.runSGDesignAnalysisDF :: ls_resp_cases:
[{'cond1': 2.0,
  'coord': 0.2,
  'response': Displacement
  u1 =     1.234684e-07
  u2 =     4.674299e-08
  u3 =     5.279585e-05
Rotation (directional cosine)
  c11 =     9.999973e-01, c12 =     1.191997e-06, c13 =     2.338594e-03
  c21 =    -1.403700e-06, c22 =     1.000000e+00, c23 =     9.051800e-05
  c31 =    -2.338594e-03, c32 =    -9.052075e-05, c33 =     9.999973e-01
Load
  f1 =    -5.731561e+00
  f2 =     2.184032e+01
  f3 =     9.755541e+02
  m1 =     1.706671e+01
  m2 =     5.973511e-01
  m3 =    -1.422416e-01},
 {'cond1': 5.0,
  'coord': 0.2,
  'response': Displacement
  u1 =     7.901295e-07
  u2 =     2.966777e-07
  u3 =     1.335076e-04
Rotation (directional cosine)
  c11 =     9.999825e-01, c12 =     7.664019e-06, c13 =     5.918115e-03
  c21 =    -8.948300e-06, c22 =     1.000000e+00, c23 =     2.169700e-04
  c31 =    -5.918115e-03, c32 =    -2.170205e-04, c33 =     9.999825e-01
Load
  f1 =    -1.624783e+01
  f2 =     1.324988e+02
  f3 =     2.436210e+03
  m1 =     4.220395e+01
  m2 =     3.700510e+00
  m3 =    -8.149391e-01},
 {'cond1': 8.0,
  'coord': 0.2,
  'response': Displacement
  u1 =     1.048698e-06
  u2 =     7.651169e-07
  u3 =     2.149095e-04
Rotation (directional cosine)
  c11 =     9.999546e-01, c12 =     2.004809e-05, c13 =     9.532544e-03
  c21 =    -2.312800e-05, c22 =     9.999999e-01, c23 =     3.229900e-04
  c31 =    -9.532543e-03, c32 =    -3.232015e-04, c33 =     9.999545e-01
Load
  f1 =    -3.329002e+01
  f2 =     3.269003e+02
  f3 =     3.866488e+03
  m1 =     6.618203e+01
  m2 =     9.320603e+00
  m3 =    -2.432339e+00}] 
DEBUG    [2023-07-12 11:08:51] core.runSGDesignAnalysisDF :: case_name = case1 
INFO     [2023-07-12 11:08:51] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:08:51] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:08:51] execu.run :: prevabs -i cs1_set1.xml -vabs -d 
INFO     [2023-07-12 11:08:51] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:08:51] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:08:58] presg2d.solve :: reading results cs1_set1.sg... 
INFO     [2023-07-12 11:08:58] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:08:58] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:08:58] execu.run :: prevabs -i cs1_set1.xml -vabs -fi 
INFO     [2023-07-12 11:08:58] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:08:58] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:08:59] presg2d.solve :: reading results cs1_set1.sg.fi... 
DEBUG    [2023-07-12 11:08:59] core.runSGDesignAnalysisDF :: case_name = case2 
INFO     [2023-07-12 11:08:59] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:08:59] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:08:59] execu.run :: prevabs -i cs1_set1.xml -vabs -d 
INFO     [2023-07-12 11:08:59] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:08:59] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:06] presg2d.solve :: reading results cs1_set1.sg... 
INFO     [2023-07-12 11:09:06] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:06] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:09:06] execu.run :: prevabs -i cs1_set1.xml -vabs -fi 
INFO     [2023-07-12 11:09:06] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:06] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:07] presg2d.solve :: reading results cs1_set1.sg.fi... 
DEBUG    [2023-07-12 11:09:07] core.runSGDesignAnalysisDF :: case_name = case3 
INFO     [2023-07-12 11:09:07] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:07] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:09:07] execu.run :: prevabs -i cs1_set1.xml -vabs -d 
INFO     [2023-07-12 11:09:07] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:07] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:14] presg2d.solve :: reading results cs1_set1.sg... 
INFO     [2023-07-12 11:09:14] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:14] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:09:14] execu.run :: prevabs -i cs1_set1.xml -vabs -fi 
INFO     [2023-07-12 11:09:14] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:14] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:15] presg2d.solve :: reading results cs1_set1.sg.fi... 
INFO     [2023-07-12 11:09:15] core.runSGenomeDesignAnalysis :: running cs design d,fi analysis... 
DEBUG    [2023-07-12 11:09:15] core.runSGenomeDesignAnalysis :: set_name = set2 
INFO     [2023-07-12 11:09:15] core.runSGDesignAnalysisDF :: running cs dehomogenization/failure analysis: cs1_set2... 
DEBUG    [2023-07-12 11:09:15] core.runSGDesignAnalysisDF :: local variables:
{'analysis': {'analysis': 'd,fi',
              'input': {'load_case': {'condition_tags': 'cond1',
                                      'condition_value_types': 'float',
                                      'data_form': 'file',
                                      'file_name': 'struct_resp.csv',
                                      'location_tags': 'coord',
                                      'location_value_types': 'float'}},
              'step': 'recovery',
              'type': 'cs'},
 'case_name_config_index': {},
 'case_select': [],
 'design_config_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'glb_resp_cases': --------------------
Location:
  coord = 0.9
Condition:
  cond1 = 2.0
Displacement
  u1 =     6.856111e-06
  u2 =     7.328434e-07
  u3 =     8.420519e-04
Rotation (directional cosine)
  c11 =     9.999669e-01, c12 =     3.146690e-06, c13 =     8.141874e-03
  c21 =    -6.104100e-06, c22 =     9.999999e-01, c23 =     3.632000e-04
  c31 =    -8.141874e-03, c32 =    -3.632288e-04, c33 =     9.999668e-01
Load
  f1 =    -7.029041e+00
  f2 =     4.100140e+01
  f3 =     1.504192e+03
  m1 =     2.226463e+01
  m2 =     7.846431e-01
  m3 =    -1.368644e-01
--------------------
Location:
  coord = 0.9
Condition:
  cond1 = 5.0
Displacement
  u1 =     2.402524e-05
  u2 =     4.632019e-06
  u3 =     2.132655e-03
Rotation (directional cosine)
  c11 =     9.997870e-01, c12 =     2.062294e-05, c13 =     2.064094e-02
  c21 =    -3.870999e-05, c22 =     9.999996e-01, c23 =     8.758597e-04
  c31 =    -2.064093e-02, c32 =    -8.764670e-04, c33 =     9.997866e-01
Load
  f1 =    -7.648640e+00
  f2 =     2.556829e+02
  f3 =     3.745333e+03
  m1 =     5.533279e+01
  m2 =     4.884110e+00
  m3 =    -8.328761e-01
--------------------
Location:
  coord = 0.9
Condition:
  cond1 = 8.0
Displacement
  u1 =     7.450767e-05
  u2 =     1.184276e-05
  u3 =     3.437213e-03
Rotation (directional cosine)
  c11 =     9.994453e-01, c12 =     5.520974e-05, c13 =     3.330184e-02
  c21 =    -9.927491e-05, c22 =     9.999991e-01, c23 =     1.321599e-03
  c31 =    -3.330115e-02, c32 =    -1.324106e-03, c33 =     9.994445e-01
Load
  f1 =    -8.276122e+00
  f2 =     6.509478e+02
  f3 =     5.940916e+03
  m1 =     8.720639e+01
  m2 =     1.236017e+01
  m3 =    -3.129104e+00
--------------------,
 'mdao_data': {'cs1_set1': {'eiyy': 182531.84636,
                            'eizz': 2808826.6659,
                            'gj': 56848.707959,
                            'sr_min_case1': 1.2530300450681748,
                            'sr_min_case2': 0.489458655631586,
                            'sr_min_case3': 0.3014199500083792},
               'cs1_set2': {'eiyy': 17308.792358,
                            'eizz': 1205526.0405,
                            'gj': 3681.5878674},
               'eiyy': 17308.792358,
               'eizz': 1205526.0405,
               'gj': 3681.5878674,
               'sr_min_case1': 1.2530300450681748,
               'sr_min_case2': 0.489458655631586,
               'sr_min_case3': 0.3014199500083792},
 'model_config_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set2',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 21.499999999999996,
                                 'ang_spar_2': -22.499999999999996,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.2,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                        'cmp11r': 5.0878057051e-07,
                                        'cmp12c': -1.5401286717e-09,
                                        'cmp12r': -7.530958881e-11,
                                        'cmp13c': -4.3595365276e-08,
                                        'cmp13r': 2.8556613473e-09,
                                        'cmp14c': 4.1606368247e-07,
                                        'cmp14r': -1.5401286717e-09,
                                        'cmp15r': -4.3595365276e-08,
                                        'cmp16r': 4.1606368247e-07,
                                        'cmp21c': -1.5401286717e-09,
                                        'cmp21r': -7.530958881e-11,
                                        'cmp22c': 1.75905493e-05,
                                        'cmp22r': 1.5539551764e-06,
                                        'cmp23c': -1.0894250155e-09,
                                        'cmp23r': -2.5828925164e-07,
                                        'cmp24c': 5.9281787424e-09,
                                        'cmp24r': 2.7187260737e-07,
                                        'cmp25r': 5.9677090988e-09,
                                        'cmp26r': 1.1403413773e-10,
                                        'cmp31c': -4.3595365276e-08,
                                        'cmp31r': 2.8556613473e-09,
                                        'cmp32c': -1.0894250155e-09,
                                        'cmp32r': -2.5828925164e-07,
                                        'cmp33c': 5.47838635e-06,
                                        'cmp33r': 2.6105890461e-05,
                                        'cmp34c': 2.3705357301e-08,
                                        'cmp34r': -2.1004911352e-05,
                                        'cmp35r': 1.1050021026e-09,
                                        'cmp36r': -6.2115945295e-09,
                                        'cmp41c': 4.1606368247e-07,
                                        'cmp41r': -1.5401286717e-09,
                                        'cmp42c': 5.9281787424e-09,
                                        'cmp42r': 2.7187260737e-07,
                                        'cmp43c': 2.3705357301e-08,
                                        'cmp43r': -2.1004911352e-05,
                                        'cmp44c': 3.5613024885e-07,
                                        'cmp44r': 1.75905493e-05,
                                        'cmp45r': -1.0894250155e-09,
                                        'cmp46r': 5.9281787424e-09,
                                        'cmp51r': -4.3595365276e-08,
                                        'cmp52r': 5.9677090988e-09,
                                        'cmp53r': 1.1050021026e-09,
                                        'cmp54r': -1.0894250155e-09,
                                        'cmp55r': 5.47838635e-06,
                                        'cmp56r': 2.3705357301e-08,
                                        'cmp61r': 4.1606368247e-07,
                                        'cmp62r': 1.1403413773e-10,
                                        'cmp63r': -6.2115945295e-09,
                                        'cmp64r': 5.9281787424e-09,
                                        'cmp65r': 2.3705357301e-08,
                                        'cmp66r': 3.5613024885e-07,
                                        'ea': 45936361.817,
                                        'ei22': 182531.84636,
                                        'ei33': 2808826.6659,
                                        'ga22': 641849.92559,
                                        'ga33': 984601.87622,
                                        'gj': 56848.707959,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1941020712,
                                        'sc3': 0.015455606458,
                                        'stf11c': 45945000.559,
                                        'stf11r': 45945000.559,
                                        'stf12c': 22162.951903,
                                        'stf12r': -5.5750261835,
                                        'stf13c': 598059.49438,
                                        'stf13r': -3.0120584186,
                                        'stf14c': -53717298.054,
                                        'stf14r': 22159.441361,
                                        'stf15r': 598059.50033,
                                        'stf16r': -53717298.047,
                                        'stf21c': 22162.951903,
                                        'stf21r': -5.5750261835,
                                        'stf22c': 56859.719142,
                                        'stf22r': 647062.21773,
                                        'stf23c': 303.89610305,
                                        'stf23r': -41934.168158,
                                        'stf24c': -26859.493003,
                                        'stf24r': -60074.499223,
                                        'stf25r': -708.88756699,
                                        'stf26r': 115.10153036,
                                        'stf31c': 598059.49438,
                                        'stf31r': -3.0120584186,
                                        'stf32c': 303.89610305,
                                        'stf32r': -41934.168158,
                                        'stf33c': 190372.95062,
                                        'stf33r': 979394.40393,
                                        'stf34c': -711384.37902,
                                        'stf34r': 1170145.8133,
                                        'stf35r': 91.123412413,
                                        'stf36r': -2384.9633539,
                                        'stf41c': -53717298.054,
                                        'stf41r': 22159.441361,
                                        'stf42c': -26859.493003,
                                        'stf42r': -60074.499223,
                                        'stf43c': -711384.37902,
                                        'stf43r': 1170145.8133,
                                        'stf44c': 65613184.403,
                                        'stf44r': 1455062.7137,
                                        'stf45r': 423.66317411,
                                        'stf46r': -29709.163618,
                                        'stf51r': 598059.50033,
                                        'stf52r': -708.88756699,
                                        'stf53r': 91.123412413,
                                        'stf54r': 423.66317411,
                                        'stf55r': 190373.72933,
                                        'stf56r': -711384.61511,
                                        'stf61r': -53717298.047,
                                        'stf62r': 115.10153036,
                                        'stf63r': -2384.9633539,
                                        'stf64r': -29709.163618,
                                        'stf65r': -711384.61511,
                                        'stf66r': 65613190.211,
                                        'tc2': 1.1691572113,
                                        'tc3': 0.013016726123}}},
                  {'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 16.666666666666686,
                                 'ang_spar_2': 60.0,
                                 'chord': 1.73,
                                 'cs1_set1': {'eiyy': 182531.84636,
                                              'eizz': 2808826.6659,
                                              'gj': 56848.707959},
                                 'eiyy': 182531.84636,
                                 'eizz': 2808826.6659,
                                 'gj': 56848.707959,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.9,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'},
                   'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                        'cmp11r': 8.6878954609e-07,
                                        'cmp12c': 0.0,
                                        'cmp12r': 0.0,
                                        'cmp13c': -4.9180730583e-07,
                                        'cmp13r': 0.0,
                                        'cmp14c': 7.2917599121e-07,
                                        'cmp14r': 0.0,
                                        'cmp15r': -4.9180730583e-07,
                                        'cmp16r': 7.2917599121e-07,
                                        'cmp21c': 0.0,
                                        'cmp21r': 0.0,
                                        'cmp22c': 0.00027162192945,
                                        'cmp22r': 3.9379621974e-06,
                                        'cmp23c': 0.0,
                                        'cmp23r': -1.0747935643e-05,
                                        'cmp24c': 0.0,
                                        'cmp24r': 9.9217524276e-06,
                                        'cmp25r': 0.0,
                                        'cmp26r': 0.0,
                                        'cmp31c': -4.9180730583e-07,
                                        'cmp31r': 0.0,
                                        'cmp32c': 0.0,
                                        'cmp32r': -1.0747935643e-05,
                                        'cmp33c': 5.7758156681e-05,
                                        'cmp33r': 0.0003372421267,
                                        'cmp34c': 9.5286858883e-07,
                                        'cmp34r': -0.00029847292879,
                                        'cmp35r': 0.0,
                                        'cmp36r': 0.0,
                                        'cmp41c': 7.2917599121e-07,
                                        'cmp41r': 0.0,
                                        'cmp42c': 0.0,
                                        'cmp42r': 9.9217524276e-06,
                                        'cmp43c': 9.5286858883e-07,
                                        'cmp43r': -0.00029847292879,
                                        'cmp44c': 8.4546245969e-07,
                                        'cmp44r': 0.00027162192945,
                                        'cmp45r': 0.0,
                                        'cmp46r': 0.0,
                                        'cmp51r': -4.9180730583e-07,
                                        'cmp52r': 0.0,
                                        'cmp53r': 0.0,
                                        'cmp54r': 0.0,
                                        'cmp55r': 5.7758156681e-05,
                                        'cmp56r': 9.5286858883e-07,
                                        'cmp61r': 7.2917599121e-07,
                                        'cmp62r': 0.0,
                                        'cmp63r': 0.0,
                                        'cmp64r': 0.0,
                                        'cmp65r': 9.5286858883e-07,
                                        'cmp66r': 8.4546245969e-07,
                                        'ea': 4774098.1646,
                                        'ei22': 17308.792358,
                                        'ei33': 1205526.0405,
                                        'ga22': 107897.42799,
                                        'ga33': 280006.76136,
                                        'gj': 3681.5878674,
                                        'mc2': 1.0027721065,
                                        'mc3': 0.026707103412,
                                        'mmoi1': 0.006197578454,
                                        'mmoi2': 6.4318592849e-05,
                                        'mmoi3': 0.0061332598612,
                                        'ms11': 0.033471831002,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00089393565197,
                                        'ms16': -0.033564618483,
                                        'ms21': 0.0,
                                        'ms22': 0.033471831002,
                                        'ms23': 0.0,
                                        'ms24': -0.00089393565197,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.033471831002,
                                        'ms34': 0.033564618483,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00089393565197,
                                        'ms43': 0.033564618483,
                                        'ms44': 0.039879116067,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00089393565197,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 8.9734363542e-05,
                                        'ms56': -0.00099311905661,
                                        'ms61': -0.033564618483,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00099311905661,
                                        'ms66': 0.039789381703,
                                        'mu': 0.033471831002,
                                        'sc2': 1.0988543134,
                                        'sc3': 0.036527803361,
                                        'stf11c': 4774098.1646,
                                        'stf11r': 4774098.1646,
                                        'stf12c': 0.0,
                                        'stf12r': 0.0,
                                        'stf13c': 110636.30469,
                                        'stf13r': 0.0,
                                        'stf14c': -4242151.2389,
                                        'stf14r': 0.0,
                                        'stf15r': 110636.30469,
                                        'stf16r': -4242151.2389,
                                        'stf21c': 0.0,
                                        'stf21r': 0.0,
                                        'stf22c': 3681.5878674,
                                        'stf22r': 279879.86959,
                                        'stf23c': 0.0,
                                        'stf23r': -4671.5261463,
                                        'stf24c': 0.0,
                                        'stf24r': -15356.723497,
                                        'stf25r': 0.0,
                                        'stf26r': 0.0,
                                        'stf31c': 110636.30469,
                                        'stf31r': 0.0,
                                        'stf32c': 0.0,
                                        'stf32r': -4671.5261463,
                                        'stf33c': 20205.505805,
                                        'stf33r': 108024.31976,
                                        'stf34c': -118191.56225,
                                        'stf34r': 118873.63031,
                                        'stf35r': 0.0,
                                        'stf36r': 0.0,
                                        'stf41c': -4242151.2389,
                                        'stf41r': 0.0,
                                        'stf42c': 0.0,
                                        'stf42r': -15356.723497,
                                        'stf43c': -118191.56225,
                                        'stf43r': 118873.63031,
                                        'stf44c': 4974668.9678,
                                        'stf44r': 134867.33666,
                                        'stf45r': 0.0,
                                        'stf46r': 0.0,
                                        'stf51r': 110636.30469,
                                        'stf52r': 0.0,
                                        'stf53r': 0.0,
                                        'stf54r': 0.0,
                                        'stf55r': 20205.505805,
                                        'stf56r': -118191.56225,
                                        'stf61r': -4242151.2389,
                                        'stf62r': 0.0,
                                        'stf63r': 0.0,
                                        'stf64r': 0.0,
                                        'stf65r': -118191.56225,
                                        'stf66r': 4974668.9678,
                                        'tc2': 0.88857645835,
                                        'tc3': 0.023174283576}}}]},
 'sgs_data': {'cs1_set1': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1095.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 21.499999999999996,
                                         'ang_spar_2': -22.499999999999996,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0.2,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_gbox_uni.xml.tmp'},
                           'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                                'cmp11r': 5.0878057051e-07,
                                                'cmp12c': -1.5401286717e-09,
                                                'cmp12r': -7.530958881e-11,
                                                'cmp13c': -4.3595365276e-08,
                                                'cmp13r': 2.8556613473e-09,
                                                'cmp14c': 4.1606368247e-07,
                                                'cmp14r': -1.5401286717e-09,
                                                'cmp15r': -4.3595365276e-08,
                                                'cmp16r': 4.1606368247e-07,
                                                'cmp21c': -1.5401286717e-09,
                                                'cmp21r': -7.530958881e-11,
                                                'cmp22c': 1.75905493e-05,
                                                'cmp22r': 1.5539551764e-06,
                                                'cmp23c': -1.0894250155e-09,
                                                'cmp23r': -2.5828925164e-07,
                                                'cmp24c': 5.9281787424e-09,
                                                'cmp24r': 2.7187260737e-07,
                                                'cmp25r': 5.9677090988e-09,
                                                'cmp26r': 1.1403413773e-10,
                                                'cmp31c': -4.3595365276e-08,
                                                'cmp31r': 2.8556613473e-09,
                                                'cmp32c': -1.0894250155e-09,
                                                'cmp32r': -2.5828925164e-07,
                                                'cmp33c': 5.47838635e-06,
                                                'cmp33r': 2.6105890461e-05,
                                                'cmp34c': 2.3705357301e-08,
                                                'cmp34r': -2.1004911352e-05,
                                                'cmp35r': 1.1050021026e-09,
                                                'cmp36r': -6.2115945295e-09,
                                                'cmp41c': 4.1606368247e-07,
                                                'cmp41r': -1.5401286717e-09,
                                                'cmp42c': 5.9281787424e-09,
                                                'cmp42r': 2.7187260737e-07,
                                                'cmp43c': 2.3705357301e-08,
                                                'cmp43r': -2.1004911352e-05,
                                                'cmp44c': 3.5613024885e-07,
                                                'cmp44r': 1.75905493e-05,
                                                'cmp45r': -1.0894250155e-09,
                                                'cmp46r': 5.9281787424e-09,
                                                'cmp51r': -4.3595365276e-08,
                                                'cmp52r': 5.9677090988e-09,
                                                'cmp53r': 1.1050021026e-09,
                                                'cmp54r': -1.0894250155e-09,
                                                'cmp55r': 5.47838635e-06,
                                                'cmp56r': 2.3705357301e-08,
                                                'cmp61r': 4.1606368247e-07,
                                                'cmp62r': 1.1403413773e-10,
                                                'cmp63r': -6.2115945295e-09,
                                                'cmp64r': 5.9281787424e-09,
                                                'cmp65r': 2.3705357301e-08,
                                                'cmp66r': 3.5613024885e-07,
                                                'ea': 45936361.817,
                                                'ei22': 182531.84636,
                                                'ei33': 2808826.6659,
                                                'ga22': 641849.92559,
                                                'ga33': 984601.87622,
                                                'gj': 56848.707959,
                                                'mc2': 1.1494335098,
                                                'mc3': 0.012439183019,
                                                'mmoi1': 0.0072632486361,
                                                'mmoi2': 0.00026525481038,
                                                'mmoi3': 0.0069979938258,
                                                'ms11': 0.075463162134,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00093870008494,
                                                'ms16': -0.086739887313,
                                                'ms21': 0.0,
                                                'ms22': 0.075463162134,
                                                'ms23': 0.0,
                                                'ms24': -0.00093870008494,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.075463162134,
                                                'ms34': 0.086739887313,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00093870008494,
                                                'ms43': 0.086739887313,
                                                'ms44': 0.10697665841,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00093870008494,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 0.00027700892854,
                                                'ms56': -0.00110180938,
                                                'ms61': -0.086739887313,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00110180938,
                                                'ms66': 0.10669964949,
                                                'mu': 0.075463162134,
                                                'sc2': 1.1941020712,
                                                'sc3': 0.015455606458,
                                                'stf11c': 45945000.559,
                                                'stf11r': 45945000.559,
                                                'stf12c': 22162.951903,
                                                'stf12r': -5.5750261835,
                                                'stf13c': 598059.49438,
                                                'stf13r': -3.0120584186,
                                                'stf14c': -53717298.054,
                                                'stf14r': 22159.441361,
                                                'stf15r': 598059.50033,
                                                'stf16r': -53717298.047,
                                                'stf21c': 22162.951903,
                                                'stf21r': -5.5750261835,
                                                'stf22c': 56859.719142,
                                                'stf22r': 647062.21773,
                                                'stf23c': 303.89610305,
                                                'stf23r': -41934.168158,
                                                'stf24c': -26859.493003,
                                                'stf24r': -60074.499223,
                                                'stf25r': -708.88756699,
                                                'stf26r': 115.10153036,
                                                'stf31c': 598059.49438,
                                                'stf31r': -3.0120584186,
                                                'stf32c': 303.89610305,
                                                'stf32r': -41934.168158,
                                                'stf33c': 190372.95062,
                                                'stf33r': 979394.40393,
                                                'stf34c': -711384.37902,
                                                'stf34r': 1170145.8133,
                                                'stf35r': 91.123412413,
                                                'stf36r': -2384.9633539,
                                                'stf41c': -53717298.054,
                                                'stf41r': 22159.441361,
                                                'stf42c': -26859.493003,
                                                'stf42r': -60074.499223,
                                                'stf43c': -711384.37902,
                                                'stf43r': 1170145.8133,
                                                'stf44c': 65613184.403,
                                                'stf44r': 1455062.7137,
                                                'stf45r': 423.66317411,
                                                'stf46r': -29709.163618,
                                                'stf51r': 598059.50033,
                                                'stf52r': -708.88756699,
                                                'stf53r': 91.123412413,
                                                'stf54r': 423.66317411,
                                                'stf55r': 190373.72933,
                                                'stf56r': -711384.61511,
                                                'stf61r': -53717298.047,
                                                'stf62r': 115.10153036,
                                                'stf63r': -2384.9633539,
                                                'stf64r': -29709.163618,
                                                'stf65r': -711384.61511,
                                                'stf66r': 65613190.211,
                                                'tc2': 1.1691572113,
                                                'tc3': 0.013016726123}}},
              'cs1_set2': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1094r8.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 16.666666666666686,
                                         'ang_spar_2': 60.0,
                                         'chord': 1.73,
                                         'cs1_set1': {'eiyy': 182531.84636,
                                                      'eizz': 2808826.6659,
                                                      'gj': 56848.707959},
                                         'eiyy': 182531.84636,
                                         'eizz': 2808826.6659,
                                         'gj': 56848.707959,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0.9,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_solid.xml.tmp'},
                           'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                                'cmp11r': 8.6878954609e-07,
                                                'cmp12c': 0.0,
                                                'cmp12r': 0.0,
                                                'cmp13c': -4.9180730583e-07,
                                                'cmp13r': 0.0,
                                                'cmp14c': 7.2917599121e-07,
                                                'cmp14r': 0.0,
                                                'cmp15r': -4.9180730583e-07,
                                                'cmp16r': 7.2917599121e-07,
                                                'cmp21c': 0.0,
                                                'cmp21r': 0.0,
                                                'cmp22c': 0.00027162192945,
                                                'cmp22r': 3.9379621974e-06,
                                                'cmp23c': 0.0,
                                                'cmp23r': -1.0747935643e-05,
                                                'cmp24c': 0.0,
                                                'cmp24r': 9.9217524276e-06,
                                                'cmp25r': 0.0,
                                                'cmp26r': 0.0,
                                                'cmp31c': -4.9180730583e-07,
                                                'cmp31r': 0.0,
                                                'cmp32c': 0.0,
                                                'cmp32r': -1.0747935643e-05,
                                                'cmp33c': 5.7758156681e-05,
                                                'cmp33r': 0.0003372421267,
                                                'cmp34c': 9.5286858883e-07,
                                                'cmp34r': -0.00029847292879,
                                                'cmp35r': 0.0,
                                                'cmp36r': 0.0,
                                                'cmp41c': 7.2917599121e-07,
                                                'cmp41r': 0.0,
                                                'cmp42c': 0.0,
                                                'cmp42r': 9.9217524276e-06,
                                                'cmp43c': 9.5286858883e-07,
                                                'cmp43r': -0.00029847292879,
                                                'cmp44c': 8.4546245969e-07,
                                                'cmp44r': 0.00027162192945,
                                                'cmp45r': 0.0,
                                                'cmp46r': 0.0,
                                                'cmp51r': -4.9180730583e-07,
                                                'cmp52r': 0.0,
                                                'cmp53r': 0.0,
                                                'cmp54r': 0.0,
                                                'cmp55r': 5.7758156681e-05,
                                                'cmp56r': 9.5286858883e-07,
                                                'cmp61r': 7.2917599121e-07,
                                                'cmp62r': 0.0,
                                                'cmp63r': 0.0,
                                                'cmp64r': 0.0,
                                                'cmp65r': 9.5286858883e-07,
                                                'cmp66r': 8.4546245969e-07,
                                                'ea': 4774098.1646,
                                                'ei22': 17308.792358,
                                                'ei33': 1205526.0405,
                                                'ga22': 107897.42799,
                                                'ga33': 280006.76136,
                                                'gj': 3681.5878674,
                                                'mc2': 1.0027721065,
                                                'mc3': 0.026707103412,
                                                'mmoi1': 0.006197578454,
                                                'mmoi2': 6.4318592849e-05,
                                                'mmoi3': 0.0061332598612,
                                                'ms11': 0.033471831002,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00089393565197,
                                                'ms16': -0.033564618483,
                                                'ms21': 0.0,
                                                'ms22': 0.033471831002,
                                                'ms23': 0.0,
                                                'ms24': -0.00089393565197,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.033471831002,
                                                'ms34': 0.033564618483,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00089393565197,
                                                'ms43': 0.033564618483,
                                                'ms44': 0.039879116067,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00089393565197,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 8.9734363542e-05,
                                                'ms56': -0.00099311905661,
                                                'ms61': -0.033564618483,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00099311905661,
                                                'ms66': 0.039789381703,
                                                'mu': 0.033471831002,
                                                'sc2': 1.0988543134,
                                                'sc3': 0.036527803361,
                                                'stf11c': 4774098.1646,
                                                'stf11r': 4774098.1646,
                                                'stf12c': 0.0,
                                                'stf12r': 0.0,
                                                'stf13c': 110636.30469,
                                                'stf13r': 0.0,
                                                'stf14c': -4242151.2389,
                                                'stf14r': 0.0,
                                                'stf15r': 110636.30469,
                                                'stf16r': -4242151.2389,
                                                'stf21c': 0.0,
                                                'stf21r': 0.0,
                                                'stf22c': 3681.5878674,
                                                'stf22r': 279879.86959,
                                                'stf23c': 0.0,
                                                'stf23r': -4671.5261463,
                                                'stf24c': 0.0,
                                                'stf24r': -15356.723497,
                                                'stf25r': 0.0,
                                                'stf26r': 0.0,
                                                'stf31c': 110636.30469,
                                                'stf31r': 0.0,
                                                'stf32c': 0.0,
                                                'stf32r': -4671.5261463,
                                                'stf33c': 20205.505805,
                                                'stf33r': 108024.31976,
                                                'stf34c': -118191.56225,
                                                'stf34r': 118873.63031,
                                                'stf35r': 0.0,
                                                'stf36r': 0.0,
                                                'stf41c': -4242151.2389,
                                                'stf41r': 0.0,
                                                'stf42c': 0.0,
                                                'stf42r': -15356.723497,
                                                'stf43c': -118191.56225,
                                                'stf43r': 118873.63031,
                                                'stf44c': 4974668.9678,
                                                'stf44r': 134867.33666,
                                                'stf45r': 0.0,
                                                'stf46r': 0.0,
                                                'stf51r': 110636.30469,
                                                'stf52r': 0.0,
                                                'stf53r': 0.0,
                                                'stf54r': 0.0,
                                                'stf55r': 20205.505805,
                                                'stf56r': -118191.56225,
                                                'stf61r': -4242151.2389,
                                                'stf62r': 0.0,
                                                'stf63r': 0.0,
                                                'stf64r': 0.0,
                                                'stf65r': -118191.56225,
                                                'stf66r': 4974668.9678,
                                                'tc2': 0.88857645835,
                                                'tc3': 0.023174283576}}}}} 
DEBUG    [2023-07-12 11:09:15] core.runSGDesignAnalysisDF :: ls_resp_cases:
[{'cond1': 2.0,
  'coord': 0.9,
  'response': Displacement
  u1 =     6.856111e-06
  u2 =     7.328434e-07
  u3 =     8.420519e-04
Rotation (directional cosine)
  c11 =     9.999669e-01, c12 =     3.146690e-06, c13 =     8.141874e-03
  c21 =    -6.104100e-06, c22 =     9.999999e-01, c23 =     3.632000e-04
  c31 =    -8.141874e-03, c32 =    -3.632288e-04, c33 =     9.999668e-01
Load
  f1 =    -7.029041e+00
  f2 =     4.100140e+01
  f3 =     1.504192e+03
  m1 =     2.226463e+01
  m2 =     7.846431e-01
  m3 =    -1.368644e-01},
 {'cond1': 5.0,
  'coord': 0.9,
  'response': Displacement
  u1 =     2.402524e-05
  u2 =     4.632019e-06
  u3 =     2.132655e-03
Rotation (directional cosine)
  c11 =     9.997870e-01, c12 =     2.062294e-05, c13 =     2.064094e-02
  c21 =    -3.870999e-05, c22 =     9.999996e-01, c23 =     8.758597e-04
  c31 =    -2.064093e-02, c32 =    -8.764670e-04, c33 =     9.997866e-01
Load
  f1 =    -7.648640e+00
  f2 =     2.556829e+02
  f3 =     3.745333e+03
  m1 =     5.533279e+01
  m2 =     4.884110e+00
  m3 =    -8.328761e-01},
 {'cond1': 8.0,
  'coord': 0.9,
  'response': Displacement
  u1 =     7.450767e-05
  u2 =     1.184276e-05
  u3 =     3.437213e-03
Rotation (directional cosine)
  c11 =     9.994453e-01, c12 =     5.520974e-05, c13 =     3.330184e-02
  c21 =    -9.927491e-05, c22 =     9.999991e-01, c23 =     1.321599e-03
  c31 =    -3.330115e-02, c32 =    -1.324106e-03, c33 =     9.994445e-01
Load
  f1 =    -8.276122e+00
  f2 =     6.509478e+02
  f3 =     5.940916e+03
  m1 =     8.720639e+01
  m2 =     1.236017e+01
  m3 =    -3.129104e+00}] 
DEBUG    [2023-07-12 11:09:15] core.runSGDesignAnalysisDF :: case_name = case1 
INFO     [2023-07-12 11:09:15] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:15] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:09:15] execu.run :: prevabs -i cs1_set2.xml -vabs -d 
INFO     [2023-07-12 11:09:15] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:15] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:19] presg2d.solve :: reading results cs1_set2.sg... 
INFO     [2023-07-12 11:09:19] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:19] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:09:19] execu.run :: prevabs -i cs1_set2.xml -vabs -fi 
INFO     [2023-07-12 11:09:19] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:19] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:19] presg2d.solve :: reading results cs1_set2.sg.fi... 
DEBUG    [2023-07-12 11:09:19] core.runSGDesignAnalysisDF :: case_name = case2 
INFO     [2023-07-12 11:09:19] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:19] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:09:19] execu.run :: prevabs -i cs1_set2.xml -vabs -d 
INFO     [2023-07-12 11:09:19] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:19] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:23] presg2d.solve :: reading results cs1_set2.sg... 
INFO     [2023-07-12 11:09:23] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:23] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:09:23] execu.run :: prevabs -i cs1_set2.xml -vabs -fi 
INFO     [2023-07-12 11:09:23] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:23] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:24] presg2d.solve :: reading results cs1_set2.sg.fi... 
DEBUG    [2023-07-12 11:09:24] core.runSGDesignAnalysisDF :: case_name = case3 
INFO     [2023-07-12 11:09:24] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:24] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:09:24] execu.run :: prevabs -i cs1_set2.xml -vabs -d 
INFO     [2023-07-12 11:09:24] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:24] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:28] presg2d.solve :: reading results cs1_set2.sg... 
INFO     [2023-07-12 11:09:28] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:09:28] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:09:28] execu.run :: prevabs -i cs1_set2.xml -vabs -fi 
INFO     [2023-07-12 11:09:28] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:09:28] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:09:28] presg2d.solve :: reading results cs1_set2.sg.fi... 
INFO     [2023-07-12 11:09:28] _msgd.writeMDAOEvalOut :: writing output files... 
CRITICAL [2023-12-14 11:50:53] io.readMSGDInput :: reading main input main.yml... 
INFO     [2023-12-14 11:50:53] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2023-12-14 11:50:53] _msgd.updateData :: updating current design... 
CRITICAL [2023-12-14 11:50:53] analysis.analyze :: [eval 0] analysis start 
INFO     [2023-12-14 11:50:53] analysis.analyze :: loading global structural data... 
INFO     [2023-12-14 11:50:53] distribution.loadDistribution :: loading parameter distribution... 
INFO     [2023-12-14 11:50:53] distribution.createInterpolationDistribution :: reading distribution of parameter ['topology']... 
INFO     [2023-12-14 11:50:53] distribution.loadDistribution :: loading parameter distribution... 
INFO     [2023-12-14 11:50:53] distribution.createInterpolationDistribution :: reading distribution of parameter ['airfoil']... 
INFO     [2023-12-14 11:50:53] distribution.loadDistribution :: loading parameter distribution... 
INFO     [2023-12-14 11:50:53] distribution.createInterpolationDistribution :: reading distribution of parameter ['ang_spar_1', 'ang_spar_2']... 
INFO     [2023-12-14 11:50:53] _msgd.discretize :: discretizing the design... 
INFO     [2023-12-14 11:50:53] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
CRITICAL [2023-12-14 11:50:54] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\msgd.py", line 179, in main
    mca.analyze(msgd)
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\core\analysis.py", line 260, in analyze
    msgd.discretize()
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\core\_msgd.py", line 740, in discretize
    sets, sg_base_sets, param_sets, elem_sets, node_params = md.calcParamsFromDistr(
                                                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\design\distribution.py", line 331, in calcParamsFromDistr
    logger.debug('eval {} {} ({}) at {}...'.format(type(pdistr).__name__, pname, pdistr.ytype, loc))
                                                                                 ^^^^^^^^^^^^
AttributeError: 'Distribution' object has no attribute 'ytype'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\msgd.py", line 179, in main
    mca.analyze(msgd)
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\core\analysis.py", line 260, in analyze
    msgd.discretize()
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\core\_msgd.py", line 740, in discretize
    sets, sg_base_sets, param_sets, elem_sets, node_params = md.calcParamsFromDistr(
                                                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\msg-design\scripts\msgd\design\distribution.py", line 331, in calcParamsFromDistr
    logger.debug('eval {} {} ({}) at {}...'.format(type(pdistr).__name__, pname, pdistr.ytype, loc))
                                                                                 ^^^^^^^^^^^^
AttributeError: 'Distribution' object has no attribute 'ytype'
INFO     [2023-12-14 11:50:54] _msgd.writeMDAOEvalOut :: writing output files... 
