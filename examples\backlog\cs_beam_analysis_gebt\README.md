# Multiple CS Analyses and Global Beam Analysis

Analyses of multiple cross-sections and beam free vibration analysis.

- An explicit blade definition in a separate file for GEBT.
- Cross-sectional designs are varied along the beam reference line:
  - Structural topology (box-spar and solid fill)
  - Airfoil
  - Layup
- Individual cross-sections are created by cutting the structure at given locations.
- Resulting beam properties are passed to the beam input to create the beam model for analysis in GEBT.
- Free vibration analysis is carried out.


## Run this example

1. Open a command prompt and change the working directory to this folder.
2. Run the following command:

    `ivabs main.yml --mode 1`

   `--mode 1` means that this example is analysis only, without going through Dakota for iterative running for design optimization.


## Input files

- Main input file
  - `main.yml`
- Supporting files
  - `airfoil_gbox_uni.xml.tmp`: CS template input for general box spar design
  - `airfoil_solid.xml.tmp`: CS template input for solid fill design
  - `sc1095.txt`: Airfoil SC1095 downloaded from (http://airfoiltools.com/airfoil/details?airfoil=sc1095-il)
  - `sc1095.txt`: Airfoil SC1094R8 downloaded from (http://airfoiltools.com/airfoil/details?airfoil=sc1094r8-il)
  - `material_database_us_ft.xml`: Material database
  - `beam_design.yml`: Beam design input


## Output files

- Main output file
  - `main.out`
- Other files
  - `cs1_set*`: Files related with cross-sectional analysis (PreVABS and VABS files)
  - `interim*`: Interim data files
  - `eval.log`: Logging file
  - `beam_design.dat`: Beam analysis file (GEBT input)
  - `beam_design.dat.out`: Beam analysis result (GEBT output)

