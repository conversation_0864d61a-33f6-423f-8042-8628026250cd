
Cross-sectional analysis and local field recovery
======================================================

In many cases, cross-sectional analysis includes not only calculating the beam properties, but also recovering local fields (displacement, strain, stress) after the global structural analysis.
This example considers a cross-section with an airfoil profile.
There are two steps in the analysis.
The first step is to calculate the beam properties.
The second step is to recover the local fields by giving a set of global responses including beam displacements, forces, and moments.

Running
--------

Example location: `examples/anl_cs_airfoil_box_d`

..  code-block:: shell

    ivabs analyze main.yml



Input files
-----------

main.yml
    Main input file.

cs_config_a.xml
    Box cross-section input file.

naca0012.dat
    Airfoil data.
