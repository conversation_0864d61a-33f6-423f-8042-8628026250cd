analysis:
  steps:
  - step: homogenization
    activate: true
    type: sg
    analysis: h
    work_dir: sg
  - step: buckling analysis
    activate: true
    setting:
      timeout: 300
    kwargs:
      exec:
        args:
        - interactive
        kwargs:
          ask_delete: 'OFF'
    type: abaqus
    post_process:
    - script: abq_get_result.py
      args:
      - plate_sq2_ss_nfx_bck_s4r_40x40_si.odb
      - abq_result.dat
    step_output_file: abq_result.dat
