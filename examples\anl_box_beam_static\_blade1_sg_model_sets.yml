main_cs_set1:
  name: main_cs_set1
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 4.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set2:
  name: main_cs_set2
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 13.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set3:
  name: main_cs_set3
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 22.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set4:
  name: main_cs_set4
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 31.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set5:
  name: main_cs_set5
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 40.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set6:
  name: main_cs_set6
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 49.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set7:
  name: main_cs_set7
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 58.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set8:
  name: main_cs_set8
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 67.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set9:
  name: main_cs_set9
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 76.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
main_cs_set10:
  name: main_cs_set10
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.0'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 85.5
      lyr_ply: 6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
