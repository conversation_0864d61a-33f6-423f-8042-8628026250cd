1

   Abaqus 2023                                  Date 22-May-2024   Time 17:03:23
   For use by PURDUE UNIVERSITY under license from Dassault Systemes or its subsidiary.



                         The Abaqus Software is a product of:

                           Dassault Systemes SIMULIA Corp.
                           1301 Atwood Avenue, Suite 101W
                              Johnston, RI 02919, USA
 


                   The Abaqus Software is available only under license
                   from Dassault Systemes or its subsidiary and may be
                   used or reproduced only in accordance with the terms
                   of such license.
 
                          On machine stn-w-xps 
                          you are authorized to run
                          Abaqus/Foundation until 31-Jan-2025

                          Your site id is: 100000000002199 


 
                    For assistance or any other information you may
                    obtain contact information for your local office
                    from the world wide web at:

                      http://www.3ds.com/products/simulia/locations/

 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 
                *                                                         * 
                *                   *****************                     * 
                *                   *  N O T I C E  *                     * 
                *                   *****************                     * 
                *                                                         * 
                *                                                         * 
                *                       Abaqus 2023                       * 
                *                                                         * 
                *          BUILD ID: 2022_09_28-14.11.55 183150           * 
                *                                                         * 
                *                                                         * 
                *  Please make sure you are using                         * 
                *  release Abaqus 2023 manuals                            * 
                *  plus the notes accompanying this release.              * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 


 



     PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   *******************************************************


     END PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   ***********************************************************




     OPTIONS BEING PROCESSED
   ***************************


  *Heading
  *Node
  *Element, type=S4R
  *Nset, nset=ASSEMBLY_PART-1-1_SET-1
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET1
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET10
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET2
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET3
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET4
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET5
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET6
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET7
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET8
  *Elset, elset=ASSEMBLY_PART-1-1_ESET-SET9
  *Elset, elset=ASSEMBLY_PART-1-1_SET-1
  *Elset, elset=ASSEMBLY_PART-1-1__SURF-1_SPOS
  *distribution, name=ASSEMBLY_PART-1-1_ORI-1-DISCORIENT, location=ELEMENT, table=ORI-1-DISCORIENT_TABLE
  *Nset, nset=ASSEMBLY_SET-1
  *Nset, nset=ASSEMBLY_SET-2
  *Nset, nset=ASSEMBLY_SET-3
  *Nset, nset=ASSEMBLY_SET-4
  *Elset, elset=ASSEMBLY_SET-1
  *Elset, elset=ASSEMBLY_SET-2
  *Elset, elset=ASSEMBLY__SURF-1_E3
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET1, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET2, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET3, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET4, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET5, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET6, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET7, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET8, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET9, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET10, orientation=ASSEMBLY_PART-1-1_ORI-1
  *orientation, name=ASSEMBLY_PART-1-1_ORI-1, system=RECTANGULAR
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET1, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET2, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET3, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET4, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET5, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET6, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET7, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET8, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET9, orientation=ASSEMBLY_PART-1-1_ORI-1
  *shellgeneralsection, elset=ASSEMBLY_PART-1-1_ESET-SET10, orientation=ASSEMBLY_PART-1-1_ORI-1
  *surface, type=ELEMENT, name=ASSEMBLY_PART-1-1_SURF-1
  *surface, type=ELEMENT, name=ASSEMBLY_SURF-1
  *surface, type=ELEMENT, name=ASSEMBLY_PART-1-1_SURF-1
  *surface, type=ELEMENT, name=ASSEMBLY_SURF-1
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *surface, type=ELEMENT, name=ASSEMBLY_PART-1-1_SURF-1
  *surface, type=ELEMENT, name=ASSEMBLY_SURF-1
  *output, field, variable=PRESELECT
  *output, field, variable=PRESELECT
  *output, field, variable=PRESELECT
  *Step, name=Step-1, nlgeom=NO, perturbation
  *output, field, variable=PRESELECT
  *Step, name=Step-1, nlgeom=NO, perturbation
  *Step, name=Step-1, nlgeom=NO, perturbation
  *buckle
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *dsload
  *output, field, variable=PRESELECT
  *endstep
  *Step, name=Step-1, nlgeom=NO, perturbation
  *buckle
  *buckle
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *boundary, op=NEW, loadcase=1
  *boundary, op=NEW, loadcase=2
  *endstep

     - (RAMP) OR (STEP) - INDICATE USE OF DEFAULT AMPLITUDES ASSOCIATED WITH THE STEP



                            P R O B L E M   S I Z E


          NUMBER OF ELEMENTS IS                                   400
          NUMBER OF NODES IS                                      441
          NUMBER OF NODES DEFINED BY THE USER                     441
          TOTAL NUMBER OF VARIABLES IN THE MODEL                 2646
          (DEGREES OF FREEDOM PLUS MAX NO. OF ANY LAGRANGE MULTIPLIER
           VARIABLES. INCLUDE *PRINT,SOLVE=YES TO GET THE ACTUAL NUMBER.)



                              END OF USER INPUT PROCESSING



     JOB TIME SUMMARY
       USER TIME (SEC)      =     0.30    
       SYSTEM TIME (SEC)    =      0.0    
       TOTAL CPU TIME (SEC) =     0.30    
       WALLCLOCK TIME (SEC) =            0
1

   Abaqus 2023                                  Date 22-May-2024   Time 17:03:26
   For use by PURDUE UNIVERSITY under license from Dassault Systemes or its subsidiary.

                                                                                               STEP    1  INCREMENT    1
                                                                                          TIME COMPLETED IN THIS STEP   0.00    


                        S T E P       1     C A L C U L A T I O N   O F   E I G E N V A L U E S

                                            F O R   B U C K L I N G   P R E D I C T I O N



                                                                                          

     THE SUBSPACE ITERATION METHOD IS USED FOR THIS ANALYSIS
     NUMBER OF EIGENVALUES                     10
     MAXIMUM NUMBER OF ITERATIONS                     30
     NUMBER OF VECTORS IN ITERATION                   18
     THE BUCKLING MODE CONSTRAINTS 
      ARE DEFINED AS LOAD CASE 2
                              ONLY INITIAL STRESS EFFECTS ARE INCLUDED IN THE
                              STIFFNESS MATRIX

     THIS IS A LINEAR PERTURBATION STEP.
     ALL LOADS ARE DEFINED AS CHANGE IN LOAD TO THE REFERENCE STATE
  
                   M E M O R Y   E S T I M A T E
  
 PROCESS      FLOATING PT       MINIMUM MEMORY        MEMORY TO
              OPERATIONS           REQUIRED          MINIMIZE I/O
             PER ITERATION           (MB)               (MB)
  
     1          3.24E+07               15                 28
  
 NOTE:
      (1) SINCE ABAQUS DOES NOT PRE-ALLOCATE MEMORY AND ONLY ALLOCATES MEMORY AS NEEDED DURING THE ANALYSIS,
          THE MEMORY REQUIREMENT PRINTED HERE CAN ONLY BE VIEWED AS A GENERAL GUIDELINE BASED ON THE BEST
          KNOWLEDGE AVAILABLE AT THE BEGINNING OF A STEP BEFORE THE SOLUTION PROCESS HAS BEGUN.
      (2) THE ESTIMATE IS NORMALLY UPDATED AT THE BEGINNING OF EVERY STEP. IT IS THE MAXIMUM VALUE OF THE
          ESTIMATE FROM THE CURRENT STEP TO THE LAST STEP OF THE ANALYSIS, WITH UNSYMMETRIC SOLUTION TAKEN
          INTO ACCOUNT IF APPLICABLE. 
      (3) SINCE THE ESTIMATE IS BASED ON THE ACTIVE DEGREES OF FREEDOM IN THE FIRST ITERATION OF THE 
          CURRENT STEP, THE MEMORY ESTIMATE MIGHT BE SIGNIFICANTLY DIFFERENT THAN ACTUAL USAGE FOR 
          PROBLEMS WITH SUBSTANTIAL CHANGES IN ACTIVE DEGREES OF FREEDOM BETWEEN STEPS (OR EVEN WITHIN
          THE SAME STEP). EXAMPLES ARE: PROBLEMS WITH SIGNIFICANT CONTACT CHANGES, PROBLEMS WITH MODEL
          CHANGE, PROBLEMS WITH BOTH STATIC STEP AND STEADY STATE DYNAMIC PROCEDURES WHERE ACOUSTIC 
          ELEMENTS WILL ONLY BE ACTIVATED IN THE STEADY STATE DYNAMIC STEPS.
      (4) FOR MULTI-PROCESS EXECUTION, THE ESTIMATED VALUE OF FLOATING POINT OPERATIONS FOR EACH PROCESS
          IS BASED ON AN INITIAL SCHEDULING OF OPERATIONS AND MIGHT NOT REFLECT THE ACTUAL FLOATING 
          POINT OPERATIONS COMPLETED ON EACH PROCESS. OPERATIONS ARE DYNAMICALLY BALANCED DURING EXECUTION, 
          SO THE ACTUAL BALANCE OF OPERATIONS BETWEEN PROCESSES IS EXPECTED TO BE BETTER THAN THE ESTIMATE
          PRINTED HERE.
      (5) THE UPPER LIMIT OF MEMORY THAT CAN BE ALLOCATED BY ABAQUS WILL IN GENERAL DEPEND ON THE VALUE OF
          THE "MEMORY" PARAMETER AND THE AMOUNT OF PHYSICAL MEMORY AVAILABLE ON THE MACHINE. PLEASE SEE
          THE "ABAQUS ANALYSIS USER'S MANUAL" FOR MORE DETAILS. THE ACTUAL USAGE OF MEMORY AND OF DISK
          SPACE FOR SCRATCH DATA WILL DEPEND ON THIS UPPER LIMIT AS WELL AS THE MEMORY REQUIRED TO MINIMIZE
          I/O. IF THE MEMORY UPPER LIMIT IS GREATER THAN THE MEMORY REQUIRED TO MINIMIZE I/O, THEN THE ACTUAL
          MEMORY USAGE WILL BE CLOSE TO THE ESTIMATED "MEMORY TO MINIMIZE I/O" VALUE, AND THE SCRATCH DISK
          USAGE WILL BE CLOSE-TO-ZERO; OTHERWISE, THE ACTUAL MEMORY USED WILL BE CLOSE TO THE PREVIOUSLY
          MENTIONED MEMORY LIMIT, AND THE SCRATCH DISK USAGE WILL BE ROUGHLY PROPORTIONAL TO THE DIFFERENCE
          BETWEEN THE ESTIMATED "MEMORY TO MINIMIZE I/O" AND THE MEMORY UPPER LIMIT. HOWEVER ACCURATE
          ESTIMATE OF THE SCRATCH DISK SPACE IS NOT POSSIBLE.
      (6) USING "*RESTART, WRITE" CAN GENERATE A LARGE AMOUNT OF DATA WRITTEN IN THE WORK DIRECTORY.
  
                   M E M O R Y   E S T I M A T E
  
 PROCESS      FLOATING PT       MINIMUM MEMORY        MEMORY TO
              OPERATIONS           REQUIRED          MINIMIZE I/O
             PER ITERATION           (MB)               (MB)
  
     1          3.24E+07               17                 30
  
 NOTE:
      (1) SINCE ABAQUS DOES NOT PRE-ALLOCATE MEMORY AND ONLY ALLOCATES MEMORY AS NEEDED DURING THE ANALYSIS,
          THE MEMORY REQUIREMENT PRINTED HERE CAN ONLY BE VIEWED AS A GENERAL GUIDELINE BASED ON THE BEST
          KNOWLEDGE AVAILABLE AT THE BEGINNING OF A STEP BEFORE THE SOLUTION PROCESS HAS BEGUN.
      (2) THE ESTIMATE IS NORMALLY UPDATED AT THE BEGINNING OF EVERY STEP. IT IS THE MAXIMUM VALUE OF THE
          ESTIMATE FROM THE CURRENT STEP TO THE LAST STEP OF THE ANALYSIS, WITH UNSYMMETRIC SOLUTION TAKEN
          INTO ACCOUNT IF APPLICABLE. 
      (3) SINCE THE ESTIMATE IS BASED ON THE ACTIVE DEGREES OF FREEDOM IN THE FIRST ITERATION OF THE 
          CURRENT STEP, THE MEMORY ESTIMATE MIGHT BE SIGNIFICANTLY DIFFERENT THAN ACTUAL USAGE FOR 
          PROBLEMS WITH SUBSTANTIAL CHANGES IN ACTIVE DEGREES OF FREEDOM BETWEEN STEPS (OR EVEN WITHIN
          THE SAME STEP). EXAMPLES ARE: PROBLEMS WITH SIGNIFICANT CONTACT CHANGES, PROBLEMS WITH MODEL
          CHANGE, PROBLEMS WITH BOTH STATIC STEP AND STEADY STATE DYNAMIC PROCEDURES WHERE ACOUSTIC 
          ELEMENTS WILL ONLY BE ACTIVATED IN THE STEADY STATE DYNAMIC STEPS.
      (4) FOR MULTI-PROCESS EXECUTION, THE ESTIMATED VALUE OF FLOATING POINT OPERATIONS FOR EACH PROCESS
          IS BASED ON AN INITIAL SCHEDULING OF OPERATIONS AND MIGHT NOT REFLECT THE ACTUAL FLOATING 
          POINT OPERATIONS COMPLETED ON EACH PROCESS. OPERATIONS ARE DYNAMICALLY BALANCED DURING EXECUTION, 
          SO THE ACTUAL BALANCE OF OPERATIONS BETWEEN PROCESSES IS EXPECTED TO BE BETTER THAN THE ESTIMATE
          PRINTED HERE.
      (5) THE UPPER LIMIT OF MEMORY THAT CAN BE ALLOCATED BY ABAQUS WILL IN GENERAL DEPEND ON THE VALUE OF
          THE "MEMORY" PARAMETER AND THE AMOUNT OF PHYSICAL MEMORY AVAILABLE ON THE MACHINE. PLEASE SEE
          THE "ABAQUS ANALYSIS USER'S MANUAL" FOR MORE DETAILS. THE ACTUAL USAGE OF MEMORY AND OF DISK
          SPACE FOR SCRATCH DATA WILL DEPEND ON THIS UPPER LIMIT AS WELL AS THE MEMORY REQUIRED TO MINIMIZE
          I/O. IF THE MEMORY UPPER LIMIT IS GREATER THAN THE MEMORY REQUIRED TO MINIMIZE I/O, THEN THE ACTUAL
          MEMORY USAGE WILL BE CLOSE TO THE ESTIMATED "MEMORY TO MINIMIZE I/O" VALUE, AND THE SCRATCH DISK
          USAGE WILL BE CLOSE-TO-ZERO; OTHERWISE, THE ACTUAL MEMORY USED WILL BE CLOSE TO THE PREVIOUSLY
          MENTIONED MEMORY LIMIT, AND THE SCRATCH DISK USAGE WILL BE ROUGHLY PROPORTIONAL TO THE DIFFERENCE
          BETWEEN THE ESTIMATED "MEMORY TO MINIMIZE I/O" AND THE MEMORY UPPER LIMIT. HOWEVER ACCURATE
          ESTIMATE OF THE SCRATCH DISK SPACE IS NOT POSSIBLE.
      (6) USING "*RESTART, WRITE" CAN GENERATE A LARGE AMOUNT OF DATA WRITTEN IN THE WORK DIRECTORY.


                              E I G E N V A L U E    O U T P U T     

 BUCKLING LOAD ESTIMATE = ("DEAD" LOADS) + EIGENVALUE * ("LIVE" LOADS).
          "DEAD" LOADS = TOTAL LOAD BEFORE  *BUCKLE STEP.
          "LIVE" LOADS = INCREMENTAL LOAD IN *BUCKLE STEP


 MODE NO      EIGENVALUE


       1       36.918    
       2       81.435    
       3       115.67    
       4       131.94    
       5       194.34    
       6       225.42    
       7       248.67    
       8       274.31    
       9       279.95    
      10       362.88    


          THE ANALYSIS HAS BEEN COMPLETED



                              ANALYSIS COMPLETE



     JOB TIME SUMMARY
       USER TIME (SEC)      =      1.2    
       SYSTEM TIME (SEC)    =     0.20    
       TOTAL CPU TIME (SEC) =      1.4    
       WALLCLOCK TIME (SEC) =            2
