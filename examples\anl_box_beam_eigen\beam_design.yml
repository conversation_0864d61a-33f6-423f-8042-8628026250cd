# Main input for GEBT
# More detailed instruction can be found in the GEBT manual.

# This example:
# - straight beam
# - cantilever beam
# - free vibration analysis


# Overall analysis configuration block
# ====================================================================
analysis: 3

# Maximum number of iterations for the nonlinear analysis.
# If 1, carry out linear analysis
max_iteration: 1

# Number of analysis steps.
num_steps: 1

# Number of eigenvalues extracted from the analysis.
num_eigens: 20


# Geometry
# ====================================================================
# List of key points on the beam model.
point:
  - id: 1
    coordinates: [0, 0, 0]
  - id: 2
    coordinates: [5, 0, 0]
  - id: 3
    coordinates: [10, 0, 0]

# List of beam memebrs.
member:
  - id: 1
    points: [1, 2]
    # IDs of the begining and ending points defining the member.
  - id: 2
    points: [2, 3]

# Create some sets for the use below.
set:
  - name: "root"
    type: 'point'
    items: [1,]
  - name: "tip"
    type: 'point'
    items: [3,]
  - name: 'segment1'
    type: 'member'
    items: [1, 2]


# Sectional properties block
# ====================================================================



# Boundary condition and load block
# ====================================================================
condition:
  - region: 'root'
    # Set name defined above.
    # Fix the root.

    dofs: [1, 2, 3, 4, 5, 6]
    # Degree-of-freedom labels
    # - 1, 2, 3: displacements in x, y, z
    # - 4, 5, 6: rotations in x, y, z

    values: [0, 0, 0, 0, 0, 0]
    # Values corresponding to each DoF.

  - region: 'tip'
    # No BCs and loads at the tip.

    dofs: [7, 8, 9, 10, 11, 12]
    # Degree-of-freedom labels
    # - 7, 8, 9: forces in x, y, z
    # - 10, 11, 12: moments in x, y, z

    values: [0, 0, 0, 0, 0, 0]


# Meshing block
# ====================================================================

meshing:
  member_division:
    - member: 1
      division: 32
    - member: 2
      division: 32

