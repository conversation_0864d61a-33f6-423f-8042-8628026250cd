.. _kw-design:

design
======

Parent keyword: |sg|
----------------------------------------------------------------------

Basic design setup of the structure.

..  code-block:: yaml

    cs:
      - name: "my_cs"
        design:
          ...
        ...



Specification
^^^^^^^^^^^^^^

:Arguments: None
:Required: True
:Default: None


Parent keyword: structure > |sg|
----------------------------------------------------------------------

Specification of the name of the |sg| design.

..  code-block:: yaml

    structure:
      cs:
        - name: "my_cs_model"
          design: "my_cs"
          ...



Specification
^^^^^^^^^^^^^^

:Arguments: String
:Required: True
:Default: None


