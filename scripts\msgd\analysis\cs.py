import csv
import math
import platform
import numpy as np
from scipy.interpolate import interp2d

import sgio

from msgd.analysis.design_analysis import DesignAnalysis
import msgd.design.cross_section as mcs
# import msgd.model.execu as sga
# import msgd.model.io as mmi
# import msgpi.utils.container as utcnt
import msgd.ext.presg2d as mep
import msgd.ext.rcas as meg
import msgd.utils.math as mum
import msgd.utils.logger as mul

import dakota.interfacing as di


class CrossSectionDesignAnalysis(DesignAnalysis):

    class_name = 'cs'

    def __init__(self, name='', inputs={}, outputs={}, settings={}, prepros=[], postpros=[], analyses=[], logger=None, parent=None, debug=None):
        DesignAnalysis.__init__(self, inputs, outputs, settings, prepros, postpros, analyses, logger, parent, debug)
        # self.cs = cs
        # self.job_args = job_args

        self.name = name
        self.sname = name

        # if logger is None:
        #     self.logger = mlog.initLogger(__name__)
        # else:
        #     self.logger = logger

        # self.inputs = inputs
        # self.outputs = {}
        self.settings['integrated'] = False
        self.settings['timeout'] = 30
        self.settings['scrnout'] = False
        self.settings['substitution'] = True
        self.settings['sr_filter'] = False

        self.cs = mcs.CrossSection(self.sname)
        self.blade = None

        self.data.update(self.parent.data)

        if not 'cs' in self.data['structures'].keys():
            self.data['structures']['cs'] = []
        if not self.name in self.data['structures']['cs']:
            self.data['structures']['cs'].append(self.name)


    def run(self):
        # self.logger.info(f'running design analysis for {self.name}...')
        self.logger.info('running design analysis for {}...'.format(self.sname))

        # if platform.system() == 'Windows':
        #     ppcmd = self.settings['prevabs_cmd_win']
        # elif platform.system() == 'Linux':
        #     ppcmd = self.settings['prevabs_cmd_linux']


        self.preprocess()


        if self.settings['analysis'] == 0 or self.settings['analysis'] == 'h' or self.settings['analysis'] == '':
            self.runH()

        elif self.settings['analysis'] == 3 or self.settings['analysis'] == 'fi':
            self.runDF()




        # Post-process data
        # -----------------
        self.postprocess()


        # Push data back to parent
        # ------------------------
        # if not self.name in self.parent.inputs['cs'].keys():
        #     self.parent.inputs['cs'][self.name] = {}
        # utcnt.updateDict(source=self.inputs, target=self.parent.inputs['cs'][self.name])
        # if not self.name in self.parent.outputs['cs'].keys():
        #     self.parent.outputs['cs'][self.name] = {}
        # utcnt.updateDict(source=self.outputs, target=self.parent.outputs['cs'][self.name])


        return


    def runH(self):
        # Substitute parameters
        # ---------------------
        # print(self.data)
        if self.settings['substitution']:
            self.data[self.sname]['input_file'] = self.sname + '.xml'
            self.logger.info('substituting {} -> {}...'.format(
                self.data[self.sname]['base_design'],
                self.data[self.sname]['input_file']
            ))
            try:
                di.dprepro(
                    template=self.data[self.sname]['base_design'],
                    output=self.data[self.sname]['input_file'],
                    include=self.data[self.sname]
                )
                self.settings['substitution'] = False
            except KeyError:
                pass

        # Solve
        # -----
        try:
            ppcmd = self.settings['prevabs_cmd']
        except KeyError:
            ppcmd = 'prevabs'

        if platform.system() == 'Windows':
            try:
                ppcmd = self.settings['prevabs_cmd_win']
            except KeyError:
                pass
            if ppcmd[-4:] != '.exe':
                ppcmd = ppcmd + '.exe'
        elif platform.system() == 'Linux':
            try:
                ppcmd = self.settings['prevabs_cmd_linux']
            except KeyError:
                pass

        results = mep.solve(
            self.data[self.sname]['input_file'],
            self.settings['analysis'],
            ppcmd,
            self.settings['solver'],
            self.settings['integrated'],
            timeout=self.settings['timeout'],
            scrnout=self.settings['scrnout'],
            logger=self.logger
        )

        # Extract beam properties
        # -----------------------
        self.cs.props = results
        self.logger.debug('extracting beam properties...')
        self.data[self.name].update(self.cs.props.getAll())
        try:
            for n in self.data[self.sname]['beam_properties']:
                self.outputs[n] = self.cs.props.get(n)
                self.data[self.sname][n] = self.cs.props.get(n)
        except KeyError:
            self.data[self.name].update(self.cs.props.getAll())

        # print(self.outputs)

        try:
            for rn, bn in self.data[self.name]['final'].items():
                self.data['dakota'][rn] = self.cs.props.get(bn)
        except KeyError:
            pass

        return


    def runDF(self):
        """
        load_cases = {
            ...
            'case1': {
            'measure': 1,
            'loads': [],
            'displacements': [],
            'rotations': []
            },
        }
        """
        self.logger.debug('running dehomogenization/failure analysis...')
        # substitute load case or write load case file
        # read rcas data
        load_cases = self.data[self.sname]['load_cases']
        data_form = load_cases['data_form']
        data_format = load_cases['data_format']

        self.logger.debug('data_form = {}, data_format = {}'.format(data_form, data_format))

        """
        load_vector_list = [
            [F1, F2, F3, M1, M2, M3],  # case 1
            [F1, F2, F3, M1, M2, M3],  # case 2
            ...
        ]
        """
        load_vector_list = []

        load_functions = []
        load_azimuths = []

        if data_form == 'file' and data_format == 'rcas':
            rd = meg.GalaxyBladeData()
            flight_condition = str(load_cases['flight_condition'])
            # load_location = load_cases['load_location']
            # if data_form == 'rcas_csv':
            fn_force = load_cases['file_name'][0]
            fn_moment = load_cases['file_name'][1]
            fn_oml = load_cases['file_name'][2]
            # oml_data = mmr.readRcasOml(fn_oml)
            # load_data = mmr.readRcasLoad(fn_force, fn_moment)
            try:
                load_file_delimiter = load_cases['load_file_delimiter']
            except KeyError:
                load_file_delimiter = ','

            rd.getRcasLoadCaseInterpFunctions(
                fn_force, fn_moment, fn_oml, flight_condition,
                load_file_delimiter=load_file_delimiter)

            load_functions = rd.load_functions
            load_azimuths = rd.load['azimuth']

            try:
                azimuth = load_cases['azimuth']
            except KeyError:
                azimuth = 'all'

            if azimuth == 'all':
                azimuth = load_azimuths
            elif isinstance(azimuth, int) or isinstance(azimuth, float):
                azimuth = [azimuth, ]

            cs_loc = self.data[self.name]['x']
            cs_loc = cs_loc * self.data[self.name]['R']
            for a in azimuth:
                load = meg.getRcasLoadVectorAt(load_functions, a, cs_loc)
                load_vector_list.append(load)


        elif data_form == 'file' and data_format == 'csv':
            fn_load = load_cases['file_name']

            try:
                load_file_delimiter = load_cases['load_file_delimiter']
            except KeyError:
                load_file_delimiter = ','

            load_data, load_azimuths = readLoadCsv(fn_load, delimiter=load_file_delimiter)

            # load_functions, load_azimuths = getLoadCaseInterpFunctions(
            #     fn_load, flight_condition,
            #     load_file_delimiter=load_file_delimiter)

            try:
                flight_condition = str(load_cases['flight_condition'])
            except KeyError:
                flight_condition = '1'

            try:
                azimuth = load_cases['azimuth']
            except KeyError:
                azimuth = 'all'

            if azimuth == 'all':
                azimuth = load_azimuths
            elif isinstance(azimuth, int) or isinstance(azimuth, float):
                azimuth = [azimuth, ]

            cs_loc = self.data[self.name]['x']
            # cs_loc = cs_loc * self.data[self.name]['R']
            for a in azimuth:
                load = getLoadVectorAt(
                    load_data, flight_condition, a, cs_loc)
                load_vector_list.append(load)


        self.data[self.name]['load_cases']['list'] = load_vector_list

        # print(self.data[self.name]['load_cases'])

        # self.settings['substitution'] = False

        # if self.settings['substitution']:
        #     self.data[self.sname]['input_file'] = self.sname + '.xml'
        #     # Substitute parameters
        #     # ---------------------
        #     try:
        #         di.dprepro(
        #             template=self.data[self.sname]['base_design'],
        #             output=self.data[self.sname]['input_file'],
        #             include=self.data[self.sname]
        #         )
        #         self.settings['substitution'] = False
        #     except KeyError:
        #         pass


        if platform.system() == 'Windows':
            ppcmd = self.settings['prevabs_cmd_win']
        elif platform.system() == 'Linux':
            ppcmd = self.settings['prevabs_cmd_linux']

        for i, case in enumerate(self.data[self.name]['load_cases']['list']):
            # Write load case to csv file
            fn_loadcases = 'loadcases.csv'
            headname = ['F1', 'F2', 'F3', 'M1', 'M2', 'M3']
            self.logger.info('load case: ' + ', '.join(['{}={}'.format(headname[j], case[j]) for j in range(len(headname))]))

            with open(fn_loadcases, 'w') as csvfile:
                csvwriter = csv.writer(csvfile)
                csvwriter.writerow(headname)
                csvwriter.writerow(case)

            # Solve
            results, sr_min, eid_sr_min = mep.solve(
                self.data[self.sname]['input_file'],
                self.settings['analysis'],
                ppcmd,
                self.settings['solver'],
                self.settings['integrated'],
                timeout=self.settings['timeout'],
                scrnout=self.settings['scrnout'],
                logger=self.logger
            )

            # Extract beam properties
            # self.cs.fis = results[0]
            # self.cs.srs = results[1]
            # self.cs.sr = results[-1]

            # sr_min = None
            if self.settings['sr_filter']:
                fn_sg = self.data[self.sname]['input_file'].replace('.xml', '.sg')

                try:
                    write_ignored_element_ids = self.settings['write_ignored_element_ids']
                except KeyError:
                    write_ignored_element_ids = False

                self.logger.debug('before filter sr_min = {} at element {}'.format(sr_min, eid_sr_min))
                sr_min, eid_sr_min = getFilteredStrengthRatio(
                    results, fn_sg,
                    write_ignored_element_ids=write_ignored_element_ids,
                    logger=self.logger)
                self.logger.debug('after filter sr_min = {} at element {}'.format(sr_min, eid_sr_min))

            self.cs.sr = sr_min

            self.outputs['sr_case_{}'.format(i+1)] = sr_min # self.cs.sr
            self.data[self.sname]['sr_case_{}'.format(i+1)] = sr_min # self.cs.sr

        return









def getFilteredStrengthRatio(
    fi_result, fn_sg, fn_sg_itf_pairs='', fn_sg_itf_nodes='', fn_sg_node_elements='',
    itf_theta3_th=30, itf_theta1_th=30, solver='vabs',
    write_ignored_element_ids=True, logger=None):

    if not logger:
        logger = mul.initLogger(__name__)

    logger.info('filtering strengh ratios...')

    # sr_min_filtered = None

    if fn_sg_itf_pairs == '':
        fn_sg_itf_pairs = fn_sg.replace('.sg', '_interface_pairs.dat')
    if fn_sg_itf_nodes == '':
        fn_sg_itf_nodes = fn_sg.replace('.sg', '_interface_nodes.dat')
    if fn_sg_node_elements == '':
        fn_sg_node_elements = fn_sg.replace('.sg', '_node_elements.dat')

    # sg = sgio.readInputSG(fn_sg, 'vabs')
    sg = sgio.read(fn_sg, 'vabs', 1)
    sg.itf_pairs = sgio.readSGInterfacePairs(fn_sg_itf_pairs)
    sg.itf_nodes = sgio.readSGInterfaceNodes(fn_sg_itf_nodes)
    sg.node_elements = sgio.readSGNodeElements(fn_sg_node_elements)

    eid_ignore = []
    # itf_theta3_th = 30
    # itf_theta1_th = 30
    for i, itf_pair in enumerate(sg.itf_pairs):
        ignore = False
        if (itf_pair[0] != itf_pair[1]):
            ignore = True
        else:
            if (math.fabs(itf_pair[2] - itf_pair[3]) > itf_theta3_th):
                ignore = True
            elif (math.fabs(itf_pair[4] - itf_pair[5]) > itf_theta1_th):
                ignore = True

        if not ignore:
            continue

        for nid in sg.itf_nodes[i]:
            for eid in sg.node_elements[nid-1]:
                if not eid in eid_ignore:
                    eid_ignore.append(eid)

    if write_ignored_element_ids:
        fn = fn_sg.replace('.sg', '_ignored_elements.dat')
        with open(fn, 'w') as fobj:
            for eid in eid_ignore:
                fobj.write('{:16d}\n'.format(eid))

    sr_min_filtered = None
    eid_sr_min_filtered = None
    for efs in fi_result:
        eid = efs[0]
        if eid in eid_ignore:
            continue

        sr = efs[2]
        if (sr_min_filtered is None) or (sr < sr_min_filtered):
            sr_min_filtered = sr
            eid_sr_min_filtered = eid

    return sr_min_filtered, eid_sr_min_filtered









def getLoadVectorAt(
    load_data, condition, a, r,
    azimuth_scale=1, spanloc_scale=1, tol=1e-3):

    load = []
    found = False

    # Get load from the data
    # load_data = readLoadCsv(fn_load, delimiter=load_file_delimiter)

    for comp in ['fx', 'fy', 'fz', 'mx', 'my', 'mz']:
        data = load_data[condition][comp]
        for i, vi in enumerate(data['v']):
            if mum.isEqual(data['a'][i]*azimuth_scale, a, tol) and mum.isEqual(data['r'][i]*spanloc_scale, r, tol):
                load.append(vi)
                break

    if len(load) < 6:
        found = False
        load = []
    else:
        found = True

    # Use interpolation if needed
    if not found:
        load_functions, load_azimuth = getLoadCaseInterpFunctions(
            load_data, azimuth_scale=azimuth_scale, spanloc_scale=spanloc_scale
        )

        for comp in ['fx', 'fy', 'fz', 'mx', 'my', 'mz']:
            v = load_functions[condition][comp](r, a)[0]
            load.append(v)

    return load









def getLoadCaseInterpFunctions(
    load_data, azimuth_scale=1, spanloc_scale=1,
    kind='linear', fill_value=None
    ):

    """
    load_functions = {
        'condition_1': {
            'fx': function, 'fy': function, 'fz', function,
            'mx', function, 'my', function, 'mz', function
        },
        ...
    }
    """
    load_functions = {}
    load_azimuth = []

    # load_data = readLoadCsv(fn_load, delimiter=load_file_delimiter)

    for cond, data in load_data.items():
        load_functions[cond] = {}

        for comp in ['fx', 'fy', 'fz', 'mx', 'my', 'mz']:

            print(data[comp])

            a = data[comp]['a']
            r = data[comp]['r']
            v = data[comp]['v']

            if len(load_azimuth) == 0:
                load_azimuth = list(set(a))

            if len(set(a)) == 1:
                temp = [ai+180 for ai in a]
                a = a + temp
                r = r + r
                v = v + v

            print(a)
            print(r)
            print(v)

            load_functions[cond][comp] = interp2d(
                np.array(r)*spanloc_scale,
                np.array(a)*azimuth_scale,
                np.array(v),
                kind=kind, fill_value=fill_value)


    return load_functions, load_azimuth









def readLoadCsv(fn, delimiter=',', nhead=1, encoding='utf-8-sig'):
    r"""
    load = {
        'flight_condition_1': {
            'fx': {
                'a': [],
                'r': [],
                'v': []
            },
            'fy': [],
            'fz': [],
            'mx', [],
            'my', [],
            'mz', []
        },
        'flight_condition_2': {},
        ...
    }
    """

    load = {}
    azimuth = []

    with open(fn, 'r', encoding=encoding) as file:
        cr = csv.reader(file, delimiter=delimiter)

        for i, row in enumerate(cr):
            row = [s.strip() for s in row]
            if row[0] == '':
                continue

            if i < nhead:
                continue
                # # Read head
                # for label in row:
                #     if label.lower().startswith('rotor'):
                #         nid = int(label.split('NODE')[1])
                #         load['node_id'].append(nid)

            else:
                condition = str(row[0])
                if not condition in load.keys():
                    load[condition] = {
                        'fx': {'a': [], 'r': [], 'v': []},
                        'fy': {'a': [], 'r': [], 'v': []},
                        'fz': {'a': [], 'r': [], 'v': []},
                        'mx': {'a': [], 'r': [], 'v': []},
                        'my': {'a': [], 'r': [], 'v': []},
                        'mz': {'a': [], 'r': [], 'v': []}
                    }

                a, r, fx, fy, fz, mx, my, mz = list(map(float, row[1:]))
                v = {
                    'fx': fx, 'fy': fy, 'fz': fz,
                    'mx': mx, 'my': my, 'mz': mz
                }

                azimuth.append(a)

                for component in ['fx', 'fy', 'fz', 'mx', 'my', 'mz']:
                    load[condition][component]['a'].append(a)
                    load[condition][component]['r'].append(r)
                    load[condition][component]['v'].append(v[component])

    azimuth = list(set(azimuth))

    return load, azimuth






