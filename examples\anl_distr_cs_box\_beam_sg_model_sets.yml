set1:
  name: main_cs_set1
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 0.0
      lyr_ply: 1.0
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
set2:
  name: main_cs_set2
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 18.0
      lyr_ply: 2.6
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
set3:
  name: main_cs_set3
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 45.0
      lyr_ply: 5.0
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
set4:
  name: main_cs_set4
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 63.0
      lyr_ply: 6.6000000000000005
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
set5:
  name: main_cs_set5
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 81.0
      lyr_ply: 8.2
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
set6:
  name: main_cs_set6
  parameter: {}
  model:
    type: bm2
    tool: VABS
    tool_version: '4.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: box
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 90.0
      lyr_ply: 9.0
    dim: 2
    builder: prevabs
    design:
      base_file: box.xml.tmp
