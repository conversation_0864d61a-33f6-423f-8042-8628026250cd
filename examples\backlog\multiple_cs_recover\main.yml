# Main input file

version: '0.9'  # Required.


# Design parameters/variables of the structure
# ====================================================================
structure:
  name: 'blade1'

  # Basic design inputs of the blade
  design:
    dim: 1

    # Without any dedicated blade design input from other tools (e.g., abaqus, rcas),
    #   we need to give a series of numbers to indicate the locations
    #   where c/s will be created and analyzed.
    section_locations: [0.2, 0.9]
    # section_locations: [0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]

    # Assign c/s design to structural regions
    cs_assignment:
      - region: 'all'
        cs: 'cs1'
        model: 'b2'


  # The 'distribution' block defines how c/s parameters vary along the blade span
  distribution:
    - name: 'topology'
      # Required.
      # Name(s) of the c/s design parameter (defined in the 'cs' block below)
      # To define multiple parameters at the same time, put the names in a list:
      #   name: ['param1', 'param2', 'param3']

      ytype: 'str'
      # Optional (choose one from 'float' (default), 'int', or 'str').
      # Value type of the parameter.

      # Definition of the distribution function
      # Specific inputs vary depending on the function
      function: 'interpolation'
      # Required.
      # Can be the name of a pre-defined function or 'interpolation'.
      # The interpolation function is implemented using scipy.interpolate.interp1d

      kind: 'previous'
      # For 'interpolation', the 'kind' can be the following options
      # - 'linear': linear interpolation
      # - 'previous': step function, f(x) = f(x1) if x1 < x < x2
      # - 'next': step function, f(x) = f(x2) if x1 < x < x2
      # - others (see online doc of scipy.interpolate.interp1d)

      data_form: 'compact'
      # Optional (choose one from 'explicit' (default) and 'compact')
      # Indicate the syntax of the interpolated data block below.

      data: |
        0.0, airfoil_gbox_uni.xml.tmp
        0.8, airfoil_solid.xml.tmp
      # Required.
      # For the 'compact' form, the data is a comma-separated tabular form
      #   written as a multiline string
      # In general, data is arranged as
      #   x1, param1_value1, param2_value1, ...
      #   x2, param1_value2, param2_value2, ...
      #   x3, param1_value3, param2_value3, ...
      #   ...
      # The first column is the spanwise location of the interpolated data
      # The rest columns are the data values,
      #   the number and sequence of which should match 'name'.

    - name: 'airfoil'
      ytype: 'str'
      function: 'interpolation'
      kind: 'previous'
      data_form: 'compact'
      data: |
        0.0, sc1095.txt
        0.5, sc1094r8.txt

    - name: ['ang_spar_1', 'ang_spar_2']
      ytype: 'float'
      function: 'interpolation'
      kind: 'linear'
      data_form: 'compact'
      data: |
        0.1, 46, -45
        0.3, -3, 0
        0.7, -44, 90
        1.0, 47, 45



# Base (default/template) design of the cross-section
# ====================================================================
cs:
  - name: 'cs1'
    # Required. Name of the base design.

    # Basic design input of the c/s.
    design:
      tool: 'prevabs'
      # Requried.
      # Building tool of the cross-section.
      # This is the actual command that will be called.

      base_file: topology
      # Requried.
      # CS base design file (template) for PreVABS.
      # The value can be a file name
      #   or a parameter name that will be substituted during running.

    # List of default parameters for the cross-sectional design.
    # Parameter name should be in consistent with those defined in the template file.
    # Default values can actaully be assigned at two places:
    # - here, or
    # - in the template file
    parameter:
      mdb_name: 'material_database_us_ft'
      airfoil: 'sc1095.txt'
      airfoil_file_head: 1
      airfoil_point_order: 1
      airfoil_point_reverse: 1
      chord: 1.73
      a2p1: 0.8
      a2p3: 0.6
      lam_skin: 'T300 15k/976_0.0053'
      lam_front: 'T300 15k/976_0.0053'
      lam_back: 'T300 15k/976_0.0053'
      lam_spar_1: 'T300 15k/976_0.0053'
      ang_spar_1: 0
      ply_spar_1: 10
      lam_cap: 'Aluminum 8009_0.01'
      mat_nsm: 'lead'
      mat_fill_front: 'Rohacell 70'
      mat_fill_back: 'Plascore PN2-3/16OX3.0'
      mat_fill_te: 'Plascore PN2-3/16OX3.0'
      rnsm: 0.001
      gms: 0.004

    model:
      md1:
        tool: 'vabs'
        # Required.
        # Cross-sectional analysis tool.
        # This is the actual command that will be called.


# Analysis process
# ====================================================================
analysis:
  steps:
    - step: 'cs analysis'  # Arbitrary name. No specific meaning.

      type: 'cs'
      # Required.
      # This means that this is a 'cross-sectional analysis' step.

      analysis: 'h'
      # Required.
      # 'h' stands for 'homogenization'.
      # This analysis will calculate the effective beam properties.
      # Other supported keywords:
      # - 'd': 'dehomogenization' for stress/strain recovery
      # - 'fi': 'failure index' for calculating the initial failure index and strength ratio

      setting:
        timeout: 60
        # Optional (default 60).
        # Waiting time (seconds) before killing the CS analysis process.
        # This is used to prevent the program from stucking at a failed CS analysis.
        # Usually used in design optimizaiton, since not all CS designs are valid in the design space.

      output:
        - value: ["gj", "eiyy", "eizz"]
        # Optional.
        # Beam properties requested to write in the output file `main.out`.
        # They will be grouped and listed for each specific cross-section name.
        # See https://wenbinyugroup.github.io/ivabs/ref/beam_properties.html for more property keywords.

    - step: "recovery"
      type: "cs"
      analysis: "d,fi"
      # Required.
      # This means that this is a local step for stress/strain recovery and initial failure analysis, where
      # - 'd' stands for 'dehomogenization', i.e., stress/strain recovery
      # - 'fi' stands for 'failure index', which also calculates strength ratio.

      input:
      # Required (for this step).
      # In general, 'input' is the block for passing extra input data
      # to all types of analysis steps.

        load_case:
        # Required (for this step).

          data_form: "file"
          # Optional (choose one from 'list' (default), 'file')

          # The following inputs are required if 'data_form' is 'file'
          file_name: "struct_resp.csv"
          # File containing global structural responses.

          location_tags: 'coord'
          location_value_types: 'float'
          # Column name(s) and value type(s) for the locations of the responses.

          condition_tags: 'cond1'
          condition_value_types: 'float'
          # Column name(s) and value type(s) for the conditions of the responses.

