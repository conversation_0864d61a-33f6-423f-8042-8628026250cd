import math


def dakota_postpro(data, sname, *args, **kwargs):
    """ Post-process results from previous analysis steps.

    This is the last step in a Dakota evaluation.
    The goal is to calculate response/objective/constraint function values
    and store them in data['dakota']:

        data['main'] = {
            'descriptor1': value1,
            'descriptor2': value2,
            ...
        }
    """

    cs_name = 'main_cs_set1'

    target_props = list(map(float, kwargs['target']))

    calc_props = []
    for n in kwargs['beam_properties']:
        p = data['structure']['css_data'][cs_name]['property']['md1'][n]
        calc_props.append(p)

    data['main']['diff_gj'] = math.fabs((calc_props[0] - target_props[0]) / target_props[0])
    data['main']['diff_eiyy'] = math.fabs((calc_props[1] - target_props[1]) / target_props[1])
    data['main']['diff_eizz'] = math.fabs((calc_props[2] - target_props[2]) / target_props[2])


    return

