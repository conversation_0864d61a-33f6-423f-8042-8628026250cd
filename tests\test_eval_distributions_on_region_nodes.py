"""Test module for eval_distributions_on_region_nodes function.

This module tests the eval_distributions_on_region_nodes function which evaluates
distributions on all nodes in specified regions.

Available fixtures from conftest.py:
- linear_function, quadratic_function: Test functions for distributions
- single_param_distribution, multi_param_distribution: Pre-configured distributions
- simple_mesh_data: Basic triangular mesh with regions
- sample_mesh_nodes, sample_mesh_cells, sample_cell_data_etags: Mesh components
- sample_entity_sets, complex_mesh_scenario: Entity sets and complex scenarios
"""

import pytest
import numpy as np
from meshio import CellBlock
from msgd.design.distribution import Distribution
from msgd.design.discretize import eval_distributions_on_region_nodes
from msgd.core._data_classes import EntitySet


class TestEvalDistributionsOnRegionNodes:
    """Test suite for the eval_distributions_on_region_nodes function."""

    def test_empty_distributions(self, simple_mesh_data):
        """Test with empty distributions list"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data
        distributions = []
        
        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )
        
        assert isinstance(result, dict)
        assert len(result) == 0

    def test_single_distribution_single_param(self, single_param_distribution, simple_mesh_data):
        """Test with single distribution having single parameter"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data
        distributions = [single_param_distribution]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "thickness" in result

        # Check that result is a numpy array with same length as nodes
        assert isinstance(result["thickness"], np.ndarray)
        assert len(result["thickness"]) == len(nodes)

        # Region1 contains elements 1,2 which use nodes 0,1,2,3
        # Expected nodes in region1: {0, 1, 2, 3}
        expected_nodes = {0, 1, 2, 3}

        # Check parameter values (linear function: 2*x + 1)
        for node_id in range(len(nodes)):
            if node_id in expected_nodes:
                # Node is in region - should have calculated value
                expected_value = 2 * nodes[node_id, 0] + 1  # Use x-coordinate
                assert np.isclose(result["thickness"][node_id], expected_value)
            else:
                # Node is not in region - should be NaN
                assert np.isnan(result["thickness"][node_id])

    def test_single_distribution_multi_param(self, multi_param_distribution, simple_mesh_data):
        """Test with single distribution having multiple parameters"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data
        distributions = [multi_param_distribution]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "thickness" in result
        assert "density" in result

        # Check that results are numpy arrays with same length as nodes
        assert isinstance(result["thickness"], np.ndarray)
        assert isinstance(result["density"], np.ndarray)
        assert len(result["thickness"]) == len(nodes)
        assert len(result["density"]) == len(nodes)

        # Region2 contains element 3 which uses nodes 2,3,4
        expected_nodes = {2, 3, 4}

        # Check parameter values
        for node_id in range(len(nodes)):
            if node_id in expected_nodes:
                # Node is in region - should have calculated values
                x_coord = nodes[node_id, 0]
                # thickness: linear function (2*x + 1)
                expected_thickness = 2 * x_coord + 1
                assert np.isclose(result["thickness"][node_id], expected_thickness)

                # density: quadratic function (x^2)
                expected_density = x_coord**2
                assert np.isclose(result["density"][node_id], expected_density)
            else:
                # Node is not in region - should be NaN
                assert np.isnan(result["thickness"][node_id])
                assert np.isnan(result["density"][node_id])

    def test_multiple_distributions(self, single_param_distribution, multi_param_distribution, simple_mesh_data):
        """Test with multiple distributions"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data
        distributions = [single_param_distribution, multi_param_distribution]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "thickness" in result
        assert "density" in result

        # Check that results are numpy arrays with same length as nodes
        assert isinstance(result["thickness"], np.ndarray)
        assert isinstance(result["density"], np.ndarray)
        assert len(result["thickness"]) == len(nodes)
        assert len(result["density"]) == len(nodes)

        # Should have nodes from both regions
        # Region1: nodes {0,1,2,3}, Region2: nodes {2,3,4}
        # Combined: {0,1,2,3,4}
        expected_all_nodes = {0, 1, 2, 3, 4}
        expected_region2_nodes = {2, 3, 4}

        # Check thickness values (appears in both distributions)
        for node_id in range(len(nodes)):
            if node_id in expected_all_nodes:
                # Node is in at least one region - should have calculated value
                assert not np.isnan(result["thickness"][node_id])
            else:
                # Node is not in any region - should be NaN
                assert np.isnan(result["thickness"][node_id])

        # Check density values (only appears in multi_param_distribution - region2)
        for node_id in range(len(nodes)):
            if node_id in expected_region2_nodes:
                # Node is in region2 - should have calculated value
                assert not np.isnan(result["density"][node_id])
            else:
                # Node is not in region2 - should be NaN
                assert np.isnan(result["density"][node_id])

    def test_empty_region(self, linear_function, simple_mesh_data):
        """Test with region that has no elements"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data

        # Create distribution for empty region
        empty_region_dist = Distribution(
            name="empty_param",
            region="empty_region",
            func_base=linear_function,
            vtype="float"
        )
        empty_region_dist._function = [linear_function]

        # Add empty region to regions dict
        regions["empty_region"] = EntitySet(name="empty_region", type="element", items=[])
        distributions = [empty_region_dist]

        # The function should handle empty regions gracefully
        # This might raise an error if the function doesn't handle empty regions properly
        # In that case, this test documents the current behavior
        try:
            result = eval_distributions_on_region_nodes(
                distributions, mesh_cells, cell_data_etags, nodes, regions
            )

            assert isinstance(result, dict)
            assert "empty_param" in result
            # Should return array with same length as nodes, all NaN
            assert isinstance(result["empty_param"], np.ndarray)
            assert len(result["empty_param"]) == len(nodes)
            assert np.all(np.isnan(result["empty_param"]))
        except (IndexError, ValueError):
            # If the function doesn't handle empty regions, that's a limitation to document
            pytest.skip("Function doesn't handle empty regions - this is a known limitation")

    def test_complex_mesh_multiple_cell_blocks(self, linear_function):
        """Test with more complex mesh having multiple cell blocks"""
        # Create mesh with triangles and quads
        mesh_cells = [
            CellBlock('triangle', np.array([
                [0, 1, 2],  # Element 1
                [1, 2, 3]   # Element 2
            ])),
            CellBlock('quad', np.array([
                [2, 3, 4, 5],  # Element 3
                [3, 4, 6, 7]   # Element 4
            ]))
        ]

        cell_data_etags = [
            np.array([10, 20]),     # Triangle element IDs
            np.array([30, 40])      # Quad element IDs
        ]

        # Node coordinates
        nodes = np.array([
            [0.0, 0.0],  # Node 0
            [1.0, 0.0],  # Node 1
            [0.5, 1.0],  # Node 2
            [1.5, 1.0],  # Node 3
            [2.0, 0.0],  # Node 4
            [2.5, 1.0],  # Node 5
            [2.0, 2.0],  # Node 6
            [2.5, 2.0]   # Node 7
        ])

        regions = {
            'tri_region': EntitySet(name='tri_region', type='element', items=[10, 20]),    # Triangle elements
            'quad_region': EntitySet(name='quad_region', type='element', items=[30, 40])    # Quad elements
        }

        # Create distributions
        tri_dist = Distribution(
            name="tri_param",
            region="tri_region",
            func_base=linear_function,
            vtype="float"
        )
        tri_dist._function = [linear_function]

        quad_dist = Distribution(
            name="quad_param",
            region="quad_region",
            func_base=linear_function,
            vtype="float"
        )
        quad_dist._function = [linear_function]

        distributions = [tri_dist, quad_dist]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "tri_param" in result
        assert "quad_param" in result

        # Check that results are numpy arrays with same length as nodes
        assert isinstance(result["tri_param"], np.ndarray)
        assert isinstance(result["quad_param"], np.ndarray)
        assert len(result["tri_param"]) == len(nodes)
        assert len(result["quad_param"]) == len(nodes)

        # Triangle region should have nodes {0,1,2,3}
        expected_tri_nodes = {0, 1, 2, 3}
        # Quad region should have nodes {2,3,4,5,6,7}
        expected_quad_nodes = {2, 3, 4, 5, 6, 7}

        # Check tri_param values
        for node_id in range(len(nodes)):
            if node_id in expected_tri_nodes:
                # Node is in triangle region - should have calculated value
                assert not np.isnan(result["tri_param"][node_id])
            else:
                # Node is not in triangle region - should be NaN
                assert np.isnan(result["tri_param"][node_id])

        # Check quad_param values
        for node_id in range(len(nodes)):
            if node_id in expected_quad_nodes:
                # Node is in quad region - should have calculated value
                assert not np.isnan(result["quad_param"][node_id])
            else:
                # Node is not in quad region - should be NaN
                assert np.isnan(result["quad_param"][node_id])



    def test_parameter_name_consistency(self, linear_function, quadratic_function, simple_mesh_data):
        """Test that parameter names are handled correctly for both string and list names"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data

        # Test string name
        string_name_dist = Distribution(
            name="single_name",
            region="region1",
            func_base=linear_function,
            vtype="float"
        )
        string_name_dist._function = [linear_function]

        # Test list name
        list_name_dist = Distribution(
            name=["param1", "param2"],
            region="region2",
            func_base=None,
            vtype="float"
        )
        list_name_dist._function = [linear_function, quadratic_function]

        distributions = [string_name_dist, list_name_dist]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "single_name" in result
        assert "param1" in result
        assert "param2" in result

        # Verify that each parameter is a numpy array with correct length
        assert isinstance(result["single_name"], np.ndarray)
        assert isinstance(result["param1"], np.ndarray)
        assert isinstance(result["param2"], np.ndarray)
        assert len(result["single_name"]) == len(nodes)
        assert len(result["param1"]) == len(nodes)
        assert len(result["param2"]) == len(nodes)

    def test_nonexistent_region(self, linear_function, simple_mesh_data):
        """Test with distribution referencing non-existent region"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data

        # Create distribution for non-existent region
        bad_dist = Distribution(
            name="bad_param",
            region="nonexistent_region",
            func_base=linear_function,
            vtype="float"
        )
        bad_dist._function = [linear_function]

        distributions = [bad_dist]

        # Should raise KeyError when trying to access non-existent region
        with pytest.raises(KeyError):
            eval_distributions_on_region_nodes(
                distributions, mesh_cells, cell_data_etags, nodes, regions
            )

    def test_empty_mesh(self, linear_function):
        """Test with empty mesh data"""
        mesh_cells = []
        cell_data_etags = []
        nodes = np.array([]).reshape(0, 2)  # Empty nodes array with correct shape
        regions = {'empty_region': EntitySet(name='empty_region', type='element', items=[])}

        dist = Distribution(
            name="test_param",
            region="empty_region",
            func_base=linear_function,
            vtype="float"
        )
        dist._function = [linear_function]
        distributions = [dist]

        # The function should handle empty mesh gracefully
        # This might raise an error if the function doesn't handle empty regions properly
        try:
            result = eval_distributions_on_region_nodes(
                distributions, mesh_cells, cell_data_etags, nodes, regions
            )

            assert isinstance(result, dict)
            assert "test_param" in result
            # Should return empty array for empty mesh
            assert isinstance(result["test_param"], np.ndarray)
            assert len(result["test_param"]) == 0
        except (IndexError, ValueError):
            # If the function doesn't handle empty mesh, that's a limitation to document
            pytest.skip("Function doesn't handle empty mesh - this is a known limitation")

    def test_single_node_element(self, linear_function):
        """Test with elements that reference single nodes (point elements)"""
        # Create mesh with point elements
        mesh_cells = [
            CellBlock('vertex', np.array([
                [0],  # Point element at node 0
                [1],  # Point element at node 1
                [2]   # Point element at node 2
            ]))
        ]

        cell_data_etags = [
            np.array([1, 2, 3])
        ]

        nodes = np.array([
            [0.0],  # Node 0
            [1.0],  # Node 1
            [2.0]   # Node 2
        ])

        regions = {
            'point_region': EntitySet(name='point_region', type='element', items=[1, 3])  # Elements 1 and 3
        }

        dist = Distribution(
            name="point_param",
            region="point_region",
            func_base=linear_function,
            vtype="float"
        )
        dist._function = [linear_function]
        distributions = [dist]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "point_param" in result

        # Check that result is a numpy array with same length as nodes
        assert isinstance(result["point_param"], np.ndarray)
        assert len(result["point_param"]) == len(nodes)

        # Should have nodes 0 and 2 (from elements 1 and 3)
        expected_nodes = {0, 2}

        # Check values
        for node_id in range(len(nodes)):
            if node_id in expected_nodes:
                # Node is in region - should have calculated value
                expected_value = 2 * nodes[node_id, 0] + 1
                assert np.isclose(result["point_param"][node_id], expected_value)
            else:
                # Node is not in region - should be NaN
                assert np.isnan(result["point_param"][node_id])

    def test_overlapping_regions(self, linear_function, quadratic_function, simple_mesh_data):
        """Test with distributions that have overlapping regions"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data

        # Both distributions use same parameter name but different regions
        dist1 = Distribution(
            name="overlap_param",
            region="region1",  # Elements 1,2 -> nodes 0,1,2,3
            func_base=linear_function,
            vtype="float"
        )
        dist1._function = [linear_function]

        dist2 = Distribution(
            name="overlap_param",
            region="region2",  # Element 3 -> nodes 2,3,4
            func_base=quadratic_function,
            vtype="float"
        )
        dist2._function = [quadratic_function]

        distributions = [dist1, dist2]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "overlap_param" in result

        # Check that result is a numpy array with same length as nodes
        assert isinstance(result["overlap_param"], np.ndarray)
        assert len(result["overlap_param"]) == len(nodes)

        # Should have all nodes from both regions: {0,1,2,3,4}
        expected_nodes = {0, 1, 2, 3, 4}

        # Check values for all nodes
        for node_id in range(len(nodes)):
            if node_id in expected_nodes:
                # Node is in at least one region - should have calculated value
                assert not np.isnan(result["overlap_param"][node_id])

                x_coord = nodes[node_id, 0]
                if node_id in [2, 3]:
                    # Nodes 2,3 are in both regions - the second distribution should overwrite
                    # Check that nodes 2,3 have quadratic values (from dist2)
                    expected_value = x_coord**2  # quadratic function
                    assert np.isclose(result["overlap_param"][node_id], expected_value)
                elif node_id in [0, 1]:
                    # Nodes 0,1 should have linear values (from dist1 only)
                    expected_value = 2 * x_coord + 1  # linear function
                    assert np.isclose(result["overlap_param"][node_id], expected_value)
                elif node_id == 4:
                    # Node 4 should have quadratic value (from dist2 only)
                    expected_value = x_coord**2  # quadratic function
                    assert np.isclose(result["overlap_param"][node_id], expected_value)
            else:
                # Node is not in any region - should be NaN
                assert np.isnan(result["overlap_param"][node_id])

    def test_return_type_and_structure(self, single_param_distribution, simple_mesh_data):
        """Test that return type and structure are correct"""
        mesh_cells, cell_data_etags, nodes, regions = simple_mesh_data
        distributions = [single_param_distribution]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        # Check return type
        assert isinstance(result, dict)

        # Check parameter structure
        assert "thickness" in result
        assert isinstance(result["thickness"], np.ndarray)
        assert len(result["thickness"]) == len(nodes)

        # Check that all values are numeric (float or NaN)
        for value in result["thickness"]:
            assert isinstance(value, (float, np.floating)) or np.isnan(value)

    def test_entity_set_structure(self, linear_function):
        """Test that the function correctly handles EntitySet objects"""
        # Create simple mesh
        mesh_cells = [
            CellBlock('triangle', np.array([
                [0, 1, 2],  # Element 1
                [1, 2, 3]   # Element 2
            ]))
        ]

        cell_data_etags = [
            np.array([10, 20])  # Element IDs
        ]

        nodes = np.array([
            [0.0, 0.0],  # Node 0
            [1.0, 0.0],  # Node 1
            [0.5, 1.0],  # Node 2
            [1.5, 1.0]   # Node 3
        ])

        # Create EntitySet with specific attributes
        test_entity_set = EntitySet(
            name="test_region",
            type="element",
            items=[10, 20]
        )

        regions = {
            'test_region': test_entity_set
        }

        # Create distribution
        dist = Distribution(
            name="test_param",
            region="test_region",
            func_base=linear_function,
            vtype="float"
        )
        dist._function = [linear_function]
        distributions = [dist]

        # Test that function works with EntitySet
        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "test_param" in result

        # Check that result is a numpy array with same length as nodes
        assert isinstance(result["test_param"], np.ndarray)
        assert len(result["test_param"]) == len(nodes)

        # Should have all nodes from both elements: {0, 1, 2, 3}
        expected_nodes = {0, 1, 2, 3}

        # Verify that the EntitySet.items attribute was correctly accessed
        # by checking that the correct elements were processed
        for node_id in range(len(nodes)):
            if node_id in expected_nodes:
                # Node is in region - should have calculated value
                expected_value = 2 * nodes[node_id, 0] + 1  # linear function: 2x + 1
                assert np.isclose(result["test_param"][node_id], expected_value)
            else:
                # Node is not in region - should be NaN
                assert np.isnan(result["test_param"][node_id])

    def test_entity_set_with_different_types(self, linear_function):
        """Test EntitySet objects with different types (though function expects 'element')"""
        mesh_cells = [
            CellBlock('triangle', np.array([
                [0, 1, 2]  # Element 1
            ]))
        ]

        cell_data_etags = [
            np.array([5])  # Element ID
        ]

        nodes = np.array([
            [0.0, 0.0],  # Node 0
            [1.0, 0.0],  # Node 1
            [0.5, 1.0]   # Node 2
        ])

        # Create EntitySet with 'element' type (correct for this function)
        element_set = EntitySet(
            name="element_region",
            type="element",  # This is what the function expects
            items=[5]
        )

        regions = {
            'element_region': element_set
        }

        dist = Distribution(
            name="test_param",
            region="element_region",
            func_base=linear_function,
            vtype="float"
        )
        dist._function = [linear_function]
        distributions = [dist]

        result = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, nodes, regions
        )

        assert isinstance(result, dict)
        assert "test_param" in result

        # Check that result is a numpy array with same length as nodes
        assert isinstance(result["test_param"], np.ndarray)
        assert len(result["test_param"]) == len(nodes)  # Same length as nodes array

        # All 3 nodes should have values since they're all in the single triangle element
        for node_id in range(len(nodes)):
            assert not np.isnan(result["test_param"][node_id])

if __name__ == "__main__":
    pytest.main([__file__])
