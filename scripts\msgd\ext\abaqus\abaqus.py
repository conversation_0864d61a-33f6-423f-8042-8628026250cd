import io
import logging
import math
import platform
import subprocess as sbp
from contextlib import redirect_stdout

import meshio
from sgio import inprw

import msgd._global as GLOBAL
import msgd.utils as mutils
# from msgd.core._structure import StructureModel
from msgd.core._analysis import AnalysisStep

logger = logging.getLogger(__name__)


# class AbaqusStructureImporter():
#     def importFromFile(fn:str) -> MSGDStructure:
#         return


class AbaqusAnalysisStep(AnalysisStep):
    step_type = 'abaqus'
    def __init__(
        self, name='', activate:bool=True, parameters:dict={},
        setting:dict={}, step_args:list=[], step_kwargs:dict={},
        post_process:list=[], step_output_file:str='abaqus_step_output.dat'
        ):
        super().__init__(
            name=name,
            activate=activate,
            parameters=parameters,
            setting=setting,
            step_args=step_args,
            step_kwargs=step_kwargs
            )

        # Temporary
        self._post_process = post_process
        self._step_output_file = step_output_file

    def toDictionary(self):
        _dict = super().toDictionary()
        _dict['type'] = type(self).step_type
        _dict['post_process'] = self._post_process
        _dict['step_output_file'] = self._step_output_file
        return _dict
 

    def run(self, structure_model, **kwargs):
        logger.info(f'[step: {self._name}] running abaqus analysis step...')

        # print(f'self.step_kwargs: {self.step_kwargs}')

        # Subsitute parametes
        mutils.substituteParams(
            self._post_process, structure_model.design.parameters
        )
        # print(f'post_process: {self._post_process}')

        abq_job = AbaqusJob(
            name=self._name,
            fn_model=structure_model.fn_model,
            mdb=structure_model,
            postpros=self._post_process,
            exec_args=self.step_kwargs.get('exec', {}).get('args', []),
            exec_kwargs=self.step_kwargs.get('exec', {}).get('kwargs', {})
            )

        timeout = self._setting.get('timeout', 600)

        if len(structure_model.orient_sets) == 0:
            structure_model.orient_sets['default'] = structure_model.configs['orient_name']

        # Write section properties to a .inp file
        _prop_sets = {}
        for _set_name, _sg in structure_model.sg_model_sets.items():
            _prop_sets[_set_name] = _sg.constitutive

        # print(structure_model.entity_sets)
        # print(structure_model.elem_sets)

        writeAbaqusSectionsInp(
            fn=structure_model.fn_prop,
            prop_sets=_prop_sets,
            elem_sets=structure_model.elem_sets,
            orient_sets=structure_model.orient_sets,
            physics=structure_model.physics,
        )

        # Run structural analysis
        abq_job.run(timeout=timeout)
        abq_job.postProcess(timeout=timeout)

        try:
            with open(self._step_output_file, 'r') as file:
                for line in file.readlines():
                    line = line.strip()
                    if line == '':
                        continue
                    line = line.split('=')
                    self._step_output[line[0].strip()] = float(line[1].strip())
        except KeyError:
            pass

        # print('step output:\n', self._step_output)

        return


# class AbaqusDesignAnalysis(DesignAnalysis):

#     class_name = 'abaqus'

#     def __init__(self):
#         super().__init__()

#         self.fn_job = ''
#         self.command_args = []

#         """
#         postpros = [
#             {
#                 'lang': 'python',
#                 'file': 'script.py'
#                 'args': [],
#                 'kwargs': {}
#             },
#             ...
#         ]
#         """
#         self.postpros = []

#     def run(self):
#         if self.logger:
#             self.logger('running...')

#         cmd = ['abaqus', 'job={}'.format(self.fn_job)] + self.command_args
#         self.logger.debug(' '.join(cmd))

#         print()
#         sbp.call(cmd, shell=True)
#         print()

#         # Post process
#         for pp in self.postpros:

#             cmd = ['abaqus', 'python', pp['script']] + pp['args']
#             self.logger.debug(' '.join(cmd))

#             print()
#             sbp.call(cmd, shell=True)
#             print()









# class AbaqusModel():
#     def __init__(self, name=''):
#         super().__init__(name)

#         # self.name = name

#         self.tool = 'abaqus'

#         # self.nodes = {}  # nodal coords in the global inertial frame {nid1: [coord1, coord2, coord3], ...}
#         self.nodes_t = {}  # nodal coords in the local surface frame
#         # self.elements = {}  # {eid1: [nid1, nid2, nid3, nid4, ...], ...}
#         self.sections = {}
#         # self.element_sets = {}
#         self.orient = {}
#         self.eid_to_set = {}

#         #
#         self.sect_prop_sets = {}
#         self.sect_elem_sets = {}

#         # self.includes = []
#         self.fn_section = ''


#     def summary(self):
#         if logger:
#             printer = logger.debug
#         else:
#             printer = print

#         printer('summary of {}'.format(self.name))
#         printer('nodes')
#         printer(self.nodes)
#         printer('elements')
#         printer(self.elements)
#         printer('element sets')
#         printer(self.element_sets)
#         printer('sections')
#         printer(self.sections)
#         printer('orientations')
#         printer(self.orient)


#     def calcElementCentroidT(self, ei, trans=None):
#         xc = [0, 0, 0]

#         ecnnt = self.elements[ei]
#         # if ei == 400:
#         #     print('element', ei, ':', ecnnt)
#         for ni in ecnnt:

#             try:
#                 ni_coords = self.nodes_t[ni]
#             except KeyError:
#                 ni_coords = self.nodes[ni]
#                 if trans:
#                     ni_coords = trans(ni_coords)
#                 self.nodes_t[ni] = ni_coords

#             # if ei == 400:
#             #     print('node', ni, ':', self.nodes[ni], '->', ni_coords)

#             xc[0] += ni_coords[0]
#             xc[1] += ni_coords[1]
#             xc[2] += ni_coords[2]

#         xc = [xi / len(ecnnt) for xi in xc]

#         return xc









class AbaqusJob():
    def __init__(
        self, name:str='', fn_model:str='', mdb=None, odb=None,
        postpros:list=[], exec_args=[], exec_kwargs={}):
        self._name = name
        self._fn_model = fn_model
        self._mdb = mdb
        self._odb = odb
        self._postpros = postpros

        # Execution arguments
        self._args = exec_args
        self._kwargs = exec_kwargs


    def writeSectionsInp(
            self, fn, set_base_name='ESET', physics='elastic',
            abd_request=0, geo_correct=None):

        if abd_request == 0:
            abd_type = 'classical'
        elif abd_request == 1:
            abd_type = 'geometrically refined'
        elif abd_request == 2:
            abd_type = 'transverse shear refined'

        logger.info('writing {} ABD matrix entries to {}...'.format(abd_type, fn))

        self._mdb.summary(logger=logger)

        with open(fn, 'w') as fo:
            fo.write('** Shell section properties for each element set')
            for sg_name, elems in self._mdb.element_sets.items():
                # sn = f'{set_base_name}-{sg_name}'
                sn = '{}-{}'.format(set_base_name, sg_name)
                logger.debug('set name: {}'.format(sn))
                sp = self._mdb.sections[sg_name]
                if abd_request == 0:
                    abd = sp.stff
                elif abd_request == 1:
                    abd = sp.stff_geo
                elif abd_request == 2:
                    abd = sp.stff_rm

                fo.write('\n**\n')
                # fo.write(f'*Elset, elset={sn}\n')
                fo.write('*Elset, elset={}\n'.format(sn))
                for i in range(int(math.ceil(len(elems) / 16))):
                    # Up to 16 entries per line
                    fo.write(', '.join(
                        list(map(str, elems[i*16:(i+1)*16]))
                        ))
                    fo.write('\n')

                # fo.write(f'*Shell General Section, elset={sn}')
                fo.write('*Shell General Section, elset={}'.format(sn))
                if self._mdb.orient:
                    # fo.write(f', orientation={self.mdb.orient}')
                    if len(self._mdb.orient) == 1:
                        fo.write(', orientation={}'.format(list(self._mdb.orient.keys())[0]))
                fo.write('\n')

                # Write stiffness
                k = 0
                list_tmp = []
                for j in range(6):
                    for i in range(0, j+1):
                        k += 1
                        # list_tmp.append(f'{abd[i][j]}')
                        list_tmp.append('{}'.format(abd[i][j]))
                        if k % 8 == 0:
                            fo.write(', '.join(list_tmp))
                            fo.write('\n')
                            list_tmp = []
                fo.write(', '.join(list_tmp))

                # Write thermal properties
                if physics == 'thermoelastic':
                    list_tmp = list(map(str, [
                        -sp.n11_t, -sp.n22_t, -sp.n12_t, -sp.m11_t, -sp.m22_t, -sp.m12_t
                    ]))
                    fo.write('\n')
                    fo.write(', '.join(list_tmp))

                    list_tmp = ['1', '1']
                    fo.write('\n')
                    fo.write(', '.join(list_tmp))

        return


    def run(self, timeout=600):

        cmd = ['abaqus', 'job={}'.format(self._fn_model)]

        print(f'exec_args: {self._args}')
        print(f'exec_kwargs: {self._kwargs}')

        cmd.extend(self._args)
        cmd.extend([f'{k}={v}' for k, v in self._kwargs.items()])

        logger.info(f'[eval {GLOBAL.EVAL_ID}] ' + ' '.join(cmd))

        if platform.system() == 'Windows':

            sbp.run(cmd, shell=True, timeout=timeout)

        elif platform.system() == 'Linux':
            # cmd = ['abq', '-j', self._fn_model, '-interactive']
            # logger.critical(f'[eval {GLOBAL.EVAL_ID}] ' + ' '.join(cmd))
            sbp.run(cmd, timeout=timeout)

        return


    def postProcess(self, timeout=600):
        for pp in self._postpros:

            try:
                args = pp['args']
            except KeyError:
                args = []

            cmd = ['abaqus', 'python', pp['script']] + args
            logger.info(f'[eval {GLOBAL.EVAL_ID}] ' + ' '.join(cmd))

            print()
            if platform.system() == 'Windows':
                sbp.run(cmd, shell=True, timeout=timeout)
            elif platform.system() == 'Linux':
                sbp.run(cmd, timeout=timeout)
            print()

        return


    def report(self, response, fn_output, ndset=None, elset=None):
        cmd = ['abaqus', 'python', 'writeFieldOutput.py']
        # cmd.append('-o')
        # cmd.append(f'{self.mdb.name}')
        cmd += ['-o', self._mdb.name]
        # cmd.append('-r')
        # cmd.append(f'{response}')
        cmd += ['-r', response]
        # cmd.append('-f')
        # cmd.append(f'{fn_output}')
        cmd += ['-f', fn_output]
        # cmd = f'abaqus python writeFieldOutput.py -o {self.mdb.name}'
        # cmd += f' -r {response}'
        # cmd += f' -f {fn_output}'

        if ndset:
            # cmd.append('-n')
            # cmd.append(f'{ndset}')
            cmd += ['-n', ndset]
            # cmd += f' -n {ndset}'
        if elset:
            # cmd.append('-e')
            # cmd.append(f'{elset}')
            cmd += ['-e', elset]
            # cmd += f' -e {elset}'

        logger.debug(' '.join(cmd))

        print()
        sbp.call(cmd, shell=True)
        print()

        return









def run(
    cmd=['abaqus', ], identifier='', cmd_format='long', timeout=600,
    **kwargs
    ):
    """ Run Abaqus command

    Parameters
    ----------
    cmd : list
        Abaqus command
    identifier : str
        Identifier of the command
    cmd_format : str
        Command format
    timeout : int
        Timeout in seconds
    """

    if identifier == 'job':
        cmd.append(f"job={kwargs['job_name']}")
        if kwargs['interactive']:
            cmd.append('interactive')

    elif identifier == 'cae':
        cmd.append('cae')

    logger.critical(f'[eval {GLOBAL.EVAL_ID}] ' + ' '.join(cmd))

    if platform.system() == 'Windows':
        # cmd = ['abaqus', 'job={}'.format(self.mdb.name), 'interactive']
        sbp.run(cmd, shell=True, timeout=timeout)

    elif platform.system() == 'Linux':
        # cmd = ['abq', '-j', self.mdb.name, '-interactive']
        # logger.critical(f'[eval {GLOBAL.EVAL_ID}] ' + ' '.join(cmd))
        sbp.run(cmd, timeout=timeout)

    return




def parseAbaqusInput(fn):
    """Reads a Abaqus inp file."""

    # Parse input file
    inp = inprw.inpRW(fn)

    stdout_capture = io.StringIO()
    with redirect_stdout(stdout_capture):
        inp.parse()
    print()

    inprw_output = stdout_capture.getvalue()
    if inprw_output.strip():
        logger.debug(inprw_output.strip())

    return inp




def readAbaqusInput(fn, **kwargs) -> meshio.Mesh:
    """
    """

    if fn[-4:] != '.inp':
        fn += '.inp'

    logger.info('reading abaqus input file {}...'.format(fn))

    mesh = meshio.read(fn, 'abaqus')

    logger.debug(str(mesh))

    return mesh









def writeAbaqusSectionsInp(
    fn, prop_sets, elem_sets, orient_sets={}, set_base_name='ESET',
    physics='elastic', abd_request=0, geo_correct=None,
    ):
    """
    """

    # if logger:
    if abd_request == 0:
        abd_type = 'classical'
    elif abd_request == 1:
        abd_type = 'geometrically refined'
    elif abd_request == 2:
        abd_type = 'transverse shear refined'

    logger.info('writing {} ABD matrix entries to {}...'.format(abd_type, fn))

    with open(fn, 'w') as fo:
        fo.write('** Shell section properties for each element set')
        for sg_name, elems in elem_sets.items():
            # sn = f'{set_base_name}-{sg_name}'
            sn = '{}-{}'.format(set_base_name, sg_name)
            sp = prop_sets[sg_name]

            if abd_request == 0:
                abd = sp.stff
            elif abd_request == 1:
                abd = sp.stff_geo
            elif abd_request == 2:
                abd = sp.stff_rm

            fo.write('\n**\n')
            # fo.write(f'*Elset, elset={sn}\n')
            fo.write('*Elset, elset={}\n'.format(sn))
            for i in range(int(math.ceil(len(elems) / 16))):
                # Up to 16 entries per line
                fo.write(', '.join(
                    list(map(str, elems[i*16:(i+1)*16]))
                    ))
                fo.write('\n')

            # fo.write(f'*Shell General Section, elset={sn}')
            fo.write('*Shell General Section, elset={}'.format(sn))
            if orient_sets != {}:
                # fo.write(f', orientation={self.mdb.orient}')
                try:
                    fo.write(', orientation={}'.format(orient_sets[sg_name]))
                except KeyError:
                    fo.write(', orientation={}'.format(orient_sets['default']))

            fo.write('\n')

            k = 0
            list_tmp = []
            for j in range(6):
                for i in range(0, j+1):
                    k += 1
                    # list_tmp.append(f'{abd[i][j]}')
                    list_tmp.append('{}'.format(abd[i][j]))
                    if k % 8 == 0:
                        fo.write(', '.join(list_tmp))
                        fo.write('\n')
                        list_tmp = []
            fo.write(', '.join(list_tmp))

            # Write thermal properties
            if physics == 'thermoelastic':
                list_tmp = list(map(str, [
                    -sp.n11_t, -sp.n22_t, -sp.n12_t, -sp.m11_t, -sp.m22_t, -sp.m12_t
                ]))
                fo.write('\n')
                fo.write(', '.join(list_tmp))

                list_tmp = ['1', '1']
                fo.write('\n')
                fo.write(', '.join(list_tmp))

        fo.write('\n')

    return


