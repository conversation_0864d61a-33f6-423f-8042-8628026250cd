# cs_param_study.dakota

environment
  output_file = 'cs_param_study.out'
  write_restart = 'cs_param_study.rst'
  error_file = 'cs_param_study.err'
  tabular_data
    tabular_data_file = 'cs_param_study_tabular.dat'
  results_output
    results_output_file = 'cs_param_study_results'


method
  multidim_parameter_study
    partitions =  2  2


model
  single

variables
  active = design

  continuous_design = 2
    descriptors = 'a2p1'  'a2p3'
    upper_bounds = 0.9  0.6
    lower_bounds = 0.8  0.5




interface
  analysis_driver = 'python run.py cs_param_study.yml 1'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  'run.py'  'cs_param_study.yml'  'airfoil_gbox_uni.xml.tmp'  'material_database_us_ft.xml'  'SC1095.dat'


responses
  descriptors =  'gj'  'eiyy'  'eizz'
  response_functions = 3
  no_gradients
  no_hessians


