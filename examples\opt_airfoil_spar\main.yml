version: "0.10"


# Design parameters/variables of the structure
# mainly the distribution of SG parameters w.r.t. structural coordinates
# ====================================================================

structure:
  name: "blade_1"
  parameter:
    a2p1: 0.8
    a2p3: 0.6
  # design:
  #   dim: 1
  #   cs_assignment:
  #     all: 'main_cs'
  cs:
    - name: 'main_cs'
      design: 'airfoil'
      model:
        type: 'bm2'
        solver: 'vabs'


cs:
- name: "airfoil"
  builder: "prevabs"
  parameter:
    mdb_name: "material_database_us_ft"
    airfoil: 'SC1095.dat'
    airfoil_point_order: -1
    chord: 20.76
    pan2: -1
    pan3: 0
    a2nsm: 0.96
    a3nsm: 0
    rnsm: 0.01
    gms: 0.04
    lam_skin: "T300 15k/976_0.0053"
    lam_cap: "Aluminum 8009_0.01"
    lam_spar: "T300 15k/976_0.0053"
    lam_front: "T300 15k/976_0.0053"
    lam_back: "T300 15k/976_0.0053"
    ssc_spar: "[-45:10/0:10/45:10/90:10]"
    mat_nsm: "lead"
    mat_fill_front: "Rohacell 70"
    mat_fill_back: "Plascore PN2-3/16OX3.0"
    mat_fill_te: "Plascore PN2-3/16OX3.0"
  design:
    base_file: 'airfoil_gbox_uni.xml.tmp'


# Analysis process
# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: 'sg'
      analysis: 'h'

    - step: "calc diff"
      type: "script"
      module: 'data_proc_funcs'  # or file: 'name'?
      function: "analysis_postpro"
      kwargs:
        beam_properties: ["gj", "eiyy", "eizz", 'scy', 'mcy']
        target: [1.74e8, 1.96e8, 3.52e9, -6.38, -4.39]


# Configurations of design study, e.g., parameter study, optimization, etc.
# Mainly for Dakota
# ====================================================================
study:
  method:
    format: "keyword"
    output: "normal"
    # list_parameter_study:
    #   list_of_points: [0.8, 0.6]
    soga:
      max_function_evaluations: 10
      # max_function_evaluations: 2000
      population_size: 5
      # population_size: 50
      seed: 1027
      print_each_pop: true
  variables:
    data_form: "compact"
    data: |
      a2p1, design, continuous, 0.7:0.9
      a2p3, design, continuous, 0.5:0.7
  responses:
    data_form: "compact"
    data: |
      diff_gj,   objective, min, 0.5
      diff_eiyy, objective, min, 0.8
      diff_eizz, objective, min, 0.8
      diff_scy, objective, min, 0.8
      diff_mcy, objective, min, 0.8
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        directory_tag: on
        directory_save: on
    required_files:
      - "sup_files/*"
    # asynchronous:
    #   evaluation_concurrency: 20
    # failure_capture:
    #   recover: [1e12, 1e12, 1e12, 1e12, 1e12]
