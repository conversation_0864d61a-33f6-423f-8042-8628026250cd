version: "0.10"

structure:
  name: "blade"
  cs:
    - name: 'main_cs'
      design: "airfoil"
      model:
        type: "bm1"
        solver: 'vabs'

cs:
  - name: "airfoil"
    builder: "prevabs"
    # builder_cmd: "prevabs160"
    # builder_version: "1.6"
    parameter:
      a2p1: 0.82
      a2p3: 0.58
      mdb_name: "material_database_us_ft"
      airfoil: "SC1095.dat"
      gms: 0.004  # global mesh size
      lam_spar: "cfrp_0.0053"
      seq_spar: "[-45/0/45/90]"
      seq_front: "[0]"
      seq_back: "[0]"
      fill_mat_front: "foam"
      fill_mat_back: "honeycomb"
      mat_nsm: "metal"
    design:
      base_file: "airfoil_simple.xml.tmp"

analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      output:
        value: ["gj", "eiyy", "eizz"]

