*Heading
** Job name: plate400_s4r_10x10 Model name: Model-1
** Generated by: Abaqus/CAE 2017
*Preprint, echo=NO, model=NO, history=NO, contact=NO
**
** PARTS
**
*Part, name=Part-1
*Node
      1,         200.,         200.,           0.
      2,         160.,         200.,           0.
      3,         120.,         200.,           0.
      4,          80.,         200.,           0.
      5,          40.,         200.,           0.
      6,           0.,         200.,           0.
      7,         -40.,         200.,           0.
      8,         -80.,         200.,           0.
      9,        -120.,         200.,           0.
     10,        -160.,         200.,           0.
     11,        -200.,         200.,           0.
     12,         200.,         160.,           0.
     13,         160.,         160.,           0.
     14,         120.,         160.,           0.
     15,          80.,         160.,           0.
     16,          40.,         160.,           0.
     17,           0.,         160.,           0.
     18,         -40.,         160.,           0.
     19,         -80.,         160.,           0.
     20,        -120.,         160.,           0.
     21,        -160.,         160.,           0.
     22,        -200.,         160.,           0.
     23,         200.,         120.,           0.
     24,         160.,         120.,           0.
     25,         120.,         120.,           0.
     26,          80.,         120.,           0.
     27,          40.,         120.,           0.
     28,           0.,         120.,           0.
     29,         -40.,         120.,           0.
     30,         -80.,         120.,           0.
     31,        -120.,         120.,           0.
     32,        -160.,         120.,           0.
     33,        -200.,         120.,           0.
     34,         200.,          80.,           0.
     35,         160.,          80.,           0.
     36,         120.,          80.,           0.
     37,          80.,          80.,           0.
     38,          40.,          80.,           0.
     39,           0.,          80.,           0.
     40,         -40.,          80.,           0.
     41,         -80.,          80.,           0.
     42,        -120.,          80.,           0.
     43,        -160.,          80.,           0.
     44,        -200.,          80.,           0.
     45,         200.,          40.,           0.
     46,         160.,          40.,           0.
     47,         120.,          40.,           0.
     48,          80.,          40.,           0.
     49,          40.,          40.,           0.
     50,           0.,          40.,           0.
     51,         -40.,          40.,           0.
     52,         -80.,          40.,           0.
     53,        -120.,          40.,           0.
     54,        -160.,          40.,           0.
     55,        -200.,          40.,           0.
     56,         200.,           0.,           0.
     57,         160.,           0.,           0.
     58,         120.,           0.,           0.
     59,          80.,           0.,           0.
     60,          40.,           0.,           0.
     61,           0.,           0.,           0.
     62,         -40.,           0.,           0.
     63,         -80.,           0.,           0.
     64,        -120.,           0.,           0.
     65,        -160.,           0.,           0.
     66,        -200.,           0.,           0.
     67,         200.,         -40.,           0.
     68,         160.,         -40.,           0.
     69,         120.,         -40.,           0.
     70,          80.,         -40.,           0.
     71,          40.,         -40.,           0.
     72,           0.,         -40.,           0.
     73,         -40.,         -40.,           0.
     74,         -80.,         -40.,           0.
     75,        -120.,         -40.,           0.
     76,        -160.,         -40.,           0.
     77,        -200.,         -40.,           0.
     78,         200.,         -80.,           0.
     79,         160.,         -80.,           0.
     80,         120.,         -80.,           0.
     81,          80.,         -80.,           0.
     82,          40.,         -80.,           0.
     83,           0.,         -80.,           0.
     84,         -40.,         -80.,           0.
     85,         -80.,         -80.,           0.
     86,        -120.,         -80.,           0.
     87,        -160.,         -80.,           0.
     88,        -200.,         -80.,           0.
     89,         200.,        -120.,           0.
     90,         160.,        -120.,           0.
     91,         120.,        -120.,           0.
     92,          80.,        -120.,           0.
     93,          40.,        -120.,           0.
     94,           0.,        -120.,           0.
     95,         -40.,        -120.,           0.
     96,         -80.,        -120.,           0.
     97,        -120.,        -120.,           0.
     98,        -160.,        -120.,           0.
     99,        -200.,        -120.,           0.
    100,         200.,        -160.,           0.
    101,         160.,        -160.,           0.
    102,         120.,        -160.,           0.
    103,          80.,        -160.,           0.
    104,          40.,        -160.,           0.
    105,           0.,        -160.,           0.
    106,         -40.,        -160.,           0.
    107,         -80.,        -160.,           0.
    108,        -120.,        -160.,           0.
    109,        -160.,        -160.,           0.
    110,        -200.,        -160.,           0.
    111,         200.,        -200.,           0.
    112,         160.,        -200.,           0.
    113,         120.,        -200.,           0.
    114,          80.,        -200.,           0.
    115,          40.,        -200.,           0.
    116,           0.,        -200.,           0.
    117,         -40.,        -200.,           0.
    118,         -80.,        -200.,           0.
    119,        -120.,        -200.,           0.
    120,        -160.,        -200.,           0.
    121,        -200.,        -200.,           0.
*Element, type=S4R
  1,   1,   2,  13,  12
  2,   2,   3,  14,  13
  3,   3,   4,  15,  14
  4,   4,   5,  16,  15
  5,   5,   6,  17,  16
  6,   6,   7,  18,  17
  7,   7,   8,  19,  18
  8,   8,   9,  20,  19
  9,   9,  10,  21,  20
 10,  10,  11,  22,  21
 11,  12,  13,  24,  23
 12,  13,  14,  25,  24
 13,  14,  15,  26,  25
 14,  15,  16,  27,  26
 15,  16,  17,  28,  27
 16,  17,  18,  29,  28
 17,  18,  19,  30,  29
 18,  19,  20,  31,  30
 19,  20,  21,  32,  31
 20,  21,  22,  33,  32
 21,  23,  24,  35,  34
 22,  24,  25,  36,  35
 23,  25,  26,  37,  36
 24,  26,  27,  38,  37
 25,  27,  28,  39,  38
 26,  28,  29,  40,  39
 27,  29,  30,  41,  40
 28,  30,  31,  42,  41
 29,  31,  32,  43,  42
 30,  32,  33,  44,  43
 31,  34,  35,  46,  45
 32,  35,  36,  47,  46
 33,  36,  37,  48,  47
 34,  37,  38,  49,  48
 35,  38,  39,  50,  49
 36,  39,  40,  51,  50
 37,  40,  41,  52,  51
 38,  41,  42,  53,  52
 39,  42,  43,  54,  53
 40,  43,  44,  55,  54
 41,  45,  46,  57,  56
 42,  46,  47,  58,  57
 43,  47,  48,  59,  58
 44,  48,  49,  60,  59
 45,  49,  50,  61,  60
 46,  50,  51,  62,  61
 47,  51,  52,  63,  62
 48,  52,  53,  64,  63
 49,  53,  54,  65,  64
 50,  54,  55,  66,  65
 51,  56,  57,  68,  67
 52,  57,  58,  69,  68
 53,  58,  59,  70,  69
 54,  59,  60,  71,  70
 55,  60,  61,  72,  71
 56,  61,  62,  73,  72
 57,  62,  63,  74,  73
 58,  63,  64,  75,  74
 59,  64,  65,  76,  75
 60,  65,  66,  77,  76
 61,  67,  68,  79,  78
 62,  68,  69,  80,  79
 63,  69,  70,  81,  80
 64,  70,  71,  82,  81
 65,  71,  72,  83,  82
 66,  72,  73,  84,  83
 67,  73,  74,  85,  84
 68,  74,  75,  86,  85
 69,  75,  76,  87,  86
 70,  76,  77,  88,  87
 71,  78,  79,  90,  89
 72,  79,  80,  91,  90
 73,  80,  81,  92,  91
 74,  81,  82,  93,  92
 75,  82,  83,  94,  93
 76,  83,  84,  95,  94
 77,  84,  85,  96,  95
 78,  85,  86,  97,  96
 79,  86,  87,  98,  97
 80,  87,  88,  99,  98
 81,  89,  90, 101, 100
 82,  90,  91, 102, 101
 83,  91,  92, 103, 102
 84,  92,  93, 104, 103
 85,  93,  94, 105, 104
 86,  94,  95, 106, 105
 87,  95,  96, 107, 106
 88,  96,  97, 108, 107
 89,  97,  98, 109, 108
 90,  98,  99, 110, 109
 91, 100, 101, 112, 111
 92, 101, 102, 113, 112
 93, 102, 103, 114, 113
 94, 103, 104, 115, 114
 95, 104, 105, 116, 115
 96, 105, 106, 117, 116
 97, 106, 107, 118, 117
 98, 107, 108, 119, 118
 99, 108, 109, 120, 119
100, 109, 110, 121, 120
*Nset, nset=Set-1, generate
   1,  121,    1
*Elset, elset=Set-1, generate
   1,  100,    1
*Nset, nset=nset_nx, generate
  11,  121,   11
*Nset, nset=nset_px, generate
   1,  111,   11
*Nset, nset=nset_ny, generate
 111,  121,    1
*Nset, nset=nset_py, generate
  1,  11,   1
*Elset, elset=_Surf-1_SPOS, internal, generate
   1,  100,    1
*Surface, type=ELEMENT, name=Surf-1
_Surf-1_SPOS, SPOS
*Distribution, name=Ori-1-DiscOrient, location=ELEMENT, Table=Ori-1-DiscOrient_Table
** Description: Distribution generated from Discrete Orientation
,           1.,           0.,           0.,           0.,           1.,           0.
1,           1.,           0.,           0.,           0.,           1.,           0.
2,           1.,           0.,           0.,           0.,           1.,           0.
3,           1.,           0.,           0.,           0.,           1.,           0.
4,           1.,           0.,           0.,           0.,           1.,           0.
5,           1.,           0.,           0.,           0.,           1.,           0.
6,           1.,           0.,           0.,           0.,           1.,           0.
7,           1.,           0.,           0.,           0.,           1.,           0.
8,           1.,           0.,           0.,           0.,           1.,           0.
9,           1.,           0.,           0.,           0.,           1.,           0.
10,           1.,           0.,           0.,           0.,           1.,           0.
11,           1.,           0.,           0.,           0.,           1.,           0.
12,           1.,           0.,           0.,           0.,           1.,           0.
13,           1.,           0.,           0.,           0.,           1.,           0.
14,           1.,           0.,           0.,           0.,           1.,           0.
15,           1.,           0.,           0.,           0.,           1.,           0.
16,           1.,           0.,           0.,           0.,           1.,           0.
17,           1.,           0.,           0.,           0.,           1.,           0.
18,           1.,           0.,           0.,           0.,           1.,           0.
19,           1.,           0.,           0.,           0.,           1.,           0.
20,           1.,           0.,           0.,           0.,           1.,           0.
21,           1.,           0.,           0.,           0.,           1.,           0.
22,           1.,           0.,           0.,           0.,           1.,           0.
23,           1.,           0.,           0.,           0.,           1.,           0.
24,           1.,           0.,           0.,           0.,           1.,           0.
25,           1.,           0.,           0.,           0.,           1.,           0.
26,           1.,           0.,           0.,           0.,           1.,           0.
27,           1.,           0.,           0.,           0.,           1.,           0.
28,           1.,           0.,           0.,           0.,           1.,           0.
29,           1.,           0.,           0.,           0.,           1.,           0.
30,           1.,           0.,           0.,           0.,           1.,           0.
31,           1.,           0.,           0.,           0.,           1.,           0.
32,           1.,           0.,           0.,           0.,           1.,           0.
33,           1.,           0.,           0.,           0.,           1.,           0.
34,           1.,           0.,           0.,           0.,           1.,           0.
35,           1.,           0.,           0.,           0.,           1.,           0.
36,           1.,           0.,           0.,           0.,           1.,           0.
37,           1.,           0.,           0.,           0.,           1.,           0.
38,           1.,           0.,           0.,           0.,           1.,           0.
39,           1.,           0.,           0.,           0.,           1.,           0.
40,           1.,           0.,           0.,           0.,           1.,           0.
41,           1.,           0.,           0.,           0.,           1.,           0.
42,           1.,           0.,           0.,           0.,           1.,           0.
43,           1.,           0.,           0.,           0.,           1.,           0.
44,           1.,           0.,           0.,           0.,           1.,           0.
45,           1.,           0.,           0.,           0.,           1.,           0.
46,           1.,           0.,           0.,           0.,           1.,           0.
47,           1.,           0.,           0.,           0.,           1.,           0.
48,           1.,           0.,           0.,           0.,           1.,           0.
49,           1.,           0.,           0.,           0.,           1.,           0.
50,           1.,           0.,           0.,           0.,           1.,           0.
51,           1.,           0.,           0.,           0.,           1.,           0.
52,           1.,           0.,           0.,           0.,           1.,           0.
53,           1.,           0.,           0.,           0.,           1.,           0.
54,           1.,           0.,           0.,           0.,           1.,           0.
55,           1.,           0.,           0.,           0.,           1.,           0.
56,           1.,           0.,           0.,           0.,           1.,           0.
57,           1.,           0.,           0.,           0.,           1.,           0.
58,           1.,           0.,           0.,           0.,           1.,           0.
59,           1.,           0.,           0.,           0.,           1.,           0.
60,           1.,           0.,           0.,           0.,           1.,           0.
61,           1.,           0.,           0.,           0.,           1.,           0.
62,           1.,           0.,           0.,           0.,           1.,           0.
63,           1.,           0.,           0.,           0.,           1.,           0.
64,           1.,           0.,           0.,           0.,           1.,           0.
65,           1.,           0.,           0.,           0.,           1.,           0.
66,           1.,           0.,           0.,           0.,           1.,           0.
67,           1.,           0.,           0.,           0.,           1.,           0.
68,           1.,           0.,           0.,           0.,           1.,           0.
69,           1.,           0.,           0.,           0.,           1.,           0.
70,           1.,           0.,           0.,           0.,           1.,           0.
71,           1.,           0.,           0.,           0.,           1.,           0.
72,           1.,           0.,           0.,           0.,           1.,           0.
73,           1.,           0.,           0.,           0.,           1.,           0.
74,           1.,           0.,           0.,           0.,           1.,           0.
75,           1.,           0.,           0.,           0.,           1.,           0.
76,           1.,           0.,           0.,           0.,           1.,           0.
77,           1.,           0.,           0.,           0.,           1.,           0.
78,           1.,           0.,           0.,           0.,           1.,           0.
79,           1.,           0.,           0.,           0.,           1.,           0.
80,           1.,           0.,           0.,           0.,           1.,           0.
81,           1.,           0.,           0.,           0.,           1.,           0.
82,           1.,           0.,           0.,           0.,           1.,           0.
83,           1.,           0.,           0.,           0.,           1.,           0.
84,           1.,           0.,           0.,           0.,           1.,           0.
85,           1.,           0.,           0.,           0.,           1.,           0.
86,           1.,           0.,           0.,           0.,           1.,           0.
87,           1.,           0.,           0.,           0.,           1.,           0.
88,           1.,           0.,           0.,           0.,           1.,           0.
89,           1.,           0.,           0.,           0.,           1.,           0.
90,           1.,           0.,           0.,           0.,           1.,           0.
91,           1.,           0.,           0.,           0.,           1.,           0.
92,           1.,           0.,           0.,           0.,           1.,           0.
93,           1.,           0.,           0.,           0.,           1.,           0.
94,           1.,           0.,           0.,           0.,           1.,           0.
95,           1.,           0.,           0.,           0.,           1.,           0.
96,           1.,           0.,           0.,           0.,           1.,           0.
97,           1.,           0.,           0.,           0.,           1.,           0.
98,           1.,           0.,           0.,           0.,           1.,           0.
99,           1.,           0.,           0.,           0.,           1.,           0.
100,           1.,           0.,           0.,           0.,           1.,           0.
*Orientation, name=Ori-1, system=RECTANGULAR
Ori-1-DiscOrient
3, 0.
** ** Section: Section-1
** *Shell Section, elset=Set-1, material=Material-1, orientation=Ori-1
** 1., 5
*Include, input=shellsections.inp
*End Part
**  
**
** ASSEMBLY
**
*Assembly, name=Assembly
**  
*Instance, name=Part-1-1, part=Part-1
*End Instance
**  
*Nset, nset=Set-1, instance=Part-1-1, generate
  11,  121,   11
*Elset, elset=Set-1, instance=Part-1-1, generate
  10,  100,   10
*Nset, nset=Set-2, instance=Part-1-1, generate
 111,  121,    1
*Elset, elset=Set-2, instance=Part-1-1, generate
  91,  100,    1
*Nset, nset=Set-3, instance=Part-1-1, generate
   1,  111,   11
*Elset, elset=Set-3, instance=Part-1-1, generate
  1,  91,  10
*Nset, nset=Set-4, instance=Part-1-1, generate
  1,  11,   1
*Elset, elset=Set-4, instance=Part-1-1, generate
  1,  10,   1
*Elset, elset=_Surf-1_E4, internal, instance=Part-1-1, generate
  1,  91,  10
*Surface, type=ELEMENT, name=Surf-1
_Surf-1_E4, E4
*Elset, elset=_Surf-2_E4, internal, instance=Part-1-1, generate
  1,  91,  10
*Surface, type=ELEMENT, name=Surf-2
_Surf-2_E4, E4
*End Assembly
*Distribution Table, name=Ori-1-DiscOrient_Table
coord3D, coord3D
** ** 
** ** MATERIALS
** ** 
** *Material, name=Material-1
** *Elastic, type=ENGINEERING CONSTANTS
** 100000.,1000.,1000.,  0.3, 0.28, 0.28,1000.,1000.
** 1000.,
** 
** BOUNDARY CONDITIONS
** 
** Name: bc_nx Type: Displacement/Rotation
*Boundary
Set-1, 1, 1
Set-1, 3, 3
Set-1, 4, 4
Set-1, 6, 6
** Name: bc_ny Type: Displacement/Rotation
*Boundary
Set-2, 2, 2
Set-2, 3, 3
Set-2, 5, 5
Set-2, 6, 6
** Name: bc_px Type: Displacement/Rotation
*Boundary
Set-3, 3, 3
Set-3, 4, 4
Set-3, 6, 6
** Name: bc_py Type: Displacement/Rotation
*Boundary
Set-4, 3, 3
Set-4, 5, 5
Set-4, 6, 6
** ----------------------------------------------------------------
** 
** STEP: Step-1
** 
*Step, name=Step-1, nlgeom=NO, perturbation
*Buckle
10, , 18, 30
** 
** BOUNDARY CONDITIONS
** 
** Name: bc_nx Type: Displacement/Rotation
*Boundary, op=NEW, load case=1
Set-1, 1, 1
Set-1, 3, 3
Set-1, 4, 4
Set-1, 6, 6
*Boundary, op=NEW, load case=2
Set-1, 1, 1
Set-1, 3, 3
Set-1, 4, 4
Set-1, 6, 6
** Name: bc_ny Type: Displacement/Rotation
*Boundary, op=NEW, load case=1
Set-2, 2, 2
Set-2, 3, 3
Set-2, 5, 5
Set-2, 6, 6
*Boundary, op=NEW, load case=2
Set-2, 2, 2
Set-2, 3, 3
Set-2, 5, 5
Set-2, 6, 6
** Name: bc_px Type: Displacement/Rotation
*Boundary, op=NEW, load case=1
Set-3, 3, 3
Set-3, 4, 4
Set-3, 6, 6
*Boundary, op=NEW, load case=2
Set-3, 3, 3
Set-3, 4, 4
Set-3, 6, 6
** Name: bc_py Type: Displacement/Rotation
*Boundary, op=NEW, load case=1
Set-4, 3, 3
Set-4, 5, 5
Set-4, 6, 6
*Boundary, op=NEW, load case=2
Set-4, 3, 3
Set-4, 5, 5
Set-4, 6, 6
** 
** LOADS
** 
** Name: load_px_n1   Type: Shell edge load
*Dsload
Surf-2, EDNOR, 1.
** 
** OUTPUT REQUESTS
** 
*Restart, write, frequency=0
** 
** FIELD OUTPUT: F-Output-1
** 
*Output, field, variable=PRESELECT
*End Step
