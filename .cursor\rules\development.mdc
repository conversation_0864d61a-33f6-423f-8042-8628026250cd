---
description: 
globs: 
alwaysApply: false
---
# Development and Testing Guide

This guide covers the development setup and testing procedures for the MSG Design framework.

## Development Setup

1. **Environment Setup**
   - Add `bin` to system PATH
   - Add `scripts` to PYTHONPATH
   - Create a Python virtual environment
   - Install dependencies:
     ```bash
     pip install -r requirements.txt
     ```

2. **Project Structure**
   - [bin/](mdc:bin): Executable scripts and tools
   - [scripts/](mdc:scripts): Python utility scripts
   - [test/](mdc:test): Test cases and validation
   - [doc/](mdc:doc): Documentation

## Testing

1. **Test Directory**
   The [test/](mdc:test) directory contains:
   - Unit tests
   - Integration tests
   - Validation cases

2. **Running Tests**
   - Use the test configuration in [sg_library/test.yml](mdc:sg_library/test.yml)
   - Run tests using the `datc` command with test input files

## Documentation

The [doc/](mdc:doc) directory contains:
- API documentation
- User guides
- Development guides
- Example configurations

## Version Control

- [CHANGELOG.md](mdc:CHANGELOG.md) tracks version history
- [.gitignore](mdc:.gitignore) specifies ignored files
- [.cursorignore](mdc:.cursorignore) specifies Cursor-specific ignored files

