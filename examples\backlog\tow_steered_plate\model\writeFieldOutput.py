"""
writeFieldOutput.py

Usage: abaqus python writeFieldOutput.py -odb odb_name
       -ndset ndset_name
       -elset(optional) elset_name
       -response response_name
       -filename file_name

Requirements:
1. -odb   : Name of the output database.
2. -ndset : Name of the assembly level element set.
3. -elset : Name of the assembly level element set.
            Search will be done only for element belonging
            to this set. If this parameter is not provided,
            search will be performed over the entire model.
4. -response  : Field output label, e.g., 'u'.
5. -filename : File name of the csv tabular data
"""

from odbAccess import openOdb
import sys
# import csv


def main(odb_name, *args):

    ndset = ''
    elset = ''
    field = ''
    fn_result = ''

    argc = len(args)
    i = 0
    while (i < argc):
        if (args[i][:2] == '-n'):
            i += 1
            ndset = args[i]
        elif (args[i][:2] == '-e'):
            i += 1
            elset = args[i]
        elif (args[i][:2] == '-r'):
            i += 1
            field = args[i]
        elif (args[i][:2] == '-f'):
            i += 1
            fn_result = args[i]
        i += 1

    result = getOutputFromOdb(odb_name, ndset, elset, field)

    writeResultToFile(result, fn_result)

    return




def rightTrim(input, suffix):
    if (input.find(suffix) == -1):
        input = input + suffix
    return input




def getOutputFromOdb(odb_name, ndset='', elset='', field=''):
    step_name = 'Step-1'
    print 'reading result from odb: ' + odb_name

    print('ndset:', ndset)
    print('field:', field)

    result = {}

    # print odbname
    odb = openOdb(path=odb_name)

    # Get sets
    # print ndset.upper()
    ndset = odb.rootAssembly.nodeSets[ndset.upper()]

    # Get field outputs
    # print field.upper()
    # print odb.steps.values()
    step = odb.steps[step_name]
    print step.frames
    frame = step.frames[-1]
    field_outputs = frame.fieldOutputs
    field_u = field_outputs[field.upper()]
    field_u_set = field_u.getSubset(region=ndset)

    data_u_set = field_u_set.values[0].data
    # print data_u_set

    labels = field_u_set.componentLabels

    # # Write output
    # with open(fn_output, 'w') as fo:
    #     csv_writer = csv.writer(fo)
    #     csv_writer.writerow(labels)
    #     # csv_writer.writerow(data_u_set)
    #     csv_writer.writerow(["%.16e" % v for v in data_u_set])

    for label, value in zip(labels, data_u_set):
        result[label.lower()] = value

    print 'done'

    odb.close()

    return result




def writeResultToFile(result, file_name):
    print 'writing result to file: ' + file_name

    # Write buckling moment (eigenvalues) and max displacement
    with open(file_name, 'w') as file:
        for k, v in result.items():
            file.write('{} = {}\n'.format(k, v))

    return




if __name__ == '__main__':

    main(sys.argv[1], *sys.argv[2:])

    # odbname = None
    # ndset = ''
    # elset = ''
    # field = ''
    # fn_output = ''

    # argc = len(argv)
    # i = 0
    # while (i < argc):
    #     if (argv[i][:2] == '-o'):
    #         i += 1
    #         odbname = rightTrim(argv[i], '.odb')
    #     elif (argv[i][:2] == '-n'):
    #         i += 1
    #         ndset = argv[i]
    #     elif (argv[i][:2] == '-e'):
    #         i += 1
    #         elset = argv[i]
    #     elif (argv[i][:2] == '-r'):
    #         i += 1
    #         field = argv[i]
    #     elif (argv[i][:2] == '-f'):
    #         i += 1
    #         fn_output = argv[i]
    #     i += 1

    # if not odbname:
    #     print 'ERROR ODB name is not provided'
    #     print __doc__

    # getOutputFromOdb(odbname, ndset, elset, field, fn_output)

