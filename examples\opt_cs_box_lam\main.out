Dakota version 6.18 released May 15 2023.
Repository revision 8ed67325d (2023-05-10) built May 10 2023 20:24:18.
Running serial Dakota executable in serial mode.
Start time: Sat Jun 22 23:50:19 2024

-----------------------
Begin DAKOTA input file
main.dakota
-----------------------
# main.dakota

environment
  output_file = 'main.out'
  write_restart = 'main.rest'
  error_file = 'main.err'
  tabular_data
    tabular_data_file = 'main_tabular.dat'
  results_output
    results_output_file = 'main_results'


method
  soga
    max_function_evaluations =  10
    population_size =  5
    seed =  1027
    print_each_pop


model
  single

variables
  active = design

  continuous_design = 1
    descriptors = 'lyr_ang'
    upper_bounds = 90
    lower_bounds = -90


  discrete_design_range = 1
    descriptors = 'lyr_ply'
    upper_bounds = 8
    lower_bounds = 1




interface
  analysis_driver = 'ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile {PARAMETERS} --resultfile {RESULTS}'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  'main.yml'  'files/*'
      verbatim


responses
  descriptors =  'ea'
  objective_functions = 1
    sense =  'maximize'

  no_gradients
  no_hessians


---------------------
End DAKOTA input file
---------------------

Using Dakota input file 'main.dakota'
Writing new restart file 'main.rest'.

>>>>> Executing environment.

>>>>> Running soga iterator.

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                     -7.1366618854e+01 lyr_ang
                                     1 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 1:
Active set vector = { 1 }
                      2.1955615204e+04 ea



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                      7.0232245857e+00 lyr_ang
                                     3 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 2:
Active set vector = { 1 }
                      6.8531523040e+05 ea



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      8.1054109317e+00 lyr_ang
                                     4 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 3:
Active set vector = { 1 }
                      8.4329907911e+05 ea



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                      5.4782250435e+01 lyr_ang
                                     2 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 4:
Active set vector = { 1 }
                      5.1595759483e+04 ea



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                      5.7204809717e+01 lyr_ang
                                     2 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 5:
Active set vector = { 1 }
                      4.9801392192e+04 ea



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                     -7.1366618854e+01 lyr_ang
                                     6 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 6:
Active set vector = { 1 }
                      1.2732099753e+05 ea



---------------------
Begin Evaluation    7
---------------------
Parameters for evaluation 7:
                      7.0232245857e+00 lyr_ang
                                     2 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 7:
Active set vector = { 1 }
                      4.5971413122e+05 ea



---------------------
Begin Evaluation    8
---------------------
Parameters for evaluation 8:
                      7.0232245857e+00 lyr_ang
                                     8 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 8:
Active set vector = { 1 }
                      1.7713022514e+06 ea



---------------------
Begin Evaluation    9
---------------------
Parameters for evaluation 9:
                      7.0232245857e+00 lyr_ang
                                     6 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 9:
Active set vector = { 1 }
                      1.3453004674e+06 ea



---------------------
Begin Evaluation   10
---------------------
Parameters for evaluation 10:
                      7.0232245857e+00 lyr_ang
                                     4 lyr_ply

blocking fork: ivabs --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile input.in --resultfile output.out

Active response data for evaluation 10:
Active set vector = { 1 }
                      9.0824815643e+05 ea


<<<<< Function evaluation summary: 10 total (10 new, 0 duplicate)
<<<<< Best parameters          =
                      7.0232245857e+00 lyr_ang
                                     8 lyr_ply
<<<<< Best objective function  =
                      1.7713022514e+06
<<<<< Best evaluation ID: 8


<<<<< Iterator soga completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =     27.639 [parent =     27.639, child =          0]
  Total wall clock =     27.639
