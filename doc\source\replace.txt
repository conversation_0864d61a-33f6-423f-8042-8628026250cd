.. |x| replace:: :math:`x`
.. |y| replace:: :math:`y`
.. |z| replace:: :math:`z`
.. |X| replace:: :math:`X`
.. |Y| replace:: :math:`Y`
.. |Z| replace:: :math:`Z`
.. |z1| replace:: :math:`z_1`
.. |z2| replace:: :math:`z_2`
.. |z3| replace:: :math:`z_3`
.. |x1| replace:: :math:`x_1`
.. |x2| replace:: :math:`x_2`
.. |x3| replace:: :math:`x_3`
.. |y1| replace:: :math:`y_1`
.. |y2| replace:: :math:`y_2`
.. |y3| replace:: :math:`y_3`

.. |len_im| replace:: :math:`\mathrm{in}`
.. |len_im_ft| replace:: :math:`\mathrm{ft}`
.. |den_im| replace:: :math:`\mathrm{lb\cdot sec^2/in^4}`
.. |den_im_k| replace:: :math:`10^3\ \mathrm{lb\cdot sec^2/in^4}`
.. |den_im_ft| replace:: :math:`\mathrm{slug/ft^3}`
.. |mod_im| replace:: :math:`\mathrm{psi}`
.. |mod_im_k| replace:: :math:`10^3\ \mathrm{psi}`
.. |mod_im_m| replace:: :math:`10^6\ \mathrm{psi}`
.. |mod_im_ft| replace:: :math:`\mathrm{lbf/ft^2}`
.. |len_si| replace:: :math:`\mathrm{m}`
.. |den_si| replace:: :math:`\mathrm{kg/m^3}`
.. |den_si_k| replace:: :math:`10^3\ \mathrm{kg/m^3}`
.. |mod_si| replace:: :math:`\mathrm{Pa}`
.. |mod_si_k| replace:: :math:`10^3\ \mathrm{Pa}`
.. |mod_si_m| replace:: :math:`\mathrm{MPa}`
.. |mod_si_g| replace:: :math:`\mathrm{GPa}`
.. |stf0_im| replace:: :math:`\mathrm{lbf}`
.. |stf1_im| replace:: :math:`\mathrm{lbf \cdot in}`
.. |stf2_im| replace:: :math:`\mathrm{lbf \cdot in^2}`
.. |stf1_im_ft| replace:: :math:`\mathrm{lbf \cdot ft}`
.. |stf2_im_ft| replace:: :math:`\mathrm{lbf \cdot ft^2}`

.. |e| replace:: :math:`E`
.. |nu| replace:: :math:`\nu`
.. |e1| replace:: :math:`E_{1}`
.. |e2| replace:: :math:`E_{2}`
.. |e3| replace:: :math:`E_{3}`
.. |e1e2| replace:: :math:`E_1=E_2`
.. |e2e3| replace:: :math:`E_2=E_3`
.. |g12| replace:: :math:`G_{12}`
.. |g13| replace:: :math:`G_{13}`
.. |g12g13| replace:: :math:`G_{12}=G_{13}`
.. |g23| replace:: :math:`G_{23}`
.. |nu12| replace:: :math:`\nu_{12}`
.. |nu13| replace:: :math:`\nu_{13}`
.. |nu12nu13| replace:: :math:`\nu_{12}=\nu_{13}`
.. |nu13nu23| replace:: :math:`\nu_{13}=\nu_{23}`
.. |nu23| replace:: :math:`\nu_{23}`

.. |ea| replace:: :math:`EA`
.. |gj| replace:: :math:`GJ`
.. |ei22| replace:: :math:`EI_{22}`
.. |ei33| replace:: :math:`EI_{33}`
.. |eiyy| replace:: :math:`EI_{yy}`
.. |eizz| replace:: :math:`EI_{zz}`

.. |u1| replace:: :math:`u_1`
.. |u2| replace:: :math:`u_2`
.. |u3| replace:: :math:`u_3`
.. |c11| replace:: :math:`C_{11}`
.. |c12| replace:: :math:`C_{12}`
.. |c13| replace:: :math:`C_{13}`
.. |c21| replace:: :math:`C_{21}`
.. |c22| replace:: :math:`C_{22}`
.. |c23| replace:: :math:`C_{23}`
.. |c31| replace:: :math:`C_{31}`
.. |c32| replace:: :math:`C_{32}`
.. |c33| replace:: :math:`C_{33}`
.. |F1| replace:: :math:`F_1`
.. |F2| replace:: :math:`F_2`
.. |F3| replace:: :math:`F_3`
.. |M1| replace:: :math:`M_1`
.. |M2| replace:: :math:`M_2`
.. |M3| replace:: :math:`M_3`
.. |f1| replace:: :math:`f_1`
.. |f2| replace:: :math:`f_2`
.. |f3| replace:: :math:`f_3`
.. |f'1| replace:: :math:`f'_1`
.. |f'2| replace:: :math:`f'_2`
.. |f'3| replace:: :math:`f'_3`
.. |f''1| replace:: :math:`f''_1`
.. |f''2| replace:: :math:`f''_2`
.. |f''3| replace:: :math:`f''_3`
.. |f'''1| replace:: :math:`f'''_1`
.. |f'''2| replace:: :math:`f'''_2`
.. |f'''3| replace:: :math:`f'''_3`
.. |m1| replace:: :math:`m_1`
.. |m2| replace:: :math:`m_2`
.. |m3| replace:: :math:`m_3`
.. |m'1| replace:: :math:`m'_1`
.. |m'2| replace:: :math:`m'_2`
.. |m'3| replace:: :math:`m'_3`
.. |m''1| replace:: :math:`m''_1`
.. |m''2| replace:: :math:`m''_2`
.. |m''3| replace:: :math:`m''_3`
.. |m'''1| replace:: :math:`m'''_1`
.. |m'''2| replace:: :math:`m'''_2`
.. |m'''3| replace:: :math:`m'''_3`

