# main.dakota

environment
  output_file = 'main.out'
  write_restart = 'main.rest'
  error_file = 'main.err'
  tabular_data
    tabular_data_file = 'main_tabular.dat'
  results_output
    results_output_file = 'main_results'


method
  output  normal
  soga
    max_function_evaluations =  10
    population_size =  5
    seed =  1027
    print_each_pop


model
  single

variables
  active = design

  continuous_design = 2
    descriptors = 'a2p1'  'a2p3'
    upper_bounds = 0.9  0.7
    lower_bounds = 0.7  0.5




interface
  analysis_driver = 'ivabs main.yml --mode 1 --paramfile {PARAMETERS} --resultfile {RESULTS} --loglevelcmd info --loglevelfile info --logfile eval.log'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  'main.yml'  'sup_files/*'
      verbatim


responses
  descriptors =  'diff_gj'  'diff_eiyy'  'diff_eizz'  'diff_scy'  'diff_mcy'
  objective_functions = 5
    sense =  'min'  'min'  'min'  'min'  'min'
    weights = 0.5 0.8 0.8 0.8 0.8
  no_gradients
  no_hessians


