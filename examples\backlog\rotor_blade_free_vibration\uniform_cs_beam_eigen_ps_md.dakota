# uniform_cs_beam_eigen_ps_md.dakota

environment
  output_file = 'uniform_cs_beam_eigen_ps_md.out'
  write_restart = 'uniform_cs_beam_eigen_ps_md.rst'
  error_file = 'uniform_cs_beam_eigen_ps_md.err'
  tabular_data
    tabular_data_file = 'uniform_cs_beam_eigen_ps_md_tabular.dat'
  results_output
    results_output_file = 'uniform_cs_beam_eigen_ps_md_results'


method
  output  normal
  multidim_parameter_study
    partitions =  36


model
  single

variables
  active = design

  discrete_design_range = 1
    descriptors = 'ang_spar_1'
    upper_bounds = 90
    lower_bounds = -90




interface
  analysis_driver = 'msgd ./uniform_cs_beam_eigen_ps_md.yml --mode 1 --paramfile {PARAMETERS} --resultfile {RESULTS} --loglevelcmd info --loglevelfile info --logfile eval.log'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  './uniform_cs_beam_eigen_ps_md.yml'  'data/*'
      verbatim


responses
  descriptors =  'eig1'  'eig2'  'eig3'  'eig4'  'eig5'  'eig6'  'eig7'  'eig8'  'eig9'  'eig10'
  objective_functions = 10
    sense =  'max'  'max'  'max'  'max'  'max'  'max'  'max'  'max'  'max'  'max'
    weights = 1 1 1 1 1 1 1 1 1 1
  no_gradients
  no_hessians


