# --------------- Execution Environment -----------------
ABAQUSLM_LICENSE_FILE = <EMAIL>
ABAQUS_LANG = English_United States.1252
ABAQUS_PY_TRANSLATION_DICTIONARY = Configuration/Xresources/en_US/en_US_PyDict.py
ABAQUS_SEQ = 2022_09_28-14.11.55 183150
ABAQUS_TRANSLATION_DICTIONARY = Configuration/Xresources/en_US/en_US_Dict.py
ABA_BLA_LIBRARY_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
ABA_COMMAND = C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMALauncher.exe
ABA_DRIVERNAME = abq2023.bat
ABA_HOME = C:\SIMULIA\EstProducts\2023\win_b64
ABA_LIBRARY_PATHNAME = PATH
ABA_MPI_LIBRARY_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
ABA_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA
ABA_PATH_BASE = C:\SIMULIA\EstProducts\2023\win_b64;
ABQINFO = C:\SIMULIA\EstProducts\2023\win_b64\SMA\info\
ABQLMHANGLIMIT = 0
ABQLMIMPL = FLEXNET
ABQLMQUEUE = 30
ABQLMUSER = tian50
ABQ_DLALLOCATOR = 1
ALLUSERSPROFILE = C:\ProgramData
APPDATA = C:\Users\<USER>\AppData\Roaming
ASL.LOG = Destination=file
CATCOMMANDPATH = C:\SIMULIA\EstProducts\2023\win_b64\code\command
CATDICTIONARYPATH = C:\SIMULIA\EstProducts\2023\win_b64\code\dictionary
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_21944_NGLYLGXONMPFXTMY
COLORTERM = truecolor
COMMONPROGRAMFILES = C:\Program Files\Common Files
COMMONPROGRAMFILES(X86) = C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432 = C:\Program Files\Common Files
COMPUTERNAME = STN-W-XPS
COMSPEC = C:\WINDOWS\system32\cmd.exe
CONDA_DEFAULT_ENV = rnd_py38
CONDA_EXE = C:\Users\<USER>\anaconda3\Scripts\conda.exe
CONDA_PREFIX = C:\Users\<USER>\anaconda3\envs\rnd_py38
CONDA_PREFIX_1 = C:\Users\<USER>\anaconda3
CONDA_PREFIX_2 = C:\Users\<USER>\anaconda3\envs\rnd_py38
CONDA_PROMPT_MODIFIER = (rnd_py38) 
CONDA_PYTHON_EXE = C:\Users\<USER>\anaconda3\python.exe
CONDA_ROOT = C:\Users\<USER>\anaconda3
CONDA_SHLVL = 2
COVERAGE_RCFILE = C:\SIMULIA\EstProducts\2023\win_b64\SMA\site\pyCoverageConfig.env
CUSTOMWORKSPACE = 
CYGWIN = tty
DRIVERDATA = C:\Windows\System32\Drivers\DriverData
DSY_TENANT = OnPremise
FED_DSFLEX_LICENSE_CONFIG = <EMAIL>
FED_LICENSE_SERVER_TYPE = DSFLEX
FOR_DIAGNOSTIC_LOG_FILE = fort.7
FOR_DUMP_CORE_FILE = TRUE
FOR_FORCE_STACK_TRACE = TRUE
FOR_IGNORE_EXCEPTIONS = TRUE
GIT_ASKPASS = c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
GSL_CONFIG = C:\cygwin64\bin
HOMEDRIVE = C:
HOMEPATH = \Users\tian50
KMP_INIT_AT_FORK = FALSE
LANG = en_US.UTF-8
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
LOGONSERVER = \\STN-W-XPS
MKL_NUM_THREADS = 1
MP_NUMBER_OF_THREADS = 1
MSMPI_BENCHMARKS = C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN = C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS = 8
OMP_NUM_THREADS = 1
ONEDRIVE = C:\Users\<USER>\OneDrive - AnalySwift
ONEDRIVECOMMERCIAL = C:\Users\<USER>\OneDrive - AnalySwift
ONEDRIVECONSUMER = C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
OS = Windows_NT
PATH = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_35976;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\site-packages\pywin32_system32;C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs;C:\Users\<USER>\anaconda3\envs\rnd_py38;C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\bin;C:\Users\<USER>\anaconda3\envs\rnd_py38\Scripts;C:\Users\<USER>\anaconda3\condabin;C:\Program Files\Microsoft MPI\Bin;C:\SIMULIA\Commands;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\MiKTeX\miktex\bin\x64;C:\Program Files\PuTTY;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Microsoft VS Code\bin;C:\Windows\Microsoft.NET\Framework\v4.0.30319;C:\Program Files\doxygen\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;;;;;;C:\Program Files (x86)\Wolfram Research\WolframScript;C:\Program Files\dotnet;C:\Program Files\TortoiseSVN\bin;C:\Program Files\nodejs;C:\Program Files\Git\cmd;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Calibre2;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\work\dev\msg-design\bin;C:\Users\<USER>\work\dev\sgio\bin;C:\Program Files\AnalySwift;C:\Program Files\Graphviz\bin;C:\Users\<USER>\program\bff\v1.6;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\program\dakota-6.18\bin;C:\Users\<USER>\program\dakota-6.18\lib;C:\Users\<USER>\program;C:\cygwin64\usr\x86_64-w64-mingw32\sys-root\mingw\bin;C:\cygwin64\usr\x86_64-w64-mingw32\sys-root\mingw\lib;C:\cygwin64\bin;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install\bin;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install\lib;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\lapack_windows\x64;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\TexGen\Python\libxtra\TexGen;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 158 Stepping 9, GenuineIntel
PROCESSOR_LEVEL = 6
PROCESSOR_REVISION = 9e09
PROGRAMDATA = C:\ProgramData
PROGRAMFILES = C:\Program Files
PROGRAMFILES(X86) = C:\Program Files (x86)
PROGRAMW6432 = C:\Program Files
PROJ_LIB = C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\share\proj
PROMPT = (rnd_py38) $P$G
PSMODULEPATH = C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\SIMULIA\EstProducts\2023\win_b64\code\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\lib-tk;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\site-packages;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs;.;C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\Users\<USER>\work\dev\msg-design\scripts;C:\Users\<USER>\work\dev\sgio;C:\Users\<USER>\work\rnd;C:\Users\<USER>\program\dakota-6.18\share\dakota\Python;C:\Program Files\TexGen\Python\libxtra\TexGen;C:\Program Files\TexGen\Python\libxtra
PYTHONUNBUFFERED = nobuffering
PYVERDIRNAME = python2.7
SESSIONNAME = Console
SMASVT_ROOT_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA
SSL_CERT_FILE = C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\ssl\cacert.pem
SYSTEMDRIVE = C:
SYSTEMROOT = C:\WINDOWS
TEMP = C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM = vscode
TERM_PROGRAM_VERSION = 1.89.1
TMP = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_35976
TMPDIR = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_35976
USERDOMAIN = STN-W-XPS
USERDOMAIN_ROAMINGPROFILE = STN-W-XPS
USERNAME = tian50
USERPROFILE = C:\Users\<USER>\Program Files (x86)\Microsoft Visual Studio 14.0\Common7\Tools\
VS90COMNTOOLS = C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build
VSCODE_GIT_ASKPASS_EXTRA_ARGS = 
VSCODE_GIT_ASKPASS_MAIN = c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE = C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE = \\.\pipe\vscode-git-4ff5ccc15f-sock
VSCODE_INJECTION = 1
WINDIR = C:\WINDOWS
ZES_ENABLE_SYSMAN = 1
_CONDA_EXE = C:\Users\<USER>\anaconda3\Scripts\conda.exe
_CONDA_OLD_CHCP = 65001
_CONDA_ROOT = C:\Users\<USER>\anaconda3
_NT_SYMBOL_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
_SG_LICENSE_FILE = 29750@**************
__CONDA_OPENSLL_CERT_FILE_SET = "1"
decfort_dump_flag = TRUE

# --------------- Execution Options -----------------
SIMExt = .sim
abaquslm_license_file = <EMAIL>
academic = RESEARCH
ams = OFF
analysisType = STANDARD
applicationName = analysis
aqua = OFF
auto_calculate = ANALYSIS
auto_convert = ON
beamSectGen = OFF
biorid = OFF
cavityTypes = []
cavparallel = OFF
compile_cpp = ['cl', '/c', '/W0', '/MD', '/TP', '/EHsc', '/DNDEBUG', '/DWIN32', '/DTP_IP', '/D_CONSOLE', '/DNTI', '/DFLT_LIC', '/DOL_DOC', '/D__LIB__', '/DHKS_NT', '/D_WINDOWS_SOURCE', '/DFAR=', '/D_WINDOWS', '/DABQ_WIN86_64', '%P', '/I%I', '/IC:\\SIMULIA\\EstProducts\\2023']
compile_fmu = ['win64CmpWrp', '-m64', '-msvc9', 'cl', '/LD', '/D_WINDOWS', '/TC', '/W0', '/I%I', '/IC:\\SIMULIA\\EstProducts\\2023']
compile_fortran = ['ifort', '/c', '/fpp', '/extend-source', '/DABQ_WIN86_64', '/DABQ_FORTRAN', '/iface:cref', '/recursive', '/Qauto', '/align:array64byte', '/Qpc64', '/Qprec-div', '/Qprec-sqrt', '/Qfma-', '/fp:precise', '/Qimf-arch-consistency:true', '/Qfp-speculation:safe', '/Qprotect-parens', '/Qfp-stack-check', '/reentrancy:threaded', '/QxSSE3', '/QaxAVX', '/include:%I', '/include:C:\\SIMULIA\\EstProducts\\2023', '%P']
complexFrequency = OFF
connect_timeout = 30
contact = OFF
cosimulation = OFF
coupledProcedure = OFF
cpus = 1
cse = OFF
cyclicSymmetryModel = OFF
directCyclic = OFF
direct_port = None
direct_solver = DMP
doc_root = http://help.3ds.com
domains = 1
dsa = OFF
dynStepSenseAdj = OFF
dynamic = OFF
excite = OFF
externalField = OFF
externalFieldCSEAux = OFF
externalFieldExtList = ['.sim', '.SMAManifest']
externalFieldFiles = []
externalFieldSimReader = None
fieldImport = OFF
filPrt = []
fils = []
finitesliding = OFF
flexiblebody = OFF
foundation = ON
freqSimReq = OFF
geostatic = OFF
heatTransfer = OFF
impJobExpVars = {}
importJobList = []
importSim = OFF
importer = OFF
importerParts = OFF
includes = ['shellsections.inp']
indir = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl
initialConditionsFile = OFF
input = plate_sq2_ss_nfx_bck_s4r_40x40_si
inputFormat = INP
interpolExtList = ['.odb', '.sim', '.SMAManifest']
job = plate_sq2_ss_nfx_bck_s4r_40x40_si
keyword_licenses = []
lanczos = OFF
lastmessageonly = OFF
libs = []
license_server_type = FLEXNET
link_exe = ['LINK', '/nologo', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/STACK:20000000', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/LARGEADDRESSAWARE', '/out:%J', '%F', '%M', '%L', '%B', '%O', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
link_sl = ['LINK', '/nologo', '/NOENTRY', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/dll', '/def:%E', '/out:%U', '%F', '%A', '%L', '%B', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
listener_name = None
listener_resource = None
lmsuspend = None
magnetostatic = OFF
massDiffusion = OFF
materialresponse = OFF
message = OFF
messaging_mechanism = DIRECT
modifiedTet = OFF
moldflowFiles = []
moldflowMaterial = OFF
mp_environment_export = ('ABAQUSLM_LICENSE_FILE', 'ABAQUS_CCI_DEBUG', 'ABAQUS_CSE_CURRCONFIGMAPPING', 'ABAQUS_CSE_RELTIMETOLERANCE', 'ABAQUS_DEVL_MODE', 'ABAQUS_LANG', 'ABAQUS_MPF_DIAGNOSTIC_LEVEL', 'ABA_ADM_ALIGNMENT', 'ABA_ADM_MINIMUMDECREASE', 'ABA_ADM_MINIMUMINCREASE', 'ABA_ALL_ADB_IN_TMPDIR', 'ABA_CM_BUFFERING', 'ABA_CM_BUFFERING_LIMIT', 'ABA_CUTOFF_SLAVEFACET_ANGLE', 'ABA_DIRECT_SOLVER_PATH', 'ABA_DMPSOLVER_BWDPARALLELOFF', 'ABA_ELP_SURFACE_SPLIT', 'ABA_ELP_SUSPEND', 'ABA_ENABLE_DYNELEMS', 'ABA_EVOLVING_JOBS', 'ABA_EXT_SIMOUTPUT', 'ABA_GCONT_POOL_SIZE', 'ABA_HOME', 'ABA_INC_DEFAULT', 'ABA_ITERATIVE_SOLVER_VERBOSE', 'ABA_MEMORY_MODE', 'ABA_MPI_MESSAGE_TRACKING', 'ABA_MPI_VERBOSE_LEVEL', 'ABA_NEW_STOS_COHESIVE_APPROX_CONTACT', 'ABA_NEW_STOS_COHESIVE_CONTACT', 'ABA_NUM_INTEGRATION_POINTS_LINE3D', 'ABA_SHARED_SAVEDIR', 'ABA_PATH', 'ABA_PRE_DECOMPOSITION', 'ABA_PRINT_DYNELEMS', 'ABA_REMOVE_OVERCONSTRAINED_TIES', 'ABA_RESOURCE_MONITOR', 'ABA_RESOURCE_USEMALLINFO', 'ABA_RESULTS_OVERLAY', 'ABA_STD_ACTIVATE_CONTACT_FOR_AM', 'ABA_STD_MAX_SEARCH_DISTANCE', 'ABA_SYMBOLIC_GENERALCOLLAPSE', 'ABA_SYMBOLIC_GENERAL_MAXCLIQUERANK', 'ABA_TOSCA_JOB_STALL', 'ABA_TOSCA_OVERLAY', 'ABA_TOSCA_PROTOTYPE', 'ABA_TOSCA_SEQFILES', 'ABA_TOSCA_STALL', 'ABA_UNIT_INDEPENDENT_CONTACT', 'ABA_USE_NEW_STOS_SST_FORMULATION', 'ABA_USE_OLD_SURF_TO_SURF_CONTACT', 'ABA_POTENTIAL_DEV', 'ABA_XPL_DEBUG', 'ABQLMHANGLIMIT', 'ABQLMIMPL', 'ABQLMQUEUE', 'ABQLMUSER', 'ABQ_ACTIVATE_PTK', 'ABQ_BYPASS_UNCONNECTED_REGION_CHECK', 'ABQ_CRTMALLOC', 'ABQ_DATACHECK', 'ABQ_DLALLOCATOR', 'ABQ_PATTERN', 'ABQ_PATTERN_VALUE', 'ABQ_RECOVER', 'ABQ_RESTART', 'ABQ_SKIP_ANALYTIC_SURF_GCONT', 'ABQ_SPLITFILE', 'ABQ_STD_ACCUM_CSLIP', 'ABQ_STD_ACTIVATE_BEAM_ROTATION', 'ABQ_STD_ALLOW_SURFACE_TO_BEAM', 'ABQ_SUPERELASTIC_MODIFIED', 'ABQ_GC_HEAT', 'ABQ_GC_SMALL', 'ABQ_UREG_USE_CONTACT_ELEM', 'ABQ_XFEM_POREPRESSURE', 'ABQ_XPL_DSMABORT', 'ABQ_XPL_PARTITIONSIZE', 'ABQ_XPL_WINDOWDUMP', 'ACML_FAST_MALLOC', 'ACML_FAST_MALLOC_CHUNK_SIZE', 'ACML_FAST_MALLOC_DEBUG', 'ACML_FAST_MALLOC_MAX_CHUNKS', 'ADB_USE_OLDSLDB', 'ADB_USE_NEWSLDB', 'CCI_RENDEZVOUS', 'DOMAIN', 'DOMAIN_CPUS', 'DOUBLE_PRECISION', 'DSLS_AUTH_PATHNAME', 'DSLS_CONFIG', 'FI_PROVIDER', 'FI_PROVIDER_PATH', 'FLEXLM_DIAGNOSTICS', 'FOR0006', 'FOR0064', 'FOR_DISABLE_DIAGNOSTIC_DISPLAY', 'FOR_IGNORE_EXCEPTIONS', 'I_MPI_FABRICS', 'IPATH_NO_CPUAFFINITY', 'LD_PRELOAD', 'MALLOC_MMAP_THRESHOLD_', 'MKL_DYNAMIC', 'MKL_NUM_THREADS', 'MPCCI_CODEID', 'MPCCI_DEBUG', 'MPCCI_JOBID', 'MPCCI_NETDEVICE', 'MPCCI_SERVER', 'MPCCI_TINFO', 'MPC_GANG', 'MPIEXEC_AFFINITY_TABLE', 'MPI_FLAGS', 'MPI_FLUSH_FCACHE', 'MPI_PROPAGATE_TSTP', 'MPI_SOCKBUFSIZE', 'MPI_USE_MALLOPT_MMAP_MAX', 'MPI_USE_MALLOPT_MMAP_THRESHOLD', 'MPI_USE_MALLOPT_SBRK_PROTECTION', 'MPI_WORKDIR', 'MPIR_CVAR_CH4_OFI_TAG_BITS', 'MPIR_CVAR_CH4_OFI_RANK_BITS', 'MP_NUMBER_OF_THREADS', 'MPICH_ND_ZCOPY_THRESHOLD', 'NCPUS', 'OMP_DYNAMIC', 'OMP_NUM_THREADS', 'OUTDIR', 'PAIDUP', 'PARALLEL_METHOD', 'RAIDEV_NDREG_LAZYMEM', 'SMA_PARENT', 'SMA_PLATFORM', 'SMA_WS', 'SIMULIA_COSIN_PATH', 'STD_INITSTRESS_FLAG', 'STD_INITGEOICS_FLAG', 'XPL_HMP_COMMTHREAD')
mp_file_system = (DETECT, DETECT)
mp_mode = THREADS
mp_mode_requested = MPI
mp_mpiCommand = []
mp_mpi_implementation = NATIVE
mp_mpi_searchpath = ['Microsoft MPI', 'Microsoft HPC Pack', 'Microsoft HPC Pack 2008 R2', 'Microsoft HPC Pack 2008', 'Microsoft HPC Pack 2008 SDK', 'Microsoft HPC Pack 2012']
mp_mpirun_path = C:\Program Files\Microsoft MPI\bin\mpiexec.exe
mp_rsh_command = dummy %H -l %U -n %C
multiphysics = OFF
noDmpDirect = []
noMultiHost = []
noMultiHostElemLoop = ['buckle']
no_domain_check = 1
onCaeGraphicsStartup = <function onCaeGraphicsStartup at 0x000002699243F518>
onJobCompletion = []
onJobStartup = []
onestepinverse = OFF
outdir = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl
outputKeywords = ON
parameterized = OFF
partsAndAssemblies = ON
parval = OFF
pgdHeatTransfer = OFF
plugin_central_dir = C:\SIMULIA\CAE\plugins\2023
postOutput = OFF
preDecomposition = ON
queues = {}
restart = OFF
restartEndStep = OFF
restartIncrement = 0
restartStep = 0
restartWrite = OFF
resultsFormat = ODB
rezone = OFF
runCalculator = OFF
simMode = SFS
simPack = OFF
simwrk = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl\plate_sq2_ss_nfx_bck_s4r_40x40_si
soils = OFF
soliter = OFF
solverTypes = ['DIRECT']
ssd = OFF
ssdCheckOK = False
standard_parallel = ALL
staticNonlinear = OFF
steadyStateTransport = OFF
step = ON
stepSenseAdj = OFF
stressExtList = ['.odb', '.sim', '.SMAManifest']
subGen = OFF
subGenLibs = []
subGenResidual = OFF
subGenTypes = []
submodel = OFF
substrLibDefs = OFF
substructure = OFF
symmetricModelGeneration = OFF
tempNoInterpolExtList = ['.fil', '.odb', '.sim', '.SMAManifest']
thermal = OFF
tmpdir = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_35976
tracer = OFF
transientSensitivity = OFF
umbrella_host = stn-w-xps
umbrella_port = 7053
unconnected_regions = OFF
unfold_param = OFF
unsymm = OFF
visco = OFF
xplSelect = OFF

# --------------- Execution Environment -----------------
ABAQUSLM_LICENSE_FILE = <EMAIL>
ABAQUS_LANG = English_United States.1252
ABAQUS_PY_TRANSLATION_DICTIONARY = Configuration/Xresources/en_US/en_US_PyDict.py
ABAQUS_SEQ = 2022_09_28-14.11.55 183150
ABAQUS_TRANSLATION_DICTIONARY = Configuration/Xresources/en_US/en_US_Dict.py
ABA_BLA_LIBRARY_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
ABA_COMMAND = C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMALauncher.exe
ABA_DRIVERNAME = abq2023.bat
ABA_HOME = C:\SIMULIA\EstProducts\2023\win_b64
ABA_LIBRARY_PATHNAME = PATH
ABA_MPI_LIBRARY_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
ABA_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA
ABA_PATH_BASE = C:\SIMULIA\EstProducts\2023\win_b64;
ABQINFO = C:\SIMULIA\EstProducts\2023\win_b64\SMA\info\
ABQLMHANGLIMIT = 0
ABQLMIMPL = FLEXNET
ABQLMQUEUE = 30
ABQLMUSER = tian50
ABQ_DLALLOCATOR = 1
ALLUSERSPROFILE = C:\ProgramData
APPDATA = C:\Users\<USER>\AppData\Roaming
ASL.LOG = Destination=file
CATCOMMANDPATH = C:\SIMULIA\EstProducts\2023\win_b64\code\command
CATDICTIONARYPATH = C:\SIMULIA\EstProducts\2023\win_b64\code\dictionary
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_21944_NGLYLGXONMPFXTMY
COLORTERM = truecolor
COMMONPROGRAMFILES = C:\Program Files\Common Files
COMMONPROGRAMFILES(X86) = C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432 = C:\Program Files\Common Files
COMPUTERNAME = STN-W-XPS
COMSPEC = C:\WINDOWS\system32\cmd.exe
CONDA_DEFAULT_ENV = rnd_py38
CONDA_EXE = C:\Users\<USER>\anaconda3\Scripts\conda.exe
CONDA_PREFIX = C:\Users\<USER>\anaconda3\envs\rnd_py38
CONDA_PREFIX_1 = C:\Users\<USER>\anaconda3
CONDA_PREFIX_2 = C:\Users\<USER>\anaconda3\envs\rnd_py38
CONDA_PROMPT_MODIFIER = (rnd_py38) 
CONDA_PYTHON_EXE = C:\Users\<USER>\anaconda3\python.exe
CONDA_ROOT = C:\Users\<USER>\anaconda3
CONDA_SHLVL = 2
COVERAGE_RCFILE = C:\SIMULIA\EstProducts\2023\win_b64\SMA\site\pyCoverageConfig.env
CUSTOMWORKSPACE = 
CYGWIN = tty
DRIVERDATA = C:\Windows\System32\Drivers\DriverData
DSY_TENANT = OnPremise
FED_DSFLEX_LICENSE_CONFIG = <EMAIL>
FED_LICENSE_SERVER_TYPE = DSFLEX
FOR_DIAGNOSTIC_LOG_FILE = fort.7
FOR_DUMP_CORE_FILE = TRUE
FOR_FORCE_STACK_TRACE = TRUE
FOR_IGNORE_EXCEPTIONS = TRUE
GIT_ASKPASS = c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
GSL_CONFIG = C:\cygwin64\bin
HOMEDRIVE = C:
HOMEPATH = \Users\tian50
KMP_INIT_AT_FORK = FALSE
LANG = en_US.UTF-8
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
LOGONSERVER = \\STN-W-XPS
MKL_NUM_THREADS = 1
MP_NUMBER_OF_THREADS = 1
MSMPI_BENCHMARKS = C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN = C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS = 8
OMP_NUM_THREADS = 1
ONEDRIVE = C:\Users\<USER>\OneDrive - AnalySwift
ONEDRIVECOMMERCIAL = C:\Users\<USER>\OneDrive - AnalySwift
ONEDRIVECONSUMER = C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
OS = Windows_NT
PATH = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_29760;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\site-packages\pywin32_system32;C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs;C:\Users\<USER>\anaconda3\envs\rnd_py38;C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\bin;C:\Users\<USER>\anaconda3\envs\rnd_py38\Scripts;C:\Users\<USER>\anaconda3\condabin;C:\Program Files\Microsoft MPI\Bin;C:\SIMULIA\Commands;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\MiKTeX\miktex\bin\x64;C:\Program Files\PuTTY;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Microsoft VS Code\bin;C:\Windows\Microsoft.NET\Framework\v4.0.30319;C:\Program Files\doxygen\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;;;;;;C:\Program Files (x86)\Wolfram Research\WolframScript;C:\Program Files\dotnet;C:\Program Files\TortoiseSVN\bin;C:\Program Files\nodejs;C:\Program Files\Git\cmd;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Calibre2;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\work\dev\msg-design\bin;C:\Users\<USER>\work\dev\sgio\bin;C:\Program Files\AnalySwift;C:\Program Files\Graphviz\bin;C:\Users\<USER>\program\bff\v1.6;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\program\dakota-6.18\bin;C:\Users\<USER>\program\dakota-6.18\lib;C:\Users\<USER>\program;C:\cygwin64\usr\x86_64-w64-mingw32\sys-root\mingw\bin;C:\cygwin64\usr\x86_64-w64-mingw32\sys-root\mingw\lib;C:\cygwin64\bin;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install\bin;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install\lib;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\lapack_windows\x64;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\TexGen\Python\libxtra\TexGen;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 158 Stepping 9, GenuineIntel
PROCESSOR_LEVEL = 6
PROCESSOR_REVISION = 9e09
PROGRAMDATA = C:\ProgramData
PROGRAMFILES = C:\Program Files
PROGRAMFILES(X86) = C:\Program Files (x86)
PROGRAMW6432 = C:\Program Files
PROJ_LIB = C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\share\proj
PROMPT = (rnd_py38) $P$G
PSMODULEPATH = C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\SIMULIA\EstProducts\2023\win_b64\code\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\lib-tk;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\site-packages;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs;.;C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\Users\<USER>\work\dev\msg-design\scripts;C:\Users\<USER>\work\dev\sgio;C:\Users\<USER>\work\rnd;C:\Users\<USER>\program\dakota-6.18\share\dakota\Python;C:\Program Files\TexGen\Python\libxtra\TexGen;C:\Program Files\TexGen\Python\libxtra
PYTHONUNBUFFERED = nobuffering
PYVERDIRNAME = python2.7
SESSIONNAME = Console
SMASVT_ROOT_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA
SSL_CERT_FILE = C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\ssl\cacert.pem
SYSTEMDRIVE = C:
SYSTEMROOT = C:\WINDOWS
TEMP = C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM = vscode
TERM_PROGRAM_VERSION = 1.89.1
TMP = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_29760
TMPDIR = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_29760
USERDOMAIN = STN-W-XPS
USERDOMAIN_ROAMINGPROFILE = STN-W-XPS
USERNAME = tian50
USERPROFILE = C:\Users\<USER>\Program Files (x86)\Microsoft Visual Studio 14.0\Common7\Tools\
VS90COMNTOOLS = C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build
VSCODE_GIT_ASKPASS_EXTRA_ARGS = 
VSCODE_GIT_ASKPASS_MAIN = c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE = C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE = \\.\pipe\vscode-git-4ff5ccc15f-sock
VSCODE_INJECTION = 1
WINDIR = C:\WINDOWS
ZES_ENABLE_SYSMAN = 1
_CONDA_EXE = C:\Users\<USER>\anaconda3\Scripts\conda.exe
_CONDA_OLD_CHCP = 65001
_CONDA_ROOT = C:\Users\<USER>\anaconda3
_NT_SYMBOL_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
_SG_LICENSE_FILE = 29750@**************
__CONDA_OPENSLL_CERT_FILE_SET = "1"
decfort_dump_flag = TRUE

# --------------- Execution Options -----------------
SIMExt = .sim
abaquslm_license_file = <EMAIL>
academic = RESEARCH
ams = OFF
analysisType = STANDARD
applicationName = analysis
aqua = OFF
ask_delete = OFF
auto_calculate = ANALYSIS
auto_convert = ON
beamSectGen = OFF
biorid = OFF
cavityTypes = []
cavparallel = OFF
compile_cpp = ['cl', '/c', '/W0', '/MD', '/TP', '/EHsc', '/DNDEBUG', '/DWIN32', '/DTP_IP', '/D_CONSOLE', '/DNTI', '/DFLT_LIC', '/DOL_DOC', '/D__LIB__', '/DHKS_NT', '/D_WINDOWS_SOURCE', '/DFAR=', '/D_WINDOWS', '/DABQ_WIN86_64', '%P', '/I%I', '/IC:\\SIMULIA\\EstProducts\\2023']
compile_fmu = ['win64CmpWrp', '-m64', '-msvc9', 'cl', '/LD', '/D_WINDOWS', '/TC', '/W0', '/I%I', '/IC:\\SIMULIA\\EstProducts\\2023']
compile_fortran = ['ifort', '/c', '/fpp', '/extend-source', '/DABQ_WIN86_64', '/DABQ_FORTRAN', '/iface:cref', '/recursive', '/Qauto', '/align:array64byte', '/Qpc64', '/Qprec-div', '/Qprec-sqrt', '/Qfma-', '/fp:precise', '/Qimf-arch-consistency:true', '/Qfp-speculation:safe', '/Qprotect-parens', '/Qfp-stack-check', '/reentrancy:threaded', '/QxSSE3', '/QaxAVX', '/include:%I', '/include:C:\\SIMULIA\\EstProducts\\2023', '%P']
complexFrequency = OFF
connect_timeout = 30
contact = OFF
cosimulation = OFF
coupledProcedure = OFF
cpus = 1
cse = OFF
cyclicSymmetryModel = OFF
directCyclic = OFF
direct_port = None
direct_solver = DMP
doc_root = http://help.3ds.com
domains = 1
dsa = OFF
dynStepSenseAdj = OFF
dynamic = OFF
excite = OFF
externalField = OFF
externalFieldCSEAux = OFF
externalFieldExtList = ['.sim', '.SMAManifest']
externalFieldFiles = []
externalFieldSimReader = None
fieldImport = OFF
filPrt = []
fils = []
finitesliding = OFF
flexiblebody = OFF
foundation = ON
freqSimReq = OFF
geostatic = OFF
heatTransfer = OFF
impJobExpVars = {}
importJobList = []
importSim = OFF
importer = OFF
importerParts = OFF
includes = ['shellsections.inp']
indir = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl
initialConditionsFile = OFF
input = plate_sq2_ss_nfx_bck_s4r_40x40_si
inputFormat = INP
interactive = None
interpolExtList = ['.odb', '.sim', '.SMAManifest']
job = plate_sq2_ss_nfx_bck_s4r_40x40_si
keyword_licenses = []
lanczos = OFF
lastmessageonly = OFF
libs = []
license_server_type = FLEXNET
link_exe = ['LINK', '/nologo', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/STACK:20000000', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/LARGEADDRESSAWARE', '/out:%J', '%F', '%M', '%L', '%B', '%O', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
link_sl = ['LINK', '/nologo', '/NOENTRY', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/dll', '/def:%E', '/out:%U', '%F', '%A', '%L', '%B', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
listener_name = None
listener_resource = None
lmsuspend = None
magnetostatic = OFF
massDiffusion = OFF
materialresponse = OFF
message = OFF
messaging_mechanism = DIRECT
modifiedTet = OFF
moldflowFiles = []
moldflowMaterial = OFF
mp_environment_export = ('ABAQUSLM_LICENSE_FILE', 'ABAQUS_CCI_DEBUG', 'ABAQUS_CSE_CURRCONFIGMAPPING', 'ABAQUS_CSE_RELTIMETOLERANCE', 'ABAQUS_DEVL_MODE', 'ABAQUS_LANG', 'ABAQUS_MPF_DIAGNOSTIC_LEVEL', 'ABA_ADM_ALIGNMENT', 'ABA_ADM_MINIMUMDECREASE', 'ABA_ADM_MINIMUMINCREASE', 'ABA_ALL_ADB_IN_TMPDIR', 'ABA_CM_BUFFERING', 'ABA_CM_BUFFERING_LIMIT', 'ABA_CUTOFF_SLAVEFACET_ANGLE', 'ABA_DIRECT_SOLVER_PATH', 'ABA_DMPSOLVER_BWDPARALLELOFF', 'ABA_ELP_SURFACE_SPLIT', 'ABA_ELP_SUSPEND', 'ABA_ENABLE_DYNELEMS', 'ABA_EVOLVING_JOBS', 'ABA_EXT_SIMOUTPUT', 'ABA_GCONT_POOL_SIZE', 'ABA_HOME', 'ABA_INC_DEFAULT', 'ABA_ITERATIVE_SOLVER_VERBOSE', 'ABA_MEMORY_MODE', 'ABA_MPI_MESSAGE_TRACKING', 'ABA_MPI_VERBOSE_LEVEL', 'ABA_NEW_STOS_COHESIVE_APPROX_CONTACT', 'ABA_NEW_STOS_COHESIVE_CONTACT', 'ABA_NUM_INTEGRATION_POINTS_LINE3D', 'ABA_SHARED_SAVEDIR', 'ABA_PATH', 'ABA_PRE_DECOMPOSITION', 'ABA_PRINT_DYNELEMS', 'ABA_REMOVE_OVERCONSTRAINED_TIES', 'ABA_RESOURCE_MONITOR', 'ABA_RESOURCE_USEMALLINFO', 'ABA_RESULTS_OVERLAY', 'ABA_STD_ACTIVATE_CONTACT_FOR_AM', 'ABA_STD_MAX_SEARCH_DISTANCE', 'ABA_SYMBOLIC_GENERALCOLLAPSE', 'ABA_SYMBOLIC_GENERAL_MAXCLIQUERANK', 'ABA_TOSCA_JOB_STALL', 'ABA_TOSCA_OVERLAY', 'ABA_TOSCA_PROTOTYPE', 'ABA_TOSCA_SEQFILES', 'ABA_TOSCA_STALL', 'ABA_UNIT_INDEPENDENT_CONTACT', 'ABA_USE_NEW_STOS_SST_FORMULATION', 'ABA_USE_OLD_SURF_TO_SURF_CONTACT', 'ABA_POTENTIAL_DEV', 'ABA_XPL_DEBUG', 'ABQLMHANGLIMIT', 'ABQLMIMPL', 'ABQLMQUEUE', 'ABQLMUSER', 'ABQ_ACTIVATE_PTK', 'ABQ_BYPASS_UNCONNECTED_REGION_CHECK', 'ABQ_CRTMALLOC', 'ABQ_DATACHECK', 'ABQ_DLALLOCATOR', 'ABQ_PATTERN', 'ABQ_PATTERN_VALUE', 'ABQ_RECOVER', 'ABQ_RESTART', 'ABQ_SKIP_ANALYTIC_SURF_GCONT', 'ABQ_SPLITFILE', 'ABQ_STD_ACCUM_CSLIP', 'ABQ_STD_ACTIVATE_BEAM_ROTATION', 'ABQ_STD_ALLOW_SURFACE_TO_BEAM', 'ABQ_SUPERELASTIC_MODIFIED', 'ABQ_GC_HEAT', 'ABQ_GC_SMALL', 'ABQ_UREG_USE_CONTACT_ELEM', 'ABQ_XFEM_POREPRESSURE', 'ABQ_XPL_DSMABORT', 'ABQ_XPL_PARTITIONSIZE', 'ABQ_XPL_WINDOWDUMP', 'ACML_FAST_MALLOC', 'ACML_FAST_MALLOC_CHUNK_SIZE', 'ACML_FAST_MALLOC_DEBUG', 'ACML_FAST_MALLOC_MAX_CHUNKS', 'ADB_USE_OLDSLDB', 'ADB_USE_NEWSLDB', 'CCI_RENDEZVOUS', 'DOMAIN', 'DOMAIN_CPUS', 'DOUBLE_PRECISION', 'DSLS_AUTH_PATHNAME', 'DSLS_CONFIG', 'FI_PROVIDER', 'FI_PROVIDER_PATH', 'FLEXLM_DIAGNOSTICS', 'FOR0006', 'FOR0064', 'FOR_DISABLE_DIAGNOSTIC_DISPLAY', 'FOR_IGNORE_EXCEPTIONS', 'I_MPI_FABRICS', 'IPATH_NO_CPUAFFINITY', 'LD_PRELOAD', 'MALLOC_MMAP_THRESHOLD_', 'MKL_DYNAMIC', 'MKL_NUM_THREADS', 'MPCCI_CODEID', 'MPCCI_DEBUG', 'MPCCI_JOBID', 'MPCCI_NETDEVICE', 'MPCCI_SERVER', 'MPCCI_TINFO', 'MPC_GANG', 'MPIEXEC_AFFINITY_TABLE', 'MPI_FLAGS', 'MPI_FLUSH_FCACHE', 'MPI_PROPAGATE_TSTP', 'MPI_SOCKBUFSIZE', 'MPI_USE_MALLOPT_MMAP_MAX', 'MPI_USE_MALLOPT_MMAP_THRESHOLD', 'MPI_USE_MALLOPT_SBRK_PROTECTION', 'MPI_WORKDIR', 'MPIR_CVAR_CH4_OFI_TAG_BITS', 'MPIR_CVAR_CH4_OFI_RANK_BITS', 'MP_NUMBER_OF_THREADS', 'MPICH_ND_ZCOPY_THRESHOLD', 'NCPUS', 'OMP_DYNAMIC', 'OMP_NUM_THREADS', 'OUTDIR', 'PAIDUP', 'PARALLEL_METHOD', 'RAIDEV_NDREG_LAZYMEM', 'SMA_PARENT', 'SMA_PLATFORM', 'SMA_WS', 'SIMULIA_COSIN_PATH', 'STD_INITSTRESS_FLAG', 'STD_INITGEOICS_FLAG', 'XPL_HMP_COMMTHREAD')
mp_file_system = (DETECT, DETECT)
mp_mode = THREADS
mp_mode_requested = MPI
mp_mpiCommand = []
mp_mpi_implementation = NATIVE
mp_mpi_searchpath = ['Microsoft MPI', 'Microsoft HPC Pack', 'Microsoft HPC Pack 2008 R2', 'Microsoft HPC Pack 2008', 'Microsoft HPC Pack 2008 SDK', 'Microsoft HPC Pack 2012']
mp_mpirun_path = C:\Program Files\Microsoft MPI\bin\mpiexec.exe
mp_rsh_command = dummy %H -l %U -n %C
multiphysics = OFF
noDmpDirect = []
noMultiHost = []
noMultiHostElemLoop = ['buckle']
no_domain_check = 1
onCaeGraphicsStartup = <function onCaeGraphicsStartup at 0x000001ED02907518>
onJobCompletion = []
onJobStartup = []
onestepinverse = OFF
outdir = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl
outputKeywords = ON
parameterized = OFF
partsAndAssemblies = ON
parval = OFF
pgdHeatTransfer = OFF
plugin_central_dir = C:\SIMULIA\CAE\plugins\2023
postOutput = OFF
preDecomposition = ON
queues = {}
restart = OFF
restartEndStep = OFF
restartIncrement = 0
restartStep = 0
restartWrite = OFF
resultsFormat = ODB
rezone = OFF
runCalculator = OFF
simMode = SFS
simPack = OFF
simwrk = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl\plate_sq2_ss_nfx_bck_s4r_40x40_si
soils = OFF
soliter = OFF
solverTypes = ['DIRECT']
ssd = OFF
ssdCheckOK = False
standard_parallel = ALL
staticNonlinear = OFF
steadyStateTransport = OFF
step = ON
stepSenseAdj = OFF
stressExtList = ['.odb', '.sim', '.SMAManifest']
subGen = OFF
subGenLibs = []
subGenResidual = OFF
subGenTypes = []
submodel = OFF
substrLibDefs = OFF
substructure = OFF
symmetricModelGeneration = OFF
tempNoInterpolExtList = ['.fil', '.odb', '.sim', '.SMAManifest']
thermal = OFF
tmpdir = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_29760
tracer = OFF
transientSensitivity = OFF
umbrella_host = stn-w-xps
umbrella_port = 11133
unconnected_regions = OFF
unfold_param = OFF
unsymm = OFF
visco = OFF
xplSelect = OFF

# --------------- Execution Environment -----------------
ABAQUSLM_LICENSE_FILE = <EMAIL>
ABAQUS_LANG = English_United States.1252
ABAQUS_PY_TRANSLATION_DICTIONARY = Configuration/Xresources/en_US/en_US_PyDict.py
ABAQUS_SEQ = 2022_09_28-14.11.55 183150
ABAQUS_TRANSLATION_DICTIONARY = Configuration/Xresources/en_US/en_US_Dict.py
ABA_BLA_LIBRARY_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
ABA_COMMAND = C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMALauncher.exe
ABA_DRIVERNAME = abq2023.bat
ABA_HOME = C:\SIMULIA\EstProducts\2023\win_b64
ABA_LIBRARY_PATHNAME = PATH
ABA_MPI_LIBRARY_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
ABA_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA
ABA_PATH_BASE = C:\SIMULIA\EstProducts\2023\win_b64;
ABQINFO = C:\SIMULIA\EstProducts\2023\win_b64\SMA\info\
ABQLMHANGLIMIT = 0
ABQLMIMPL = FLEXNET
ABQLMQUEUE = 30
ABQLMUSER = tian50
ABQ_DLALLOCATOR = 1
ALLUSERSPROFILE = C:\ProgramData
APPDATA = C:\Users\<USER>\AppData\Roaming
ASL.LOG = Destination=file
CATCOMMANDPATH = C:\SIMULIA\EstProducts\2023\win_b64\code\command
CATDICTIONARYPATH = C:\SIMULIA\EstProducts\2023\win_b64\code\dictionary
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_21944_NGLYLGXONMPFXTMY
COLORTERM = truecolor
COMMONPROGRAMFILES = C:\Program Files\Common Files
COMMONPROGRAMFILES(X86) = C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432 = C:\Program Files\Common Files
COMPUTERNAME = STN-W-XPS
COMSPEC = C:\WINDOWS\system32\cmd.exe
CONDA_DEFAULT_ENV = rnd_py38
CONDA_EXE = C:\Users\<USER>\anaconda3\Scripts\conda.exe
CONDA_PREFIX = C:\Users\<USER>\anaconda3\envs\rnd_py38
CONDA_PREFIX_1 = C:\Users\<USER>\anaconda3
CONDA_PREFIX_2 = C:\Users\<USER>\anaconda3\envs\rnd_py38
CONDA_PROMPT_MODIFIER = (rnd_py38) 
CONDA_PYTHON_EXE = C:\Users\<USER>\anaconda3\python.exe
CONDA_ROOT = C:\Users\<USER>\anaconda3
CONDA_SHLVL = 2
COVERAGE_RCFILE = C:\SIMULIA\EstProducts\2023\win_b64\SMA\site\pyCoverageConfig.env
CUSTOMWORKSPACE = 
CYGWIN = tty
DRIVERDATA = C:\Windows\System32\Drivers\DriverData
DSY_TENANT = OnPremise
FED_DSFLEX_LICENSE_CONFIG = <EMAIL>
FED_LICENSE_SERVER_TYPE = DSFLEX
FOR_DIAGNOSTIC_LOG_FILE = fort.7
FOR_DUMP_CORE_FILE = TRUE
FOR_FORCE_STACK_TRACE = TRUE
FOR_IGNORE_EXCEPTIONS = TRUE
GIT_ASKPASS = c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
GSL_CONFIG = C:\cygwin64\bin
HOMEDRIVE = C:
HOMEPATH = \Users\tian50
KMP_INIT_AT_FORK = FALSE
LANG = en_US.UTF-8
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
LOGONSERVER = \\STN-W-XPS
MKL_NUM_THREADS = 1
MP_NUMBER_OF_THREADS = 1
MSMPI_BENCHMARKS = C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN = C:\Program Files\Microsoft MPI\Bin\
NUMBER_OF_PROCESSORS = 8
OMP_NUM_THREADS = 1
ONEDRIVE = C:\Users\<USER>\OneDrive - AnalySwift
ONEDRIVECOMMERCIAL = C:\Users\<USER>\OneDrive - AnalySwift
ONEDRIVECONSUMER = C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
OS = Windows_NT
PATH = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_21328;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\site-packages\pywin32_system32;C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs;C:\Users\<USER>\anaconda3\envs\rnd_py38;C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\bin;C:\Users\<USER>\anaconda3\envs\rnd_py38\Scripts;C:\Users\<USER>\anaconda3\condabin;C:\Program Files\Microsoft MPI\Bin;C:\SIMULIA\Commands;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\MiKTeX\miktex\bin\x64;C:\Program Files\PuTTY;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Microsoft VS Code\bin;C:\Windows\Microsoft.NET\Framework\v4.0.30319;C:\Program Files\doxygen\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;;;;;;C:\Program Files (x86)\Wolfram Research\WolframScript;C:\Program Files\dotnet;C:\Program Files\TortoiseSVN\bin;C:\Program Files\nodejs;C:\Program Files\Git\cmd;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Calibre2;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\work\dev\msg-design\bin;C:\Users\<USER>\work\dev\sgio\bin;C:\Program Files\AnalySwift;C:\Program Files\Graphviz\bin;C:\Users\<USER>\program\bff\v1.6;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\program\dakota-6.18\bin;C:\Users\<USER>\program\dakota-6.18\lib;C:\Users\<USER>\program;C:\cygwin64\usr\x86_64-w64-mingw32\sys-root\mingw\bin;C:\cygwin64\usr\x86_64-w64-mingw32\sys-root\mingw\lib;C:\cygwin64\bin;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install\bin;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install\lib;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\build_vs\install;C:\Users\<USER>\work\others\suitesparse-metis-for-windows\lapack_windows\x64;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\TexGen\Python\libxtra\TexGen;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 158 Stepping 9, GenuineIntel
PROCESSOR_LEVEL = 6
PROCESSOR_REVISION = 9e09
PROGRAMDATA = C:\ProgramData
PROGRAMFILES = C:\Program Files
PROGRAMFILES(X86) = C:\Program Files (x86)
PROGRAMW6432 = C:\Program Files
PROJ_LIB = C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\share\proj
PROMPT = (rnd_py38) $P$G
PSMODULEPATH = C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\SIMULIA\EstProducts\2023\win_b64\code\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\lib-tk;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib\site-packages;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs;.;C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\Users\<USER>\work\dev\msg-design\scripts;C:\Users\<USER>\work\dev\sgio;C:\Users\<USER>\work\rnd;C:\Users\<USER>\program\dakota-6.18\share\dakota\Python;C:\Program Files\TexGen\Python\libxtra\TexGen;C:\Program Files\TexGen\Python\libxtra
PYTHONUNBUFFERED = nobuffering
PYVERDIRNAME = python2.7
SESSIONNAME = Console
SMASVT_ROOT_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA
SSL_CERT_FILE = C:\Users\<USER>\anaconda3\envs\rnd_py38\Library\ssl\cacert.pem
SYSTEMDRIVE = C:
SYSTEMROOT = C:\WINDOWS
TEMP = C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM = vscode
TERM_PROGRAM_VERSION = 1.89.1
TMP = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_21328
TMPDIR = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_21328
USERDOMAIN = STN-W-XPS
USERDOMAIN_ROAMINGPROFILE = STN-W-XPS
USERNAME = tian50
USERPROFILE = C:\Users\<USER>\Program Files (x86)\Microsoft Visual Studio 14.0\Common7\Tools\
VS90COMNTOOLS = C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build
VSCODE_GIT_ASKPASS_EXTRA_ARGS = 
VSCODE_GIT_ASKPASS_MAIN = c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE = C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE = \\.\pipe\vscode-git-4ff5ccc15f-sock
VSCODE_INJECTION = 1
WINDIR = C:\WINDOWS
ZES_ENABLE_SYSMAN = 1
_CONDA_EXE = C:\Users\<USER>\anaconda3\Scripts\conda.exe
_CONDA_OLD_CHCP = 65001
_CONDA_ROOT = C:\Users\<USER>\anaconda3
_NT_SYMBOL_PATH = C:\SIMULIA\EstProducts\2023;C:\SIMULIA\EstProducts\2023\win_b64;C:\SIMULIA\EstProducts\2023\win_b64\code;C:\SIMULIA\EstProducts\2023\win_b64\code\bin;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal;C:\SIMULIA\EstProducts\2023\win_b64\CAEresources;C:\SIMULIA\EstProducts\2023\win_b64\SMA;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Interop;C:\SIMULIA\EstProducts\2023\win_b64\code\bin\SMAExternal\Elysium;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\lib;C:\SIMULIA\EstProducts\2023\win_b64\tools\SMApy\python2.7\DLLs
_SG_LICENSE_FILE = 29750@**************
__CONDA_OPENSLL_CERT_FILE_SET = "1"
decfort_dump_flag = TRUE

# --------------- Execution Options -----------------
SIMExt = .sim
abaquslm_license_file = <EMAIL>
academic = RESEARCH
ams = OFF
analysisType = STANDARD
applicationName = analysis
aqua = OFF
ask_delete = OFF
auto_calculate = ANALYSIS
auto_convert = ON
beamSectGen = OFF
biorid = OFF
cavityTypes = []
cavparallel = OFF
compile_cpp = ['cl', '/c', '/W0', '/MD', '/TP', '/EHsc', '/DNDEBUG', '/DWIN32', '/DTP_IP', '/D_CONSOLE', '/DNTI', '/DFLT_LIC', '/DOL_DOC', '/D__LIB__', '/DHKS_NT', '/D_WINDOWS_SOURCE', '/DFAR=', '/D_WINDOWS', '/DABQ_WIN86_64', '%P', '/I%I', '/IC:\\SIMULIA\\EstProducts\\2023']
compile_fmu = ['win64CmpWrp', '-m64', '-msvc9', 'cl', '/LD', '/D_WINDOWS', '/TC', '/W0', '/I%I', '/IC:\\SIMULIA\\EstProducts\\2023']
compile_fortran = ['ifort', '/c', '/fpp', '/extend-source', '/DABQ_WIN86_64', '/DABQ_FORTRAN', '/iface:cref', '/recursive', '/Qauto', '/align:array64byte', '/Qpc64', '/Qprec-div', '/Qprec-sqrt', '/Qfma-', '/fp:precise', '/Qimf-arch-consistency:true', '/Qfp-speculation:safe', '/Qprotect-parens', '/Qfp-stack-check', '/reentrancy:threaded', '/QxSSE3', '/QaxAVX', '/include:%I', '/include:C:\\SIMULIA\\EstProducts\\2023', '%P']
complexFrequency = OFF
connect_timeout = 30
contact = OFF
cosimulation = OFF
coupledProcedure = OFF
cpus = 1
cse = OFF
cyclicSymmetryModel = OFF
directCyclic = OFF
direct_port = None
direct_solver = DMP
doc_root = http://help.3ds.com
domains = 1
dsa = OFF
dynStepSenseAdj = OFF
dynamic = OFF
excite = OFF
externalField = OFF
externalFieldCSEAux = OFF
externalFieldExtList = ['.sim', '.SMAManifest']
externalFieldFiles = []
externalFieldSimReader = None
fieldImport = OFF
filPrt = []
fils = []
finitesliding = OFF
flexiblebody = OFF
foundation = ON
freqSimReq = OFF
geostatic = OFF
heatTransfer = OFF
impJobExpVars = {}
importJobList = []
importSim = OFF
importer = OFF
importerParts = OFF
includes = ['shellsections.inp']
indir = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl
initialConditionsFile = OFF
input = plate_sq2_ss_nfx_bck_s4r_40x40_si
inputFormat = INP
interactive = None
interpolExtList = ['.odb', '.sim', '.SMAManifest']
job = plate_sq2_ss_nfx_bck_s4r_40x40_si
keyword_licenses = []
lanczos = OFF
lastmessageonly = OFF
libs = []
license_server_type = FLEXNET
link_exe = ['LINK', '/nologo', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/STACK:20000000', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/LARGEADDRESSAWARE', '/out:%J', '%F', '%M', '%L', '%B', '%O', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
link_sl = ['LINK', '/nologo', '/NOENTRY', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/dll', '/def:%E', '/out:%U', '%F', '%A', '%L', '%B', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
listener_name = None
listener_resource = None
lmsuspend = None
magnetostatic = OFF
massDiffusion = OFF
materialresponse = OFF
message = OFF
messaging_mechanism = DIRECT
modifiedTet = OFF
moldflowFiles = []
moldflowMaterial = OFF
mp_environment_export = ('ABAQUSLM_LICENSE_FILE', 'ABAQUS_CCI_DEBUG', 'ABAQUS_CSE_CURRCONFIGMAPPING', 'ABAQUS_CSE_RELTIMETOLERANCE', 'ABAQUS_DEVL_MODE', 'ABAQUS_LANG', 'ABAQUS_MPF_DIAGNOSTIC_LEVEL', 'ABA_ADM_ALIGNMENT', 'ABA_ADM_MINIMUMDECREASE', 'ABA_ADM_MINIMUMINCREASE', 'ABA_ALL_ADB_IN_TMPDIR', 'ABA_CM_BUFFERING', 'ABA_CM_BUFFERING_LIMIT', 'ABA_CUTOFF_SLAVEFACET_ANGLE', 'ABA_DIRECT_SOLVER_PATH', 'ABA_DMPSOLVER_BWDPARALLELOFF', 'ABA_ELP_SURFACE_SPLIT', 'ABA_ELP_SUSPEND', 'ABA_ENABLE_DYNELEMS', 'ABA_EVOLVING_JOBS', 'ABA_EXT_SIMOUTPUT', 'ABA_GCONT_POOL_SIZE', 'ABA_HOME', 'ABA_INC_DEFAULT', 'ABA_ITERATIVE_SOLVER_VERBOSE', 'ABA_MEMORY_MODE', 'ABA_MPI_MESSAGE_TRACKING', 'ABA_MPI_VERBOSE_LEVEL', 'ABA_NEW_STOS_COHESIVE_APPROX_CONTACT', 'ABA_NEW_STOS_COHESIVE_CONTACT', 'ABA_NUM_INTEGRATION_POINTS_LINE3D', 'ABA_SHARED_SAVEDIR', 'ABA_PATH', 'ABA_PRE_DECOMPOSITION', 'ABA_PRINT_DYNELEMS', 'ABA_REMOVE_OVERCONSTRAINED_TIES', 'ABA_RESOURCE_MONITOR', 'ABA_RESOURCE_USEMALLINFO', 'ABA_RESULTS_OVERLAY', 'ABA_STD_ACTIVATE_CONTACT_FOR_AM', 'ABA_STD_MAX_SEARCH_DISTANCE', 'ABA_SYMBOLIC_GENERALCOLLAPSE', 'ABA_SYMBOLIC_GENERAL_MAXCLIQUERANK', 'ABA_TOSCA_JOB_STALL', 'ABA_TOSCA_OVERLAY', 'ABA_TOSCA_PROTOTYPE', 'ABA_TOSCA_SEQFILES', 'ABA_TOSCA_STALL', 'ABA_UNIT_INDEPENDENT_CONTACT', 'ABA_USE_NEW_STOS_SST_FORMULATION', 'ABA_USE_OLD_SURF_TO_SURF_CONTACT', 'ABA_POTENTIAL_DEV', 'ABA_XPL_DEBUG', 'ABQLMHANGLIMIT', 'ABQLMIMPL', 'ABQLMQUEUE', 'ABQLMUSER', 'ABQ_ACTIVATE_PTK', 'ABQ_BYPASS_UNCONNECTED_REGION_CHECK', 'ABQ_CRTMALLOC', 'ABQ_DATACHECK', 'ABQ_DLALLOCATOR', 'ABQ_PATTERN', 'ABQ_PATTERN_VALUE', 'ABQ_RECOVER', 'ABQ_RESTART', 'ABQ_SKIP_ANALYTIC_SURF_GCONT', 'ABQ_SPLITFILE', 'ABQ_STD_ACCUM_CSLIP', 'ABQ_STD_ACTIVATE_BEAM_ROTATION', 'ABQ_STD_ALLOW_SURFACE_TO_BEAM', 'ABQ_SUPERELASTIC_MODIFIED', 'ABQ_GC_HEAT', 'ABQ_GC_SMALL', 'ABQ_UREG_USE_CONTACT_ELEM', 'ABQ_XFEM_POREPRESSURE', 'ABQ_XPL_DSMABORT', 'ABQ_XPL_PARTITIONSIZE', 'ABQ_XPL_WINDOWDUMP', 'ACML_FAST_MALLOC', 'ACML_FAST_MALLOC_CHUNK_SIZE', 'ACML_FAST_MALLOC_DEBUG', 'ACML_FAST_MALLOC_MAX_CHUNKS', 'ADB_USE_OLDSLDB', 'ADB_USE_NEWSLDB', 'CCI_RENDEZVOUS', 'DOMAIN', 'DOMAIN_CPUS', 'DOUBLE_PRECISION', 'DSLS_AUTH_PATHNAME', 'DSLS_CONFIG', 'FI_PROVIDER', 'FI_PROVIDER_PATH', 'FLEXLM_DIAGNOSTICS', 'FOR0006', 'FOR0064', 'FOR_DISABLE_DIAGNOSTIC_DISPLAY', 'FOR_IGNORE_EXCEPTIONS', 'I_MPI_FABRICS', 'IPATH_NO_CPUAFFINITY', 'LD_PRELOAD', 'MALLOC_MMAP_THRESHOLD_', 'MKL_DYNAMIC', 'MKL_NUM_THREADS', 'MPCCI_CODEID', 'MPCCI_DEBUG', 'MPCCI_JOBID', 'MPCCI_NETDEVICE', 'MPCCI_SERVER', 'MPCCI_TINFO', 'MPC_GANG', 'MPIEXEC_AFFINITY_TABLE', 'MPI_FLAGS', 'MPI_FLUSH_FCACHE', 'MPI_PROPAGATE_TSTP', 'MPI_SOCKBUFSIZE', 'MPI_USE_MALLOPT_MMAP_MAX', 'MPI_USE_MALLOPT_MMAP_THRESHOLD', 'MPI_USE_MALLOPT_SBRK_PROTECTION', 'MPI_WORKDIR', 'MPIR_CVAR_CH4_OFI_TAG_BITS', 'MPIR_CVAR_CH4_OFI_RANK_BITS', 'MP_NUMBER_OF_THREADS', 'MPICH_ND_ZCOPY_THRESHOLD', 'NCPUS', 'OMP_DYNAMIC', 'OMP_NUM_THREADS', 'OUTDIR', 'PAIDUP', 'PARALLEL_METHOD', 'RAIDEV_NDREG_LAZYMEM', 'SMA_PARENT', 'SMA_PLATFORM', 'SMA_WS', 'SIMULIA_COSIN_PATH', 'STD_INITSTRESS_FLAG', 'STD_INITGEOICS_FLAG', 'XPL_HMP_COMMTHREAD')
mp_file_system = (DETECT, DETECT)
mp_mode = THREADS
mp_mode_requested = MPI
mp_mpiCommand = []
mp_mpi_implementation = NATIVE
mp_mpi_searchpath = ['Microsoft MPI', 'Microsoft HPC Pack', 'Microsoft HPC Pack 2008 R2', 'Microsoft HPC Pack 2008', 'Microsoft HPC Pack 2008 SDK', 'Microsoft HPC Pack 2012']
mp_mpirun_path = C:\Program Files\Microsoft MPI\bin\mpiexec.exe
mp_rsh_command = dummy %H -l %U -n %C
multiphysics = OFF
noDmpDirect = []
noMultiHost = []
noMultiHostElemLoop = ['buckle']
no_domain_check = 1
onCaeGraphicsStartup = <function onCaeGraphicsStartup at 0x000002D248368518>
onJobCompletion = []
onJobStartup = []
onestepinverse = OFF
outdir = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl
outputKeywords = ON
parameterized = OFF
partsAndAssemblies = ON
parval = OFF
pgdHeatTransfer = OFF
plugin_central_dir = C:\SIMULIA\CAE\plugins\2023
postOutput = OFF
preDecomposition = ON
queues = {}
restart = OFF
restartEndStep = OFF
restartIncrement = 0
restartStep = 0
restartWrite = OFF
resultsFormat = ODB
rezone = OFF
runCalculator = OFF
simMode = SFS
simPack = OFF
simwrk = C:\Users\<USER>\work\dev\msg-design\examples\anl_tow_steer_plate_buckl\plate_sq2_ss_nfx_bck_s4r_40x40_si
soils = OFF
soliter = OFF
solverTypes = ['DIRECT']
ssd = OFF
ssdCheckOK = False
standard_parallel = ALL
staticNonlinear = OFF
steadyStateTransport = OFF
step = ON
stepSenseAdj = OFF
stressExtList = ['.odb', '.sim', '.SMAManifest']
subGen = OFF
subGenLibs = []
subGenResidual = OFF
subGenTypes = []
submodel = OFF
substrLibDefs = OFF
substructure = OFF
symmetricModelGeneration = OFF
tempNoInterpolExtList = ['.fil', '.odb', '.sim', '.SMAManifest']
thermal = OFF
tmpdir = C:\Users\<USER>\AppData\Local\Temp\tian50_plate_sq2_ss_nfx_bck_s4r_40x40_si_21328
tracer = OFF
transientSensitivity = OFF
umbrella_host = stn-w-xps
umbrella_port = 11158
unconnected_regions = OFF
unfold_param = OFF
unsymm = OFF
visco = OFF
xplSelect = OFF

