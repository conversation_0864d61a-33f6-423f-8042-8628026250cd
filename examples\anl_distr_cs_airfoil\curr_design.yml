version: '0.10'
structure:
  name: blade
  parameter: {}
  model:
    type: ''
    tool: ''
    tool_version: ''
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: blade
    parameter:
      length: 30
      chord_root: 2
      chord_tip: 1
      web_f_root: 0.8
      web_f_tip: 0.75
      web_r_root: 0.6
      web_r_tip: 0.65
      ply_spar_root: 20
      ply_spar_r1: 16
      ply_spar_tip: 8
    dim: 1
    builder: default
    design: null
    distribution:
    - name: chord
      type: float
      xscale: 30
      data_form: explicit
      data:
      - coordinate: 0
        value: 2
      - coordinate: 1
        value: 1
    - name:
      - a2p1
      - a2p3
      type: float
      xscale: 30
      data_form: compact
      data: '0,0.8,0.6

        1,0.75,0.65

        '
    - name:
      - ply_spar
      type: int
      xscale: 30
      data_form: compact
      data: '0,20

        0.2,16

        1,8

        '
  cs_assignment:
  - region: all
    location: node
    cs: main_cs
  physics: elastic
functions:
- name: f_interp_prev
  type: float
  kind: previous
  fill_value: extrapolate
- name: f_interp_linear
  type: float
  kind: linear
  fill_value: extrapolate
cs:
- name: airfoil
  parameter:
    mdb_name: material_database_us_ft
    airfoil: SC1095.dat
    airfoil_point_order: -1
    chord: 1
    a2p1: 0.8
    a2p3: 0.6
    lam_spar: T300 15k/976_0.0053
    lam_cap: Aluminum 8009_0.01
    lam_front: T300 15k/976_0.0053
    lam_back: T300 15k/976_0.0053
    ang_spar: 0
    ang_front: 0
    ang_back: 0
    ply_spar: 1
    ply_front: 1
    ply_back: 1
    mat_nsm: lead
    rnsm: 0.001
    mat_fill_front: Rohacell 70
    mat_fill_back: Plascore PN2-3/16OX3.0
    mat_fill_te: AS4 12k/E7K8
    gms: 0.002
    fms: 0.04
  dim: 2
  builder: prevabs
  design:
    base_file: airfoil_gbox_uni.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    output:
      value:
      - mu
      - gyry
      - gyrz
      - ea
      - gj
      - eiyy
      - eizz
      - mcy
      - mcz
      - tcy
      - tcz
    type: sg
    analysis: h
    work_dir: cs
