{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import scipy as sp\n", "import pandas as pd\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>%eval_id</th>\n", "      <th>interface</th>\n", "      <th>ang_spar_1</th>\n", "      <th>eig1</th>\n", "      <th>eig2</th>\n", "      <th>eig3</th>\n", "      <th>eig4</th>\n", "      <th>eig5</th>\n", "      <th>eig6</th>\n", "      <th>eig7</th>\n", "      <th>eig8</th>\n", "      <th>eig9</th>\n", "      <th>eig10</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>NO_ID</td>\n", "      <td>-90</td>\n", "      <td>1.155530</td>\n", "      <td>4.631853</td>\n", "      <td>7.219115</td>\n", "      <td>18.604236</td>\n", "      <td>20.135378</td>\n", "      <td>26.868385</td>\n", "      <td>39.234914</td>\n", "      <td>55.909366</td>\n", "      <td>64.456986</td>\n", "      <td>68.048587</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>NO_ID</td>\n", "      <td>-85</td>\n", "      <td>1.155490</td>\n", "      <td>4.631818</td>\n", "      <td>7.219158</td>\n", "      <td>18.674661</td>\n", "      <td>20.136980</td>\n", "      <td>26.868947</td>\n", "      <td>39.241270</td>\n", "      <td>56.121144</td>\n", "      <td>64.475240</td>\n", "      <td>68.051997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>NO_ID</td>\n", "      <td>-80</td>\n", "      <td>1.155397</td>\n", "      <td>4.631749</td>\n", "      <td>7.219445</td>\n", "      <td>18.884804</td>\n", "      <td>20.142277</td>\n", "      <td>26.870855</td>\n", "      <td>39.260761</td>\n", "      <td>56.753120</td>\n", "      <td>64.530270</td>\n", "      <td>68.063108</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>NO_ID</td>\n", "      <td>-75</td>\n", "      <td>1.155342</td>\n", "      <td>4.631735</td>\n", "      <td>7.220463</td>\n", "      <td>19.230762</td>\n", "      <td>20.152987</td>\n", "      <td>26.874493</td>\n", "      <td>39.294696</td>\n", "      <td>57.795006</td>\n", "      <td>64.623151</td>\n", "      <td>68.082637</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>NO_ID</td>\n", "      <td>-70</td>\n", "      <td>1.155470</td>\n", "      <td>4.631917</td>\n", "      <td>7.223029</td>\n", "      <td>19.701717</td>\n", "      <td>20.174871</td>\n", "      <td>26.880503</td>\n", "      <td>39.345425</td>\n", "      <td>59.224783</td>\n", "      <td>64.756527</td>\n", "      <td>68.111824</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   %eval_id interface  ang_spar_1      eig1      eig2      eig3       eig4  \\\n", "0         1     NO_ID         -90  1.155530  4.631853  7.219115  18.604236   \n", "1         2     NO_ID         -85  1.155490  4.631818  7.219158  18.674661   \n", "2         3     NO_ID         -80  1.155397  4.631749  7.219445  18.884804   \n", "3         4     NO_ID         -75  1.155342  4.631735  7.220463  19.230762   \n", "4         5     NO_ID         -70  1.155470  4.631917  7.223029  19.701717   \n", "\n", "        eig5       eig6       eig7       eig8       eig9      eig10  \n", "0  20.135378  26.868385  39.234914  55.909366  64.456986  68.048587  \n", "1  20.136980  26.868947  39.241270  56.121144  64.475240  68.051997  \n", "2  20.142277  26.870855  39.260761  56.753120  64.530270  68.063108  \n", "3  20.152987  26.874493  39.294696  57.795006  64.623151  68.082637  \n", "4  20.174871  26.880503  39.345425  59.224783  64.756527  68.111824  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_output = pd.read_csv('uniform_cs_beam_eigen_ps_md_tabular.dat', delim_whitespace=True)\n", "df_output.head(5)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(4, 6))\n", "\n", "for i in range(10):\n", "    ax.plot(df_output['ang_spar_1'], df_output[f'eig{i+1}'], c='r')\n", "\n", "ax.set_xlim(left=-90, right=90)\n", "ax.set_ylim(bottom=0, top=80)\n", "ax.set_xticks(np.linspace(-90, 90, 7))\n", "ax.set_xlabel('Fiber angle [deg]')\n", "ax.set_ylabel('Frequency [Hz]')\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "rnd23_py38", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}