name: ''
version: '0.9'
setting:
  rel_tol: 1.0e-09
  abs_tol: 1.0e-12
  log_level_cmd: info
  log_level_file: info
  log_file_name: log.txt
structure:
  name: blade1
  design:
    dim: 1
    section_locations:
    - 0.2
    - 0.9
    cs_assignment:
    - region: all
      cs: cs1
  distribution:
  - name: topology
    ytype: str
    function: interpolation
    kind: previous
    data_form: compact
    data: '0.0, airfoil_gbox_uni.xml.tmp

      0.8, airfoil_solid.xml.tmp

      '
  - name: airfoil
    ytype: str
    function: interpolation
    kind: previous
    data_form: compact
    data: '0.0, sc1095.txt

      0.5, sc1094r8.txt

      '
  - name:
    - ang_spar_1
    - ang_spar_2
    ytype: float
    function: interpolation
    kind: linear
    data_form: compact
    data: '0.1, 46, -45

      0.3, -3, 0

      0.7, -44, 90

      1.0, 47, 45

      '
  css_data: {}
  cs:
    cs1:
      base: cs1
      model: md1
cs:
  cs1:
    name: cs1
    design:
      tool: prevabs
      base_file: topology
      dim: 2
    parameter:
      mdb_name: material_database_us_ft
      airfoil: sc1095.txt
      airfoil_file_head: 1
      airfoil_point_order: 1
      airfoil_point_reverse: 1
      chord: 1.73
      a2p1: 0.8
      a2p3: 0.6
      lam_skin: T300 15k/976_0.0053
      lam_front: T300 15k/976_0.0053
      lam_back: T300 15k/976_0.0053
      lam_spar_1: T300 15k/976_0.0053
      ang_spar_1: 0
      ply_spar_1: 10
      lam_cap: Aluminum 8009_0.01
      mat_nsm: lead
      mat_fill_front: Rohacell 70
      mat_fill_back: Plascore PN2-3/16OX3.0
      mat_fill_te: Plascore PN2-3/16OX3.0
      rnsm: 0.001
      gms: 0.004
    model:
      md1:
        tool: vabs
analysis:
  steps:
  - step: cs analysis
    type: cs
    analysis: h
    setting:
      timeout: 60
    output:
    - value:
      - gj
      - eiyy
      - eizz
