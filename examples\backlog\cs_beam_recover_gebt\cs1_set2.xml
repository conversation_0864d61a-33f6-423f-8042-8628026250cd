<cross_section name="uh60a_section" format="1">
    <include>
        <material>material_database_us_ft</material>
    </include>
    <analysis>
        <model>1</model>
    </analysis>
    <general>
        <scale>1.73</scale>
        <mesh_size>0.004</mesh_size>
        <element_type>linear</element_type>
        <track_interface>0</track_interface>
    </general>



    <baselines>

        <line name="ln_af" type="airfoil">
            <points data="file" format="1" direction="1" header="1">sc1094r8.txt</points>
            <flip>1</flip>
            <reverse>1</reverse>
        </line>

        <point name="p1" on="ln_af" by="x2" which="top">0.8</point>
        <point name="p2" on="ln_af" by="x2" which="bottom">0.8</point>
        <point name="p3" on="ln_af" by="x2" which="top">0.6</point>
        <point name="p4" on="ln_af" by="x2" which="bottom">0.6</point>

        <point name="pfle1">0.1 0</point>
        <point name="pfle2">0.9 0</point>

        <line name="line_top">
            <points>p1:p3</points>
        </line>
        <line name="line_bottom">
            <points>p4:p2</points>
        </line>
        <line name="line_le">
            <points>p2:p1</points>
        </line>
        <line name="line_te">
            <points>p3:p4</points>
        </line>
    </baselines>



    <layups>
        <layup name="lyp_le_cap">
            <layer lamina="Aluminum 8009_0.01">0:1</layer>
        </layup>
        <layup name="lyp_skin">
            <layer lamina="T300 15k/976_0.0053">0:1</layer>
        </layup>

    </layups>



    <component name="skin">
        <segment>
            <baseline>line_top</baseline>
            <layup>lyp_skin</layup>
        </segment>
        <segment>
            <baseline>line_bottom</baseline>
            <layup>lyp_skin</layup>
        </segment>
        <segment>
            <baseline>line_le</baseline>
            <layup>lyp_skin</layup>
        </segment>
        <segment>
            <baseline>line_te</baseline>
            <layup>lyp_skin</layup>
        </segment>
    </component>


    <component name="fill_front" type="fill" depend="skin">
        <location>pfle1</location>
        <material>Rohacell 70</material>
        <mesh_size at="pfle1,pfle2">0.04</mesh_size>
    </component>



<global measure="stress"><displacements>-1.713801500e-06  1.326533100e-05  -3.021506400e-03</displacements><rotations>1.000000000e+00  0.000000000e+00  0.000000000e+00  0.000000000e+00  1.000000000e+00  0.000000000e+00  0.000000000e+00  0.000000000e+00  1.000000000e+00</rotations><loads>0.000000000e+00  0.000000000e+00  0.000000000e+00  0.000000000e+00  1.000000000e+00  0.000000000e+00</loads></global></cross_section>