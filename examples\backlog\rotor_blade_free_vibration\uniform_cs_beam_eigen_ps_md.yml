version: "0.9"


# ====================================================================
# setting:
#   data_process_functions_file: "data_proc_funcs"


# ====================================================================
structure:
  name: "uh60_blade_1"
  # parameter:
    # k1: 0.01
    # r1: 0
  design:
    # type: 'discrete'
    dim: 1
    file: 'beam_design.yml'
    solver: 'gebt'
    # section_locations: [0,]
    cs_assignment:
      - region: 'segment1'
        cs: 'cs1'
      # all: 'main_cs'
  cs:
    cs1:
      base: 'airfoil'
      model: 'md1'


cs:
  - name: "airfoil"
    parameters:
      R: 26.8333
      mdb_name: "material_database_us_ft"
      airfoil: 'SC1095.dat'
      airfoil_point_order: -1
      chord: 1.73
      a2p1: 0.8
      a2p3: 0.6
      lam_skin: "T300 15k/976_0.0053"
      lam_front: "T300 15k/976_0.0053"
      lam_back: "T300 15k/976_0.0053"
      lam_spar_1: "T300 15k/976_0.0053"
      ang_spar_1: 0
      ply_spar_1: 10
      lam_cap: "Aluminum 8009_0.01"
      mat_nsm: "lead"
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "Plascore PN2-3/16OX3.0"
      rnsm: 0.001
      gms: 0.004
    design:
      dim: 2
      tool: 'prevabs'
      base_file: 'airfoil_gbox_uni.xml.tmp'
    model:
      md1:
        tool: 'vabs'



# ====================================================================
analysis:
  setting:
  steps:
    - step: "cs analysis"
      type: 'cs'
      analysis: 'h'
      # preprocess:
      #   - file: 'data_proc_funcs'
      #     function: 'materialId2Name2'
    - step: 'beam analysis'
      type: 'gebt'
      output:
        - value: ['eig1', 'eig2', 'eig3', 'eig4', 'eig5', 'eig6', 'eig7', 'eig8', 'eig9', 'eig10']
    # - step: "calc diff"
    #   type: "script"
    #   file: 'data_proc_funcs'
    #   function: "dakota_postpro"
    #   kwargs:
    #     beam_properties: ["gj", "ei22", "ei33"]
    #     target: [2.29e3, 3.98e3, 2.44e5]


# ====================================================================
study:
  method:
    format: "keyword"
    output: "normal"
    multidim_parameter_study:
      partitions: [36,]
  variables:
    data_form: "compact"
    data: |
      ang_spar_1,     design, discrete, range, -90:90
  responses:
    data_form: "compact"
    data: |
      eig1,   objective, max
      eig2,   objective, max
      eig3,   objective, max
      eig4,   objective, max
      eig5,   objective, max
      eig6,   objective, max
      eig7,   objective, max
      eig8,   objective, max
      eig9,   objective, max
      eig10,   objective, max
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        directory_tag: on
        directory_save: on
    required_files:
      - "data/*"
    # asynchronous:
    #   evaluation_concurrency: 20
    # failure_capture:
    #   recover: [1e12,]

