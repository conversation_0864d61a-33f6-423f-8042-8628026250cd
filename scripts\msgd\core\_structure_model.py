from __future__ import annotations

import copy
import logging
# from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
# from functools import partial
# from multiprocessing import Manager
# from threading import Lock

# from icecream import ic
import meshio
import numpy as np
# import yaml
import sgio
# import tqdm
from sgio.model import getModelDim

import msgd._global as GLOBAL
import msgd.ext as mext
import msgd.utils as mutils
from ._data_classes import EntitySet
from ._structure_design import StructureDesign, StructureParameters
from ._sg_assignment import SGAssignment
from ._state_loc_case import StateLocCase
# from ._data_classes import DataBase
# from ..design._helpers import process_element



logger = logging.getLogger(__name__)

class StructureModel():
    """Combination of structural design and global/macro model config.

    Parameters
    ----------
    name:str
        Name of the model.
    category:str
        Category of the model. Choose one from 'structure', 'sg'.
    design:str|StructureDesign
        Design of the structure.
    model_type:str
        Type of the model.
    physics:str
        Physics of the model.
    solver:str
        Solver of the model.
    solver_version:str
        Version of the solver.
    sg_assignments:list
        List of SG assignments.
    domains:list
        List of domain transformations.
    mesh:meshio.Mesh
        Mesh data.
    sg_data:sgio.StructureGene
        SG data.
    parameters:dict
        Design parameters.
    fn_model:str
        File name of the model.
    fn_prop:str
        File name of the properties.
    model_config:dict
        Model configs.
    mesh_nodes_pd:dict
        Mesh nodes in the parametric domain.
    constitutive:sgio.Model
        Constitutive model.
    """
    def __init__(
        self, name:str='', category:str='sg',
        design:str|StructureDesign=None,
        model_type:str='', physics:str='',
        solver:str='', solver_version:str='', data_format=None,
        sg_assignments:list=[],
        domains:list=[],
        mesh:meshio.Mesh=None, sg_data:sgio.StructureGene=None,
        parameters:dict={},
        fn_model:str='', fn_prop:str='',
        model_config:dict={},
        mesh_nodes_pd:dict={},
        constitutive:sgio.Model=None
        ):

        self.name:str = name
        self.category:str = category  # Choose one from 'structure', 'sg'
        self.level:int = 0

        self._parameters:dict = parameters  # Design parameters
        self._config:dict = model_config  # Tool specific model configs
        self._physics:str = physics

        # Name of the design
        self._design:str|StructureDesign = design

        self._domains = domains

        # Model configs
        self._type:str = model_type
        """Type of the upper level structural model.

        Solid: SD1
        Plate/shell: PL1, PL2
        Beam: BM1, BM2
        """

        self._solver:str = solver
        self._solver_version:str = solver_version
        self._data_format = data_format

        # SG model assignments
        self._sg_assignments:list[SGAssignment] = sg_assignments

        # Discrete sets
        self._discrete_sg_params:dict = {}
        """
        Parameter values at each element and node.

        ..  code-block::

            {
                eid1: {
                    nid1: sg_params_1,
                    nid2: sg_params_2,
                    ...
                },
                ...
            }
        """

        self._discrete_sg_assigns:list[EntitySet] = []
        """
        Discrete SG assignments.

        ..  code-block::

            [
                entity_set_1,
                ...
            ]
        """

        self._sg_param_sets:dict[str, StructureParameters] = {}
        """
        ..  code-block::

            {
                'set_name_1': sg_params_1,
                'set_name_2': sg_params_2,
                ...
            }
        """

        self._sg_model_sets:dict[str, StructureModel] = {}  # Structure Genome
        '''Map from specific sg set name to StructureModel.

        ..  code-block::

            {
                'sg_model_name_1': sg_model_1,
                'sg_model_name_2': sg_model_2,
                ...
            }

        '''

        self._entity_sets:dict[str, EntitySet] = {}
        """Map from set name to entity ids

        ..  code-block::

            {
                'set_name': entity_set_1,
                ...
            }
        """

        self._orient_sets:dict = {}

        self._model = None  # Object for external tools

        # Mesh data
        self._mesh:meshio.Mesh = mesh
        self._sg_data:sgio.StructureGene = sg_data
        self._fn_model:str = fn_model
        self._fn_prop:str = fn_prop
        """File name storing structural properties used by the model."""
        self._mesh_nodes_pd:dict = mesh_nodes_pd
        self.fns_sup = []  # Supplementary files

        # The sgio model object (effective properties)
        self._model_func:sgio.Model = constitutive

        self._macro_state_locases:list[StateLocCase] = []
        """
        """

        self._local_state_locases:list[StateLocCase] = []
        """
        """

    def __repr__(self):
        _str = [
            f'SG Model (name: {self.name})',
            f'  - design: {self._design}',
            f'  - type: {self._type}',
            f'  - physics: {self._physics}',
            f'  - solver: {self._solver} (v{self._solver_version})',
        ]
        if self.category == 'sg':
            _str.append(f'  - constitutive: {self._model_func}')
        return '\n'.join(_str)

    def to_dict(self) -> dict:
        _dict = {
            'name': self.name,
            'parameter': self._parameters,
            'model': {
                'type': self._type,
                'tool': self._solver,
                'tool_version': self._solver_version,
                'main_file': self._fn_model,
                'prop_file': self._fn_prop,
                'config': self._config,
            },
        }

        if isinstance(self._design, str):
            _dict['design'] = self._design
        elif isinstance(self._design, StructureDesign):
            _dict['design'] = self._design.to_dict()

        _sg_assigns = []
        for _sa in self._sg_assignments:
            _sg_assigns.append(_sa.to_dict(GLOBAL.SG_KEY))
        if len(_sg_assigns) > 0:
            _dict[f'{GLOBAL.SG_KEY}_assignment'] = _sg_assigns

        if self.category == 'structure':
            _dict['physics'] = self._physics

        return _dict

    # @property
    # def name(self): return self._name
    # @name.setter
    # def name(self, name): self._name = name
    # @property
    # def category(self): return self._category
    @property
    def design(self): return self._design
    @design.setter
    def design(self, design): self._design = design
    @property
    def db_domain(self): return self._domains
    @property
    def model(self): return self._model
    @model.setter
    def model(self, model): self._model = model
    @property
    def model_type(self): return self._type
    @property
    def smdim(self): return getModelDim(self._type)
    @property
    def physics(self): return self._physics
    @property
    def solver(self): return self._solver
    @property
    def solver_version(self): return self._solver_version
    @property
    def data_format(self):
        if self._data_format is None:
            if ('vabs' in self._solver.lower()) or (self._solver.lower().startswith('v')):
                return 'vabs'
            elif ('swiftcomp' in self._solver.lower()) or (self._solver.lower().startswith('s')):
                return 'sc'
        return self._data_format
    @property
    def sg_assignments(self): return self._sg_assignments
    @property
    def discrete_sg_assigns(self): return self._discrete_sg_assigns
    @property
    def parameters(self): return self._parameters
    @property
    def configs(self): return self._config
    @property
    def sg_model_sets(self): return self._sg_model_sets
    @property
    def entity_sets(self): return self._entity_sets
    @property
    def elem_sets(self):
        sets = {}
        for _set_name, _items in self._entity_sets.items():
            if _items['type'] == 'element':
                sets[_set_name] = _items['items']
        return sets
    @property
    def orient_sets(self): return self._orient_sets

    @property
    def mesh(self): return self._mesh
    @mesh.setter
    def mesh(self, mesh): self._mesh = mesh
    @property
    def mesh_nodes_pd(self): return self._mesh_nodes_pd
    @mesh_nodes_pd.setter
    def mesh_nodes_pd(self, mesh_nodes_pd): self._mesh_nodes_pd = mesh_nodes_pd
    @property
    def sg_data(self): return self._sg_data
    @sg_data.setter
    def sg_data(self, sg_data): self._sg_data = sg_data
    @property
    def fn_model(self): return self._fn_model
    @fn_model.setter
    def fn_model(self, fn_model): self._fn_model = fn_model
    @property
    def fn_prop(self): return self._fn_prop
    @fn_prop.setter
    def fn_prop(self, fn_prop): self._fn_prop = fn_prop
    @property
    def constitutive(self): return self._model_func
    @constitutive.setter
    def constitutive(self, constitutive): self._model_func = constitutive
    @property
    def macro_state_locases(self): return self._macro_state_locases
    @property
    def local_state_locases(self): return self._local_state_locases


    def getMeshNodes(self):
        nodes = {}

        # TODO
        # Temporary solution
        if self._model:
            nodes = self._model.points

        elif self._mesh:
            for _nid, _ncoords in enumerate(self.mesh.points):
                nodes[_nid+1] = _ncoords

        return nodes


    def getMeshElements(self):
        elements = {}

        # TODO
        # Temporary solution
        if self._model:
            elements = self._model.parts

        elif self._mesh:
            _eid_data = self.mesh.cell_data.get('element_id', [])
            # print(f'_eid_data = {_eid_data}')
            if _eid_data:
                for _cbi, _cb in enumerate(self.mesh.cells):
                    _eid_cbi = _eid_data[_cbi]
                    for _eid, _enodes in zip(_eid_cbi, _cb.data):
                        elements[int(_eid)] = [int(_enodes[i])+1 for i in range(len(_enodes))]
            else:
                _eid = 0
                for _cbi, _cb in enumerate(self.mesh.cells):
                    for _enodes in _cb.data:
                        _eid += 1
                        elements[int(_eid)] = [int(_enodes[i])+1 for i in range(len(_enodes))]

        return elements


    def getEntitySets(self):
        """
        """
        logger.debug('getting entity sets...')

        sets = copy.deepcopy(self._entity_sets)
        logger.debug(f'sets = {sets}')

        _mesh = None
        _point_sets = {}
        _cell_sets = {}

        offset_index = False

        if isinstance(self._mesh, meshio.Mesh):
            _mesh = self._mesh
            _point_sets = _mesh.point_sets
            _cell_sets = _mesh.cell_sets
            offset_index = True

        elif not self._model is None:
            if hasattr(self._model, 'mesh') and isinstance(self._model.mesh, meshio.Mesh):
                _mesh = self._model.mesh
                _point_sets = _mesh.point_sets
                _cell_sets = _mesh.cell_sets
                offset_index = True

        if _mesh is None:
            if hasattr(self._model, 'point_sets'):
                _point_sets = self._model.point_sets
            if hasattr(self._model, 'cell_sets'):
                _cell_sets = self._model.cell_sets

        logger.debug(f'_point_sets = {_point_sets}')
        logger.debug(f'_cell_sets = {_cell_sets}')

        for _k, _v in _point_sets.items():
            sets[_k] = {'type': 'node'}
            if offset_index:
                sets[_k]['items'] = [_i + 1 for _i in _v]
            else:
                sets[_k]['items'] = _v

        for _k, _v in _cell_sets.items():
            sets[_k] = {
                'type': 'element',
                'items': []
            }
            for _elems in _v:
                # print(f'type(_elemes) = {type(_elems)}')
                # print(isinstance(_elems, list))
                # print(isinstance(_elems, np.ndarray))
                if not (isinstance(_elems, list) or isinstance(_elems, np.ndarray)):
                    _elems = [_elems]
                if offset_index:
                    sets[_k]['items'].extend([_i + 1 for _i in _elems])
                else:
                    sets[_k]['items'].extend(_elems)

        return sets


    def addDataToMesh(self, data:dict, loc:str, defaults:dict={}):
        """
        Parameters
        ----------
        name:str
            Name of the data.
        loc:str
            Location of the data. ('node', 'element')
        data:dict
            Data to be added to the mesh.

            ..  code-block::

                {
                    'name1': {
                        1: value1,
                        2: value2,
                        ...
                    },
                    ...
                }
        defaults:dict
            Default values.

            ..  code-block::

                {
                    'name1': value1,
                    ...
                }
        """

        # print(data)

        if self._mesh:
            if loc == 'node':
                _point_data = []

                # self._mesh.point_data[name] = _point_data

            elif loc == 'element':

                for _name, _data in data.items():

                    _cell_data = []  # shape: (n_cell_block, n_cell, n_dim)

                    for _cbi, _cb in enumerate(self._mesh.cells):
                        _cell_data_i = []

                        for _cj, _ in enumerate(_cb.data):
                            _eid = self._mesh.cell_data['element_id'][_cbi][_cj]
                            _evalue = _data.get(_eid, defaults.get(_name, 0))

                            _cell_data_i.append(_evalue)

                        _cell_data.append(_cell_data_i)

                    self._mesh.cell_data[_name] = _cell_data

        return

    def writeMeshData(self, fn='', fmt='gmsh22'):
        """
        """

        if fn == '':
            _fn = f'{self.name}_mesh.msh'
        else:
            _fn = fn

        logger.info(f'writing mesh data to file {_fn}...')

        _file = open(_fn, 'w')
        _file.close()

        # print(f'GLOBAL.VARIANT = {GLOBAL.VARIANT}')
        # print(f'self._solver = {self._solver}')

        if isinstance(self._mesh, meshio.Mesh):
            meshio.write(
                filename=_fn,
                mesh=self._mesh,
                file_format=fmt
                )

        elif not self._model is None:
            if hasattr(self._model, 'mesh') and isinstance(self._model.mesh, meshio.Mesh):
                # print(self._model)
                # print(self._model.mesh)
                meshio.write(
                    filename=_fn,
                    mesh=self._model.mesh,
                    file_format=fmt
                    )

            elif isinstance(self._model, mext.GEBTBeam):
                _points = []
                _cells = {'line': []}

                for _i in range(self._model.num_points):
                    _p = self._model.points[_i+1]
                    _points.append(_p)

                _eids = []
                for _i in range(self._model.num_members):
                    _p1, _p2 = self._model.members[_i+1]
                    _cells['line'].append([_p1-1, _p2-1])
                    _eids.append(_i)
                _cell_data = {'element_id': [_eids,]}

                print(f'_points = {_points}')
                print(f'_cells = {_cells}')

                _mesh = meshio.Mesh(
                    points=_points,
                    cells=_cells,
                    cell_data=_cell_data
                    )

                meshio.write(
                    filename=_fn,
                    mesh=_mesh,
                    file_format=fmt
                    )

        return


    def writeDiscreteSGAssigsToFile(self):
        # _discrete_sg_assigns = []

        # for _sa in self._sg_assignments:
        #     _discrete_sg_assigns.extend(_sa.discrete_sg_assigns)

        mutils.dumpData(
            self._discrete_sg_assigns,
            f'_{self.name}_discrete_sg_assigns')

        return

    def writeInterim(self):
        # print(self._sg_model_sets)
        _dict_sg_model_sets = {}
        for _set_name, _sg_model in self._sg_model_sets.items():
            _dict_sg_model_sets[_set_name] = _sg_model.toDictionary()

        mutils.dumpData(
            _dict_sg_model_sets,
            f'_{self.name}_sg_model_sets')

        mutils.dumpData(
            self._entity_sets,
            f'_{self.name}_entity_sets')

        mutils.dumpData(
            self._discrete_sg_params,
            f'_{self.name}_discrete_sg_params')

        self.writeDiscreteSGAssigsToFile()

        return

    def loadStructureMesh(self) -> None:
        """Load structural model (mesh) from the file.
        """

        logger.info(f'[{self.name}] loading structural mesh data...')

        _model = None

        # If provide file name of the mesh
        # load the mesh from the file
        logger.debug(f'self._config = {self._config}')
        if self._fn_model != '':
            # Subsitute parameters in the file name
            try:
                self._fn_model = self._design.parameters[self._fn_model]
            except KeyError:
                pass
            _model = mext.importStructureMesh(
                self._fn_model, self._solver, **self._config
                )

        # Create default mesh object from inputs
        else:
            # If provide points/nodes
            if len(self.mesh_nodes_pd) > 0:
                # For iVABS, assume a 1D structure
                # create lines/elements between every two points/nodes
                if GLOBAL.VARIANT == 'ivabs':
                    _points = [self.mesh_nodes_pd[1],]
                    _cells = {'line': []}
                    _eids = []
                    for _i in range(1, len(self.mesh_nodes_pd)):
                        _coord = self.mesh_nodes_pd[_i+1]
                        _points.append(_coord)
                        _cells['line'].append([_i-1, _i])
                        _eids.append(_i)
                    _cell_data = {'element_id': [_eids,]}

                    logger.debug(f'_points = {_points}')
                    logger.debug(f'_cells = {_cells}')

                    _model = meshio.Mesh(
                        points=_points,
                        cells=_cells,
                        cell_data=_cell_data
                        )

                    self._entity_sets['all'] = {
                        'type': 'element',
                        'items': _eids
                    }

        # print(_model)

        if isinstance(_model, meshio.Mesh):
            self._mesh = _model
        else:
            self._model = _model

        if self._solver == 'gebt':
            # Add point/member sets to entity sets
            for _set_name, _items in self._model.point_sets.items():
                self._entity_sets[_set_name] = {
                    'type': 'node',
                    'items': _items
                }
            for _set_name, _items in self._model.member_sets.items():
                self._entity_sets[_set_name] = {
                    'type': 'element',
                    'items': _items
                }

        return


    def implementDomainTransformations(self, db_function) -> None:
        """Implement domain transformations.

        Parameters
        ----------
        db_function:DataBase
            Database of functions.
        """

        logger.info(f'[{self.name}] implementing domain transformations...')

        for _domain in self._domains:
            _domain.implementTransformation(
                db_function=db_function,
                structure_model=self,
                )

        return


    def getSGenomeProperty(self, prop_names, db_sg_model) -> list:
        """
        Parameters
        ----------
        prop_names:list
            List of property names.
        db_sg_model:DataBase
            Database of SG models.

        Returns
        -------
        list
            List of SG properties.

            ..  code-block::

                [
                    {
                        'id': ...,
                        'coords': [...],
                        'properties': {
                            'prop1': value1,
                            'prop2': value2,
                            ...
                    },
                    ...
                ]
        """

        # print(db_sg_model)
        # print(self._sg_model_sets)

        sgn_props = []

        _nodes = self.getMeshNodes()
        _elements = self.getMeshElements()
        # print(_nodes)
        # print(_elements)

        # breakpoint()
        # print(self._sg_assignments)
        # print(self._discrete_sg_assigns)

        for _i, _sg_assign in enumerate(self._discrete_sg_assigns):
            _id = _sg_assign['region']
            _entity = _sg_assign['location']
            _sg_name = _sg_assign['sg_model']

            if _entity == 'node':
                _coords = _nodes[_id]
            else:
                _coords = []

            _sg_model = None
            for _set_name, _sg in self._sg_model_sets.items():
                if _sg.name == _sg_name:
                    _sg_model = _sg
                    break

            _const = _sg_model.constitutive
            _props = []

            for _pn in prop_names:
                _prop = _const.get(_pn)
                _props.append(_prop)

            sgn_props.append({
                'id': _id,
                'coords': _coords,
                'properties': _props
            })

        if len(sgn_props) == 1:
            sgn_props = sgn_props[0]['properties']

        return sgn_props


    def writeSGPropertyFile(
        self, props_to_write:list, file_name:str='', file_format:str='',
        prop_name_map:dict={},
        **kwargs
        ) -> None:
        """Write SG property to a file.

        Parameters
        ----------
        props_to_write:list
            List of properties to write.
        file_name:str
            File name to write the properties.
        """

        _fn = file_name
        if _fn == '':
            _fn = self._fn_prop

        logger.info(f'[{self.name}] writing SG properties to file {_fn}...')

        if self._model:
            self._model.writePropsToFile(
                _fn, self._sg_model_sets, self._sg_assignments,
                props_to_write,
                prop_name_map=prop_name_map,
                **kwargs
            )

        else:
            mext.writeSGPropertyFile(
                _fn, self._solver,
                self._sg_model_sets, self._entity_sets,
                physics=self._physics,
                **self._config
                )

        return

    def setParameter(self, name:str, value):
        self._parameters[name] = value

    def getParameter(self, name:str):
        return self._parameters[name]

    def updateParameters(self, parameters:dict):
        logger.debug(f'[{self.name}] updating parameters...')
        logger.debug('input parameters')
        logger.debug(parameters)

        if self._parameters is None:
            self._parameters = {}

        _tmp_params = copy.deepcopy(parameters)
        for k, v in self._parameters.items():
            logger.debug(f'[{self.name}]  updating {k} = {v}')

            _vnew = None

            # Direct substitution
            if k in parameters.keys():
                _vnew = parameters[k]

            # Evaluate string expressions
            if isinstance(v, str):
                _str = v.split(':')
                if len(_str) > 1 and _str[0] == 'f':
                    _vnew = eval(_str[1], _tmp_params)

            if not _vnew is None:
                logger.debug(f'[{self.name}]           {k} = {_vnew}')
                self._parameters[k] = _vnew
                # _tmp_params[k] = _vnew

            _tmp_params[k] = copy.deepcopy(self._parameters[k])

        self._design.updateParameters(parameters)

        logger.debug(f'[{self.name}] self._parameters')
        logger.debug(self._parameters)
        logger.debug(f'[{self.name}] self._design.parameters')
        logger.debug(self._design.parameters)

    # def substituteParameters(self, additional_params:dict={}) -> None:
    #     """Substitute parameters in the design.
    #     """
    #     logger.debug(f'[{self.name}] substituting parameters...')
    #     mutils.substituteParams(self._base, self._parameters)
    #     for _distr in self._distributions:
    #         _distr.updateCoefficient(self._parameters)

    def getConfig(self, name:str):
        return self._config[name]

    def addSGAssignment(self, assignment):
        self._sg_assignments.append(assignment)

    # def getMacroStateCase(self, case:dict):
    #     for _sc in self._macro_state_cases:
    #         if _sc.case == case:
    #             return _sc

    # def getLocalStateCase(self, case:dict):
    #     for _sc in self._local_state_cases:
    #         if _sc.case == case:
    #             return _sc

    def addMacroStateLocCase(self, state_locase:StateLocCase):
        self._macro_state_locases.append(state_locase)

    def addLocalStateLocCase(self, state_locase:StateLocCase):
        self._local_state_locases.append(state_locase)

    def getLocalState(self, state_name, loc=[], loc_id=0, case={}):
        state = None

        if loc != []:
            ...
        else:
            _loc_state_cases = self._local_state_locases[loc_id]

        _loc_case_states = _loc_state_cases.getStateCase(case)

        state = _loc_case_states.getState(state_name).data

        return state

    def copy(self, parameters:dict={}) -> StructureModel:
        """Make a copy of this object
        and optionally substitute new values to parameters.

        Parameters
        ----------
        parameters:dict
            New parameters.

        Returns
        -------
        StructureModel
            Copied object.
        """
        new = copy.deepcopy(self)
        new._design = new._design.copy(parameters)
        for _name, _value in new.parameters.items():
            try:
                new.setParameter(name=_name, value=parameters[_name])
            except KeyError:
                pass
        return new

    def addMeshNodeData(self, name, data) -> None:
        """Add node data to the mesh object.

        Parameters
        ----------
        name:str
            Name of the data.
        data:dict
            Data of each node.

            ..  code-block::

                data = {
                  1: value1,
                  2: value2,
                  ...
                }
        """
        # Add data to the mesh object
        _list_data = [None,] * len(data)
        for _i, _v in data.items():
            _list_data[_i-1] = _v

        self.mesh.point_data[name] = np.array(_list_data)


    def group_distributions_by_region(self):
        """Group distributions by region for efficient processing."""
        region_distributions = {}
        
        for _distr in self.design.distributions:
            region = _distr.region
            if region not in region_distributions:
                region_distributions[region] = []
            region_distributions[region].append(_distr)
            
        return region_distributions


    def get_transformed_nodes(self):
        """Get mesh nodes with appropriate transformations applied."""
        if self.mesh_nodes_pd:
            return copy.deepcopy(self.mesh_nodes_pd)
        
        _nodes = copy.deepcopy(self.getMeshNodes())
        
        # Transform for iVABS
        if GLOBAL.VARIANT == 'ivabs':
            for _nid, _ncoords in _nodes.items():
                _nodes[_nid] = _ncoords[:1]
                
        return _nodes


    def get_element_regions(self, entity_sets):
        """Map each element to its regions."""
        element_regions = {}
        for _set_name, _set in entity_sets.items():
            if _set['type'] == 'element':
                for _eid in _set['items']:
                    if _eid not in element_regions:
                        element_regions[_eid] = []
                    element_regions[_eid].append(_set_name)
        
        # Add 'all' region for elements without specific region
        _elements = self.getMeshElements()
        for _eid in _elements:
            if _eid not in element_regions:
                element_regions[_eid] = ['all']
                
        return element_regions


    def get_region_nodes(self, region, element_regions, elements):
        """Get all unique nodes in a region."""
        region_nodes = set()
        
        # For 'all' region, include all nodes
        if region == 'all':
            for _eid, _enodes in elements.items():
                region_nodes.update(_enodes)
            return region_nodes
            
        # For specific regions
        for _eid, regions in element_regions.items():
            if region in regions:
                region_nodes.update(elements[_eid])
                
        return region_nodes


    def get_element_sg_models(self, element_regions):
        """Get SG model for each element based on regions and assignments."""
        element_sg_models = {}
        
        for _eid, regions in element_regions.items():
            # Find SG assignment for this element
            _sg_assign = None
            _sg_model = None
            
            for _sa in self.sg_assignments:
                if _sa.region in regions or _sa.region == 'all':
                    _sg_assign = _sa
                    _sg_model = _sa.sg_model
                    break
            
            if _sg_assign is None:
                logger.warning(f'Element {_eid} is not assigned with any {GLOBAL.SG_KEY.upper()}.')
            elif _sg_assign.location.startswith('element'):
                _sg_assign.addRegionEntity(_eid)
                
            element_sg_models[_eid] = (_sg_model, _sg_assign)
            
        return element_sg_models


    # def calcParamsFromDistributions(self, max_workers=None) -> None:
    #     """
    #     Calculate specific values of parameters for each element and node
    #     using the distributions.

    #     Parameters
    #     ----------
    #     max_workers : int, optional
    #         Maximum number of parallel processes to use. If None, uses the default
    #         (number of CPU cores). If 1, runs in serial mode.

    #     Update the `_discrete_sg_params` attribute.
    #     """
    #     logger.debug('')
    #     logger.info(f'[{self.name}] calculating parameters from distributions...')
    #     logger.info(f'Using {max_workers if max_workers else "default"} parallel processes')

    #     # Get mesh nodes and elements
    #     _nodes = {}
    #     if self.mesh_nodes_pd:
    #         _nodes = copy.deepcopy(self.mesh_nodes_pd)
    #     else:
    #         _nodes = copy.deepcopy(self.getMeshNodes())

    #         # Transform
    #         if GLOBAL.VARIANT == 'ivabs':
    #             # For iVABS, by default, use x1 to evaluate distributions
    #             for _nid, _ncoords in _nodes.items():
    #                 _nodes[_nid] = _ncoords[:1]

    #     _elements = self.getMeshElements()
    #     _entity_sets = self.getEntitySets()
    #     logger.debug(f'_entity_sets = {_entity_sets}')

    #     # Pre-calculate region assignments for elements
    #     element_regions = {}
    #     for _set_name, _set in _entity_sets.items():
    #         if _set['type'] == 'element':
    #             for _eid in _set['items']:
    #                 if _eid not in element_regions:
    #                     element_regions[_eid] = []
    #                 element_regions[_eid].append(_set_name)

    #     # Pre-calculate SG assignments for regions
    #     region_sg_assigns = {}
    #     for _sa in self.sg_assignments:
    #         if _sa.region == 'all':
    #             region_sg_assigns['all'] = _sa
    #         else:
    #             region_sg_assigns[_sa.region] = _sa

    #     # Pre-calculate distributions for regions
    #     region_distributions = {}
    #     for _distr in self.design.distributions:
    #         if _distr.region == 'all':
    #             if 'all' not in region_distributions:
    #                 region_distributions['all'] = []
    #             region_distributions['all'].append(_distr)
    #         else:
    #             if _distr.region not in region_distributions:
    #                 region_distributions[_distr.region] = []
    #             region_distributions[_distr.region].append(_distr)

    #     # Create a manager for shared data
    #     manager = Manager()
    #     _region_node_values = manager.dict()
    #     _cache_lock = manager.Lock()  # Create a proper lock object

    #     # Use ProcessPoolExecutor for parallel processing
    #     with ProcessPoolExecutor(max_workers=max_workers) as executor:
    #         # Submit all elements for processing
    #         future_to_element = {
    #             executor.submit(
    #                 process_element,
    #                 _eid, _enodes, _nodes, element_regions, region_sg_assigns, 
    #                 region_distributions, _region_node_values, _cache_lock
    #             ): _eid 
    #             for _eid, _enodes in _elements.items()
    #         }

    #         # Collect results as they complete
    #         logger.info(f'Processing {len(_elements)} elements...')
    #         for future in as_completed(future_to_element):
    #             _eid = future_to_element[future]
    #             try:
    #                 element_data = future.result()
    #                 logger.debug(f'Element {_eid} processed successfully')
    #                 logger.debug(f'Element {_eid} data: {element_data}')
    #                 self._discrete_sg_params.update(element_data)
    #             except Exception as e:
    #                 logger.error(f'Error processing element {_eid}: {str(e)}')
    #                 raise




    # def _getSpecificSgModelName(
    #     self, sg_model_base_name, sg_params:dict,
    #     db_sg_model, db_sg_model_sets:DataBase=None):
    #     """
    #     Get or create a specific SG model name based on parameters.
    #     Uses a more efficient parameter comparison and caching strategy.
    #     """
    #     logger.debug('='*16)
    #     logger.debug(f'[{self.name}] getting specific SG model name...')

    #     # Get base SG model parameters
    #     _sg_model = db_sg_model.getItemByName(sg_model_base_name)
    #     _sg_params = _sg_model.design.parameters
    #     _sg_params.update(_sg_model.parameters)

    #     # Filter parameters to only those related to the SG
    #     _curr_params = {k: v for k, v in sg_params.items() if k in _sg_params}

    #     # Create a hashable key for parameter comparison
    #     param_key = tuple(sorted(_curr_params.items()))

    #     # Check if we already have a set for these parameters
    #     if not hasattr(self, '_param_set_map'):
    #         self._param_set_map = {}
    #         self._sg_param_sets = {}
    #         self._sg_model_sets = {}

    #     if param_key in self._param_set_map:
    #         _param_set_name = self._param_set_map[param_key]
    #     else:
    #         # Create new parameter set
    #         _param_set_id = len(self._sg_param_sets) + 1
    #         _param_set_name = f'set{_param_set_id}'
    #         self._sg_param_sets[_param_set_name] = _curr_params
    #         self._param_set_map[param_key] = _param_set_name
    #         logger.debug(f'new param set ({_param_set_name}): {_curr_params}')

    #     # Create specific SG model if needed
    #     sp_sg_model_name = f'{sg_model_base_name}_{_param_set_name}'
    #     if sp_sg_model_name not in self._sg_model_sets:
    #         _sg_model = db_sg_model.getItemByName(sg_model_base_name).copy(sg_params)
    #         _sg_model.name = sp_sg_model_name
    #         self._sg_model_sets[sp_sg_model_name] = _sg_model
    #         logger.debug(f'new sg model ({sp_sg_model_name})')

    #     return sp_sg_model_name




    def _handle_no_distributions(self, db_sg_model):
        """Handle case when there are no distributions.
        
        Parameters
        ----------
        db_sg_model : DataBase
            Database of SG models
        """
        logger.debug('no distributions')

        if self.level == 0:
            _sg_name = db_sg_model.data[0].name
        else:
            _sg_name = self._design.sg_assignments[0].sg_model

        logger.debug(f'_sg_name = {_sg_name}')

        _set_name = 'set1'
        self._sg_param_sets = {
            _set_name: {GLOBAL.SG_KEY: _sg_name, 'params': {}}
        }

        entity_sets = {}
        _elements = self.getMeshElements()

        if len(_elements) > 0:
            entity_sets.update({
                _set_name: {
                    'type': 'element',
                    'items': list(_elements.keys())
                }
            })
        else:
            entity_sets.update({_set_name: {'type': 'any', 'items': [1,]}})

        # Add SG model sets
        _param = {}
        _sg_model = db_sg_model.getItemByName(_sg_name).copy(_param)
        _sg_model.name = f'{_sg_model.name}_{_set_name}'
        self._sg_model_sets[_set_name] = _sg_model

        # Add assignments
        try:
            for _i in entity_sets[_set_name]['items']:
                try:
                    self._design.sg_assignments[0].addDiscreteSGAssign(_i, _sg_model.name)
                except IndexError:
                    logger.warning(f'No SG assignment found')
        except Exception as e:
            logger.error(f'Error adding assignments: {str(e)}')

        # Add element sets
        self._entity_sets.update(entity_sets)


    # def _process_sg_assignments(self, db_sg_model, db_sg_model_sets=None):
    #     """Process SG assignments and create parameter sets.
        
    #     Parameters
    #     ----------
    #     db_sg_model : DataBase
    #         Database of SG models
    #     db_sg_model_sets : DataBase, optional
    #         Database of specific SG models
    #     """
    #     _elem_data = {}
        
    #     # Calculate parameters at each node
    #     self.calcParamsFromDistributions(max_workers=1)

    #     # Process each SG assignment
    #     for _sg_assign in self._design.sg_assignments:
    #         logger.debug('-'*16)
    #         logger.debug(f'{_sg_assign.region} ({_sg_assign.location}): {_sg_assign.region_entities}')

    #         if _sg_assign.location == 'node':
    #             self._process_node_assignments(_sg_assign, db_sg_model, db_sg_model_sets)
    #         elif _sg_assign.location.startswith('element'):
    #             self._process_element_assignments(_sg_assign, _elem_data, db_sg_model, db_sg_model_sets)

    #     # Store the data for visualization
    #     self.addDataToMesh(data=_elem_data, loc='element')


    # def _process_node_assignments(self, _sg_assign, db_sg_model, db_sg_model_sets):
    #     """Process node-based SG assignments.
        
    #     Parameters
    #     ----------
    #     _sg_assign : SGAssignment
    #         The SG assignment to process
    #     db_sg_model : DataBase
    #         Database of SG models
    #     db_sg_model_sets : DataBase
    #         Database of specific SG models
    #     """
    #     _nids_done = []
    #     for _eid, _en_sg_params in self._discrete_sg_params.items():
    #         for _nid, _nparam in _en_sg_params.items():
    #             if not _nid in _sg_assign.region_entities or _nid in _nids_done:
    #                 continue
                    
    #             _sg_name = _nparam['sg']
    #             _sp_sg_model_name = self._getSpecificSgModelName(
    #                 _nparam['sg'], _nparam['params'],
    #                 db_sg_model, db_sg_model_sets
    #             )

    #             if not _sp_sg_model_name in self._entity_sets:
    #                 self._entity_sets[_sp_sg_model_name] = {
    #                     'type': 'node',
    #                     'items': []
    #                 }
    #             self._entity_sets[_sp_sg_model_name]['items'].append(_nid)

    #             _sg_assign.addDiscreteSGAssign(_nid, _sp_sg_model_name)
    #             _nids_done.append(_nid)


    # def _process_element_assignments(self, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets):
    #     """Process element-based SG assignments.
        
    #     Parameters
    #     ----------
    #     _sg_assign : SGAssignment
    #         The SG assignment to process
    #     _elem_data : dict
    #         Dictionary to store element data
    #     db_sg_model : DataBase
    #         Database of SG models
    #     db_sg_model_sets : DataBase
    #         Database of specific SG models
    #     """
    #     for _eid in _sg_assign.region_entities:
    #         logger.debug('-'*8)
    #         logger.debug(f'element {_eid}')

    #         _en_sg_params = self._discrete_sg_params[_eid]

    #         if _sg_assign.location == 'element':
    #             self._process_whole_element(_eid, _en_sg_params, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets)
    #         elif _sg_assign.location == 'element_node':
    #             self._process_element_nodes(_eid, _en_sg_params, _sg_assign, db_sg_model, db_sg_model_sets)


    # def _process_whole_element(self, _eid, _en_sg_params, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets):
    #     """Process an element as a whole unit.
        
    #     Parameters
    #     ----------
    #     _eid : int
    #         Element ID
    #     _en_sg_params : dict
    #         Parameters for the element's nodes
    #     _sg_assign : SGAssignment
    #         The SG assignment
    #     _elem_data : dict
    #         Dictionary to store element data
    #     db_sg_model : DataBase
    #         Database of SG models
    #     db_sg_model_sets : DataBase
    #         Database of specific SG models
    #     """
    #     _esg = ''
    #     _eparams = {}
        
    #     # Collect parameters from all nodes
    #     for _nid, _nparam in _en_sg_params.items():
    #         _esg = _nparam['sg']
    #         for _pname, _pvalue in _nparam['params'].items():
    #             if _pname not in _eparams:
    #                 _eparams[_pname] = []
    #             _eparams[_pname].append(_pvalue)

    #     # Average parameters across nodes
    #     for _pname, _pvalue in _eparams.items():
    #         _ptype = self._get_parameter_type(_pname)
    #         _eparams[_pname] = self._average_parameter_values(_pvalue, _ptype)
            
    #         if _pname not in _elem_data:
    #             _elem_data[_pname] = {}
    #         _elem_data[_pname][_eid] = _eparams[_pname]

    #     # Create specific SG model
    #     _sp_sg_model_name = self._getSpecificSgModelName(
    #         _esg, _eparams, db_sg_model, db_sg_model_sets
    #     )

    #     # Update entity sets and assignments
    #     if _sp_sg_model_name not in self._entity_sets:
    #         self._entity_sets[_sp_sg_model_name] = {
    #             'type': 'element',
    #             'items': []
    #         }
    #     self._entity_sets[_sp_sg_model_name]['items'].append(_eid)
    #     _sg_assign.addDiscreteSGAssign(_eid, _sp_sg_model_name)


    # def _process_element_nodes(self, _eid, _en_sg_params, _sg_assign, db_sg_model, db_sg_model_sets):
    #     """Process individual nodes of an element.
        
    #     Parameters
    #     ----------
    #     _eid : int
    #         Element ID
    #     _en_sg_params : dict
    #         Parameters for the element's nodes
    #     _sg_assign : SGAssignment
    #         The SG assignment
    #     db_sg_model : DataBase
    #         Database of SG models
    #     db_sg_model_sets : DataBase
    #         Database of specific SG models
    #     """
    #     _sg_model_names = []
    #     for _nid, _nparam in _en_sg_params.items():
    #         _sp_sg_model_name = self._getSpecificSgModelName(
    #             _nparam['sg'], _nparam['params'],
    #             db_sg_model, db_sg_model_sets
    #         )
    #         _sg_model_names.append(_sp_sg_model_name)

    #     _sg_assign.addDiscreteSGAssign(_eid, _sp_sg_model_name)


    def _get_parameter_type(self, param_name):
        """Get the type of a parameter from distributions.
        
        Parameters
        ----------
        param_name : str
            Name of the parameter
            
        Returns
        -------
        str
            Type of the parameter ('str', 'int', or 'float')
        """
        for _distr in self.design.distributions:
            if param_name in _distr.name:
                return _distr.vtype
        return 'float'  # Default to float if not found


    def _average_parameter_values(self, values, param_type):
        """Average parameter values based on their type.
        
        Parameters
        ----------
        values : list
            List of parameter values
        param_type : str
            Type of the parameter
            
        Returns
        -------
        Any
            Averaged parameter value
        """
        if param_type == 'str':
            return values[0]  # Take first value for strings
        else:
            avg = sum(values) / len(values)
            return int(avg) if param_type == 'int' else avg


    # def discretizeDesign(self, db_sg_model, db_sg_model_sets:DataBase=None) -> None:
    #     """Calculate the discrete SG designs based on the structural mesh.

    #     Parameters
    #     ----------
    #     db_sg_model : DataBase
    #         Database of SG models
    #     db_sg_model_sets : DataBase, optional
    #         Database of specific SG models
    #     """
    #     logger.debug('='*16)
    #     logger.info(f'[lv{self.level}:{self.name}] discretizing the design...')

    #     if self.level > 0 and len(self._design.sg_assignments) == 0:
    #         return

    #     logger.debug(f'self._design.sg_assignments = {self._design.sg_assignments}')
    #     for _sa in self._design.sg_assignments:
    #         logger.debug(_sa.sg_model)

    #     try:
    #         if len(self.design.distributions) == 0:
    #             self._handle_no_distributions(db_sg_model)
    #         else:
    #             self._process_sg_assignments(db_sg_model, db_sg_model_sets)

    #         # Collect all discrete SG assignments
    #         for _sa in self._design.sg_assignments:
    #             self._discrete_sg_assigns.extend(_sa.discrete_sg_assigns)

    #         # Write mesh data
    #         self.writeMeshData()

    #         # Process sub-level SGs
    #         logger.debug(f'self._sg_model_sets = {self._sg_model_sets}')
    #         for _name, _sg_model in self._sg_model_sets.items():
    #             _sg_model.discretizeDesign(db_sg_model, db_sg_model_sets)

    #     except Exception as e:
    #         logger.error(f'Error in discretizeDesign: {str(e)}')
    #         raise
