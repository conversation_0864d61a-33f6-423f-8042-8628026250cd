#

version: "0.9"

# Design parameters/variables of the structure
# ====================================================================
structure:
  name: "cs"
  parameter:
    topo: 'airfoil_gbox_uni.xml.tmp'
    a2pan: -0.75  # Place the origin of the beam ref sys at quarder chord
    a3pan: 0
  design:
    type: "discrete"
    dim: 1
    # section_locations: [0.25, 0.96]
    # section_locations: [0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    cs_assignment:
      - region: 'all'
        cs: "main_cs"
        model: 'b2'
  cs:
    main_cs:
      base: "cs1"
      model: "md1"
      submodel: 2

# CS base design
# ====================================================================
cs:
  - name: "cs1"
    parameters:
      topo: 'airfoil_gbox_uni.xml.tmp'
      mdb_name: "material_database_us_ft"
      airfoil: "sc1095.dat"
      airfoil_point_order: -1
      chord: 1.73
      a2p1: 0.8
      a2p3: 0.6
      lam_skin: "T300 15k/976_0.0053"
      lam_front: "T300 15k/976_0.0053"
      lam_back: "T300 15k/976_0.0053"
      lam_spar_1: "T300 15k/976_0.0053"
      ang_spar_1: 0
      ply_spar_1: 4
      lam_cap: "Aluminum 8009_0.01"
      mat_nsm: "lead"
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "Plascore PN2-3/16OX3.0"
      rnsm: 0.001
      gms: 0.004
    design:
      dim: 2
      tool: "prevabs"
      base_file: topo
    model:
      md1:
        tool: "vabs"

# Analysis process
# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      # setting:
      #   timeout: 60
      output:
        - value: [
            'mu',
            'ea', 'gayy', 'gazz', "gj", "eiyy", "eizz",
            'mcy', 'mcz', 'tcy', 'tcz', 'scy', 'scz',
            'phi_pia', 'phi_pba', 'phi_psa',
            'stf14', 'stf15', 'stf16', 'stf45', 'stf46', 'stf56'
            ]

# Configurations of design study, e.g., parameter study, optimization, etc.
# Mainly for Dakota
# ====================================================================
study:
  method:
    format: "keyword"
    multidim_parameter_study:
      partitions: [12, 7]
  variables:
    data_form: "compact"
    data: |
      ang_spar_1,     design, continuous, -90:90
      ply_spar_1,  design, discrete, range, 1:8
  responses:
    data_form: "explicit"
    response_functions:
      - descriptor: 'mu'
      - descriptor: 'ea'
      - descriptor: 'gayy'
      - descriptor: 'gazz'
      - descriptor: 'gj'
      - descriptor: 'eiyy'
      - descriptor: 'eizz'
      - descriptor: 'mcy'
      - descriptor: 'mcz'
      - descriptor: 'tcy'
      - descriptor: 'tcz'
      - descriptor: 'scy'
      - descriptor: 'scz'
      - descriptor: 'phi_pia'
      - descriptor: 'phi_pba'
      - descriptor: 'phi_psa'
      - descriptor: 'stf14'
      - descriptor: 'stf15'
      - descriptor: 'stf16'
      - descriptor: 'stf45'
      - descriptor: 'stf46'
      - descriptor: 'stf56'
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: off
      work_directory:
        directory_tag: on
        directory_save: off
    required_files:
      - "support/*"
    asynchronous:
      evaluation_concurrency: 20
    failure_capture:
      recover: [nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan]
