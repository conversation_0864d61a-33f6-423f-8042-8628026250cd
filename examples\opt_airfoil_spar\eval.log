INFO     [2024-03-29 15:04:42] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:04:42] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:04:42] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:13:06] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:13:06] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:13:06] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:15:19] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:15:19] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:15:19] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:16:23] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:16:23] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:16:23] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:48:31] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:48:31] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:48:31] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:49:13] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:49:13] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:49:13] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:50:28] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:50:28] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:50:28] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:51:24] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:51:24] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:51:24] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 15:55:36] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 15:55:36] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 15:55:36] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 16:13:36] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 16:13:36] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 16:13:36] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 16:23:46] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 16:23:46] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 16:23:46] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 16:40:15] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 16:40:15] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 16:40:15] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 16:41:14] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 16:41:14] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 16:41:14] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:20:26] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:20:26] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:20:26] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:22:05] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:22:05] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:22:05] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:23:52] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:23:52] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:23:52] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:24:48] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:24:48] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:24:48] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:29:25] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:29:25] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:29:25] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:32:01] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:32:01] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:32:01] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:36:02] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:36:02] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:36:02] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:36:17] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:36:17] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:36:17] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-29 18:37:12] __main__.main :: 

********************************************************************** 
CRITICAL [2024-03-29 18:37:12] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-29 18:37:12] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-31 17:25:41] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-31 17:25:41] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-31 17:56:40] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-31 17:56:40] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-17 18:02:16] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-17 18:02:16] dkt.runDakota :: calling: dakota -i main.dakota 
