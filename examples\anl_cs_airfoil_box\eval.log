[17:43:35] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[17:43:35] INFO     reading main input main.yml... [io.readMSGDInput]
[17:43:35] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x0000021497BD2DC0>,
 'variant': 'ivabs'} [io.readMSGDInput]
[17:43:35] DEBUG    currect working directory:  [io.readMSGDInput]
[17:43:35] DEBUG    input file name: main [io.readMSGDInput]
[17:43:35] DEBUG    input file extension: .yml [io.readMSGDInput]
[17:43:35] DEBUG    output file name: main.out [io.readMSGDInput]
[17:43:35] DEBUG    variant: ivabs [io.readMSGDInput]
[17:43:35] DEBUG    version: 0.10 [io.readMSGDInput]
[17:43:35] DEBUG    _sg_assignments = [sg assignment of main_cs to region all (element)] [io.readInputStructure]
[17:43:35] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[17:43:35] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[17:43:35] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[17:43:35] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[17:43:35] INFO     updating current design... [_msgd.updateData]
[17:43:35] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[17:43:35] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[17:43:35] DEBUG    subsituting parameters... [container.substituteParams]
[17:43:35] DEBUG    before substitution [container.substituteParams]
[17:43:35] DEBUG    inputs: None [container.substituteParams]
[17:43:35] DEBUG    params: {} [container.substituteParams]
[17:43:35] DEBUG    after substitution [container.substituteParams]
[17:43:35] DEBUG    inputs: None [container.substituteParams]
[17:43:35] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[17:43:35] DEBUG    self._config = {} [_structure.loadStructureMesh]
[17:43:35] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[17:43:35] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[17:43:35] DEBUG    updated design (global structure): [_msgd.updateData]
[17:43:35] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': None} [_msgd.updateData]
[17:43:35] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[17:43:35] INFO     [main] discretizing the structure... [_msgd.discretize]
[17:43:35] DEBUG    structure model:
{'cs_assignment': [{'cs': 'main_cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': None,
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[17:43:35] DEBUG    ================ [_structure.discretizeDesign]
[17:43:35] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[17:43:35] DEBUG     [_structure.calcParamsFromDistributions]
[17:43:35] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[17:43:35] DEBUG    getting entity sets... [_structure.getEntitySets]
[17:43:35] DEBUG    sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.getEntitySets]
[17:43:35] DEBUG    _point_sets = {} [_structure.getEntitySets]
[17:43:35] DEBUG    _cell_sets = {} [_structure.getEntitySets]
[17:43:35] DEBUG    _entity_sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.calcParamsFromDistributions]
[17:43:35] DEBUG    ---------------- [_structure.discretizeDesign]
[17:43:35] DEBUG    all (element): [] [_structure.discretizeDesign]
[17:43:35] INFO     writing mesh data to file blade_mesh.msh... [_structure.writeMeshData]
[17:43:35] INFO     [main] going through steps... [_msgd.analyze]
[17:43:35] DEBUG    step config:
{'activate': True,
 'analysis': 'h',
 'output': {'value': ['gj', 'eiyy', 'eizz']},
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': 'cs'} [_msgd.analyze]
[17:43:35] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[17:43:35] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[17:43:35] DEBUG    [main_cs_set1] updating parameters... [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil] updating parameters... [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating a2p1 = 0.82 [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating a2p3 = 0.58 [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating mdb_name = material_database_us_ft [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating airfoil = SC1095.dat [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating gms = 0.004 [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating lam_spar = cfrp_0.0053 [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating seq_spar = [-45/0/45/90] [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating seq_front = [0] [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating seq_back = [0] [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating fill_mat_front = foam [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating fill_mat_back = honeycomb [_structure.updateParameters]
[17:43:35] DEBUG    [airfoil]  updating mat_nsm = metal [_structure.updateParameters]
[17:43:35] DEBUG    [cs: main_cs_set1] running cs analysis... [sg.runH]
[17:43:35] DEBUG    [airfoil] substituting parameters... [_structure.substituteParameters]
[17:43:35] DEBUG    subsituting parameters... [container.substituteParams]
[17:43:35] DEBUG    before substitution [container.substituteParams]
[17:43:35] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[17:43:35] DEBUG    params: {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [container.substituteParams]
[17:43:35] DEBUG    after substitution [container.substituteParams]
[17:43:35] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[17:43:35] DEBUG    building 2D SG (prevabs): main_cs_set1... [main.buildSGModel]
[17:43:35] DEBUG    building cs using prevabs... [main.buildSGFromPrevabs]
[17:43:35] DEBUG    parameters = {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [main.buildSGFromPrevabs]
[17:43:35] DEBUG    fns_include = ['material_database_us_ft.xml', 'SC1095.dat'] [main.buildSGFromPrevabs]
[17:43:35] DEBUG    prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h [execu.run]
[17:43:36] DEBUG    _vabs._readHeader :: reading header... 
[17:43:36] DEBUG    _vabs._readMesh :: reading mesh... 
[17:43:36] DEBUG    _vabs._readMaterialRotationCombinations :: reading combinations of material and in-plane rotations... 
[17:43:36] DEBUG    _vabs._readMaterials :: reading materials... 
[17:43:36] DEBUG    execu.run :: VABS cs\main_cs_set1.sg 
[17:43:36] DEBUG    execu.run :: return code: 0 
[17:43:36] DEBUG    execu.run :: stdout:
 
 Homogenization for computing sectional properties
 
 The inputs are echoed in cs\main_cs_set1.sg.ech
 
 Finished reading inputs for the cross-sectional analysis.
 You can run VABS for   0 days....
 
 Finished constitutive modeling
 
 Cross-sectional properties can be found in  "cs\main_cs_set1.sg.K"
 
 Finished outputing constitutive modeling results
 
 VABS finished successfully
 
 VABS Runs for    0.3750000      Seconds.
 
[17:43:36] DEBUG    execu.run :: stderr: 
[17:43:36] DEBUG    execu.run :: VABS finished successfully 
[17:43:36] DEBUG    sg_model.data_format = vabs [sg.runH]
[17:43:36] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[15:32:11] INFO     reading main input main.yml... [io.readMSGDInput]
[15:32:11] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[15:32:11] INFO     updating current design... [_msgd.updateData]
[15:32:11] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[15:32:11] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[15:32:11] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[15:32:11] INFO     [main] discretizing the structure... [_msgd.discretize]
[15:32:11] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[15:32:11] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[15:32:11] INFO     writing mesh data to file blade_mesh.msh... [_structure.writeMeshData]
[15:32:11] INFO     [main] going through steps... [_msgd.analyze]
[15:32:11] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[15:32:11] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[15:32:12] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[15:32:44] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[15:32:44] INFO     reading main input main.yml... [io.readMSGDInput]
[15:32:44] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x0000015834EC7D60>,
 'variant': 'ivabs'} [io.readMSGDInput]
[15:32:44] DEBUG    currect working directory:  [io.readMSGDInput]
[15:32:44] DEBUG    input file name: main [io.readMSGDInput]
[15:32:44] DEBUG    input file extension: .yml [io.readMSGDInput]
[15:32:44] DEBUG    output file name: main.out [io.readMSGDInput]
[15:32:44] DEBUG    variant: ivabs [io.readMSGDInput]
[15:32:44] DEBUG    version: 0.10 [io.readMSGDInput]
[15:32:44] DEBUG    _sg_assignments = [sg assignment of main_cs to region all (element)] [io.readInputStructure]
[15:32:44] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[15:32:44] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[15:32:44] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[15:32:44] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[15:32:44] INFO     updating current design... [_msgd.updateData]
[15:32:44] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[15:32:44] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[15:32:44] DEBUG    subsituting parameters... [container.substituteParams]
[15:32:44] DEBUG    before substitution [container.substituteParams]
[15:32:44] DEBUG    inputs: {} [container.substituteParams]
[15:32:44] DEBUG    params: {} [container.substituteParams]
[15:32:44] DEBUG    after substitution [container.substituteParams]
[15:32:44] DEBUG    inputs: {} [container.substituteParams]
[15:32:44] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[15:32:44] DEBUG    self._config = {} [_structure.loadStructureMesh]
[15:32:44] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[15:32:44] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[15:32:44] DEBUG    updated design (global structure): [_msgd.updateData]
[15:32:44] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': {}} [_msgd.updateData]
[15:32:44] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[15:32:44] INFO     [main] discretizing the structure... [_msgd.discretize]
[15:32:44] DEBUG    structure model:
{'cs_assignment': [{'cs': 'main_cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': {},
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[15:32:44] DEBUG    ================ [_structure.discretizeDesign]
[15:32:44] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[15:32:44] DEBUG     [_structure.calcParamsFromDistributions]
[15:32:44] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[15:32:44] DEBUG    getting entity sets... [_structure.getEntitySets]
[15:32:44] DEBUG    sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.getEntitySets]
[15:32:44] DEBUG    _point_sets = {} [_structure.getEntitySets]
[15:32:44] DEBUG    _cell_sets = {} [_structure.getEntitySets]
[15:32:44] DEBUG    _entity_sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.calcParamsFromDistributions]
[15:32:44] DEBUG    ---------------- [_structure.discretizeDesign]
[15:32:44] DEBUG    all (element): [] [_structure.discretizeDesign]
[15:32:44] INFO     writing mesh data to file blade_mesh.msh... [_structure.writeMeshData]
[15:32:44] INFO     [main] going through steps... [_msgd.analyze]
[15:32:44] DEBUG    step config:
{'activate': True,
 'analysis': 'h',
 'output': {'value': ['gj', 'eiyy', 'eizz']},
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': 'cs'} [_msgd.analyze]
[15:32:44] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[15:32:44] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[15:32:44] DEBUG    [main_cs_set1] updating parameters... [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil] updating parameters... [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating a2p1 = 0.82 [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating a2p3 = 0.58 [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating mdb_name = material_database_us_ft [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating airfoil = SC1095.dat [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating gms = 0.004 [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating lam_spar = cfrp_0.0053 [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating seq_spar = [-45/0/45/90] [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating seq_front = [0] [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating seq_back = [0] [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating fill_mat_front = foam [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating fill_mat_back = honeycomb [_structure.updateParameters]
[15:32:44] DEBUG    [airfoil]  updating mat_nsm = metal [_structure.updateParameters]
[15:32:44] DEBUG    [cs: main_cs_set1] running cs analysis... [sg.runH]
[15:32:44] DEBUG    [airfoil] substituting parameters... [_structure.substituteParameters]
[15:32:44] DEBUG    subsituting parameters... [container.substituteParams]
[15:32:44] DEBUG    before substitution [container.substituteParams]
[15:32:44] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[15:32:44] DEBUG    params: {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [container.substituteParams]
[15:32:44] DEBUG    after substitution [container.substituteParams]
[15:32:44] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[15:32:44] DEBUG    building 2D SG (prevabs): main_cs_set1... [main.buildSGModel]
[15:32:44] DEBUG    building cs using prevabs... [main.buildSGFromPrevabs]
[15:32:44] DEBUG    parameters = {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [main.buildSGFromPrevabs]
[15:32:44] DEBUG    fns_include = ['material_database_us_ft.xml', 'SC1095.dat'] [main.buildSGFromPrevabs]
[15:32:44] DEBUG    prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h [execu.run]
[15:32:44] DEBUG    _vabs._readHeader :: reading header... 
[15:32:44] DEBUG    _vabs._readMesh :: reading mesh... 
[15:32:44] DEBUG    _vabs._readMaterialRotationCombinations :: reading combinations of material and in-plane rotations... 
[15:32:44] DEBUG    _vabs._readMaterials :: reading materials... 
[15:32:44] DEBUG    execu.run :: VABS cs\main_cs_set1.sg 
[15:32:44] DEBUG    execu.run :: return code: 0 
[15:32:44] DEBUG    execu.run :: stdout:

 Homogenization for computing sectional properties

 The inputs are echoed in cs\main_cs_set1.sg.ech

 Finished reading inputs for the cross-sectional analysis.
 You can run VABS for   0 days....

 Finished constitutive modeling

 Cross-sectional properties can be found in  "cs\main_cs_set1.sg.K"

 Finished outputing constitutive modeling results

 VABS finished successfully

 VABS Runs for     7.81250000E-02  Seconds.
 
[15:32:44] DEBUG    execu.run :: stderr: 
[15:32:44] DEBUG    execu.run :: VABS finished successfully 
[15:32:44] DEBUG    sg_model.data_format = vabs [sg.runH]
[15:32:44] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[15:34:19] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[15:34:19] INFO     reading main input main.yml... [io.readMSGDInput]
[15:34:19] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x000001ECE3A48D60>,
 'variant': 'ivabs'} [io.readMSGDInput]
[15:34:19] DEBUG    currect working directory:  [io.readMSGDInput]
[15:34:19] DEBUG    input file name: main [io.readMSGDInput]
[15:34:19] DEBUG    input file extension: .yml [io.readMSGDInput]
[15:34:19] DEBUG    output file name: main.out [io.readMSGDInput]
[15:34:19] DEBUG    variant: ivabs [io.readMSGDInput]
[15:34:19] DEBUG    version: 0.10 [io.readMSGDInput]
[15:34:19] DEBUG    _sg_assignments = [sg assignment of main_cs to region all (element)] [io.readInputStructure]
[15:34:19] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[15:34:19] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[15:34:19] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[15:34:19] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[15:34:19] INFO     updating current design... [_msgd.updateData]
[15:34:19] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[15:34:19] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[15:34:19] DEBUG    subsituting parameters... [container.substituteParams]
[15:34:19] DEBUG    before substitution [container.substituteParams]
[15:34:19] DEBUG    inputs: {} [container.substituteParams]
[15:34:19] DEBUG    params: {} [container.substituteParams]
[15:34:19] DEBUG    after substitution [container.substituteParams]
[15:34:19] DEBUG    inputs: {} [container.substituteParams]
[15:34:19] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[15:34:19] DEBUG    self._config = {} [_structure.loadStructureMesh]
[15:34:19] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[15:34:19] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[15:34:19] DEBUG    updated design (global structure): [_msgd.updateData]
[15:34:19] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': {}} [_msgd.updateData]
[15:34:19] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[15:34:19] INFO     [main] discretizing the structure... [_msgd.discretize]
[15:34:19] DEBUG    structure model:
{'cs_assignment': [{'cs': 'main_cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': {},
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[15:34:19] DEBUG    ================ [_structure.discretizeDesign]
[15:34:19] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[15:34:19] DEBUG     [_structure.calcParamsFromDistributions]
[15:34:19] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[15:34:19] DEBUG    getting entity sets... [_structure.getEntitySets]
[15:34:19] DEBUG    sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.getEntitySets]
[15:34:19] DEBUG    _point_sets = {} [_structure.getEntitySets]
[15:34:19] DEBUG    _cell_sets = {} [_structure.getEntitySets]
[15:34:19] DEBUG    _entity_sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.calcParamsFromDistributions]
[15:34:19] DEBUG    ---------------- [_structure.discretizeDesign]
[15:34:19] DEBUG    all (element): [] [_structure.discretizeDesign]
[15:34:19] INFO     writing mesh data to file blade_mesh.msh... [_structure.writeMeshData]
[15:34:19] INFO     [main] going through steps... [_msgd.analyze]
[15:34:19] DEBUG    step config:
{'activate': True,
 'analysis': 'h',
 'output': {'value': ['gj', 'eiyy', 'eizz']},
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': 'cs'} [_msgd.analyze]
[15:34:19] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[15:34:19] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[15:34:19] DEBUG    [main_cs_set1] updating parameters... [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil] updating parameters... [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating a2p1 = 0.82 [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating a2p3 = 0.58 [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating mdb_name = material_database_us_ft [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating airfoil = SC1095.dat [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating gms = 0.004 [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating lam_spar = cfrp_0.0053 [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating seq_spar = [-45/0/45/90] [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating seq_front = [0] [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating seq_back = [0] [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating fill_mat_front = foam [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating fill_mat_back = honeycomb [_structure.updateParameters]
[15:34:19] DEBUG    [airfoil]  updating mat_nsm = metal [_structure.updateParameters]
[15:34:19] DEBUG    [cs: main_cs_set1] running cs analysis... [sg.runH]
[15:34:19] DEBUG    [airfoil] substituting parameters... [_structure.substituteParameters]
[15:34:19] DEBUG    subsituting parameters... [container.substituteParams]
[15:34:19] DEBUG    before substitution [container.substituteParams]
[15:34:19] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[15:34:19] DEBUG    params: {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [container.substituteParams]
[15:34:19] DEBUG    after substitution [container.substituteParams]
[15:34:19] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[15:34:19] DEBUG    building 2D SG (prevabs): main_cs_set1... [main.buildSGModel]
[15:34:19] DEBUG    building cs using prevabs... [main.buildSGFromPrevabs]
[15:34:19] DEBUG    parameters = {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [main.buildSGFromPrevabs]
[15:34:19] DEBUG    fns_include = ['material_database_us_ft.xml', 'SC1095.dat'] [main.buildSGFromPrevabs]
[15:34:19] DEBUG    prevabs1.6.0 -i cs\main_cs_set1.xml --vabs -ver 4.1 --hm [execu.run]
[15:34:19] CRITICAL Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 436, in run
    self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 171, in runH
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 839, in run
    output = self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 634, in runH
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 600, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 180, in buildSGModel
    fn_sg, fns_include = buildSGFromPrevabs(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 529, in buildSGFromPrevabs
    mutils.run(cmd, timeout)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\execu.py", line 21, in run
    out = sbp.run(
  File "C:\Users\<USER>\anaconda3\envs\rnd_py39\lib\subprocess.py", line 505, in run
    with Popen(*popenargs, **kwargs) as process:
  File "C:\Users\<USER>\anaconda3\envs\rnd_py39\lib\subprocess.py", line 951, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\Users\<USER>\anaconda3\envs\rnd_py39\lib\subprocess.py", line 1436, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
FileNotFoundError: [WinError 2] The system cannot find the file specified
 [__main__.main]
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 436, in run
    self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 171, in runH
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 839, in run
    output = self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 634, in runH
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 600, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 180, in buildSGModel
    fn_sg, fns_include = buildSGFromPrevabs(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 529, in buildSGFromPrevabs
    mutils.run(cmd, timeout)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\execu.py", line 21, in run
    out = sbp.run(
  File "C:\Users\<USER>\anaconda3\envs\rnd_py39\lib\subprocess.py", line 505, in run
    with Popen(*popenargs, **kwargs) as process:
  File "C:\Users\<USER>\anaconda3\envs\rnd_py39\lib\subprocess.py", line 951, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\Users\<USER>\anaconda3\envs\rnd_py39\lib\subprocess.py", line 1436, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
FileNotFoundError: [WinError 2] The system cannot find the file specified
[15:34:19] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[15:44:00] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[15:44:00] INFO     reading main input main.yml... [io.readMSGDInput]
[15:44:00] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x00000232FC3E7D60>,
 'variant': 'ivabs'} [io.readMSGDInput]
[15:44:00] DEBUG    currect working directory:  [io.readMSGDInput]
[15:44:00] DEBUG    input file name: main [io.readMSGDInput]
[15:44:00] DEBUG    input file extension: .yml [io.readMSGDInput]
[15:44:00] DEBUG    output file name: main.out [io.readMSGDInput]
[15:44:00] DEBUG    variant: ivabs [io.readMSGDInput]
[15:44:00] DEBUG    version: 0.10 [io.readMSGDInput]
[15:44:00] DEBUG    _sg_assignments = [sg assignment of main_cs to region all (element)] [io.readInputStructure]
[15:44:00] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[15:44:00] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[15:44:00] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[15:44:00] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[15:44:00] INFO     updating current design... [_msgd.updateData]
[15:44:00] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[15:44:00] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[15:44:00] DEBUG    subsituting parameters... [container.substituteParams]
[15:44:00] DEBUG    before substitution [container.substituteParams]
[15:44:00] DEBUG    inputs: {} [container.substituteParams]
[15:44:00] DEBUG    params: {} [container.substituteParams]
[15:44:00] DEBUG    after substitution [container.substituteParams]
[15:44:00] DEBUG    inputs: {} [container.substituteParams]
[15:44:00] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[15:44:00] DEBUG    self._config = {} [_structure.loadStructureMesh]
[15:44:00] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[15:44:00] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[15:44:00] DEBUG    updated design (global structure): [_msgd.updateData]
[15:44:00] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': {}} [_msgd.updateData]
[15:44:00] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[15:44:00] INFO     [main] discretizing the structure... [_msgd.discretize]
[15:44:00] DEBUG    structure model:
{'cs_assignment': [{'cs': 'main_cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': {},
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[15:44:00] DEBUG    ================ [_structure.discretizeDesign]
[15:44:00] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[15:44:00] DEBUG     [_structure.calcParamsFromDistributions]
[15:44:00] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[15:44:00] DEBUG    getting entity sets... [_structure.getEntitySets]
[15:44:00] DEBUG    sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.getEntitySets]
[15:44:00] DEBUG    _point_sets = {} [_structure.getEntitySets]
[15:44:00] DEBUG    _cell_sets = {} [_structure.getEntitySets]
[15:44:00] DEBUG    _entity_sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.calcParamsFromDistributions]
[15:44:00] DEBUG    ---------------- [_structure.discretizeDesign]
[15:44:00] DEBUG    all (element): [] [_structure.discretizeDesign]
[15:44:00] INFO     writing mesh data to file blade_mesh.msh... [_structure.writeMeshData]
[15:44:00] INFO     [main] going through steps... [_msgd.analyze]
[15:44:00] DEBUG    step config:
{'activate': True,
 'analysis': 'h',
 'output': {'value': ['gj', 'eiyy', 'eizz']},
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': 'cs'} [_msgd.analyze]
[15:44:00] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[15:44:00] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[15:44:00] DEBUG    [main_cs_set1] updating parameters... [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil] updating parameters... [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating a2p1 = 0.82 [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating a2p3 = 0.58 [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating mdb_name = material_database_us_ft [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating airfoil = SC1095.dat [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating gms = 0.004 [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating lam_spar = cfrp_0.0053 [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating seq_spar = [-45/0/45/90] [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating seq_front = [0] [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating seq_back = [0] [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating fill_mat_front = foam [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating fill_mat_back = honeycomb [_structure.updateParameters]
[15:44:00] DEBUG    [airfoil]  updating mat_nsm = metal [_structure.updateParameters]
[15:44:00] DEBUG    [cs: main_cs_set1] running cs analysis... [sg.runH]
[15:44:00] DEBUG    [airfoil] substituting parameters... [_structure.substituteParameters]
[15:44:00] DEBUG    subsituting parameters... [container.substituteParams]
[15:44:00] DEBUG    before substitution [container.substituteParams]
[15:44:00] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[15:44:00] DEBUG    params: {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [container.substituteParams]
[15:44:00] DEBUG    after substitution [container.substituteParams]
[15:44:00] DEBUG    inputs: {'base_file': 'airfoil_simple.xml.tmp'} [container.substituteParams]
[15:44:00] DEBUG    building 2D SG (prevabs): main_cs_set1... [main.buildSGModel]
[15:44:00] DEBUG    building cs using prevabs... [main.buildSGFromPrevabs]
[15:44:00] DEBUG    parameters = {'a2p1': 0.82, 'a2p3': 0.58, 'mdb_name': 'material_database_us_ft', 'airfoil': 'SC1095.dat', 'gms': 0.004, 'lam_spar': 'cfrp_0.0053', 'seq_spar': '[-45/0/45/90]', 'seq_front': '[0]', 'seq_back': '[0]', 'fill_mat_front': 'foam', 'fill_mat_back': 'honeycomb', 'mat_nsm': 'metal'} [main.buildSGFromPrevabs]
[15:44:00] DEBUG    fns_include = ['material_database_us_ft.xml', 'SC1095.dat'] [main.buildSGFromPrevabs]
[15:44:00] DEBUG    prevabs160 -i cs\main_cs_set1.xml --vabs --ver 4.1 --hm [execu.run]
[15:44:03] DEBUG    _vabs._readHeader :: reading header... 
[15:44:03] DEBUG    _vabs._readMesh :: reading mesh... 
[15:44:03] DEBUG    _vabs._readMaterialRotationCombinations :: reading combinations of material and in-plane rotations... 
[15:44:03] DEBUG    _vabs._readMaterials :: reading materials... 
[15:44:03] DEBUG    execu.run :: VABS cs\main_cs_set1.sg 
[15:44:03] DEBUG    execu.run :: return code: 0 
[15:44:03] DEBUG    execu.run :: stdout:

 Homogenization for computing sectional properties

 The inputs are echoed in cs\main_cs_set1.sg.ech

 Finished reading inputs for the cross-sectional analysis.
 You can run VABS for   0 days....

 Finished constitutive modeling

 Cross-sectional properties can be found in  "cs\main_cs_set1.sg.K"

 Finished outputing constitutive modeling results

 VABS finished successfully

 VABS Runs for     3.12500000E-02  Seconds.
 
[15:44:03] DEBUG    execu.run :: stderr: 
[15:44:03] DEBUG    execu.run :: VABS finished successfully 
[15:44:03] DEBUG    sg_model.data_format = vabs [sg.runH]
[15:44:03] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
