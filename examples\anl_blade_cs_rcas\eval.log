INFO     [2024-05-08 16:46:20] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-08 16:46:20] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-08 16:46:20] _msgd.updateData :: updating current design... 
INFO     [2024-05-08 16:46:20] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-08 16:46:20] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-08 16:46:20] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-08 16:46:20] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-08 16:46:20] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-08 16:46:20] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-08 16:46:20] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-08 16:46:20] distribution.loadData :: loading data (file)... 
INFO     [2024-05-08 16:46:20] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-08 16:46:20] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-08 16:46:20] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-08 16:46:20] distribution.loadData :: loading data (file)... 
INFO     [2024-05-08 16:46:20] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-08 16:46:20] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-08 16:46:20] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-08 16:46:20] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-08 16:46:20] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-08 16:46:20] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-08 16:46:21] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-08 16:46:21] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-08 16:46:21] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[7.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.75]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.75]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[17.999999999999996]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[17.999999999999996]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[7.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[7.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[6.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[6.5]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[6.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[6.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[5.200000000000001]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[5.200000000000001]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[4.4]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[4.4]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5669]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.600000000000001]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5669]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[3.600000000000001]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.8000000000000007]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.8000000000000007]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[2.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[1.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[90.0]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-08 16:46:21] function.__call__ :: y = [[90.0]] 
INFO     [2024-05-08 16:46:21] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-08 16:46:21] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-08 16:46:21] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-08 16:46:21] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-08 16:46:21] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:21] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-08 16:46:21] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:21] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-08 16:46:22] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:24] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-08 16:46:24] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:24] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-08 16:46:24] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-08 16:46:24] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:26] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-08 16:46:26] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:26] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-08 16:46:26] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:26] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-08 16:46:26] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:28] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-08 16:46:28] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:28] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-08 16:46:28] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:28] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-08 16:46:28] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:30] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-08 16:46:30] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:30] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-08 16:46:30] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:30] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-08 16:46:30] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:31] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-08 16:46:31] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:31] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-08 16:46:31] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:31] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-08 16:46:32] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:35] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-08 16:46:35] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:35] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-08 16:46:35] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-08 16:46:35] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:36] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-08 16:46:36] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:36] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-08 16:46:36] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:36] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-08 16:46:37] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:39] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-08 16:46:39] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-08 16:46:39] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-08 16:46:39] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-08 16:46:39] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-08 16:46:39] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-08 16:46:41] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-08 16:46:41] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-09 16:52:45] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-09 16:52:45] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-09 16:52:45] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-09 16:52:45] _msgd.updateData :: updating current design... 
INFO     [2024-05-09 16:52:45] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-09 16:52:45] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-09 16:52:45] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-09 16:52:45] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-09 16:52:45] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-09 16:52:45] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-09 16:52:45] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-09 16:52:45] distribution.loadData :: loading data (file)... 
INFO     [2024-05-09 16:52:45] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-09 16:52:45] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-09 16:52:45] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-09 16:52:45] distribution.loadData :: loading data (file)... 
INFO     [2024-05-09 16:52:45] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-09 16:52:45] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-09 16:52:45] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-09 16:52:45] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-09 16:52:45] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-09 16:52:45] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-09 16:52:45] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-09 16:52:45] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-09 16:52:45] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[7.5]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[3.75]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[3.75]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[17.999999999999996]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[17.999999999999996]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:45] function.__call__ :: y = [[7.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[7.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[6.5]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[6.5]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[6.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[6.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[5.200000000000001]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[5.200000000000001]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[4.4]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[4.4]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5669]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.600000000000001]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5669]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[3.600000000000001]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.8000000000000007]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.8000000000000007]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[2.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[1.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[90.0]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 16:52:46] function.__call__ :: y = [[90.0]] 
INFO     [2024-05-09 16:52:46] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-09 16:52:46] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-09 16:52:46] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-09 16:52:46] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-09 16:52:46] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:46] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-09 16:52:46] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:46] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 16:52:47] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:49] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-09 16:52:49] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:49] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-09 16:52:49] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:49] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 16:52:49] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:50] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-09 16:52:50] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:50] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-09 16:52:50] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:50] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 16:52:50] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:52] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-09 16:52:52] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:52] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-09 16:52:52] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:52] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 16:52:52] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:53] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-09 16:52:53] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:53] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-09 16:52:53] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:53] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 16:52:53] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:54] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-09 16:52:54] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:54] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-09 16:52:54] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:54] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 16:52:54] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:55] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-09 16:52:55] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:55] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-09 16:52:55] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 16:52:55] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:57] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-09 16:52:57] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:57] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-09 16:52:57] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:57] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 16:52:57] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:52:58] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-09 16:52:58] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 16:52:58] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-09 16:52:58] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 16:52:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 16:52:58] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 16:53:00] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-09 16:53:00] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-09 16:53:00] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:00] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-09 16:53:00] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-09 16:53:00] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:00] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-09 16:53:00] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:00] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:00] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [291047.0, 3807.0, 28154.0, -1360.0, 866.694, -545.46] 
INFO     [2024-05-09 16:53:00] sg.writeMacroStateToFile :: _str_load =    2.910470000e+05     3.807000000e+03     2.815400000e+04    -1.360000000e+03     8.666940000e+02    -5.454600000e+02 
INFO     [2024-05-09 16:53:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 16:53:00] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:00] sg.runDF :: copying sg files to .\local\blade\loc1_2\main_cs_set1\case1... 
INFO     [2024-05-09 16:53:00] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:00] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-05-09 16:53:00] sg.runDF :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-09 16:53:00] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:00] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [3]}] 
INFO     [2024-05-09 16:53:00] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:00] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:00] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [279032.0, 4107.0, 22941.0, -1310.0, 1299.0, -742.962] 
INFO     [2024-05-09 16:53:00] sg.writeMacroStateToFile :: _str_load =    2.790320000e+05     4.107000000e+03     2.294100000e+04    -1.310000000e+03     1.299000000e+03    -7.429620000e+02 
INFO     [2024-05-09 16:53:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 16:53:00] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:00] sg.runDF :: copying sg files to .\local\blade\loc1_3\main_cs_set2\case1... 
INFO     [2024-05-09 16:53:01] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:01] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-05-09 16:53:01] sg.runDF :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-09 16:53:01] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:01] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 2, 'locs': [4]}] 
INFO     [2024-05-09 16:53:01] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:01] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:01] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [261965.0, 4231.0, 18102.0, -1273.0, 1650.0, -825.125] 
INFO     [2024-05-09 16:53:01] sg.writeMacroStateToFile :: _str_load =    2.619650000e+05     4.231000000e+03     1.810200000e+04    -1.273000000e+03     1.650000000e+03    -8.251250000e+02 
INFO     [2024-05-09 16:53:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 16:53:01] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:01] sg.runDF :: copying sg files to .\local\blade\loc2_4\main_cs_set3\case1... 
INFO     [2024-05-09 16:53:01] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:01] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-05-09 16:53:02] sg.runDF :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-09 16:53:02] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:02] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 3, 'locs': [5]}] 
INFO     [2024-05-09 16:53:02] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:02] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:02] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [239848.0, 4410.0, 10821.0, -1212.0, 1600.0, -866.437] 
INFO     [2024-05-09 16:53:02] sg.writeMacroStateToFile :: _str_load =    2.398480000e+05     4.410000000e+03     1.082100000e+04    -1.212000000e+03     1.600000000e+03    -8.664370000e+02 
INFO     [2024-05-09 16:53:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 16:53:02] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:02] sg.runDF :: copying sg files to .\local\blade\loc3_5\main_cs_set4\case1... 
INFO     [2024-05-09 16:53:02] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:02] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-05-09 16:53:02] sg.runDF :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-09 16:53:02] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:02] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 4, 'locs': [6]}] 
INFO     [2024-05-09 16:53:02] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:02] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:02] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [211962.0, 4256.0, 5073.0, -1102.0, 1449.0, -906.772] 
INFO     [2024-05-09 16:53:02] sg.writeMacroStateToFile :: _str_load =    2.119620000e+05     4.256000000e+03     5.073000000e+03    -1.102000000e+03     1.449000000e+03    -9.067720000e+02 
INFO     [2024-05-09 16:53:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 16:53:02] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:02] sg.runDF :: copying sg files to .\local\blade\loc4_6\main_cs_set5\case1... 
INFO     [2024-05-09 16:53:03] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:03] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-05-09 16:53:03] sg.runDF :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-09 16:53:03] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:03] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 5, 'locs': [7]}] 
INFO     [2024-05-09 16:53:03] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:03] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:03] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [176875.0, 3700.0, 1670.0, -985.085, 1127.0, -1102.0] 
INFO     [2024-05-09 16:53:03] sg.writeMacroStateToFile :: _str_load =    1.768750000e+05     3.700000000e+03     1.670000000e+03    -9.850850000e+02     1.127000000e+03    -1.102000000e+03 
INFO     [2024-05-09 16:53:03] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 16:53:03] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:03] sg.runDF :: copying sg files to .\local\blade\loc5_7\main_cs_set6\case1... 
INFO     [2024-05-09 16:53:03] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:03] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-05-09 16:53:03] sg.runDF :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-09 16:53:03] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:03] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 6, 'locs': [8]}] 
INFO     [2024-05-09 16:53:03] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:03] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:03] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [129430.0, 2720.0, -593.814, -727.33, 602.513, -714.368] 
INFO     [2024-05-09 16:53:03] sg.writeMacroStateToFile :: _str_load =    1.294300000e+05     2.720000000e+03    -5.938140000e+02    -7.273300000e+02     6.025130000e+02    -7.143680000e+02 
INFO     [2024-05-09 16:53:03] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 16:53:03] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:03] sg.runDF :: copying sg files to .\local\blade\loc6_8\main_cs_set7\case1... 
INFO     [2024-05-09 16:53:04] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:04] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-05-09 16:53:04] sg.runDF :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-09 16:53:04] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:04] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 7, 'locs': [9]}] 
INFO     [2024-05-09 16:53:04] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:04] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:04] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [68614.0, 1435.0, -522.31, -341.219, -5.399, 30.09] 
INFO     [2024-05-09 16:53:04] sg.writeMacroStateToFile :: _str_load =    6.861400000e+04     1.435000000e+03    -5.223100000e+02    -3.412190000e+02    -5.399000000e+00     3.009000000e+01 
INFO     [2024-05-09 16:53:04] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 16:53:04] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:04] sg.runDF :: copying sg files to .\local\blade\loc7_9\main_cs_set8\case1... 
INFO     [2024-05-09 16:53:04] sg.runDF :: ============================================================ 
INFO     [2024-05-09 16:53:04] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-05-09 16:53:04] sg.runDF :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-09 16:53:04] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 16:53:04] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 8, 'locs': [10]}] 
INFO     [2024-05-09 16:53:04] sg.runDF :: .................... 
INFO     [2024-05-09 16:53:04] sg.runDF :: case_name = case1 
INFO     [2024-05-09 16:53:04] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-09 16:53:04] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-09 16:53:04] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 16:53:04] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 16:53:04] sg.runDF :: copying sg files to .\local\blade\loc8_10\main_cs_set9\case1... 
INFO     [2024-05-09 16:53:05] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-09 17:56:41] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-09 17:56:41] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-09 17:56:41] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-09 17:56:41] _msgd.updateData :: updating current design... 
INFO     [2024-05-09 17:56:41] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-09 17:56:41] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-09 17:56:41] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-09 17:56:41] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-09 17:56:41] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-09 17:56:41] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-09 17:56:41] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-09 17:56:41] distribution.loadData :: loading data (file)... 
INFO     [2024-05-09 17:56:41] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-09 17:56:41] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-09 17:56:41] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-09 17:56:41] distribution.loadData :: loading data (file)... 
INFO     [2024-05-09 17:56:41] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-09 17:56:41] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-09 17:56:41] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-09 17:56:41] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-09 17:56:41] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-09 17:56:41] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-09 17:56:41] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-09 17:56:41] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-09 17:56:41] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[7.5]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[3.75]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[3.75]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[17.999999999999996]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[1.6356]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[17.999999999999996]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[7.0]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: y = [[7.0]] 
INFO     [2024-05-09 17:56:41] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.5]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[2.4534]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[26.999999999999996]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[6.5]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[6.5]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.25]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[3.2712]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[35.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[6.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[6.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.089]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[45.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[5.200000000000001]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[5.200000000000001]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.6000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[4.9068]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[53.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[4.4]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[4.4]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.2]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[5.7246]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[62.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5669]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.600000000000001]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5669]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[3.600000000000001]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.8000000000000005]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[6.5424]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[71.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.8000000000000007]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.8000000000000007]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.4000000000000004]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[7.3602]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[80.99999999999999]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[0.5273]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[2.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[1.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[90.0]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: x = [[8.178]] 
INFO     [2024-05-09 17:56:42] function.__call__ :: y = [[90.0]] 
INFO     [2024-05-09 17:56:42] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-09 17:56:42] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-09 17:56:42] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-09 17:56:42] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-09 17:56:42] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:42] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-09 17:56:42] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:42] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 17:56:42] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:43] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-09 17:56:43] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:43] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-09 17:56:43] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 17:56:43] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:45] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-09 17:56:45] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:45] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-09 17:56:45] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:45] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 17:56:45] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:46] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-09 17:56:46] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:46] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-09 17:56:46] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:46] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 17:56:46] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:47] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-09 17:56:47] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:47] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-09 17:56:47] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:47] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 17:56:47] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:48] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-09 17:56:48] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:48] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-09 17:56:48] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:48] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 17:56:48] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:49] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-09 17:56:49] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:49] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-09 17:56:49] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:49] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 17:56:49] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:50] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-09 17:56:50] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:50] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-09 17:56:50] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:50] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 17:56:50] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:51] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-09 17:56:51] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 17:56:51] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-09 17:56:51] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 17:56:51] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 17:56:52] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 17:56:53] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-09 17:56:53] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-09 17:56:53] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:53] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-09 17:56:53] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-09 17:56:53] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:53] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-09 17:56:53] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:53] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:53] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [294170.0, 5163.0, 23212.0, -504.894, -782.132, 671.741] 
INFO     [2024-05-09 17:56:53] sg.writeMacroStateToFile :: _str_load =    2.941700000e+05     5.163000000e+03     2.321200000e+04    -5.048940000e+02    -7.821320000e+02     6.717410000e+02 
INFO     [2024-05-09 17:56:53] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 17:56:53] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:53] sg.runDF :: copying sg files to .\local\blade\loc1_2\main_cs_set1\case1... 
INFO     [2024-05-09 17:56:53] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:53] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:56:53] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [294170.0, 5163.0, 23212.0, -504.894, -782.132, 671.741] 
INFO     [2024-05-09 17:56:53] sg.writeMacroStateToFile :: _str_load =    2.941700000e+05     5.163000000e+03     2.321200000e+04    -5.048940000e+02    -7.821320000e+02     6.717410000e+02 
INFO     [2024-05-09 17:56:53] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 17:56:53] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:53] sg.runDF :: copying sg files to .\local\blade\loc1_2\main_cs_set1\case2... 
INFO     [2024-05-09 17:56:54] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:54] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-05-09 17:56:54] sg.runDF :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-09 17:56:54] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:54] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [3]}] 
INFO     [2024-05-09 17:56:54] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:54] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:54] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [281519.0, 4583.0, 23110.0, -520.35, -734.339, 741.891] 
INFO     [2024-05-09 17:56:54] sg.writeMacroStateToFile :: _str_load =    2.815190000e+05     4.583000000e+03     2.311000000e+04    -5.203500000e+02    -7.343390000e+02     7.418910000e+02 
INFO     [2024-05-09 17:56:54] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 17:56:54] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:54] sg.runDF :: copying sg files to .\local\blade\loc1_3\main_cs_set2\case1... 
INFO     [2024-05-09 17:56:55] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:55] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:56:55] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [281519.0, 4583.0, 23110.0, -520.35, -734.339, 741.891] 
INFO     [2024-05-09 17:56:55] sg.writeMacroStateToFile :: _str_load =    2.815190000e+05     4.583000000e+03     2.311000000e+04    -5.203500000e+02    -7.343390000e+02     7.418910000e+02 
INFO     [2024-05-09 17:56:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 17:56:55] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:55] sg.runDF :: copying sg files to .\local\blade\loc1_3\main_cs_set2\case2... 
INFO     [2024-05-09 17:56:55] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:55] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-05-09 17:56:55] sg.runDF :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-09 17:56:55] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:55] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 2, 'locs': [4]}] 
INFO     [2024-05-09 17:56:55] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:55] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:55] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [263837.0, 3926.0, 22932.0, -515.429, -712.348, 591.395] 
INFO     [2024-05-09 17:56:55] sg.writeMacroStateToFile :: _str_load =    2.638370000e+05     3.926000000e+03     2.293200000e+04    -5.154290000e+02    -7.123480000e+02     5.913950000e+02 
INFO     [2024-05-09 17:56:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 17:56:55] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:55] sg.runDF :: copying sg files to .\local\blade\loc2_4\main_cs_set3\case1... 
INFO     [2024-05-09 17:56:56] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:56] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:56:56] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [263837.0, 3926.0, 22932.0, -515.429, -712.348, 591.395] 
INFO     [2024-05-09 17:56:56] sg.writeMacroStateToFile :: _str_load =    2.638370000e+05     3.926000000e+03     2.293200000e+04    -5.154290000e+02    -7.123480000e+02     5.913950000e+02 
INFO     [2024-05-09 17:56:56] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 17:56:56] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:56] sg.runDF :: copying sg files to .\local\blade\loc2_4\main_cs_set3\case2... 
INFO     [2024-05-09 17:56:56] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:56] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-05-09 17:56:56] sg.runDF :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-09 17:56:56] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:56] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 3, 'locs': [5]}] 
INFO     [2024-05-09 17:56:56] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:56] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:56] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [241005.0, 3248.0, 22346.0, -506.046, -609.034, 258.88] 
INFO     [2024-05-09 17:56:56] sg.writeMacroStateToFile :: _str_load =    2.410050000e+05     3.248000000e+03     2.234600000e+04    -5.060460000e+02    -6.090340000e+02     2.588800000e+02 
INFO     [2024-05-09 17:56:56] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 17:56:56] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:57] sg.runDF :: copying sg files to .\local\blade\loc3_5\main_cs_set4\case1... 
INFO     [2024-05-09 17:56:57] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:57] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:56:57] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [241005.0, 3248.0, 22346.0, -506.046, -609.034, 258.88] 
INFO     [2024-05-09 17:56:57] sg.writeMacroStateToFile :: _str_load =    2.410050000e+05     3.248000000e+03     2.234600000e+04    -5.060460000e+02    -6.090340000e+02     2.588800000e+02 
INFO     [2024-05-09 17:56:57] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 17:56:57] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:57] sg.runDF :: copying sg files to .\local\blade\loc3_5\main_cs_set4\case2... 
INFO     [2024-05-09 17:56:57] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:57] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-05-09 17:56:57] sg.runDF :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-09 17:56:57] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:57] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 4, 'locs': [6]}] 
INFO     [2024-05-09 17:56:57] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:57] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:57] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [212616.0, 2638.0, 20790.0, -483.174, -410.759, -187.09] 
INFO     [2024-05-09 17:56:57] sg.writeMacroStateToFile :: _str_load =    2.126160000e+05     2.638000000e+03     2.079000000e+04    -4.831740000e+02    -4.107590000e+02    -1.870900000e+02 
INFO     [2024-05-09 17:56:57] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 17:56:57] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:58] sg.runDF :: copying sg files to .\local\blade\loc4_6\main_cs_set5\case1... 
INFO     [2024-05-09 17:56:58] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:58] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:56:58] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [212616.0, 2638.0, 20790.0, -483.174, -410.759, -187.09] 
INFO     [2024-05-09 17:56:58] sg.writeMacroStateToFile :: _str_load =    2.126160000e+05     2.638000000e+03     2.079000000e+04    -4.831740000e+02    -4.107590000e+02    -1.870900000e+02 
INFO     [2024-05-09 17:56:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 17:56:58] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:58] sg.runDF :: copying sg files to .\local\blade\loc4_6\main_cs_set5\case2... 
INFO     [2024-05-09 17:56:58] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:58] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-05-09 17:56:58] sg.runDF :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-09 17:56:58] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:58] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 5, 'locs': [7]}] 
INFO     [2024-05-09 17:56:58] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:58] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:58] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [177173.0, 2036.0, 18332.0, -446.092, -182.751, -777.102] 
INFO     [2024-05-09 17:56:58] sg.writeMacroStateToFile :: _str_load =    1.771730000e+05     2.036000000e+03     1.833200000e+04    -4.460920000e+02    -1.827510000e+02    -7.771020000e+02 
INFO     [2024-05-09 17:56:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 17:56:58] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:59] sg.runDF :: copying sg files to .\local\blade\loc5_7\main_cs_set6\case1... 
INFO     [2024-05-09 17:56:59] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:59] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:56:59] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [177173.0, 2036.0, 18332.0, -446.092, -182.751, -777.102] 
INFO     [2024-05-09 17:56:59] sg.writeMacroStateToFile :: _str_load =    1.771730000e+05     2.036000000e+03     1.833200000e+04    -4.460920000e+02    -1.827510000e+02    -7.771020000e+02 
INFO     [2024-05-09 17:56:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 17:56:59] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:56:59] sg.runDF :: copying sg files to .\local\blade\loc5_7\main_cs_set6\case2... 
INFO     [2024-05-09 17:56:59] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:56:59] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-05-09 17:56:59] sg.runDF :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-09 17:56:59] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:56:59] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 6, 'locs': [8]}] 
INFO     [2024-05-09 17:56:59] sg.runDF :: .................... 
INFO     [2024-05-09 17:56:59] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:56:59] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [129514.0, 1327.0, 13822.0, -389.229, -95.069, -672.893] 
INFO     [2024-05-09 17:56:59] sg.writeMacroStateToFile :: _str_load =    1.295140000e+05     1.327000000e+03     1.382200000e+04    -3.892290000e+02    -9.506900000e+01    -6.728930000e+02 
INFO     [2024-05-09 17:56:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 17:56:59] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:57:00] sg.runDF :: copying sg files to .\local\blade\loc6_8\main_cs_set7\case1... 
INFO     [2024-05-09 17:57:00] sg.runDF :: .................... 
INFO     [2024-05-09 17:57:00] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:57:00] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [129514.0, 1327.0, 13822.0, -389.229, -95.069, -672.893] 
INFO     [2024-05-09 17:57:00] sg.writeMacroStateToFile :: _str_load =    1.295140000e+05     1.327000000e+03     1.382200000e+04    -3.892290000e+02    -9.506900000e+01    -6.728930000e+02 
INFO     [2024-05-09 17:57:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 17:57:00] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:57:00] sg.runDF :: copying sg files to .\local\blade\loc6_8\main_cs_set7\case2... 
INFO     [2024-05-09 17:57:01] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:57:01] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-05-09 17:57:01] sg.runDF :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-09 17:57:01] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:57:01] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 7, 'locs': [9]}] 
INFO     [2024-05-09 17:57:01] sg.runDF :: .................... 
INFO     [2024-05-09 17:57:01] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:57:01] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [68702.0, 616.79, 6925.0, -321.953, -298.606, -49.447] 
INFO     [2024-05-09 17:57:01] sg.writeMacroStateToFile :: _str_load =    6.870200000e+04     6.167900000e+02     6.925000000e+03    -3.219530000e+02    -2.986060000e+02    -4.944700000e+01 
INFO     [2024-05-09 17:57:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 17:57:01] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:57:01] sg.runDF :: copying sg files to .\local\blade\loc7_9\main_cs_set8\case1... 
INFO     [2024-05-09 17:57:01] sg.runDF :: .................... 
INFO     [2024-05-09 17:57:01] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:57:01] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [68702.0, 616.79, 6925.0, -321.953, -298.606, -49.447] 
INFO     [2024-05-09 17:57:01] sg.writeMacroStateToFile :: _str_load =    6.870200000e+04     6.167900000e+02     6.925000000e+03    -3.219530000e+02    -2.986060000e+02    -4.944700000e+01 
INFO     [2024-05-09 17:57:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 17:57:01] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:57:01] sg.runDF :: copying sg files to .\local\blade\loc7_9\main_cs_set8\case2... 
INFO     [2024-05-09 17:57:02] sg.runDF :: ============================================================ 
INFO     [2024-05-09 17:57:02] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-05-09 17:57:02] sg.runDF :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-09 17:57:02] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 17:57:02] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 8, 'locs': [10]}] 
INFO     [2024-05-09 17:57:02] sg.runDF :: .................... 
INFO     [2024-05-09 17:57:02] sg.runDF :: case_name = case1 
INFO     [2024-05-09 17:57:02] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-09 17:57:02] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-09 17:57:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 17:57:02] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:57:02] sg.runDF :: copying sg files to .\local\blade\loc8_10\main_cs_set9\case1... 
INFO     [2024-05-09 17:57:02] sg.runDF :: .................... 
INFO     [2024-05-09 17:57:02] sg.runDF :: case_name = case2 
INFO     [2024-05-09 17:57:02] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-09 17:57:02] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-09 17:57:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 17:57:02] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 17:57:02] sg.runDF :: copying sg files to .\local\blade\loc8_10\main_cs_set9\case2... 
INFO     [2024-05-09 17:57:03] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-09 18:31:09] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-09 18:31:10] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-09 18:31:10] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-09 18:31:10] _msgd.updateData :: updating current design... 
INFO     [2024-05-09 18:31:10] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-09 18:31:10] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-09 18:31:10] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-09 18:31:10] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-09 18:31:10] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-09 18:31:10] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-09 18:31:10] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-09 18:31:10] distribution.loadData :: loading data (file)... 
INFO     [2024-05-09 18:31:10] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-09 18:31:10] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-09 18:31:10] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-09 18:31:10] distribution.loadData :: loading data (file)... 
INFO     [2024-05-09 18:31:10] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-09 18:31:10] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-09 18:31:10] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-09 18:31:10] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-09 18:31:10] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-09 18:31:10] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-09 18:31:10] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-09 18:31:10] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-09 18:31:10] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-09 18:31:10] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-09 18:31:10] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-09 18:31:10] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-09 18:31:10] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-09 18:31:10] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:10] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-09 18:31:10] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 18:31:10] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:11] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-09 18:31:11] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:11] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-09 18:31:11] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 18:31:11] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:12] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-09 18:31:12] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:12] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-09 18:31:12] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 18:31:12] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:13] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-09 18:31:13] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:13] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-09 18:31:13] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:13] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 18:31:13] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:14] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-09 18:31:14] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:14] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-09 18:31:14] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:14] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 18:31:14] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:15] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-09 18:31:15] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:15] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-09 18:31:15] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:15] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 18:31:15] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:15] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-09 18:31:15] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:15] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-09 18:31:15] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:15] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 18:31:16] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:16] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-09 18:31:16] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:16] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-09 18:31:16] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:16] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 18:31:16] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:17] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-09 18:31:17] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-09 18:31:17] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-09 18:31:17] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-09 18:31:17] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 18:31:18] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-09 18:31:18] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-09 18:31:18] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-09 18:31:18] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:18] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-09 18:31:18] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-09 18:31:18] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:18] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-09 18:31:18] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:18] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:18] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [291047.0, 3807.0, 28154.0, -1360.0, 866.694, -545.46] 
INFO     [2024-05-09 18:31:18] sg.writeMacroStateToFile :: _str_load =    2.910470000e+05     3.807000000e+03     2.815400000e+04    -1.360000000e+03     8.666940000e+02    -5.454600000e+02 
INFO     [2024-05-09 18:31:18] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 18:31:19] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:19] sg.runDF :: copying sg files to .\local\blade\loc1_2\main_cs_set1\case1... 
INFO     [2024-05-09 18:31:19] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:19] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:19] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [294170.0, 5163.0, 23212.0, -504.894, -782.132, 671.741] 
INFO     [2024-05-09 18:31:19] sg.writeMacroStateToFile :: _str_load =    2.941700000e+05     5.163000000e+03     2.321200000e+04    -5.048940000e+02    -7.821320000e+02     6.717410000e+02 
INFO     [2024-05-09 18:31:19] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-09 18:31:19] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:19] sg.runDF :: copying sg files to .\local\blade\loc1_2\main_cs_set1\case2... 
INFO     [2024-05-09 18:31:19] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:19] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-05-09 18:31:19] sg.runDF :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-09 18:31:19] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:19] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [3]}] 
INFO     [2024-05-09 18:31:19] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:19] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:19] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [279032.0, 4107.0, 22941.0, -1310.0, 1299.0, -742.962] 
INFO     [2024-05-09 18:31:19] sg.writeMacroStateToFile :: _str_load =    2.790320000e+05     4.107000000e+03     2.294100000e+04    -1.310000000e+03     1.299000000e+03    -7.429620000e+02 
INFO     [2024-05-09 18:31:19] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 18:31:19] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:19] sg.runDF :: copying sg files to .\local\blade\loc1_3\main_cs_set2\case1... 
INFO     [2024-05-09 18:31:20] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:20] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:20] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [281519.0, 4583.0, 23110.0, -520.35, -734.339, 741.891] 
INFO     [2024-05-09 18:31:20] sg.writeMacroStateToFile :: _str_load =    2.815190000e+05     4.583000000e+03     2.311000000e+04    -5.203500000e+02    -7.343390000e+02     7.418910000e+02 
INFO     [2024-05-09 18:31:20] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-09 18:31:20] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:20] sg.runDF :: copying sg files to .\local\blade\loc1_3\main_cs_set2\case2... 
INFO     [2024-05-09 18:31:20] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:20] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-05-09 18:31:20] sg.runDF :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-09 18:31:20] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:20] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 2, 'locs': [4]}] 
INFO     [2024-05-09 18:31:20] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:20] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:20] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [261965.0, 4231.0, 18102.0, -1273.0, 1650.0, -825.125] 
INFO     [2024-05-09 18:31:20] sg.writeMacroStateToFile :: _str_load =    2.619650000e+05     4.231000000e+03     1.810200000e+04    -1.273000000e+03     1.650000000e+03    -8.251250000e+02 
INFO     [2024-05-09 18:31:20] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 18:31:20] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:20] sg.runDF :: copying sg files to .\local\blade\loc2_4\main_cs_set3\case1... 
INFO     [2024-05-09 18:31:21] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:21] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:21] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [263837.0, 3926.0, 22932.0, -515.429, -712.348, 591.395] 
INFO     [2024-05-09 18:31:21] sg.writeMacroStateToFile :: _str_load =    2.638370000e+05     3.926000000e+03     2.293200000e+04    -5.154290000e+02    -7.123480000e+02     5.913950000e+02 
INFO     [2024-05-09 18:31:21] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-09 18:31:21] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:21] sg.runDF :: copying sg files to .\local\blade\loc2_4\main_cs_set3\case2... 
INFO     [2024-05-09 18:31:21] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:21] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-05-09 18:31:21] sg.runDF :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-09 18:31:21] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:21] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 3, 'locs': [5]}] 
INFO     [2024-05-09 18:31:21] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:21] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:21] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [239848.0, 4410.0, 10821.0, -1212.0, 1600.0, -866.437] 
INFO     [2024-05-09 18:31:21] sg.writeMacroStateToFile :: _str_load =    2.398480000e+05     4.410000000e+03     1.082100000e+04    -1.212000000e+03     1.600000000e+03    -8.664370000e+02 
INFO     [2024-05-09 18:31:21] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 18:31:21] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:21] sg.runDF :: copying sg files to .\local\blade\loc3_5\main_cs_set4\case1... 
INFO     [2024-05-09 18:31:22] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:22] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:22] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [241005.0, 3248.0, 22346.0, -506.046, -609.034, 258.88] 
INFO     [2024-05-09 18:31:22] sg.writeMacroStateToFile :: _str_load =    2.410050000e+05     3.248000000e+03     2.234600000e+04    -5.060460000e+02    -6.090340000e+02     2.588800000e+02 
INFO     [2024-05-09 18:31:22] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-09 18:31:22] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:22] sg.runDF :: copying sg files to .\local\blade\loc3_5\main_cs_set4\case2... 
INFO     [2024-05-09 18:31:22] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:22] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-05-09 18:31:22] sg.runDF :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-09 18:31:22] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:22] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 4, 'locs': [6]}] 
INFO     [2024-05-09 18:31:22] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:22] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:22] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [211962.0, 4256.0, 5073.0, -1102.0, 1449.0, -906.772] 
INFO     [2024-05-09 18:31:22] sg.writeMacroStateToFile :: _str_load =    2.119620000e+05     4.256000000e+03     5.073000000e+03    -1.102000000e+03     1.449000000e+03    -9.067720000e+02 
INFO     [2024-05-09 18:31:22] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 18:31:22] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:22] sg.runDF :: copying sg files to .\local\blade\loc4_6\main_cs_set5\case1... 
INFO     [2024-05-09 18:31:23] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:23] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:23] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [212616.0, 2638.0, 20790.0, -483.174, -410.759, -187.09] 
INFO     [2024-05-09 18:31:23] sg.writeMacroStateToFile :: _str_load =    2.126160000e+05     2.638000000e+03     2.079000000e+04    -4.831740000e+02    -4.107590000e+02    -1.870900000e+02 
INFO     [2024-05-09 18:31:23] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-09 18:31:23] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:23] sg.runDF :: copying sg files to .\local\blade\loc4_6\main_cs_set5\case2... 
INFO     [2024-05-09 18:31:23] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:23] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-05-09 18:31:23] sg.runDF :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-09 18:31:23] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:23] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 5, 'locs': [7]}] 
INFO     [2024-05-09 18:31:23] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:23] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:23] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [176875.0, 3700.0, 1670.0, -985.085, 1127.0, -1102.0] 
INFO     [2024-05-09 18:31:23] sg.writeMacroStateToFile :: _str_load =    1.768750000e+05     3.700000000e+03     1.670000000e+03    -9.850850000e+02     1.127000000e+03    -1.102000000e+03 
INFO     [2024-05-09 18:31:23] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 18:31:23] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:23] sg.runDF :: copying sg files to .\local\blade\loc5_7\main_cs_set6\case1... 
INFO     [2024-05-09 18:31:24] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:24] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:24] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [177173.0, 2036.0, 18332.0, -446.092, -182.751, -777.102] 
INFO     [2024-05-09 18:31:24] sg.writeMacroStateToFile :: _str_load =    1.771730000e+05     2.036000000e+03     1.833200000e+04    -4.460920000e+02    -1.827510000e+02    -7.771020000e+02 
INFO     [2024-05-09 18:31:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-09 18:31:24] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:24] sg.runDF :: copying sg files to .\local\blade\loc5_7\main_cs_set6\case2... 
INFO     [2024-05-09 18:31:24] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:24] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-05-09 18:31:24] sg.runDF :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-09 18:31:24] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:24] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 6, 'locs': [8]}] 
INFO     [2024-05-09 18:31:24] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:24] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:24] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [129430.0, 2720.0, -593.814, -727.33, 602.513, -714.368] 
INFO     [2024-05-09 18:31:24] sg.writeMacroStateToFile :: _str_load =    1.294300000e+05     2.720000000e+03    -5.938140000e+02    -7.273300000e+02     6.025130000e+02    -7.143680000e+02 
INFO     [2024-05-09 18:31:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 18:31:24] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:24] sg.runDF :: copying sg files to .\local\blade\loc6_8\main_cs_set7\case1... 
INFO     [2024-05-09 18:31:24] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:24] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:24] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [129514.0, 1327.0, 13822.0, -389.229, -95.069, -672.893] 
INFO     [2024-05-09 18:31:24] sg.writeMacroStateToFile :: _str_load =    1.295140000e+05     1.327000000e+03     1.382200000e+04    -3.892290000e+02    -9.506900000e+01    -6.728930000e+02 
INFO     [2024-05-09 18:31:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-09 18:31:24] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:24] sg.runDF :: copying sg files to .\local\blade\loc6_8\main_cs_set7\case2... 
INFO     [2024-05-09 18:31:25] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:25] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-05-09 18:31:25] sg.runDF :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-09 18:31:25] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:25] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 7, 'locs': [9]}] 
INFO     [2024-05-09 18:31:25] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:25] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:25] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [68614.0, 1435.0, -522.31, -341.219, -5.399, 30.09] 
INFO     [2024-05-09 18:31:25] sg.writeMacroStateToFile :: _str_load =    6.861400000e+04     1.435000000e+03    -5.223100000e+02    -3.412190000e+02    -5.399000000e+00     3.009000000e+01 
INFO     [2024-05-09 18:31:25] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 18:31:25] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:25] sg.runDF :: copying sg files to .\local\blade\loc7_9\main_cs_set8\case1... 
INFO     [2024-05-09 18:31:25] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:25] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:25] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [68702.0, 616.79, 6925.0, -321.953, -298.606, -49.447] 
INFO     [2024-05-09 18:31:25] sg.writeMacroStateToFile :: _str_load =    6.870200000e+04     6.167900000e+02     6.925000000e+03    -3.219530000e+02    -2.986060000e+02    -4.944700000e+01 
INFO     [2024-05-09 18:31:25] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-09 18:31:25] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:25] sg.runDF :: copying sg files to .\local\blade\loc7_9\main_cs_set8\case2... 
INFO     [2024-05-09 18:31:26] sg.runDF :: ============================================================ 
INFO     [2024-05-09 18:31:26] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-05-09 18:31:26] sg.runDF :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-09 18:31:26] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-09 18:31:26] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 8, 'locs': [10]}] 
INFO     [2024-05-09 18:31:26] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:26] sg.runDF :: case_name = case1 
INFO     [2024-05-09 18:31:26] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-09 18:31:26] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-09 18:31:26] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 18:31:26] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:26] sg.runDF :: copying sg files to .\local\blade\loc8_10\main_cs_set9\case1... 
INFO     [2024-05-09 18:31:26] sg.runDF :: .................... 
INFO     [2024-05-09 18:31:26] sg.runDF :: case_name = case2 
INFO     [2024-05-09 18:31:26] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-09 18:31:26] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-09 18:31:26] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-09 18:31:26] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-09 18:31:26] sg.runDF :: copying sg files to .\local\blade\loc8_10\main_cs_set9\case2... 
INFO     [2024-05-09 18:31:27] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-15 16:56:07] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-15 16:56:07] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-15 16:56:07] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-15 16:56:07] _msgd.updateData :: updating current design... 
INFO     [2024-05-15 16:56:07] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-15 16:56:07] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-15 16:56:07] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-15 16:56:07] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-15 16:56:07] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-15 16:56:07] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-15 16:56:07] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-15 16:56:07] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 16:56:07] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 16:56:07] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-15 16:56:07] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-15 16:56:07] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 16:56:07] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 16:56:07] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-15 16:56:07] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-15 16:56:07] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 16:56:07] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-15 16:56:07] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 16:56:07] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-15 16:56:07] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-15 16:56:07] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-15 16:56:07] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-15 16:56:07] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-15 16:56:07] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-15 16:56:07] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-15 16:56:07] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:07] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 16:56:07] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:07] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 16:56:07] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:07] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:08] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-15 16:56:08] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:08] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 16:56:08] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:08] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 16:56:08] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:09] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:10] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-15 16:56:10] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:10] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 16:56:10] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 16:56:10] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:10] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:11] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-15 16:56:11] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:11] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 16:56:11] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 16:56:11] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:11] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:12] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-15 16:56:12] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:12] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 16:56:12] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 16:56:12] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:13] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:13] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-15 16:56:13] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:13] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 16:56:13] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:13] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 16:56:13] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:14] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:15] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-15 16:56:15] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:15] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-15 16:56:15] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:15] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 16:56:15] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:15] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:16] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-15 16:56:16] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:16] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-15 16:56:16] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:16] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 16:56:16] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:16] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:17] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-15 16:56:17] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 16:56:17] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-15 16:56:17] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 16:56:17] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 16:56:17] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:17] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:56:18] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-15 16:56:18] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-15 16:56:18] sg.runDF :: ============================================================ 
INFO     [2024-05-15 16:56:18] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-15 16:56:18] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 16:56:18] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 16:56:18] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-15 16:56:18] sg.runDF :: .................... 
INFO     [2024-05-15 16:56:18] sg.runDF :: case_name = case1 
INFO     [2024-05-15 16:56:18] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [291047.0, 3807.0, 28154.0, -1360.0, 866.694, -545.46] 
INFO     [2024-05-15 16:56:18] sg.writeMacroStateToFile :: _str_load =    2.910470000e+05     3.807000000e+03     2.815400000e+04    -1.360000000e+03     8.666940000e+02    -5.454600000e+02 
INFO     [2024-05-15 16:56:18] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 16:56:18] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:56:18] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 16:56:20] sg.runDF :: copying sg files to cs\local\blade\loc1_2\main_cs_set1\case1... 
CRITICAL [2024-05-15 16:56:20] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 409, in run
    self.runDF(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 290, in runDF
    _output = _sg_analysis.run(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 767, in run
    output = self.runDF(
             ^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 692, in runDF
    shutil.copy(_src, _dst)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\shutil.py", line 419, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\shutil.py", line 256, in copyfile
    with open(src, 'rb') as fsrc:
         ^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'cs\\main_cs_set1.sg.glb'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 409, in run
    self.runDF(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 290, in runDF
    _output = _sg_analysis.run(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 767, in run
    output = self.runDF(
             ^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 692, in runDF
    shutil.copy(_src, _dst)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\shutil.py", line 419, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\shutil.py", line 256, in copyfile
    with open(src, 'rb') as fsrc:
         ^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'cs\\main_cs_set1.sg.glb'
INFO     [2024-05-15 16:56:20] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-15 17:04:36] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-15 17:04:36] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-15 17:04:36] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-15 17:04:36] _msgd.updateData :: updating current design... 
INFO     [2024-05-15 17:04:36] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-15 17:04:36] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-15 17:04:36] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-15 17:04:36] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-15 17:04:36] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-15 17:04:36] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-15 17:04:36] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-15 17:04:36] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:04:36] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 17:04:36] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-15 17:04:36] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-15 17:04:36] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:04:36] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 17:04:36] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-15 17:04:36] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:04:36] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:04:36] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:04:36] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:04:36] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-15 17:04:36] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-15 17:04:36] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-15 17:04:36] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-15 17:04:36] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-15 17:04:36] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-15 17:04:36] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-15 17:04:36] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:36] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:04:36] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:36] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 17:04:36] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:36] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:37] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-15 17:04:37] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:37] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 17:04:37] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:37] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 17:04:37] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:37] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:38] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-15 17:04:38] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:38] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 17:04:38] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:38] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 17:04:38] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:39] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:40] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-15 17:04:40] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:40] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 17:04:40] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:40] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 17:04:40] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:40] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:41] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-15 17:04:41] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:41] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 17:04:41] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:41] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 17:04:41] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:41] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:42] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-15 17:04:42] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:42] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 17:04:42] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:42] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 17:04:42] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:42] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:44] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-15 17:04:44] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:44] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-15 17:04:44] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:44] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 17:04:44] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:45] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:46] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-15 17:04:46] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:46] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-15 17:04:46] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:46] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 17:04:46] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:46] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:48] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-15 17:04:48] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:04:48] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-15 17:04:48] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:04:48] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 17:04:48] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:04:48] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:04:49] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-15 17:04:49] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-15 17:04:49] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:04:49] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-15 17:04:49] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:04:49] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:04:49] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-15 17:04:49] sg.runDF :: .................... 
INFO     [2024-05-15 17:04:49] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:04:49] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [291047.0, 3807.0, 28154.0, -1360.0, 866.694, -545.46] 
CRITICAL [2024-05-15 17:04:49] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 409, in run
    self.runDF(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 290, in runDF
    _output = _sg_analysis.run(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 767, in run
    output = self.runDF(
             ^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 671, in runDF
    self.writeMacroStateToFile(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 806, in writeMacroStateToFile
    _cs_xml_tree, _cs_xml_root = mutils.io.parseXML(_fn_xml)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 580, in parse
    self._root = parser._parse_whole(source)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
xml.etree.ElementTree.ParseError: syntax error: line 1, column 7
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 409, in run
    self.runDF(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 290, in runDF
    _output = _sg_analysis.run(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 767, in run
    output = self.runDF(
             ^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 671, in runDF
    self.writeMacroStateToFile(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 806, in writeMacroStateToFile
    _cs_xml_tree, _cs_xml_root = mutils.io.parseXML(_fn_xml)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 580, in parse
    self._root = parser._parse_whole(source)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
xml.etree.ElementTree.ParseError: syntax error: line 1, column 7
INFO     [2024-05-15 17:04:49] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-15 17:05:15] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-15 17:05:15] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-15 17:05:15] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-15 17:05:15] _msgd.updateData :: updating current design... 
INFO     [2024-05-15 17:05:15] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-15 17:05:15] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-15 17:05:15] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-15 17:05:15] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-15 17:05:15] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-15 17:05:15] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-15 17:05:15] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-15 17:05:15] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:05:15] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 17:05:15] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-15 17:05:15] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-15 17:05:15] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:05:15] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 17:05:15] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-15 17:05:15] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:05:15] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:05:15] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:05:15] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:05:16] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-15 17:05:16] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-15 17:05:16] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-15 17:05:16] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-15 17:05:16] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-15 17:05:16] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-15 17:05:16] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-15 17:05:16] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:16] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:05:16] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:16] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 17:05:16] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:16] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:17] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-15 17:05:17] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:17] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 17:05:17] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:17] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 17:05:17] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:17] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:18] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-15 17:05:18] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:18] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 17:05:18] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:18] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 17:05:18] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:18] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:20] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-15 17:05:20] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:20] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 17:05:20] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:20] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 17:05:20] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:20] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:21] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-15 17:05:21] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:21] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 17:05:21] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:21] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 17:05:21] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:21] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:22] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-15 17:05:22] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:22] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 17:05:22] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:22] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 17:05:22] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:22] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:24] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-15 17:05:24] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:24] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-15 17:05:24] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 17:05:24] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:24] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:25] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-15 17:05:25] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:25] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-15 17:05:25] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:25] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 17:05:25] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:26] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:28] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-15 17:05:28] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:05:28] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-15 17:05:28] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:05:28] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 17:05:28] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:05:28] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:05:29] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-15 17:05:29] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-15 17:05:29] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:05:29] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-15 17:05:29] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:05:29] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:05:29] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-15 17:05:29] sg.runDF :: .................... 
INFO     [2024-05-15 17:05:29] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:05:29] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [291047.0, 3807.0, 28154.0, -1360.0, 866.694, -545.46] 
CRITICAL [2024-05-15 17:05:29] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 409, in run
    self.runDF(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 290, in runDF
    _output = _sg_analysis.run(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 767, in run
    output = self.runDF(
             ^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 671, in runDF
    self.writeMacroStateToFile(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 806, in writeMacroStateToFile
    _cs_xml_tree, _cs_xml_root = mutils.io.parseXML(_fn_xml)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 580, in parse
    self._root = parser._parse_whole(source)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
xml.etree.ElementTree.ParseError: syntax error: line 1, column 7
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 409, in run
    self.runDF(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 290, in runDF
    _output = _sg_analysis.run(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 767, in run
    output = self.runDF(
             ^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 671, in runDF
    self.writeMacroStateToFile(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 806, in writeMacroStateToFile
    _cs_xml_tree, _cs_xml_root = mutils.io.parseXML(_fn_xml)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 580, in parse
    self._root = parser._parse_whole(source)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
xml.etree.ElementTree.ParseError: syntax error: line 1, column 7
INFO     [2024-05-15 17:05:30] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-15 17:06:44] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-15 17:06:44] io.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-05-15 17:06:44] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-15 17:06:44] _msgd.updateData :: updating current design... 
INFO     [2024-05-15 17:06:44] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-15 17:06:44] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-15 17:06:44] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-15 17:06:44] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-05-15 17:06:44] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-15 17:06:44] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-15 17:06:44] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-15 17:06:44] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:06:44] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 17:06:44] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-15 17:06:44] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-15 17:06:44] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:06:44] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-05-15 17:06:44] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-15 17:06:44] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:06:44] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:06:44] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:06:44] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:06:44] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-15 17:06:44] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-15 17:06:44] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-05-15 17:06:44] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-15 17:06:44] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-15 17:06:44] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-15 17:06:44] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-15 17:06:44] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:44] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:06:44] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:44] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 17:06:44] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:44] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:45] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-15 17:06:45] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:45] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 17:06:45] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:45] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 17:06:45] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:45] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:46] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-15 17:06:46] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:46] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 17:06:46] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:46] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 17:06:46] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:47] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:48] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-15 17:06:48] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:48] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 17:06:48] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:48] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 17:06:48] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:48] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:49] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-15 17:06:49] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:49] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 17:06:49] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:49] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 17:06:49] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:49] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:50] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-15 17:06:50] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:50] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 17:06:50] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:50] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 17:06:50] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:50] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:52] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-15 17:06:52] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:52] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-15 17:06:52] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:52] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 17:06:52] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:52] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:53] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-15 17:06:53] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:53] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-15 17:06:53] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:53] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 17:06:53] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:53] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:55] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-15 17:06:55] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:06:55] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-15 17:06:55] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:06:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 17:06:55] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:55] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:06:56] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-15 17:06:56] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-05-15 17:06:56] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:06:56] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-05-15 17:06:56] sg.runDF :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:06:56] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:06:56] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [2]}] 
INFO     [2024-05-15 17:06:56] sg.runDF :: .................... 
INFO     [2024-05-15 17:06:56] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:06:56] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [291047.0, 3807.0, 28154.0, -1360.0, 866.694, -545.46] 
INFO     [2024-05-15 17:06:56] sg.writeMacroStateToFile :: _str_load =    2.910470000e+05     3.807000000e+03     2.815400000e+04    -1.360000000e+03     8.666940000e+02    -5.454600000e+02 
INFO     [2024-05-15 17:06:56] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 17:06:56] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:56] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:06:56] sg.runDF :: copying sg files to cs\local\blade\loc1_2\main_cs_set1\case1... 
INFO     [2024-05-15 17:06:57] sg.runDF :: .................... 
INFO     [2024-05-15 17:06:57] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:06:57] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [294170.0, 5163.0, 23212.0, -504.894, -782.132, 671.741] 
INFO     [2024-05-15 17:06:57] sg.writeMacroStateToFile :: _str_load =    2.941700000e+05     5.163000000e+03     2.321200000e+04    -5.048940000e+02    -7.821320000e+02     6.717410000e+02 
INFO     [2024-05-15 17:06:57] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 17:06:57] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:57] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:06:57] sg.runDF :: copying sg files to cs\local\blade\loc1_2\main_cs_set1\case2... 
INFO     [2024-05-15 17:06:58] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:06:58] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-05-15 17:06:58] sg.runDF :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 17:06:58] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:06:58] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [3]}] 
INFO     [2024-05-15 17:06:58] sg.runDF :: .................... 
INFO     [2024-05-15 17:06:58] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:06:58] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [279032.0, 4107.0, 22941.0, -1310.0, 1299.0, -742.962] 
INFO     [2024-05-15 17:06:58] sg.writeMacroStateToFile :: _str_load =    2.790320000e+05     4.107000000e+03     2.294100000e+04    -1.310000000e+03     1.299000000e+03    -7.429620000e+02 
INFO     [2024-05-15 17:06:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 17:06:58] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:58] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:06:58] sg.runDF :: copying sg files to cs\local\blade\loc1_3\main_cs_set2\case1... 
INFO     [2024-05-15 17:06:58] sg.runDF :: .................... 
INFO     [2024-05-15 17:06:58] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:06:58] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [281519.0, 4583.0, 23110.0, -520.35, -734.339, 741.891] 
INFO     [2024-05-15 17:06:58] sg.writeMacroStateToFile :: _str_load =    2.815190000e+05     4.583000000e+03     2.311000000e+04    -5.203500000e+02    -7.343390000e+02     7.418910000e+02 
INFO     [2024-05-15 17:06:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 17:06:58] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:58] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:06:59] sg.runDF :: copying sg files to cs\local\blade\loc1_3\main_cs_set2\case2... 
INFO     [2024-05-15 17:06:59] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:06:59] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-05-15 17:06:59] sg.runDF :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 17:06:59] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:06:59] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 2, 'locs': [4]}] 
INFO     [2024-05-15 17:06:59] sg.runDF :: .................... 
INFO     [2024-05-15 17:06:59] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:06:59] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [261965.0, 4231.0, 18102.0, -1273.0, 1650.0, -825.125] 
INFO     [2024-05-15 17:06:59] sg.writeMacroStateToFile :: _str_load =    2.619650000e+05     4.231000000e+03     1.810200000e+04    -1.273000000e+03     1.650000000e+03    -8.251250000e+02 
INFO     [2024-05-15 17:06:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 17:06:59] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:06:59] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:00] sg.runDF :: copying sg files to cs\local\blade\loc2_4\main_cs_set3\case1... 
INFO     [2024-05-15 17:07:00] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:00] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:00] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [263837.0, 3926.0, 22932.0, -515.429, -712.348, 591.395] 
INFO     [2024-05-15 17:07:00] sg.writeMacroStateToFile :: _str_load =    2.638370000e+05     3.926000000e+03     2.293200000e+04    -5.154290000e+02    -7.123480000e+02     5.913950000e+02 
INFO     [2024-05-15 17:07:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 17:07:00] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:00] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:00] sg.runDF :: copying sg files to cs\local\blade\loc2_4\main_cs_set3\case2... 
INFO     [2024-05-15 17:07:01] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:07:01] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-05-15 17:07:01] sg.runDF :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 17:07:01] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:07:01] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 3, 'locs': [5]}] 
INFO     [2024-05-15 17:07:01] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:01] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:07:01] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [239848.0, 4410.0, 10821.0, -1212.0, 1600.0, -866.437] 
INFO     [2024-05-15 17:07:01] sg.writeMacroStateToFile :: _str_load =    2.398480000e+05     4.410000000e+03     1.082100000e+04    -1.212000000e+03     1.600000000e+03    -8.664370000e+02 
INFO     [2024-05-15 17:07:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 17:07:01] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:01] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:01] sg.runDF :: copying sg files to cs\local\blade\loc3_5\main_cs_set4\case1... 
INFO     [2024-05-15 17:07:02] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:02] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:02] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [241005.0, 3248.0, 22346.0, -506.046, -609.034, 258.88] 
INFO     [2024-05-15 17:07:02] sg.writeMacroStateToFile :: _str_load =    2.410050000e+05     3.248000000e+03     2.234600000e+04    -5.060460000e+02    -6.090340000e+02     2.588800000e+02 
INFO     [2024-05-15 17:07:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 17:07:02] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:02] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:02] sg.runDF :: copying sg files to cs\local\blade\loc3_5\main_cs_set4\case2... 
INFO     [2024-05-15 17:07:02] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:07:02] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-05-15 17:07:02] sg.runDF :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 17:07:02] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:07:02] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 4, 'locs': [6]}] 
INFO     [2024-05-15 17:07:02] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:02] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:07:02] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [211962.0, 4256.0, 5073.0, -1102.0, 1449.0, -906.772] 
INFO     [2024-05-15 17:07:02] sg.writeMacroStateToFile :: _str_load =    2.119620000e+05     4.256000000e+03     5.073000000e+03    -1.102000000e+03     1.449000000e+03    -9.067720000e+02 
INFO     [2024-05-15 17:07:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 17:07:02] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:02] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:02] sg.runDF :: copying sg files to cs\local\blade\loc4_6\main_cs_set5\case1... 
INFO     [2024-05-15 17:07:03] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:03] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:03] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [212616.0, 2638.0, 20790.0, -483.174, -410.759, -187.09] 
INFO     [2024-05-15 17:07:03] sg.writeMacroStateToFile :: _str_load =    2.126160000e+05     2.638000000e+03     2.079000000e+04    -4.831740000e+02    -4.107590000e+02    -1.870900000e+02 
INFO     [2024-05-15 17:07:03] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 17:07:03] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:03] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:03] sg.runDF :: copying sg files to cs\local\blade\loc4_6\main_cs_set5\case2... 
INFO     [2024-05-15 17:07:04] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:07:04] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-05-15 17:07:04] sg.runDF :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 17:07:04] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:07:04] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 5, 'locs': [7]}] 
INFO     [2024-05-15 17:07:04] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:04] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:07:04] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [176875.0, 3700.0, 1670.0, -985.085, 1127.0, -1102.0] 
INFO     [2024-05-15 17:07:04] sg.writeMacroStateToFile :: _str_load =    1.768750000e+05     3.700000000e+03     1.670000000e+03    -9.850850000e+02     1.127000000e+03    -1.102000000e+03 
INFO     [2024-05-15 17:07:04] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 17:07:04] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:04] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:04] sg.runDF :: copying sg files to cs\local\blade\loc5_7\main_cs_set6\case1... 
INFO     [2024-05-15 17:07:04] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:04] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:04] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [177173.0, 2036.0, 18332.0, -446.092, -182.751, -777.102] 
INFO     [2024-05-15 17:07:04] sg.writeMacroStateToFile :: _str_load =    1.771730000e+05     2.036000000e+03     1.833200000e+04    -4.460920000e+02    -1.827510000e+02    -7.771020000e+02 
INFO     [2024-05-15 17:07:04] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 17:07:04] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:04] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:04] sg.runDF :: copying sg files to cs\local\blade\loc5_7\main_cs_set6\case2... 
INFO     [2024-05-15 17:07:05] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:07:05] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-05-15 17:07:05] sg.runDF :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-15 17:07:05] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:07:05] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 6, 'locs': [8]}] 
INFO     [2024-05-15 17:07:05] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:05] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:07:05] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [129430.0, 2720.0, -593.814, -727.33, 602.513, -714.368] 
INFO     [2024-05-15 17:07:05] sg.writeMacroStateToFile :: _str_load =    1.294300000e+05     2.720000000e+03    -5.938140000e+02    -7.273300000e+02     6.025130000e+02    -7.143680000e+02 
INFO     [2024-05-15 17:07:05] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 17:07:05] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:05] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:05] sg.runDF :: copying sg files to cs\local\blade\loc6_8\main_cs_set7\case1... 
INFO     [2024-05-15 17:07:05] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:05] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:05] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [129514.0, 1327.0, 13822.0, -389.229, -95.069, -672.893] 
INFO     [2024-05-15 17:07:05] sg.writeMacroStateToFile :: _str_load =    1.295140000e+05     1.327000000e+03     1.382200000e+04    -3.892290000e+02    -9.506900000e+01    -6.728930000e+02 
INFO     [2024-05-15 17:07:05] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 17:07:05] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:05] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:05] sg.runDF :: copying sg files to cs\local\blade\loc6_8\main_cs_set7\case2... 
INFO     [2024-05-15 17:07:06] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:07:06] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-05-15 17:07:06] sg.runDF :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-15 17:07:06] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:07:06] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 7, 'locs': [9]}] 
INFO     [2024-05-15 17:07:06] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:06] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:07:06] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [68614.0, 1435.0, -522.31, -341.219, -5.399, 30.09] 
INFO     [2024-05-15 17:07:06] sg.writeMacroStateToFile :: _str_load =    6.861400000e+04     1.435000000e+03    -5.223100000e+02    -3.412190000e+02    -5.399000000e+00     3.009000000e+01 
INFO     [2024-05-15 17:07:06] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 17:07:06] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:06] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:06] sg.runDF :: copying sg files to cs\local\blade\loc7_9\main_cs_set8\case1... 
INFO     [2024-05-15 17:07:06] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:06] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:06] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [68702.0, 616.79, 6925.0, -321.953, -298.606, -49.447] 
INFO     [2024-05-15 17:07:06] sg.writeMacroStateToFile :: _str_load =    6.870200000e+04     6.167900000e+02     6.925000000e+03    -3.219530000e+02    -2.986060000e+02    -4.944700000e+01 
INFO     [2024-05-15 17:07:06] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 17:07:06] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:07] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:07] sg.runDF :: copying sg files to cs\local\blade\loc7_9\main_cs_set8\case2... 
INFO     [2024-05-15 17:07:07] sg.runDF :: ============================================================ 
INFO     [2024-05-15 17:07:07] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-05-15 17:07:07] sg.runDF :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-15 17:07:07] sg.runDF :: ---------------------------------------- 
INFO     [2024-05-15 17:07:07] sg.runDF :: loc: [{'structure': 'blade', 'loc_id': 8, 'locs': [10]}] 
INFO     [2024-05-15 17:07:07] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:07] sg.runDF :: case_name = case1 
INFO     [2024-05-15 17:07:07] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 90
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-15 17:07:07] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-15 17:07:07] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 17:07:07] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:07] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:07] sg.runDF :: copying sg files to cs\local\blade\loc8_10\main_cs_set9\case1... 
INFO     [2024-05-15 17:07:08] sg.runDF :: .................... 
INFO     [2024-05-15 17:07:08] sg.runDF :: case_name = case2 
INFO     [2024-05-15 17:07:08] sg.runDF :: _state_case = state case
case:
  case: 1
  azimuth: 270
states:
  state: load ([])
  point data: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] 
INFO     [2024-05-15 17:07:08] sg.writeMacroStateToFile :: _str_load =    0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00     0.000000000e+00 
INFO     [2024-05-15 17:07:08] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 17:07:08] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:07:08] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -fi 
INFO     [2024-05-15 17:07:08] sg.runDF :: copying sg files to cs\local\blade\loc8_10\main_cs_set9\case2... 
INFO     [2024-05-15 17:07:08] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-04 14:15:43] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-04 14:15:43] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-04 14:15:43] _msgd.updateData :: updating current design... 
INFO     [2024-06-04 14:15:43] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-06-04 14:15:43] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-06-04 14:15:43] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-06-04 14:15:43] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-06-04 14:15:43] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-06-04 14:15:43] distribution.loadData :: loading data (file)... 
INFO     [2024-06-04 14:15:43] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-04 14:15:43] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-06-04 14:15:43] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-06-04 14:15:43] distribution.loadData :: loading data (file)... 
INFO     [2024-06-04 14:15:43] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-04 14:15:43] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-06-04 14:15:43] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-06-04 14:15:43] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-04 14:15:43] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-06-04 14:15:43] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-04 14:15:43] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-04 14:15:43] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-06-04 14:15:43] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-06-04 14:15:43] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-06-04 14:15:43] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-04 14:15:43] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-04 14:15:43] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-04 14:15:55] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-06-04 14:15:55] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-04 14:15:55] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-06-04 14:15:55] sg.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-06-04 14:15:55] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:15:55] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-06-04 14:15:56] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:15:56] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-06-04 14:15:57] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:15:57] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-06-04 14:15:58] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:15:58] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-06-04 14:15:59] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:15:59] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-06-04 14:16:00] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:16:00] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-06-04 14:16:01] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:16:01] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-06-04 14:16:02] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:16:02] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-06-04 14:16:03] sg.runDF :: ============================================================ 
INFO     [2024-06-04 14:16:03] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-06-04 14:16:04] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-23 01:13:33] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-23 01:13:33] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-23 01:13:33] _msgd.updateData :: updating current design... 
INFO     [2024-06-23 01:13:33] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-06-23 01:13:33] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-06-23 01:13:33] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-06-23 01:13:33] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-06-23 01:13:33] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-06-23 01:13:33] distribution.loadData :: loading data (file)... 
INFO     [2024-06-23 01:13:33] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-23 01:13:33] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-06-23 01:13:33] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-06-23 01:13:33] distribution.loadData :: loading data (file)... 
INFO     [2024-06-23 01:13:33] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-23 01:13:33] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-06-23 01:13:33] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-06-23 01:13:33] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-23 01:13:33] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-06-23 01:13:33] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-23 01:13:33] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-23 01:13:33] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-06-23 01:13:33] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-06-23 01:13:33] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-06-23 01:13:33] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-23 01:13:33] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-23 01:13:33] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-23 01:13:44] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-06-23 01:13:44] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-23 01:13:44] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-06-23 01:13:44] sg.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-06-23 01:13:44] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:44] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-06-23 01:13:44] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:44] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-06-23 01:13:46] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:46] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-06-23 01:13:47] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:47] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-06-23 01:13:48] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:48] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-06-23 01:13:48] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:48] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-06-23 01:13:49] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:49] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-06-23 01:13:50] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:50] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-06-23 01:13:51] sg.runDF :: ============================================================ 
INFO     [2024-06-23 01:13:51] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-06-23 01:13:52] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-24 21:57:55] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-24 21:57:55] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-24 21:57:55] _msgd.updateData :: updating current design... 
INFO     [2024-06-24 21:57:55] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-06-24 21:57:55] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-06-24 21:57:55] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-06-24 21:57:55] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-06-24 21:57:55] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-06-24 21:57:55] distribution.loadData :: loading data (file)... 
INFO     [2024-06-24 21:57:55] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-24 21:57:55] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-06-24 21:57:55] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-06-24 21:57:55] distribution.loadData :: loading data (file)... 
INFO     [2024-06-24 21:57:55] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-24 21:57:55] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-06-24 21:57:55] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-06-24 21:57:55] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-24 21:57:55] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-06-24 21:57:55] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-24 21:57:55] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-24 21:57:55] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-06-24 21:57:55] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-06-24 21:57:56] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-06-24 21:57:56] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-24 21:57:56] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-24 21:57:56] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-24 21:58:07] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-06-24 21:58:07] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-24 21:58:07] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-06-24 21:58:07] sg.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-06-24 21:58:07] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:07] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-06-24 21:58:08] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:08] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-06-24 21:58:09] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:09] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-06-24 21:58:10] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:10] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-06-24 21:58:11] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:11] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-06-24 21:58:12] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:12] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-06-24 21:58:13] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:13] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-06-24 21:58:14] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:14] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-06-24 21:58:15] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:15] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-06-24 21:58:16] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-24 21:58:39] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-24 21:58:39] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-24 21:58:39] _msgd.updateData :: updating current design... 
INFO     [2024-06-24 21:58:39] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-06-24 21:58:39] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-06-24 21:58:39] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-06-24 21:58:39] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-06-24 21:58:39] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-06-24 21:58:39] distribution.loadData :: loading data (file)... 
INFO     [2024-06-24 21:58:39] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-24 21:58:39] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-06-24 21:58:39] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-06-24 21:58:39] distribution.loadData :: loading data (file)... 
INFO     [2024-06-24 21:58:39] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-06-24 21:58:39] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-06-24 21:58:39] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-06-24 21:58:39] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-24 21:58:39] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-06-24 21:58:39] distribution.loadData :: loading data (compact)... 
INFO     [2024-06-24 21:58:39] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-24 21:58:39] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-06-24 21:58:39] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-06-24 21:58:39] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-06-24 21:58:39] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-24 21:58:39] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-24 21:58:39] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-24 21:58:49] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-06-24 21:58:49] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-24 21:58:49] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-06-24 21:58:49] sg.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-06-24 21:58:49] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:49] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-06-24 21:58:50] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:50] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-06-24 21:58:51] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:51] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-06-24 21:58:52] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:52] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-06-24 21:58:53] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:53] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-06-24 21:58:53] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:53] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-06-24 21:58:54] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:54] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-06-24 21:58:55] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:55] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-06-24 21:58:56] sg.runDF :: ============================================================ 
INFO     [2024-06-24 21:58:56] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-06-24 21:58:57] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-07-22 15:41:51] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-07-22 15:41:51] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-07-22 15:41:51] _msgd.updateData :: updating current design... 
INFO     [2024-07-22 15:41:51] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-07-22 15:41:51] rcas.readRcasInput :: reading rcas input file rcas_input.dat... 
INFO     [2024-07-22 15:41:51] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-07-22 15:41:51] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-07-22 15:41:51] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-07-22 15:41:51] distribution.loadData :: loading data (file)... 
INFO     [2024-07-22 15:41:51] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-07-22 15:41:51] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-07-22 15:41:51] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-07-22 15:41:51] distribution.loadData :: loading data (file)... 
INFO     [2024-07-22 15:41:51] interface.loadDataFromFile :: loading data from rcas_input.dat (rcas)... 
INFO     [2024-07-22 15:41:51] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-07-22 15:41:51] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-07-22 15:41:51] distribution.loadData :: loading data (compact)... 
INFO     [2024-07-22 15:41:51] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-07-22 15:41:51] distribution.loadData :: loading data (compact)... 
INFO     [2024-07-22 15:41:51] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-07-22 15:41:51] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-07-22 15:41:51] _structure.calcParamsFromDistributions :: [blade] calculating parameters from distributions... 
INFO     [2024-07-22 15:41:51] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-07-22 15:41:51] _msgd.analyze :: [main] going through steps... 
INFO     [2024-07-22 15:41:51] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-07-22 15:41:51] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-07-22 15:41:55] _structure.writeSGPropertyFile :: [blade] writing SG properties to file prop_calc.dat... 
INFO     [2024-07-22 15:41:55] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-07-22 15:41:55] sg.runDF :: [step: cs recovery] running cs analysis (fi)... 
INFO     [2024-07-22 15:41:55] sg.readMacroStateFileCsv :: reading structural response file global_force_moment.csv... 
INFO     [2024-07-22 15:41:55] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:55] sg.runDF :: loc_id = 1, entity_id = 2, sg_model_name = main_cs_set1 
INFO     [2024-07-22 15:41:55] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:55] sg.runDF :: loc_id = 1, entity_id = 3, sg_model_name = main_cs_set2 
INFO     [2024-07-22 15:41:56] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:56] sg.runDF :: loc_id = 2, entity_id = 4, sg_model_name = main_cs_set3 
INFO     [2024-07-22 15:41:56] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:56] sg.runDF :: loc_id = 3, entity_id = 5, sg_model_name = main_cs_set4 
INFO     [2024-07-22 15:41:57] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:57] sg.runDF :: loc_id = 4, entity_id = 6, sg_model_name = main_cs_set5 
INFO     [2024-07-22 15:41:57] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:57] sg.runDF :: loc_id = 5, entity_id = 7, sg_model_name = main_cs_set6 
INFO     [2024-07-22 15:41:57] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:57] sg.runDF :: loc_id = 6, entity_id = 8, sg_model_name = main_cs_set7 
INFO     [2024-07-22 15:41:58] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:58] sg.runDF :: loc_id = 7, entity_id = 9, sg_model_name = main_cs_set8 
INFO     [2024-07-22 15:41:58] sg.runDF :: ============================================================ 
INFO     [2024-07-22 15:41:58] sg.runDF :: loc_id = 8, entity_id = 10, sg_model_name = main_cs_set9 
INFO     [2024-07-22 15:41:58] _msgd.writeAnalysisOut :: [main] writing output to file ... 
[22:28:00] INFO     reading main input main.yml... [io.readMSGDInput]
[22:28:00] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[22:28:00] INFO     updating current design... [_msgd.updateData]
[22:28:00] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[22:28:00] INFO     reading rcas input file rcas_input.dat... [rcas.readRcasInput]
[22:28:00] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[22:28:00] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[22:28:00] INFO     [airfoil] implementing parameter distribution... [distribution.implement]
[22:28:00] INFO     loading data (file)... [distribution.loadData]
[22:28:00] INFO     loading data from rcas_input.dat (rcas)... [interface.loadDataFromFile]
[22:28:00] INFO     loading data airfoilinterp... [interface.loadDataFromFile]
[22:28:00] INFO     [chord] implementing parameter distribution... [distribution.implement]
[22:28:00] INFO     loading data (file)... [distribution.loadData]
[22:28:00] INFO     loading data from rcas_input.dat (rcas)... [interface.loadDataFromFile]
[22:28:00] INFO     loading data chord_structure... [interface.loadDataFromFile]
[22:28:00] INFO     [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... [distribution.implement]
[22:28:00] INFO     loading data (compact)... [distribution.loadData]
[22:28:00] CRITICAL Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 460, in updateData
    self._global_structure.design.implementDistributionFunctions(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 216, in implementDistributionFunctions
    _distr.implement(db_function, db_domain, self.parameters)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\design\distribution.py", line 364, in implement
    _data = loadData(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\design\distribution.py", line 114, in loadData
    _y.append(eval(_t)(_v))
ValueError: invalid literal for int() with base 10: ' ply_spar_l1_root: 8'
 [__main__.main]
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 460, in updateData
    self._global_structure.design.implementDistributionFunctions(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 216, in implementDistributionFunctions
    _distr.implement(db_function, db_domain, self.parameters)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\design\distribution.py", line 364, in implement
    _data = loadData(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\design\distribution.py", line 114, in loadData
    _y.append(eval(_t)(_v))
ValueError: invalid literal for int() with base 10: ' ply_spar_l1_root: 8'
[22:28:00] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
