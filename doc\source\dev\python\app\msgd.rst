.. _section-ref_msgd:

Class MSGD
============

.. currentmodule:: msgd._msgd

.. highlight:: python

::

    msgd._msgd.MSGD


..  autoclass:: MSGD
    :members:

.. Class attributes
.. --------------------

.. ..  autosummary::
..     :toctree: /_temp

..     MSGD.name
..     MSGD.mode
..     MSGD.data
..     MSGD.structure_data
..     MSGD.structure
..     MSGD.sg_lib
..     MSGD.sg_db
..     MSGD.param_distrs
..     MSGD.functions
..     MSGD.eval_id
..     MSGD.eval_in
..     MSGD.eval_out


.. Class methods
.. -----------------

.. ..  autosummary::
..     :toctree: /_temp

..     MSGD.summary
..     MSGD.writeInput
..     MSGD.writeInterim
..     MSGD.runMDAO
..     MSGD.readMDAOEvalIn
..     MSGD.writeMDAOEvalOut
..     MSGD.updateData
..     MSGD.discretize

