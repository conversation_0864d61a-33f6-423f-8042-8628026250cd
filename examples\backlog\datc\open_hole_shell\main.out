Dakota version 6.18 released May 15 2023.
Repository revision 8ed67325d (2023-05-10) built May 10 2023 20:24:18.
Running serial Dakota executable in serial mode.
Start time: Fri Feb  7 12:32:47 2025

-----------------------
Begin DAKOTA input file
main.dakota
-----------------------
# main.dakota

environment
  output_file = 'main.out'
  write_restart = 'main.rest'
  error_file = 'main.err'
  tabular_data
    tabular_data_file = 'main_tabular.dat'
  results_output
    results_output_file = 'main_results'


method
  sampling
    sample_type
      lhs
    samples =  10
    seed =  1027


model
  single

variables
  active = design

  continuous_design = 8
    descriptors = 'l1v1'  'l1v2'  'l1v3'  'l1v4'  'l1v5'  'l1v6'  'l1v7'  'l1v8'
    upper_bounds = 90  90  90  90  90  90  90  90
    lower_bounds = -90  -90  -90  -90  -90  -90  -90  -90




interface
  analysis_driver = 'datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile {PARAMETERS} --resultfile {RESULTS}'
    fork
      parameters_file =  'params.in'
      results_file =  'results.out'
      file_save
      work_directory
        named =  'evals/eval'
        directory_tag
        directory_save
        copy_file =  'main.yml'  'sup_files/*'
      verbatim


responses
  descriptors =  'u1'  'u2'  'u3'  'sr_min'
  response_functions = 4
  no_gradients
  no_hessians


---------------------
End DAKOTA input file
---------------------

Using Dakota input file 'main.dakota'
Writing new restart file 'main.rest'.

>>>>> Executing environment.

>>>>> Running random_sampling iterator.

NonD lhs Samples = 10 Seed (user-specified) = 1027

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                      1.9364460488e+01 l1v1
                     -8.8369112564e+00 l1v2
                     -7.9239821934e+01 l1v3
                      7.4994923078e+01 l1v4
                     -7.6531580547e+00 l1v5
                     -7.9424305284e+01 l1v6
                     -5.0484695113e+01 l1v7
                      4.8681715571e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 1:
Active set vector = { 1 1 1 1 }
                     -1.2434894643e-06 u1
                     -5.3838133628e-38 u2
                      6.6467202942e-07 u3
                      4.8274139000e+04 sr_min



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                      4.1067699399e+01 l1v1
                      5.0703239048e+01 l1v2
                     -4.0899919766e+01 l1v3
                     -2.3765624920e+01 l1v4
                     -3.8053521354e+01 l1v5
                      8.3684678910e+01 l1v6
                     -6.4208306084e+01 l1v7
                     -8.9514696871e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 2:
Active set vector = { 1 1 1 1 }
                      4.4129806156e-06 u1
                     -5.1771408970e-38 u2
                      7.7516870078e-07 u3
                      3.5655936000e+04 sr_min



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      2.1627831752e+00 l1v1
                      2.9841036650e+01 l1v2
                     -3.6976772966e-01 l1v3
                      1.9564451692e+00 l1v4
                      7.9143390519e+01 l1v5
                      1.6780153116e+01 l1v6
                      4.1737522155e+01 l1v7
                      3.0673896446e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 3:
Active set vector = { 1 1 1 1 }
                      9.3063317053e-09 u1
                     -5.6562807152e-38 u2
                      5.9888895976e-07 u3
                      5.1123719000e+04 sr_min



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                     -4.3155260480e+01 l1v1
                      8.7169420017e+01 l1v2
                     -2.8480269610e+01 l1v3
                     -6.1195629909e+01 l1v4
                     -2.6935808854e+01 l1v5
                     -1.2653156273e+01 l1v6
                     -9.7615015861e+00 l1v7
                      8.3661797714e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 4:
Active set vector = { 1 1 1 1 }
                      9.9153396604e-07 u1
                     -4.0161093476e-38 u2
                      6.9536275760e-07 u3
                      3.5079064000e+04 sr_min



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                     -5.6070330205e+01 l1v1
                     -5.6271777186e+01 l1v2
                     -6.1146934806e+01 l1v3
                     -8.3636680658e+01 l1v4
                      4.3448101893e+01 l1v5
                      4.0167143594e+01 l1v6
                      1.6485057935e+01 l1v7
                     -3.6895086039e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 5:
Active set vector = { 1 1 1 1 }
                     -4.1433615650e-08 u1
                     -4.3921990780e-38 u2
                      4.4052399062e-07 u3
                      3.7058171000e+04 sr_min



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                     -8.8028941515e+01 l1v1
                     -3.6105485702e+01 l1v2
                      5.9245379222e+01 l1v3
                      3.7546371872e+01 l1v4
                     -7.5195585048e+01 l1v5
                     -3.4673473625e+01 l1v6
                      1.9900131944e+01 l1v7
                     -5.3194712098e+00 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 6:
Active set vector = { 1 1 1 1 }
                      1.8360837828e-07 u1
                     -4.8835178614e-38 u2
                      6.2592306449e-07 u3
                      4.6770438000e+04 sr_min



---------------------
Begin Evaluation    7
---------------------
Parameters for evaluation 7:
                      7.7206501620e+01 l1v1
                     -3.2765602880e+01 l1v2
                      4.6765008573e+01 l1v3
                     -4.5885187963e+01 l1v4
                     -5.4934349103e+01 l1v5
                     -6.8111141890e+01 l1v6
                      7.0322926601e+01 l1v7
                     -6.7993241960e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 7:
Active set vector = { 1 1 1 1 }
                     -1.8721020467e-07 u1
                     -5.4914420532e-38 u2
                      6.2541812440e-07 u3
                      4.2919884000e+04 sr_min



---------------------
Begin Evaluation    8
---------------------
Parameters for evaluation 8:
                     -1.9226786794e+01 l1v1
                      1.1652908840e+01 l1v2
                      7.2125695217e+01 l1v3
                     -1.4158224104e+01 l1v4
                      6.9595708319e+01 l1v5
                     -4.6184500855e+01 l1v6
                     -8.7238244276e+01 l1v7
                     -2.1077434219e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 8:
Active set vector = { 1 1 1 1 }
                     -3.8418903614e-07 u1
                     -6.3082617306e-38 u2
                      5.2441669141e-07 u3
                      5.9393721000e+04 sr_min



---------------------
Begin Evaluation    9
---------------------
Parameters for evaluation 9:
                     -1.2608430362e+00 l1v1
                      6.6477306887e+01 l1v2
                      3.5778769112e+01 l1v3
                      5.5826267415e+01 l1v4
                      3.0891073225e+00 l1v5
                      3.2623678404e+01 l1v6
                      8.3396098420e+01 l1v7
                      6.5966674858e+00 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 9:
Active set vector = { 1 1 1 1 }
                     -2.1226217939e-07 u1
                     -5.3706523676e-38 u2
                      7.3664591582e-07 u3
                      4.7323750000e+04 sr_min



---------------------
Begin Evaluation   10
---------------------
Parameters for evaluation 10:
                      7.1319516792e+01 l1v1
                     -8.1144741063e+01 l1v2
                      6.5077806674e+00 l1v3
                      3.1786869836e+01 l1v4
                      3.3211330350e+01 l1v5
                      7.1156914874e+01 l1v6
                     -2.1855903254e+01 l1v7
                      5.9637372896e+01 l1v8

blocking fork: datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile params.in --resultfile results.out

Active response data for evaluation 10:
Active set vector = { 1 1 1 1 }
                      3.1572929515e-07 u1
                     -5.1083287346e-38 u2
                      7.5894712381e-07 u3
                      4.7062348000e+04 sr_min


<<<<< Function evaluation summary: 10 total (10 new, 0 duplicate)
-----------------------------------------------------------------------------
Statistics based on 10 samples:

Sample moment statistics for each response function:
                            Mean           Std Dev          Skewness          Kurtosis
            u1  3.8445740866e-07  1.5234694710e-06  2.3684111154e+00  6.6477305646e+00
            u2 -5.1787746148e-38  6.4489495682e-39  2.5289663123e-01  5.9445259920e-01
            u3  6.4459673581e-07  1.0565823390e-07 -6.7156558240e-01  1.7551486285e-02
        sr_min  4.5066117000e+04  7.6105116478e+03  2.7351227493e-01 -7.3642143457e-02

95% confidence intervals for each response function:
                    LowerCI_Mean      UpperCI_Mean    LowerCI_StdDev    UpperCI_StdDev
            u1 -7.0536699847e-07  1.4742818158e-06  1.0478959386e-06  2.7812613791e-06
            u2 -5.6401046758e-38 -4.7174445538e-38  4.4358145596e-39  1.1773267998e-38
            u3  5.6901338851e-07  7.2018008311e-07  7.2675453163e-08  1.9289074768e-07
        sr_min  3.9621884935e+04  5.0510349065e+04  5.2347778527e+03  1.3893827558e+04

Simple Correlation Matrix among all inputs and outputs:
                     l1v1         l1v2         l1v3         l1v4         l1v5         l1v6         l1v7         l1v8           u1           u2           u3       sr_min 
        l1v1  1.00000e+00 
        l1v2 -1.51554e-01  1.00000e+00 
        l1v3 -4.45029e-02 -5.49503e-02  1.00000e+00 
        l1v4  1.35148e-01 -4.74224e-02  1.28483e-01  1.00000e+00 
        l1v5  2.27680e-02 -5.83893e-02 -5.88091e-02 -6.00957e-02  1.00000e+00 
        l1v6  1.41806e-01  4.94074e-02 -2.28817e-01 -1.45589e-01  2.37066e-01  1.00000e+00 
        l1v7 -1.21354e-02 -2.07727e-03  2.57013e-01 -9.23588e-03 -1.77314e-01 -5.53186e-03  1.00000e+00 
        l1v8 -1.59161e-01  1.09204e-01 -1.78781e-01  3.51618e-01  2.35018e-01 -1.08539e-01 -2.27754e-02  1.00000e+00 
          u1  1.55283e-01  3.51192e-01 -1.98678e-01 -3.14625e-01 -2.99921e-01  6.27594e-01 -2.95301e-01 -4.37441e-01  1.00000e+00 
          u2 -3.90452e-01  3.93698e-02 -4.92714e-01 -3.95720e-01 -3.77372e-01  2.62771e-01  1.71958e-01  2.75884e-01  2.24785e-01  1.00000e+00 
          u3  4.95022e-01  3.35924e-01 -5.04794e-02  4.60208e-01 -4.15431e-01  3.37611e-01 -2.26009e-02  2.06526e-01  4.64217e-01  7.01941e-02  1.00000e+00 
      sr_min  3.16594e-02 -1.52774e-01  5.65666e-01  5.31151e-01  4.97549e-01 -3.81411e-01 -1.44064e-01  1.59412e-01 -5.71820e-01 -8.32596e-01 -2.19767e-01  1.00000e+00 

Partial Correlation Matrix between input and output:
                       u1           u2           u3       sr_min 
        l1v1  3.94614e-01 -9.38919e-01  9.99660e-01 -5.71993e-02 
        l1v2  8.00693e-01 -8.26510e-01  9.99257e-01 -4.89587e-01 
        l1v3  1.20774e-01 -9.43401e-01  9.94648e-01  9.89342e-01 
        l1v4 -5.49434e-01 -9.68904e-01  9.98823e-01  9.89995e-01 
        l1v5 -8.71532e-01 -9.81164e-01 -9.99741e-01  9.94285e-01 
        l1v6  9.31439e-01  9.51721e-01  9.99632e-01 -9.83197e-01 
        l1v7 -8.39792e-01  8.06753e-01 -9.95847e-01 -9.18194e-01 
        l1v8 -5.97933e-01  9.71392e-01  9.99140e-01 -8.50090e-01 

Simple Rank Correlation Matrix among all inputs and outputs:
                     l1v1         l1v2         l1v3         l1v4         l1v5         l1v6         l1v7         l1v8           u1           u2           u3       sr_min 
        l1v1  1.00000e+00 
        l1v2 -7.87879e-02  1.00000e+00 
        l1v3 -9.09091e-02 -7.87879e-02  1.00000e+00 
        l1v4  1.15152e-01 -3.03030e-02  1.27273e-01  1.00000e+00 
        l1v5 -6.66667e-02 -1.81818e-02 -9.09091e-02 -3.03030e-02  1.00000e+00 
        l1v6  4.24242e-02  6.06061e-03 -2.48485e-01 -2.36364e-01  2.00000e-01  1.00000e+00 
        l1v7 -4.24242e-02  4.24242e-02  2.12121e-01  6.66667e-02 -1.39394e-01 -4.24242e-02  1.00000e+00 
        l1v8 -1.03030e-01  1.27273e-01 -1.63636e-01  3.57576e-01  2.24242e-01 -1.15152e-01  6.06061e-03  1.00000e+00 
          u1 -6.06061e-03  1.03030e-01 -1.39394e-01 -3.93939e-01 -2.96970e-01  6.60606e-01 -9.09091e-02  4.24242e-02  1.00000e+00 
          u2 -4.42424e-01 -1.03030e-01 -3.57576e-01 -3.09091e-01 -3.69697e-01  4.18182e-01  4.24242e-02  2.36364e-01  6.00000e-01  1.00000e+00 
          u3  4.18182e-01  2.96970e-01 -1.75758e-01  3.33333e-01 -4.18182e-01  4.30303e-01 -1.27273e-01  2.36364e-01  4.90909e-01  2.84848e-01  1.00000e+00 
      sr_min  7.87879e-02 -9.09091e-02  3.45455e-01  6.24242e-01  5.87879e-01 -3.69697e-01 -5.45455e-02  1.75758e-01 -6.84848e-01 -7.81818e-01 -3.21212e-01  1.00000e+00 

Partial Rank Correlation Matrix between input and output:
                       u1           u2           u3       sr_min 
        l1v1  5.69657e-02 -9.36203e-01  9.41352e-01  6.13456e-01 
        l1v2  1.04364e-01 -8.24214e-01  9.13278e-01 -6.23853e-02 
        l1v3  3.24249e-01 -7.96046e-01  3.26628e-01  9.22322e-01 
        l1v4 -6.48366e-01 -8.57597e-01  9.12344e-01  9.86018e-01 
        l1v5 -7.88846e-01 -9.63289e-01 -9.76202e-01  9.91672e-01 
        l1v6  8.64125e-01  9.38174e-01  9.78414e-01 -9.62401e-01 
        l1v7 -3.28319e-01  1.94247e-01 -8.53311e-01 -4.87113e-01 
        l1v8  6.60449e-01  9.30036e-01  9.18052e-01 -8.83267e-01 

-----------------------------------------------------------------------------

<<<<< Iterator random_sampling completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =    247.069 [parent =    247.069, child =          0]
  Total wall clock =    247.069
