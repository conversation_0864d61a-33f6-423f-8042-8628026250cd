sg:
- name: mainsg
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: &id001 {}
  design:
    name: lv1_layup
    parameter: &id002
      a1: 0
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design: &id003
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
- name: lv1_layup
  parameter:
    mesh_size: -1
  model:
    type: pl1
    tool: swiftcomp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: *id001
  design:
    name: lv1_layup
    parameter: *id002
    dim: 1
    builder: default
    design: *id003
- name: m2
  parameter: {}
  model:
    type: sd1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: *id001
  design:
    name: m2
    parameter: {}
    dim: null
    builder: default
    design: null
