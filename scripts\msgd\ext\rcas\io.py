"""
A function reading RCAS OML data file.
Data formatting rules:
1. Key tags:
  - 'S': keyword line (space needed after 'S' and before keyword)
  - '!': comment line
  - 'A' or 'a': data line
2. Keywords:
  - 'FENODE': finite element node (required)
  - 'AERONODE': aerodynamic node
  - 'AEROSEG': aerodynamic segment
  - 'AIRFOILINTERP': airfoil interpolation
3. Comments:
  - Anything after '!' is a comment and should be omitted.
4. Data lines:
  - Each keyword can contain multiple data lines and columns.
  - For each data line, data columns are separated by space(s).
  - The first column is the tag (drop), and the rest are data columns (keep).
6. 'FENODE':
  - Columns: id, x, y, z
  - Types: int, float, float, float
7. 'AERONODE':
    - Columns: id, x, y, z
    - Types: int, float, float, float
8. 'AEROSEG':
    - Columns: id, pnode, cnode, chord, airfoil, elem_id, twist, shear
    - Types: int, int, int, float, str, int, float, float
9. 'AIRFOILINTERP':
    - Columns: r, airfoil
    - Types: float, str

Input
- file name, type: string

Output
- parsed data, type: dictionary

  {
    'fenode': [
        {'id': ..., 'coord': [x, y, z]},
        ...
    ],
    'aeronode': [
        {'id': ..., 'coord': [x, y, z]},
        ...
    ],
    'aeroseg': [
        {
            'id': ...,
            'pnode': ...,
            'cnode': ...,
            'chord': ...,
            'airfoil': ...,
            'elem_id': ...,
            'twist': ...,
            'shear': ...
        },
        ...
    ],
    'airfoilinterp': [
        {'loc': ..., 'airfoil': ...},
        ...
    ]
  }

"""

