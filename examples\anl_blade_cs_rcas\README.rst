
Analysis of cross-sections of a blade linking to RCAS
===============================================================

Caluclate cross-sectional properties.
Blade planform parameters are provided in the RCAS format.
Property data are written in the RCAS format.


Running
----------------------

Example location: `examples/anl_blade_cs_rcas`

..  code-block:: shell

    ivabs analyze main.yml


Results
-------

prop_calc.dat
    Beam properties with respect to the beam axis in the RCAS format.


To visualize all cross-sections, use the following plot command:

..  code-block:: shell

    ivabs plot --sgdir cs main.yml


..  figure:: /../../examples/anl_blade_cs_rcas/cross-sections.png
    :width: 6in
    :align: center

    Cross-sections of the blade.


Input files
-----------

main.yml
    Main input file.

airfoil_gbox_uni.xml.tmp
    Cross-section design template.

material_database_si.xml
    Material database.

rcas_input.dat
    Blade planform parameters in the RCAS format.

sc1095.dat
    Airfoil data.

sc1095r8.dat
    Airfoil data.

global_force_moment.csv
    Global beam forces and moments.
