# Main input file

version: '0.10'


# Design parameters/variables of the structure
# ====================================================================
structure:
  name: 'blade1'

  parameter:
    lyr_ang_sta1: 0
    lyr_ang_sta2: 90
    lyr_ply: 6

  distribution:
    - name: 'lyr_ang'
      function: 'f_interp'
      # domain: ''
      data:
        - coordinate: 0
          value: lyr_ang_sta1
        - coordinate: 10
          value: lyr_ang_sta2

  # parametric_domain:
  #   - name: 'pd1'

  model:
    main_file: 'beam_design.yml'
    tool: 'gebt'

  cs_assignment:
    - region: 'segment1'
      cs: 'main_cs'
      location: 'element_node'
  cs:
    - name: 'main_cs'
      design: "box"
      model:
        type: "bm2"
        solver: 'vabs'


function:
  - name: 'f_interp'
    type: 'interpolation'
    kind: 'linear'


# CS base design
# ====================================================================
cs:
  - name: "box"
    builder: "prevabs"
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 0
      lyr_ply: 6
    #   lma_thk: 0.005
    #   gms: 0.01
    #   elem_type: 'linear'
    design:
      base_file: "box.xml.tmp"



# Analysis process
# ====================================================================
analysis:
  steps:
    - step: 'cs analysis'
      type: 'cs'
      analysis: 'h'

    - step: 'beam analysis'
      type: 'gebt'
      analysis: 3
      output:
        - value: [
            'eig1', 'eig2', 'eig3', 'eig4', 'eig5',
            'eig6', 'eig7', 'eig8', 'eig9', 'eig10'
          ]
          # Request the first 10 real eigenvalues as the output.

