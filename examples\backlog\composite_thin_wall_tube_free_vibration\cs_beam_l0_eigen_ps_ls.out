Writing new restart file 'cs_beam_l0_eigen_ps_ls.rst'.

>>>>> Executing environment.

>>>>> Running list_parameter_study iterator.

List parameter study for 1 samples


---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                      4.5000000000e+01 ang_1
                     -4.5000000000e+01 ang_2
                     -4.5000000000e+01 ang_3
                      4.5000000000e+01 ang_4

blocking fork: msgd ./cs_beam_l0_eigen_ps_ls.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 1:
Active set vector = { 1 1 1 1 1 1 1 1 1 1 }
                      1.1535090000e+01 eig1
                      1.1535090000e+01 eig2
                      7.2845980000e+01 eig3
                      7.2845980000e+01 eig4
                      2.0719551000e+02 eig5
                      2.0719551000e+02 eig6
                      3.6685150000e+02 eig7
                      4.7781144000e+02 eig8
                      4.1630912000e+02 eig9
                      4.1630914000e+02 eig10


<<<<< Function evaluation summary: 1 total (1 new, 0 duplicate)
<<<<< Best parameters          =
                      4.5000000000e+01 ang_1
                     -4.5000000000e+01 ang_2
                     -4.5000000000e+01 ang_3
                      4.5000000000e+01 ang_4
<<<<< Best objective functions =
                      1.1535090000e+01
                      1.1535090000e+01
                      7.2845980000e+01
                      7.2845980000e+01
                      2.0719551000e+02
                      2.0719551000e+02
                      3.6685150000e+02
                      4.7781144000e+02
                      4.1630912000e+02
                      4.1630914000e+02
<<<<< Best data captured at function evaluation 1

<<<<< Iterator list_parameter_study completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =      7.344 [parent =      7.344, child =          0]
  Total wall clock =      7.344
