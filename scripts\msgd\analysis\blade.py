import xml.etree.ElementTree as et
import msgd.design.blade as mdb
# import msgpi.ms.io as msio
import msgd.ext.gebt.beam as gb
import msgd.ext.gebt.execu as mge
import msgd.ext.gebt.io as mgio
from msgd.analysis.design_analysis import DesignAnalysis
# import msgpi.utils.container as utcnt
# import dakota.interfacing as di


class BladeDesignAnalysis(DesignAnalysis):

    class_name = 'blade'

    def __init__(self, name='', inputs={}, outputs={}, settings={}, prepros=[], postpros=[], analyses=[], logger=None, parent=None, debug=False):
        DesignAnalysis.__init__(self, inputs, outputs, settings, prepros, postpros, analyses, logger, parent=parent, debug=debug)

        self.name = name
        self.sname = name

        self.design_input = {}

        # self.blade: msb.Blade = None
        self.blade = None

        # self.model: gb.GEBTBeam = None
        self.model = None

        if not 'cs' in self.inputs.keys():
            self.inputs['cs'] = {}


    # def substituteParameters(self):
    #     print('  substituteParameters()')

    #     print('Parameter base values')
    #     print('  Dakota parameters')
    #     for h, struct_design in enumerate(self.raw_inputs['design']):
    #         param_list = struct_design['parameters']['list']
    #         param_distr = struct_design['parameters']['distributions']
    #         for dparam_name in self.dakota_parameters.descriptors:
    #             dparam_value = self.dakota_parameters[dparam_name]
    #             print('  {} = {}'.format(dparam_name, dparam_value))
    #             for param_name in param_list.keys():
    #                 if param_name == dparam_name:
    #                     param_list[param_name] = dparam_value
    #             for k, pdistr in enumerate(param_distr):
    #                 distr_input = pdistr['input']
    #                 # split rows
    #                 distr_input = distr_input.split('\n')
    #                 for i in range(len(distr_input)):
    #                     # split cols
    #                     cols = distr_input[i].split(',')
    #                     for j in range(len(cols)):
    #                         entry = cols[j].split(':')
    #                         if len(entry) > 1: # there is a token
    #                             if entry[0].strip() == dparam_name:
    #                                 cols[j] = '{}: {}'.format(dparam_name, dparam_value)
    #                     distr_input[i] = ','.join(cols)
    #                 param_distr[k]['input'] = '\n'.join(distr_input)

    #     print('Parameter updated values')

    #     return


    def generateDesign(self):
        return


    def run(self):
        # self.logger.info(f'running design analysis for {self.name}...')
        self.logger.info('running design analysis for {}...'.format(self.sname))

        # Pull and update data
        # --------------------
        # try:
        #     self.updateData(settings=self.parent.settings)
        #     self.pullInputs(self.parent, ignore_keywords=self.parent.classes+self.parent.objects)
        #     # self.updateDataAll(bld_all)
        #     # self.updateDataAll(bld_data)
        #     for data in self.extra_data:
        #         self.updateDataAll(data)
        # except:
        #     pass

        # self.summary(title='BladeDesignAnalysis Summary')

        # Pre-process data
        # ----------------
        self.preprocess()

        if self.debug:
            self.summary()

        # Substitute parameters
        # ---------------------
        # try:
        #     di.dprepro(
        #         template=self.inputs['template'],
        #         output=self.inputs['input_file'],
        #         include=self.inputs
        #     )
        # except KeyError:
        #     pass

        # Create a blade object
        # ---------------------
        if not self.blade:
            # self.substituteParameters()
            self.blade = mdb.readBladeDesignInputYaml(self.data[self.sname], self.logger)
            # self.blade.summary()

        # self.blade.summary()

        if self.settings['analysis'] == 1:
            self.generateCSDesign()
            # self.blade.summary()
        elif self.settings['analysis'] == 2:
            self.analysis()


        # Post-process data
        # -----------------
        self.postprocess()


        # Push data back to parent
        # ------------------------
        # self.summary()
        # try:
        #     # utcnt.updateDict(source=self.inputs, target=self.parent.inputs['blade'][self.name], ignore=['cs'])
        #     # utcnt.updateDict(source=self.inputs['cs'], target=self.parent.inputs['cs'])
        #     utcnt.updateDict(source=self.data, target=self.parent.data)
        #     # self.parent.sg_names = self.sg_names
        #     if not 'cs' in self.parent.structures_name.keys():
        #         self.parent.structures_name['cs'] = []
        #     for sg_name in self.sg_names:
        #         if not sg_name in self.parent.sg_names:
        #             self.parent.sg_names.append(sg_name)
        #             self.parent.structures_name['cs'].append(sg_name)
        # except:
        #     pass

        if not 'blade' in self.data['structures'].keys():
            self.data['structures']['blade'] = []
        if not self.sname in self.data['structures']['blade']:
            self.data['structures']['blade'].append(self.sname)


        return


    def analysis(self):
        # Construct the beam model and carry out the analysis
        self.createBeamModel()

        self.model.mesh()

        self.model.writeGEBTIn()

        mge.run(self.model.input_file)

        mgio.readGEBTOut(self.model.input_file+'.out', self.model, method=1)

        for brstep in self.model.results_steps:
            brstep.printAll(analysis=0)

        return


    def generateCSDesign(self):
        self.logger.info('generating cross-sectional designs...')

        for r in self.data[self.sname]['stations']:
            cs_name, cs_params = self.blade.genCrossSectionDesignR(r, self.logger)
            self.blade.sg_design_params[cs_name] = cs_params
            self.data[cs_name] = cs_params
            self.data[cs_name]['structure_class'] = 'cs'
            # if not 'parameters' in self.data[cs_name]:
            #     self.data[cs_name]['parameters'] = {}
            # if not 'list' in self.data[cs_name]['parameters']:
            #     self.data[cs_name]['parameters']['list'] = {}
            # self.data[cs_name]['parameters']['list'].update(cs_params)

            if not 'cs' in self.data['structures'].keys():
                self.data['structures']['cs'] = []
            if not cs_name in self.data['structures']['cs']:
                self.data['structures']['cs'].append(cs_name)

        # # Substitute parameters
        # # ---------------------
        # try:
        #     di.dprepro(
        #         template=self.inputs['template'],
        #         output=self.inputs['input_file'],
        #         include=self.inputs
        #     )
        # except KeyError:
        #     pass

        # # Create a blade object
        # # ---------------------
        # if not self.blade:
        #     self.blade = msio.readBladeInput(self.inputs['input_file'])

        # # self.blade.summary()

        # # if self.settings['analysis'] == 1:
        # self.inputs['cs'] = {}
        # # Generate cross-sectional designs
        # for k, bs in self.blade.segments.items():
        #     for i, cs_name in enumerate(bs.css):
        #         if not cs_name in self.sg_names:
        #             cs_name, cs_params = self.blade.genCrossSectionDesign(k, i)
        #             self.inputs['cs'][cs_name] = cs_params
        #             self.sg_names.append(cs_name)

        return


    def createBeamModel(self):
        self.logger.info('creating beam model...')

        # Create a beam model
        self.model = gb.GEBTBeam(logger=self.logger)
        self.model.createBeamFromDesign(self.blade)

        model_config = {}
        with open(self.blade.input_file, 'r') as fo:
            tree = et.parse(fo)
            xe_root = tree.getroot()

            # Read model
            xe_model = xe_root.find('model')

            try:
                model_config['analysis'] = int(xe_model.find('analysis').text.strip())
            except:
                pass

            try:
                model_config['max_iteration'] = int(xe_model.find('max_iteration').text.strip())
            except:
                pass

            try:
                model_config['num_steps'] = int(xe_model.find('num_steps').text.strip())
            except:
                pass

            try:
                model_config['global_mesh_size'] = float(xe_model.find('global_mesh_size').text.strip())
            except:
                pass

            xe_conds = xe_model.find('conditions')
            if xe_conds:
                model_config['pconds'] = {}
                model_config['mconds'] = {}
                for xe_point in xe_conds.findall('point'):
                    object_name = xe_point.find('object').text.strip()
                    model_config['pconds'][object_name] = {}

                    dofs = xe_point.find('dofs').text.strip()
                    model_config['pconds'][object_name]['dofs'] = list(map(int, dofs.split()))

                    try:
                        values = xe_point.find('values').text.strip()
                        model_config['pconds'][object_name]['values'] = list(map(float, values.split()))
                    except:
                        pass

                    try:
                        tfs = xe_point.find('time_funcs').text.strip()
                        model_config['pconds'][object_name]['time_funcs'] = list(map(int, tfs.split()))
                    except:
                        pass

                    try:
                        flws = xe_point.find('followers').text.strip()
                        model_config['pconds'][object_name]['followers'] = list(map(int, flws.split()))
                    except:
                        pass


        self.model.addSectionalProperties()

        try:
            self.model.addPointConditions(model_config['pconds'])
        except KeyError:
            pass

        self.model.configModel(model_config)

        return
