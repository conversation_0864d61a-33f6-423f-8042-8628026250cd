Writing new restart file 'main.rest'.

>>>>> Executing environment.

>>>>> Running soga iterator.

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                      7.2070375683e-01 a2p1
                      5.2146061586e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 1:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                      8.0780358287e-01 a2p1
                      5.5119785150e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 2:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      8.0900601215e-01 a2p1
                      5.8596453749e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 3:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                      8.6086916715e-01 a2p1
                      5.4798730430e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 4:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                      8.6356089969e-01 a2p1
                      5.4884182257e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 5:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                      7.2070375683e-01 a2p1
                      6.2795190283e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 6:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    7
---------------------
Parameters for evaluation 7:
                      8.0780358287e-01 a2p1
                      5.4884182257e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 7:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    8
---------------------
Parameters for evaluation 8:
                      8.0780358287e-01 a2p1
                      6.8135929441e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 8:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation    9
---------------------
Parameters for evaluation 9:
                      8.0780358287e-01 a2p1
                      6.2618793298e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 9:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy



---------------------
Begin Evaluation   10
---------------------
Parameters for evaluation 10:
                      8.0900601215e-01 a2p1
                      5.4798730430e-01 a2p3

blocking fork: ivabs main.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 10:
Active set vector = { 1 1 1 1 1 }
                      1.4188889655e-03 diff_gj
                      1.9486688776e-04 diff_eiyy
                      9.0571852273e-04 diff_eizz
                      4.7884528213e-04 diff_scy
                      1.5342309795e-04 diff_mcy


<<<<< Function evaluation summary: 10 total (10 new, 0 duplicate)
<<<<< Best parameters          =
                      7.2070375683e-01 a2p1
                      5.2146061586e-01 a2p3
<<<<< Best objective functions =
                      1.4188889655e-03
                      1.9486688776e-04
                      9.0571852273e-04
                      4.7884528213e-04
                      1.5342309795e-04
<<<<< Best data captured at function evaluation 1


<<<<< Iterator soga completed.
<<<<< Environment execution completed.
DAKOTA execution time in seconds:
  Total CPU        =     51.328 [parent =     51.328, child =          0]
  Total wall clock =     51.328
