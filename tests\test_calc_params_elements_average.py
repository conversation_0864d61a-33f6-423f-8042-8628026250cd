"""Test module for calc_params_elements_average function.

This module tests the calc_params_elements_average function which calculates
the average parameter values for each element based on its nodes.

Available fixtures from conftest.py:
- sample_mesh_nodes, sample_mesh_cells, sample_cell_data_etags: Basic mesh components
- sample_mesh_nodes_2d_3x3: 3x3 grid nodes for more complex scenarios
- sample_mesh_cells_with_etags_2d_quad_2x2: 2x2 quad mesh with element tags
"""

import pytest
import numpy as np
from meshio import CellBlock
from msgd.design.discretize import calc_params_elements_average


class TestCalcParamsElementsAverage:
    """Test suite for the calc_params_elements_average function."""

    def test_docstring_example(self):
        """Test the exact example from the function docstring."""
        # Set up the test data from the docstring
        cells = [
            CellBlock('triangle', [[1, 5, 4], [1, 2, 5]]),
            CellBlock('quad', [[0, 1, 4, 3]])
        ]
        cell_data_etags = [
            [2, 3],
            [1]
        ]
        point_data_param = np.array([1.0, 3.0, 6.0, 1.0, 3.0, 6.0])
        etags_in_region = [1, 2]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Verify the structure
        assert len(result) == 2, f"Expected 2 cell blocks, got {len(result)}"
        assert len(result[0]) == 2, f"Expected 2 elements in first block, got {len(result[0])}"
        assert len(result[1]) == 1, f"Expected 1 element in second block, got {len(result[1])}"

        # Manual calculation:
        # Element 2 (first triangle): nodes [1, 5, 4] -> values [3.0, 6.0, 3.0] -> avg = 4.0
        # Element 3 (second triangle): not in region -> NaN
        # Element 1 (quad): nodes [0, 1, 4, 3] -> values [1.0, 3.0, 3.0, 1.0] -> avg = 2.0

        # Check first block (triangles)
        assert np.isclose(result[0][0], 4.0), f"Expected 4.0, got {result[0][0]}"
        assert np.isnan(result[0][1]), f"Expected NaN, got {result[0][1]}"

        # Check second block (quad)
        assert np.isclose(result[1][0], 2.0), f"Expected 2.0, got {result[1][0]}"

    def test_empty_region(self):
        """Test with empty region - all elements should have NaN values."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1.0, 2.0, 3.0])
        etags_in_region = []  # Empty region

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        assert len(result) == 1
        assert np.isnan(result[0][0]), "Expected NaN for element not in region"

    def test_all_elements_in_region(self):
        """Test with all elements in region."""
        cells = [CellBlock('triangle', [[0, 1, 2], [1, 2, 3]])]
        cell_data_etags = [[10, 20]]
        point_data_param = np.array([1.0, 2.0, 3.0, 4.0])
        etags_in_region = [10, 20]  # All elements in region

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Element 10: nodes [0, 1, 2] -> values [1.0, 2.0, 3.0] -> avg = 2.0
        # Element 20: nodes [1, 2, 3] -> values [2.0, 3.0, 4.0] -> avg = 3.0
        expected = [2.0, 3.0]

        assert len(result) == 1
        assert len(result[0]) == 2
        assert np.isclose(result[0][0], expected[0])
        assert np.isclose(result[0][1], expected[1])

    def test_mixed_cell_types(self):
        """Test with mixed cell types (triangles and quads)."""
        cells = [
            CellBlock('triangle', [[0, 1, 2], [1, 3, 2]]),
            CellBlock('quad', [[0, 1, 4, 3], [1, 2, 5, 4]])
        ]
        cell_data_etags = [
            [101, 102],  # Triangle elements
            [201, 202]   # Quad elements
        ]
        point_data_param = np.array([1.0, 2.0, 3.0, 4.0, 5.0, 6.0])
        etags_in_region = [101, 201]  # One triangle, one quad

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Triangle 101: nodes [0, 1, 2] -> values [1.0, 2.0, 3.0] -> avg = 2.0
        # Triangle 102: not in region -> NaN
        # Quad 201: nodes [0, 1, 4, 3] -> values [1.0, 2.0, 5.0, 4.0] -> avg = 3.0
        # Quad 202: not in region -> NaN

        assert len(result) == 2
        assert np.isclose(result[0][0], 2.0)  # Triangle 101
        assert np.isnan(result[0][1])         # Triangle 102
        assert np.isclose(result[1][0], 3.0)  # Quad 201
        assert np.isnan(result[1][1])         # Quad 202

    def test_single_element_single_cell_block(self):
        """Test with single element in single cell block."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[42]]
        point_data_param = np.array([10.0, 20.0, 30.0])
        etags_in_region = [42]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = (10.0 + 20.0 + 30.0) / 3.0  # 20.0

        assert len(result) == 1
        assert len(result[0]) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_with_nan_values_in_point_data(self):
        """Test handling of NaN values in point data."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1.0, np.nan, 3.0])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Should use nanmean: (1.0 + 3.0) / 2 = 2.0
        expected_avg = 2.0

        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_all_nan_values_in_element(self):
        """Test element with all NaN values in point data."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([np.nan, np.nan, np.nan])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        assert len(result) == 1
        assert np.isnan(result[0][0]), "Expected NaN when all node values are NaN"

    def test_partial_region_coverage(self):
        """Test with only some elements in the region."""
        cells = [CellBlock('quad', [[0, 1, 2, 3], [1, 4, 5, 2], [4, 6, 7, 5]])]
        cell_data_etags = [[10, 20, 30]]
        point_data_param = np.array([1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0])
        etags_in_region = [10, 30]  # Skip element 20

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Element 10: nodes [0, 1, 2, 3] -> values [1.0, 2.0, 3.0, 4.0] -> avg = 2.5
        # Element 20: not in region -> NaN
        # Element 30: nodes [4, 6, 7, 5] -> values [5.0, 7.0, 8.0, 6.0] -> avg = 6.5

        assert len(result) == 1
        assert len(result[0]) == 3
        assert np.isclose(result[0][0], 2.5)
        assert np.isnan(result[0][1])
        assert np.isclose(result[0][2], 6.5)

    def test_empty_cells_list(self):
        """Test with empty cells list."""
        cells = []
        cell_data_etags = []
        point_data_param = np.array([1.0, 2.0, 3.0])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        assert len(result) == 0

    def test_numpy_array_inputs(self):
        """Test that function works with numpy array inputs."""
        cells = [CellBlock('triangle', np.array([[0, 1, 2]]))]
        cell_data_etags = [np.array([1])]
        point_data_param = np.array([1.0, 2.0, 3.0])
        etags_in_region = np.array([1])

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = 2.0
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_large_element_ids(self):
        """Test with large element IDs."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[999999]]
        point_data_param = np.array([10.0, 20.0, 30.0])
        etags_in_region = [999999]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = 20.0
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_return_type_consistency(self):
        """Test that return type is consistent with input structure."""
        cells = [
            CellBlock('triangle', [[0, 1, 2]]),
            CellBlock('quad', [[0, 1, 3, 2]])
        ]
        cell_data_etags = [[1], [2]]
        point_data_param = np.array([1.0, 2.0, 3.0, 4.0])
        etags_in_region = [1, 2]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Check structure matches input
        assert len(result) == len(cells)
        assert len(result[0]) == len(cell_data_etags[0])
        assert len(result[1]) == len(cell_data_etags[1])

        # Check all results are numpy arrays
        for cell_result in result:
            assert isinstance(cell_result, np.ndarray)


class TestCalcParamsElementsAverageEdgeCases:
    """Test edge cases and error conditions for calc_params_elements_average."""

    def test_duplicate_element_ids_in_region(self):
        """Test with duplicate element IDs in region list."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1.0, 2.0, 3.0])
        etags_in_region = [1, 1, 1]  # Duplicates

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Should still work correctly despite duplicates
        expected_avg = 2.0
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_zero_values_in_point_data(self):
        """Test with zero values in point data."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([0.0, 0.0, 0.0])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        assert len(result) == 1
        assert np.isclose(result[0][0], 0.0)

    def test_negative_values_in_point_data(self):
        """Test with negative values in point data."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([-1.0, -2.0, -3.0])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = -2.0
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_very_large_values(self):
        """Test with very large values in point data."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1e10, 2e10, 3e10])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = 2e10
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_very_small_values(self):
        """Test with very small values in point data."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1e-10, 2e-10, 3e-10])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = 2e-10
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)

    def test_mixed_positive_negative_values(self):
        """Test with mixed positive and negative values."""
        cells = [CellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([-5.0, 0.0, 5.0])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        expected_avg = 0.0
        assert len(result) == 1
        assert np.isclose(result[0][0], expected_avg)


class TestCalcParamsElementsAveragePerformance:
    """Test performance-related aspects of calc_params_elements_average."""

    def test_large_mesh_performance(self):
        """Test with a larger mesh to verify vectorization benefits."""
        # Create a larger mesh with many elements
        n_elements = 1000
        nodes_per_element = 4

        # Generate random connectivity
        np.random.seed(42)  # For reproducible tests
        connectivity = np.random.randint(0, n_elements * 2, size=(n_elements, nodes_per_element))

        cells = [CellBlock('quad', connectivity)]
        cell_data_etags = [np.arange(1, n_elements + 1)]

        # Generate random point data
        point_data_param = np.random.rand(n_elements * 2)

        # Select half the elements for the region
        etags_in_region = np.arange(1, n_elements // 2 + 1)

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Verify structure
        assert len(result) == 1
        assert len(result[0]) == n_elements

        # Verify that elements in region have values, others have NaN
        region_mask = np.isin(cell_data_etags[0], etags_in_region)
        assert not np.any(np.isnan(result[0][region_mask])), "Elements in region should not be NaN"
        assert np.all(np.isnan(result[0][~region_mask])), "Elements not in region should be NaN"

    def test_multiple_cell_blocks_performance(self):
        """Test with multiple cell blocks to verify vectorization across blocks."""
        n_blocks = 10
        elements_per_block = 100

        cells = []
        cell_data_etags = []

        for i in range(n_blocks):
            # Create connectivity for this block
            connectivity = np.random.randint(0, 500, size=(elements_per_block, 3))
            cells.append(CellBlock('triangle', connectivity))

            # Create element IDs for this block
            start_id = i * elements_per_block + 1
            end_id = (i + 1) * elements_per_block + 1
            cell_data_etags.append(np.arange(start_id, end_id))

        # Generate point data
        point_data_param = np.random.rand(500)

        # Select some elements from each block
        etags_in_region = []
        for i in range(n_blocks):
            start_id = i * elements_per_block + 1
            # Select first 50 elements from each block
            etags_in_region.extend(range(start_id, start_id + 50))

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)

        # Verify structure
        assert len(result) == n_blocks
        for i in range(n_blocks):
            assert len(result[i]) == elements_per_block
