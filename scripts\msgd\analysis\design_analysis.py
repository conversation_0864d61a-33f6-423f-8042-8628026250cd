import logging
import os
# import sys
# import abc

import pprint
# import platform

# import msgd.builder.main as mbp
# import msgpi.sg.cross_section as mcs
import msgd.utils.container as utcnt
# import msgd.utils.logger as mlog
# import dakota.interfacing as di
# import sgio
# from msgd.core import StructureModel

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)


# class AnalysisStep():
#     step_type = ''
#     def __init__(self, name=''):
#         self._name = name

#     def toDictionary(self):
#         return {
#             'step': self._name,
#             'type': self.step_type,
#         }

#     def run(self):
#         ...




# class SGAnalysisStep(AnalysisStep):

#     step_type = GLOBAL.SG_KEY

#     def __init__(
#         self, name='', analysis:str='',
#         output_quantity:list=[]
#         ):
#         super().__init__(name=name)

#         self._analysis:str = analysis
#         self._output_quantity:list = output_quantity

#     def __repr__(self) -> str:
#         _str = [
#             f'SG Analysis Step: {self._name}',
#             f'  - type: {self.step_type}',
#             f'  - analysis: {self._analysis}',
#             f'  - output quantity:',
#         ]

#         for _q in self._output_quantity:
#             _str.append(f'    - value: {_q.get("value")}')
#             if 'name' in _q.keys():
#                 _str.append(f'      name: {_q.get("name")}')

#         return '\n'.join(_str)

#     def toDictionary(self):
#         _dict = super().toDictionary()
#         _dict['analysis'] = self._analysis
#         _dict['output'] = self._output_quantity

#         return _dict

#     @property
#     def analysis(self): return self._analysis
#     @property
#     def output_quantity(self): return self._output_quantity

#     def build(self, sg_model:StructureModel, sets_sg_data=None):
#         """
#         """
#         mbp.buildSGModel(
#             sg_model,
#             analysis=self._analysis,
#             sg_database=sets_sg_data
#         )

#         return


#     def run(self, sg_model:StructureModel):
#         """
#         """
#         logger.info(f'[{GLOBAL.SG_KEY}: {self._sg_data.name}] running {GLOBAL.SG_KEY} analysis...')

#         # Run
#         sgio.run(
#             solver=sg_model.solver,
#             input_name=sg_model.fn_sg_data,
#             analysis=self._analysis,
#             smdim=sg_model.smdim
#             )

#         # Get output
#         _model_func = sgio.readOutput(
#             fn=sg_model.fn_sg_data,
#             file_format=sg_model.solver,
#             analysis=self._analysis,
#             model=sg_model.smdim
#         )


#         if self._analysis == 'h':
#             sg_model.model_function = _model_func


#         return




# class DesignAnalysis(metaclass=abc.ABCMeta):
class DesignAnalysis():

    class_name = ''

    def __init__(self, inputs={}, outputs={}, settings={}, prepros=[], postpros=[], analyses=[], logger=None, parent=None, debug=False):
        # self.object = object
        self.name = ''  #: Name of the DesignAnalysis object
        self.sname = ''  #: Name of the designed structure
        self.design = None
        self.model = None

        self.settings = settings

        self.data = {}
        """
        {
          'name': 'value',
          'structure1': {'name': 'value', ...},
          'structure2': {'name': 'value', ...},
          ...
        }
        """

        self.inputs = inputs
        self.outputs = outputs

        self.prepros = prepros

        """
        [
            {
                'function': function1,
                'args': [arg1, arg2, ...]
                'kwargs': {
                    'kw1': arg1,
                    'kw2': arg2,
                    ...
                }
            },
            ...
        ]
        """
        self.postpros = postpros

        # if logger:
        #     self.logger = logger
        # else:
        #     self.logger = mlog.initLogger('DesignAnalysis')

        self.analyses = analyses

        # self.extra_data = []

        # self.parent: DesignAnalysis = parent
        self.parent = parent
        # self.sg_names = []
        # self.sgs = {}
        self.children = {}

        self.debug = debug


    def summary(self, title='SUMARY'):
        pp = pprint.PrettyPrinter(indent=1, width=-1)
        print()
        print(title)
        print('='*20)
        print(self.class_name, ':', self.name)

        print('\nsettings')
        print('-'*10)
        pp.pprint(self.settings)

        # print('\nstructures name')
        # print('-'*10)
        # pp.pprint(self.structures_name)

        # print('\nstructures design')
        # print('-'*10)
        # pp.pprint(self.structures_design)

        # print('\nstructures model')
        # print('-'*10)
        # pp.pprint(self.structures_model)
        # for struct_name in self.structures_name:
        #     pp.pprint(self.structures_design[struct_name])

        print('\ndata')
        print('-'*10)
        pp.pprint(self.data)

        print('\ninputs')
        print('-'*10)
        pp.pprint(self.inputs)

        print('\noutputs')
        print('-'*10)
        pp.pprint(self.outputs)

        print('\nanalyses')
        print('-'*10)
        pp.pprint(self.analyses)

        print('\nprepros')
        print('-'*10)
        pp.pprint(self.prepros)

        print('\npostpros')
        print('-'*10)
        pp.pprint(self.postpros)

        # print('\nsg names')
        # print('-'*10)
        # pp.pprint(self.sg_names)

        print('\nchildren')
        print('-'*10)
        pp.pprint(self.children)

        print('\nparent:')
        print('-'*10)
        print(self.parent)
        # print('\nanalyses')
        # for da in self.analyses:
        #     if isinstance(da, DesignAnalysis):
        #         da.summary()
        print()


    # def addExtraData(self, data):
    #     self.extra_data.append(data)


    def updateData(self, inputs={}, outputs={}, settings={}, prepros=[], postpros=[], analyses=[]):

        self.inputs.update(inputs)
        self.outputs.update(outputs)
        self.settings.update(settings)
        # self.prepros += prepros
        for new_pp in prepros:
            found_old = False
            for old_pp in self.prepros:
                if old_pp['function'] == new_pp['function']:
                    found_old = True
                    break
            if found_old:
                old_pp.update(new_pp)
            else:
                self.prepros.append(new_pp)

        # self.postpros += postpros
        for new_pp in postpros:
            found_old = False
            for old_pp in self.postpros:
                if old_pp['function'] == new_pp['function']:
                    found_old = True
                    break
            if found_old:
                old_pp.update(new_pp)
            else:
                self.postpros.append(new_pp)

        self.analyses += analyses

        return


    def updateDataAll(self, data):
        """
        data = {
            'inputs': {},
            'outputs': {},
            'settings': {},
            'prepros': [],
            'postpros': []
        }
        """
        try:
            # self.inputs.update(data['inputs'])
            inputs = data['inputs']
        except KeyError:
            inputs = {}

        try:
            # self.outputs.update(data['outputs'])
            outputs = data['outputs']
        except KeyError:
            outputs = {}

        try:
            # self.settings.update(data['settings'])
            settings = data['settings']
        except KeyError:
            settings = {}

        try:
            # self.prepros += data['prepros']
            prepros = data['prepros']
        except KeyError:
            prepros = []

        try:
            # self.postpros += data['postpros']
            postpros = data['postpros']
        except KeyError:
            postpros = []

        try:
            # self.analyses += data['analyses']
            analyses = data['analyses']
        except KeyError:
            analyses = []
        
        self.updateData(inputs, outputs, settings, prepros, postpros, analyses)


    def updateDataList(self, datalist=[]):
        for data in datalist:
            self.updateDataAll(data)


    def pullInputs(self, other_da, ignore_keywords=[]):
        assert isinstance(other_da, DesignAnalysis)

        for k, v in other_da.inputs.items():
            if (k in ignore_keywords):
                continue
            self.inputs[k] = v

        try:
            for k, v in other_da.inputs[self.class_name].items():
                if (k in ignore_keywords):
                    continue
                self.inputs[k] = v

            # if self.name in other_da.inputs[self.class_name].items():
            for k, v in other_da.inputs[self.class_name][self.name].items():
                if (k in ignore_keywords):
                    continue
                self.inputs[k] = v
        except KeyError:
            pass


    # def pushData(self, source, target, depth=-1, ignore=[]):
    #     assert (type(source).__name__ == type(target).__name__)

    def pushInputs(self, other_da, structured=True):
        assert isinstance(other_da, DesignAnalysis)

        if structured:
            for k, v in self.inputs.items():
                other_da.inputs[self.class_name][self.name][k] = v
        else:
            for k, v in self.inputs.items():
                other_da.inputs[k] = v


    def pushOutputs(self, other_da, structured=True):
        assert isinstance(other_da, DesignAnalysis)

        if structured:
            for k, v in self.outputs.items():
                other_da.outputs[self.class_name][self.name][k] = v
        else:
            for k, v in self.outputs.items():
                other_da.outputs[k] = v


    def pullInputsFromParent(self, ignore_keywords=[]):
        ignore_keywords += self.parent.classes
        ignore_keywords += self.parent.objects

        utcnt.updateDict(self.parent.inputs, self.inputs, ignore_keywords)
        utcnt.updateDict(self.parent.inputs[self.class_name], self.inputs, ignore_keywords)
        utcnt.updateDict(self.parent.inputs[self.class_name][self.name], self.inputs, ignore_keywords)


    # def pushOutputsToParent(self, ignore_keywords=[]):


    # def initLogger(self):
    #     try:
    #         log_level_cmd = self.settings['log_level_cmd'].upper()
    #     except KeyError:
    #         log_level_cmd = 'INFO'
    #         pass

    #     try:
    #         log_level_file = self.settings['log_level_file'].upper()
    #     except KeyError:
    #         log_level_file = 'INFO'
    #         pass

    #     try:
    #         log_file_name = self.settings['log_file_name']
    #     except KeyError:
    #         log_file_name = 'log.txt'
    #         pass

    #     self.logger = mlog.initLogger(
    #         __name__,
    #         cout_level=log_level_cmd, fout_level=log_level_file, filename=log_file_name
    #     )


    # @abc.abstractmethod
    # def analyze(self):
    #     pass


    def preprocess(self):
        logger.info('pre-processing...')

        # for path in sys.path:
        #     if path == '':
        #         print('EMPTY')
        #     else:
        #         print(path)

        for p in self.prepros:
            # print(p)
            func = p['function']

            try:
                args = p['args']
            except KeyError:
                args = []

            try:
                kwargs = p['kwargs']
            except KeyError:
                kwargs = {}

            module_name = self.settings['data_process_functions_file']

            cwd = os.getcwd()
            # self.logger.info('cwd: {}'.format(cwd))
            # import_str = os.path.join(cwd, module_name)

            try:
                import_str = 'import {} as user_mod'.format(module_name)
                logger.debug(import_str)
                exec(import_str)
                exec('print(os.path.abspath({}.__file__))'.format('user_mod'))
                func_str = 'user_mod.{}'.format(func)
                logger.info('evaluating user function: {}'.format(func_str))
                func_obj = eval(func_str)
            except ImportError:
                try:
                    import_str = 'from {} import {}'.format(module_name, func)
                    logger.debug(import_str)
                    exec(import_str)
                    logger.info('evaluating user function: {}'.format(func))
                    func_obj = eval(func_str)
                except ImportError:
                    print('something wrong when importing module:', module_name)

            func_obj(self.data, self.sname, logger, *args, **kwargs)

            # exec(f'from {module_name} import {func}')
            # exec('from {} import {}'.format(module_name, func))
            # eval(f'{func}')(self.inputs, self.outputs, self.logger, *args, **kwargs)
            # eval(func)(self.inputs, self.outputs, self.logger, *args, **kwargs)
            # eval(func)(self.data, self.sname, self.logger, *args, **kwargs)


    def postprocess(self):
        logger.info('post-processing...')
        for p in self.postpros:
            func = p['function']

            try:
                args = p['args']
            except KeyError:
                args = []

            try:
                kwargs = p['kwargs']
            except KeyError:
                kwargs = {}

            module_name = self.settings['data_process_functions_file']

            cwd = os.getcwd()
            # self.logger.info('cwd: {}'.format(cwd))
            # import_str = os.path.join(cwd, module_name)

            try:
                import_str = 'import {} as user_mod'.format(module_name)
                logger.debug(import_str)
                exec(import_str)
                func_str = 'user_mod.{}'.format(func)
                logger.info('evaluating user function: {}'.format(func_str))
                func_obj = eval(func_str)
            except ImportError:
                try:
                    import_str = 'from {} import {}'.format(module_name, func)
                    logger.debug(import_str)
                    exec(import_str)
                    logger.info('evaluating user function: {}'.format(func))
                    func_obj = eval(func_str)
                except ImportError:
                    print('something wrong when importing module:', module_name)

            func_obj(self.data, self.sname, logger, *args, **kwargs)

            # exec(f'from {module_name} import {func}')
            # exec('from {} import {}'.format(module_name, func))
            # eval(f'{func}')(self.inputs, self.outputs, self.logger, *args, **kwargs)
            # eval(func)(self.inputs, self.outputs, self.logger, *args, **kwargs)
            # eval(func)(self.data, self.sname, self.logger, *args, **kwargs)


    def run(self):
        self.preprocess()
        self.analyze()
        self.postprocess()

