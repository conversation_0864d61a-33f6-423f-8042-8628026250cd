from __future__ import annotations
from typing import List, Any, Optional

import yaml

from pydantic import BaseModel, Field

class EntitySet(BaseModel):
    """Set of entities."""
    name: str = Field(default='', description="Name of the set")
    type: str = Field(default='', description="Type of the entities")
    items: List[Any] = Field(default_factory=list, description="Items in the set")



class DataBase(BaseModel):
    """Collection of a type of data/object."""
    
    name: str = Field(default="", description="Name of the database")
    data: List[Any] = Field(default_factory=list, description="List of data items")

    def __repr__(self):
        s = ['-'*10,]
        for _d in self.data:
            s.append(str(_d))
            s.append('-'*10)
        return '\n'.join(s)

    def get_item_by_name(self, name: str) -> Optional[Any]:
        """Get an item by name."""
        for _item in self.data:
            if _item.name == name:
                return _item
        return None

    def add_item(self, item: Any) -> None:
        """Add an item to the database."""
        self.data.append(item)
    
    def update(self, db: List[Any] | DataBase) -> None:
        """Update the database with a list of items or another database."""
        if isinstance(db, list):
            self.data.extend(db)
        elif isinstance(db, DataBase):
            self.data.extend(db.data)
    
    def to_dict(self, **kwargs) -> dict:
        """Convert the database to a dictionary."""
        _items = []
        for _item in self.data:
            _items.append(_item.to_dict(**kwargs))
        _dict = {self.name: _items}
        return _dict
    
    def dump_to_file(self, file, sg_key: str = 'sg') -> None:
        """Dump the database to a file."""
        _list = []
        for _d in self.data:
            _list.append(_d.to_dict(sg_key=sg_key))
        _data = {self.name: _list}
        yaml.dump(
            _data, file,
            default_flow_style=False, sort_keys=False)
