# main.dakota

environment
  output_file = 'main.out'
  write_restart = 'main.rst'
  error_file = 'main.err'
  tabular_data
    tabular_data_file = 'main_tabular.dat'
  results_output
    results_output_file = 'main_results'


method
  multidim_parameter_study
    partitions =  1  3


model
  single

variables
  active = design

  discrete_design_set
  integer = 1
    descriptors = 'mesh_type'
    elements_per_variable =  2
    elements =  1  2


  real = 1
    descriptors = 'mesh_size'
    elements_per_variable =  4
    elements =  0.005  0.01  0.02  0.04




interface
  analysis_driver = 'ivabs main.yml --mode 1 --paramfile {PARAMETERS} --resultfile {RESULTS} --loglevelcmd info --loglevelfile info --logfile eval.log'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        named =  'evals/eval'
        directory_tag
        directory_save
        copy_file =  'main.yml'  'design/*'
      verbatim


responses
  descriptors =  'ea'  'gj'  'eiyy'  'eizz'
  response_functions = 4
  no_gradients
  no_hessians


