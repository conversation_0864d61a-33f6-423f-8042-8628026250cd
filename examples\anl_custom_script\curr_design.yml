version: '0.10'
structure:
  name: blade
  parameter: {}
  model:
    type: ''
    tool: ''
    tool_version: ''
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: blade
    parameter:
      ilam_spar: 4
    dim: 1
    builder: default
    design: null
  cs_assignment:
  - region: all
    location: element
    cs: main_cs
  physics: elastic
functions: []
cs:
- name: airfoil
  parameter:
    mdb_name: material_database_us_ft
    airfoil: sc1095.dat
    airfoil_point_order: -1
    chord: 1.8
    rnsm: 0.001
    ilam_spar: 1
    mat_nsm: lead
    mat_fill_front: Rohacell 70
    mat_fill_back: Plascore PN2-3/16OX3.0
    mat_fill_te: AS4 12k/E7K8
    gms: 0.002
    fms: 0.04
  dim: 2
  builder: prevabs
  design:
    base_file: airfoil_gbox_uni.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    output:
      value:
      - gj
      - eiyy
      - eizz
    type: sg
    analysis: h
    work_dir: cs
  - step: postprocess
    activate: true
