#!/usr/bin/env python3
"""
Simple validation script for calc_params_elements_average function
This script tests the basic functionality without requiring pytest or external dependencies.
"""

import sys
import os
import numpy as np

# Add the scripts directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'scripts'))

# Mock CellBlock class for testing
class MockCellBlock:
    def __init__(self, cell_type, data):
        self.type = cell_type
        self.data = np.array(data)

def test_basic_functionality():
    """Test basic functionality with the docstring example."""
    print("Testing basic functionality...")
    
    try:
        from msgd.design.discretize import calc_params_elements_average
        
        # Set up test data from docstring
        cells = [
            MockCellBlock('triangle', [[1, 5, 4], [1, 2, 5]]),
            MockCellBlock('quad', [[0, 1, 4, 3]])
        ]
        cell_data_etags = [
            [2, 3],
            [1]
        ]
        point_data_param = np.array([1.0, 3.0, 6.0, 1.0, 3.0, 6.0])
        etags_in_region = [1, 2]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)
        
        print(f"Result: {result}")
        
        # Verify structure
        assert len(result) == 2, f"Expected 2 cell blocks, got {len(result)}"
        assert len(result[0]) == 2, f"Expected 2 elements in first block, got {len(result[0])}"
        assert len(result[1]) == 1, f"Expected 1 element in second block, got {len(result[1])}"
        
        # Check values
        assert np.isclose(result[0][0], 4.0), f"Expected 4.0, got {result[0][0]}"
        assert np.isnan(result[0][1]), f"Expected NaN, got {result[0][1]}"
        assert np.isclose(result[1][0], 2.0), f"Expected 2.0, got {result[1][0]}"
        
        print("✓ Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_region():
    """Test with empty region."""
    print("Testing empty region...")
    
    try:
        from msgd.design.discretize import calc_params_elements_average
        
        cells = [MockCellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1.0, 2.0, 3.0])
        etags_in_region = []  # Empty region

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)
        
        assert len(result) == 1
        assert np.isnan(result[0][0]), "Expected NaN for element not in region"
        
        print("✓ Empty region test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Empty region test failed: {e}")
        return False

def test_vectorization_efficiency():
    """Test that vectorization works with larger data."""
    print("Testing vectorization efficiency...")
    
    try:
        from msgd.design.discretize import calc_params_elements_average
        
        # Create larger test data
        n_elements = 100
        connectivity = []
        for i in range(n_elements):
            connectivity.append([i, i+1, i+2, i+3])
        
        cells = [MockCellBlock('quad', connectivity)]
        cell_data_etags = [list(range(1, n_elements + 1))]
        point_data_param = np.random.rand(n_elements + 4)
        etags_in_region = list(range(1, 51))  # First 50 elements
        
        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)
        
        assert len(result) == 1
        assert len(result[0]) == n_elements
        
        # Check that first 50 elements have values, rest are NaN
        for i in range(50):
            assert not np.isnan(result[0][i]), f"Element {i+1} should have a value"
        for i in range(50, n_elements):
            assert np.isnan(result[0][i]), f"Element {i+1} should be NaN"
        
        print("✓ Vectorization efficiency test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Vectorization efficiency test failed: {e}")
        return False

def test_nan_handling():
    """Test handling of NaN values in input data."""
    print("Testing NaN handling...")
    
    try:
        from msgd.design.discretize import calc_params_elements_average
        
        cells = [MockCellBlock('triangle', [[0, 1, 2]])]
        cell_data_etags = [[1]]
        point_data_param = np.array([1.0, np.nan, 3.0])
        etags_in_region = [1]

        result = calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)
        
        # Should use nanmean: (1.0 + 3.0) / 2 = 2.0
        expected_avg = 2.0
        assert np.isclose(result[0][0], expected_avg), f"Expected {expected_avg}, got {result[0][0]}"
        
        print("✓ NaN handling test passed!")
        return True
        
    except Exception as e:
        print(f"✗ NaN handling test failed: {e}")
        return False

if __name__ == "__main__":
    print("Validating calc_params_elements_average function...")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_empty_region,
        test_vectorization_efficiency,
        test_nan_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validation tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed")
        sys.exit(1)
