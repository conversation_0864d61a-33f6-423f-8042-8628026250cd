- set: set1
  responses: !!python/object:sgio.model.general.StructureResponseCases
    loc_tags: &id001
    - coord
    cond_tags: &id002 []
    responses:
    - coord: 0.0
      response: !!python/object:sgio.model.general.SectionResponse
        displacement:
        - 0.0
        - 0.0
        - 0.0
        directional_cosine:
        - - 1.0
          - 0.0
          - 0.0
        - - 0.0
          - 1.0
          - 0.0
        - - 0.0
          - 0.0
          - 1.0
        load_type: 0
        load_tags:
        - f1
        - f2
        - f3
        - m1
        - m2
        - m3
        load:
        - 0.0
        - 0.0
        - 0.0
        - 0.0
        - 1.0
        - 0.0
        distr_load:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        distr_load_d1:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        distr_load_d2:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        distr_load_d3:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
- set: set2
  responses: !!python/object:sgio.model.general.StructureResponseCases
    loc_tags: *id001
    cond_tags: *id002
    responses:
    - coord: 26.83
      response: !!python/object:sgio.model.general.SectionResponse
        displacement:
        - -1.7138015e-06
        - 1.3265331e-05
        - -0.0030215064
        directional_cosine:
        - - 1.0
          - 0.0
          - 0.0
        - - 0.0
          - 1.0
          - 0.0
        - - 0.0
          - 0.0
          - 1.0
        load_type: 0
        load_tags:
        - f1
        - f2
        - f3
        - m1
        - m2
        - m3
        load:
        - 0.0
        - 0.0
        - 0.0
        - 0.0
        - 1.0
        - 0.0
        distr_load:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        distr_load_d1:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        distr_load_d2:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
        distr_load_d3:
        - 0
        - 0
        - 0
        - 0
        - 0
        - 0
