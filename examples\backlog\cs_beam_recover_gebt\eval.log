DEBUG    [2023-07-12 11:02:50] msgd.main :: {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} 
CRITICAL [2023-07-12 11:02:50] io.readMSGDInput :: reading main input main.yml... 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: local variables:
{'fn': 'main.yml',
 'fn_run': 'run.py',
 'mode': '1',
 'msgd': <msgd.core._msgd.MSGD object at 0x000002AD75032A30>,
 'py_cmd': 'python',
 'variant': 'ivabs'} 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: currect working directory:  
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: input file name: main 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: input file extension: .yml 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: output file name: main.out 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: variant: ivabs 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: version: 0.9 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {},
 'design': {'cs_assignment': [{'cs': 'cs1',
                               'model': 'b2',
                               'region': 'segment1'}],
            'dim': 1,
            'file': 'beam_design.yml',
            'tool': 'gebt'},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'xscale': 26.83,
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'xscale': 26.83,
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'xscale': 26.83,
                   'ytype': 'float'}],
 'name': 'blade1'} 
DEBUG    [2023-07-12 11:02:50] io.readMSGDInput :: msgd.analysis =
{'steps': [{'analysis': 'h',
            'setting': {'timeout': 60},
            'step': 'cs analysis',
            'type': 'cs'},
           {'analysis': 1,
            'setting': {'output_file': 'struct_resp.csv'},
            'step': 'beam analysis',
            'type': 'gebt'},
           {'analysis': 'd,fi',
            'input': {'load_case': {'data_form': 'file',
                                    'file_name': 'struct_resp.csv',
                                    'location_tags': 'coord',
                                    'location_value_types': 'float'}},
            'step': 'recovery',
            'type': 'cs'}]} 
INFO     [2023-07-12 11:02:50] _msgd.readMDAOEvalIn :: reading mdao input... 
DEBUG    [2023-07-12 11:02:50] _msgd.readMDAOEvalIn :: mdao_tool: dakota 
DEBUG    [2023-07-12 11:02:50] _msgd.readMDAOEvalIn :: fn_dakota_params:  
DEBUG    [2023-07-12 11:02:50] _msgd.readMDAOEvalIn :: fn_dakota_results:  
INFO     [2023-07-12 11:02:50] _msgd.updateData :: updating current design... 
DEBUG    [2023-07-12 11:02:50] _msgd.updateData :: cs1 
DEBUG    [2023-07-12 11:02:50] _msgd.updateData :: step 1 
DEBUG    [2023-07-12 11:02:50] _msgd.updateData :: step 2 
DEBUG    [2023-07-12 11:02:50] _msgd.updateData :: step 3 
DEBUG    [2023-07-12 11:02:50] _msgd.writeInput :: writing input file: curr_design.yml... 
CRITICAL [2023-07-12 11:02:50] analysis.analyze :: [eval 0] analysis start 
INFO     [2023-07-12 11:02:50] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:02:50] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                         '0.8, airfoil_solid.xml.tmp\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'topology',
                 'xscale': 26.83,
                 'ytype': 'str'},
 'dname': 'topology',
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: reading distribution of parameter ['topology']... 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.0, airfoil_gbox_uni.xml.tmp\n0.8, airfoil_solid.xml.tmp\n',
 'interp_kind': 'previous',
 'name': None,
 'other_input': {'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                         '0.8, airfoil_solid.xml.tmp\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'topology',
                 'xscale': 26.83,
                 'ytype': 'str'},
 'xndim': 1,
 'xscale': 26.83,
 'yname': ['topology'],
 'yndim': 1,
 'ytype': ['str']} 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: interp_data =
0.0, airfoil_gbox_uni.xml.tmp
0.8, airfoil_solid.xml.tmp
 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: topology: msgd.utils.function, xdim=1, ydim=24, ytype=str, kind=previous, fill=extrapolate 
INFO     [2023-07-12 11:02:50] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:02:50] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'airfoil',
                 'xscale': 26.83,
                 'ytype': 'str'},
 'dname': 'airfoil',
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: reading distribution of parameter ['airfoil']... 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
 'interp_kind': 'previous',
 'name': None,
 'other_input': {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'airfoil',
                 'xscale': 26.83,
                 'ytype': 'str'},
 'xndim': 1,
 'xscale': 26.83,
 'yname': ['airfoil'],
 'yndim': 1,
 'ytype': ['str']} 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: interp_data =
0.0, sc1095.txt
0.5, sc1094r8.txt
 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: airfoil: msgd.utils.function, xdim=1, ydim=10, ytype=str, kind=previous, fill=extrapolate 
INFO     [2023-07-12 11:02:50] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:02:50] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.1, 46, -45\n'
                         '0.3, -3, 0\n'
                         '0.7, -44, 90\n'
                         '1.0, 47, 45\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'linear',
                 'name': ['ang_spar_1', 'ang_spar_2'],
                 'xscale': 26.83,
                 'ytype': 'float'},
 'dname': ['ang_spar_1', 'ang_spar_2'],
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: reading distribution of parameter ['ang_spar_1', 'ang_spar_2']... 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate', 'extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.1, 46, -45\n0.3, -3, 0\n0.7, -44, 90\n1.0, 47, 45\n',
 'interp_kind': 'linear',
 'name': None,
 'other_input': {'data': '0.1, 46, -45\n'
                         '0.3, -3, 0\n'
                         '0.7, -44, 90\n'
                         '1.0, 47, 45\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'linear',
                 'name': ['ang_spar_1', 'ang_spar_2'],
                 'xscale': 26.83,
                 'ytype': 'float'},
 'xndim': 1,
 'xscale': 26.83,
 'yname': ['ang_spar_1', 'ang_spar_2'],
 'yndim': 2,
 'ytype': ['float', 'float']} 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: interp_data =
0.1, 46, -45
0.3, -3, 0
0.7, -44, 90
1.0, 47, 45
 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: ang_spar_1: msgd.utils.function, xdim=1, ydim=1, ytype=float, kind=linear, fill=extrapolate 
DEBUG    [2023-07-12 11:02:50] distribution.createInterpolationDistribution :: ang_spar_2: msgd.utils.function, xdim=1, ydim=1, ytype=float, kind=linear, fill=extrapolate 
INFO     [2023-07-12 11:02:50] _msgd.discretize :: discretizing the design... 
DEBUG    [2023-07-12 11:02:50] _msgd.discretize :: structure model:
{'analysis': 1,
 'condition': [{'dofs': [1, 2, 3, 4, 5, 6],
                'region': 'root',
                'values': [0, 0, 0, 0, 0, 0]},
               {'dofs': [7, 8, 9, 10, 11, 12],
                'region': 'tip',
                'values': [0, 0, 0, 0, 1, 0]}],
 'max_iteration': 1,
 'member': [{'division': 32, 'id': 1, 'points': [1, 2]}],
 'name': 'beam_design',
 'num_steps': 1,
 'point': [{'coordinates': [0, 0, 0], 'id': 1},
           {'coordinates': [26.83, 0, 0], 'id': 2}],
 'set': [{'name': 'root', 'objects': [1], 'type': 'point'},
         {'name': 'tip', 'objects': [2], 'type': 'point'},
         {'name': 'segment1', 'objects': [1], 'type': 'member'}]} 
DEBUG    [2023-07-12 11:02:50] _msgd.discretize :: parameter distributions:
{'airfoil': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8F7E20>,
 'ang_spar_1': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8F7D90>,
 'ang_spar_2': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8F7D30>,
 'topology': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8D87C0>} 
INFO     [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: local variables:
{'abs_tol': 1e-12,
 'distributions': {'airfoil': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8F7E20>,
                   'ang_spar_1': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8F7D90>,
                   'ang_spar_2': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8F7D30>,
                   'topology': <msgd.utils.function.InterpolationFunction object at 0x000002AD6E8D87C0>},
 'locations': [0, 26.83],
 'model_elems': None,
 'model_nodes': None,
 'rel_tol': 1e-09,
 'sg_assignment': [{'cs': 'cs1', 'model': 'b2', 'region': 'segment1'}],
 'sg_key': 'cs',
 'trans_func': None} 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction topology (str) at 0... 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = airfoil_gbox_uni.xml.tmp (str) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction airfoil (str) at 0... 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = sc1095.txt (str) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_1 (float) at 0... 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: x 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: 0 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = 70.5 (float) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_2 (float) at 0... 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: x 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: 0 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = -67.5 (float) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction topology (str) at 26.83... 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = airfoil_solid.xml.tmp (str) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction airfoil (str) at 26.83... 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = sc1094r8.txt (str) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_1 (float) at 26.83... 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: x 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: 26.83 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = 7882.099999999999 (float) 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_2 (float) at 26.83... 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: x 
DEBUG    [2023-07-12 11:02:50] function.__call__ :: 26.83 
DEBUG    [2023-07-12 11:02:50] distribution.calcParamsFromDistr :: result = -3829.499999999999 (float) 
INFO     [2023-07-12 11:02:50] analysis.analyze :: going through steps... 
CRITICAL [2023-07-12 11:02:50] analysis.analyze :: ==================== 
CRITICAL [2023-07-12 11:02:50] analysis.analyze :: [eval 0] running cs step:  
DEBUG    [2023-07-12 11:02:50] analysis.analyze :: step config:
{'analysis': 'h',
 'setting': {'timeout': 60},
 'step': 'cs analysis',
 'type': 'cs'} 
DEBUG    [2023-07-12 11:02:50] analysis.analyze :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {},
 'design': {'cs_assignment': [{'cs': 'cs1',
                               'model': 'b2',
                               'region': 'segment1'}],
            'dim': 1,
            'file': 'beam_design.yml',
            'section_locations': [0, 26.83],
            'tool': 'gebt'},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'xscale': 26.83,
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'xscale': 26.83,
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'xscale': 26.83,
                   'ytype': 'float'}],
 'name': 'blade1'} 
INFO     [2023-07-12 11:02:50] core.runSGenomeDesignAnalysis :: running cs design h analysis... 
DEBUG    [2023-07-12 11:02:50] core.runSGenomeDesignAnalysis :: cs: cs1, model: md1 
INFO     [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: ---------------- 
INFO     [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: running cs design analysis: cs1_set1... 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: local variables:
{'analysis': {'analysis': 'h',
              'setting': {'timeout': 60},
              'step': 'cs analysis',
              'type': 'cs'},
 'calculate_strength': False,
 'design_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'design_name': 'cs1',
 'dir_list': ['.'],
 'mdao_data': {},
 'model_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set1',
 'params': {'a2p1': 0.8,
            'a2p3': 0.6,
            'airfoil': 'sc1095.txt',
            'airfoil_file_head': 1,
            'airfoil_point_order': 1,
            'airfoil_point_reverse': 1,
            'ang_spar_1': 70.5,
            'ang_spar_2': -67.5,
            'chord': 1.73,
            'gms': 0.004,
            'lam_back': 'T300 15k/976_0.0053',
            'lam_cap': 'Aluminum 8009_0.01',
            'lam_front': 'T300 15k/976_0.0053',
            'lam_skin': 'T300 15k/976_0.0053',
            'lam_spar_1': 'T300 15k/976_0.0053',
            'location': 0,
            'mat_fill_back': 'Plascore PN2-3/16OX3.0',
            'mat_fill_front': 'Rohacell 70',
            'mat_fill_te': 'Plascore PN2-3/16OX3.0',
            'mat_nsm': 'lead',
            'mdb_name': 'material_database_us_ft',
            'ply_spar_1': 10,
            'rnsm': 0.001,
            'topology': 'airfoil_gbox_uni.xml.tmp'},
 'physics': 'elastic',
 'returns': None,
 'sg_id': 'set1',
 'sg_key': 'cs',
 'sgdb': {},
 'sgdb_map': {},
 'sglib': {'cs1': {'design': {'base_file': 'topology',
                              'dim': 2,
                              'tool': 'prevabs'},
                   'model': {'md1': {'tool': 'vabs'}},
                   'name': 'cs1',
                   'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 70.5,
                                 'ang_spar_2': -67.5,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'}}},
 'sgs_data': {},
 'sgs_temp': {}} 
INFO     [2023-07-12 11:02:50] core.findSGPropByParam :: - finding cs cs1 properties by parameters... 
INFO     [2023-07-12 11:02:50] core.findSGPropByParam :: - not found 
INFO     [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: [cs: cs1_set1] updating current design inputs... 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: design before substitution:
{'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: model before substitution:
{'tool': 'vabs'} 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: design after substitution:
{'base_file': 'airfoil_gbox_uni.xml.tmp', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: model after substitution:
{'tool': 'vabs'} 
INFO     [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: [cs: cs1_set1] checking if lower level cs properties exist... 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: parameters before preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1095.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 70.5,
 'ang_spar_2': -67.5,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_gbox_uni.xml.tmp'} 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: prepros =
[] 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: _fn_base = airfoil_gbox_uni.xml.tmp 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: _fn_spec = cs1_set1.xml 
DEBUG    [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: parameters after preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1095.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 70.5,
 'ang_spar_2': -67.5,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_gbox_uni.xml.tmp'} 
INFO     [2023-07-12 11:02:50] core.runSGDesignAnalysisH :: [cs: cs1_set1] creating cs... 
INFO     [2023-07-12 11:02:50] main.buildSG :: building 2D SG: cs1_set1... 
CRITICAL [2023-07-12 11:02:50] execu.run :: prevabs -i cs1_set1.xml -vabs -ver 4.0 -h 
INFO     [2023-07-12 11:02:51] core.runSGDesignAnalysisH :: [cs: cs1_set1] writing cs input file... 
INFO     [2023-07-12 11:02:51] core.runSGDesignAnalysisH :: [cs: cs1_set1] running cs analysis... 
INFO     [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: [cs: cs1_set1] reading cs analysis output file... 
INFO     [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: [cs: cs1_set1] adding cs outputs to the database... 
INFO     [2023-07-12 11:02:52] core.runSGenomeDesignAnalysis :: running cs design h analysis... 
DEBUG    [2023-07-12 11:02:52] core.runSGenomeDesignAnalysis :: cs: cs1, model: md1 
INFO     [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: ---------------- 
INFO     [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: running cs design analysis: cs1_set2... 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: local variables:
{'analysis': {'analysis': 'h',
              'setting': {'timeout': 60},
              'step': 'cs analysis',
              'type': 'cs'},
 'calculate_strength': False,
 'design_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'design_name': 'cs1',
 'dir_list': ['.'],
 'mdao_data': {},
 'model_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set2',
 'params': {'a2p1': 0.8,
            'a2p3': 0.6,
            'airfoil': 'sc1094r8.txt',
            'airfoil_file_head': 1,
            'airfoil_point_order': 1,
            'airfoil_point_reverse': 1,
            'ang_spar_1': 7882.099999999999,
            'ang_spar_2': -3829.499999999999,
            'chord': 1.73,
            'gms': 0.004,
            'lam_back': 'T300 15k/976_0.0053',
            'lam_cap': 'Aluminum 8009_0.01',
            'lam_front': 'T300 15k/976_0.0053',
            'lam_skin': 'T300 15k/976_0.0053',
            'lam_spar_1': 'T300 15k/976_0.0053',
            'location': 26.83,
            'mat_fill_back': 'Plascore PN2-3/16OX3.0',
            'mat_fill_front': 'Rohacell 70',
            'mat_fill_te': 'Plascore PN2-3/16OX3.0',
            'mat_nsm': 'lead',
            'mdb_name': 'material_database_us_ft',
            'ply_spar_1': 10,
            'rnsm': 0.001,
            'topology': 'airfoil_solid.xml.tmp'},
 'physics': 'elastic',
 'returns': None,
 'sg_id': 'set2',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 70.5,
                                 'ang_spar_2': -67.5,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                        'cmp11r': 5.4844993952e-07,
                                        'cmp12c': 1.0598978638e-09,
                                        'cmp12r': 8.9235923897e-11,
                                        'cmp13c': -6.3876314212e-08,
                                        'cmp13r': -4.7643828627e-09,
                                        'cmp14c': 4.4880988512e-07,
                                        'cmp14r': 1.0598978638e-09,
                                        'cmp15r': -6.3876314212e-08,
                                        'cmp16r': 4.4880988512e-07,
                                        'cmp21c': 1.0598978638e-09,
                                        'cmp21r': 8.9235923897e-11,
                                        'cmp22c': 1.8627229409e-05,
                                        'cmp22r': 1.7667949994e-06,
                                        'cmp23c': 1.6150669739e-09,
                                        'cmp23r': -2.4678796711e-07,
                                        'cmp24c': -6.4868398022e-09,
                                        'cmp24r': 2.8947742359e-07,
                                        'cmp25r': -7.4760283647e-09,
                                        'cmp26r': -1.2065334963e-10,
                                        'cmp31c': -6.3876314212e-08,
                                        'cmp31r': -4.7643828627e-09,
                                        'cmp32c': 1.6150669739e-09,
                                        'cmp32r': -2.4678796711e-07,
                                        'cmp33c': 8.3947049682e-06,
                                        'cmp33r': 2.7325344108e-05,
                                        'cmp34c': 3.7413165037e-08,
                                        'cmp34r': -2.2054535366e-05,
                                        'cmp35r': -1.7459754752e-09,
                                        'cmp36r': 4.6260657341e-09,
                                        'cmp41c': 4.4880988512e-07,
                                        'cmp41r': 1.0598978638e-09,
                                        'cmp42c': -6.4868398022e-09,
                                        'cmp42r': 2.8947742359e-07,
                                        'cmp43c': 3.7413165037e-08,
                                        'cmp43r': -2.2054535366e-05,
                                        'cmp44c': 3.9040038704e-07,
                                        'cmp44r': 1.8627229409e-05,
                                        'cmp45r': 1.6150669739e-09,
                                        'cmp46r': -6.4868398022e-09,
                                        'cmp51r': -6.3876314212e-08,
                                        'cmp52r': -7.4760283647e-09,
                                        'cmp53r': -1.7459754752e-09,
                                        'cmp54r': 1.6150669739e-09,
                                        'cmp55r': 8.3947049682e-06,
                                        'cmp56r': 3.7413165037e-08,
                                        'cmp61r': 4.4880988512e-07,
                                        'cmp62r': -1.2065334963e-10,
                                        'cmp63r': 4.6260657341e-09,
                                        'cmp64r': -6.4868398022e-09,
                                        'cmp65r': 3.7413165037e-08,
                                        'cmp66r': 3.9040038704e-07,
                                        'ea': 32123231.314,
                                        'ei22': 119120.22795,
                                        'ei33': 2562620.733,
                                        'ga22': 562248.80781,
                                        'ga33': 835685.78392,
                                        'gj': 53684.849101,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1839944031,
                                        'sc3': 0.015540551804,
                                        'stf11c': 32127277.521,
                                        'stf11r': 32127277.572,
                                        'stf12c': -14739.357462,
                                        'stf12r': 10.197839899,
                                        'stf13c': 409244.80607,
                                        'stf13r': -204.38975585,
                                        'stf14c': -36973443.08,
                                        'stf14r': -14981.512851,
                                        'stf15r': 409244.827,
                                        'stf16r': -36973444.739,
                                        'stf21c': -14739.357462,
                                        'stf21r': 10.197839899,
                                        'stf22c': 53691.923587,
                                        'stf22r': 569898.38406,
                                        'stf23c': -202.06359968,
                                        'stf23r': -45085.363622,
                                        'stf24c': 17856.078801,
                                        'stf24r': -62237.532434,
                                        'stf25r': 511.91968891,
                                        'stf26r': -384.5445086,
                                        'stf31c': 409244.80607,
                                        'stf31r': -204.38975585,
                                        'stf32c': -202.06359968,
                                        'stf32r': -45085.363622,
                                        'stf33c': 124386.66909,
                                        'stf33r': 828054.88201,
                                        'stf34c': -482397.38069,
                                        'stf34r': 981115.35652,
                                        'stf35r': -88.188473075,
                                        'stf36r': 6719.495598,
                                        'stf41c': -36973443.08,
                                        'stf41r': -14981.512851,
                                        'stf42c': 17856.078801,
                                        'stf42r': -62237.532434,
                                        'stf43c': -482397.38069,
                                        'stf43r': 981115.35652,
                                        'stf44c': 45113199.598,
                                        'stf44r': 1216297.0163,
                                        'stf45r': -314.434069,
                                        'stf46r': 25817.919161,
                                        'stf51r': 409244.827,
                                        'stf52r': 511.91968891,
                                        'stf53r': -88.188473075,
                                        'stf54r': -314.434069,
                                        'stf55r': 124387.13169,
                                        'stf56r': -482398.11302,
                                        'stf61r': -36973444.739,
                                        'stf62r': -384.5445086,
                                        'stf63r': 6719.495598,
                                        'stf64r': 25817.919161,
                                        'stf65r': -482398.11302,
                                        'stf66r': 45113254.126,
                                        'tc2': 1.1508350738,
                                        'tc3': 0.012738112556}}}]},
 'sgdb_map': {},
 'sglib': {'cs1': {'design': {'base_file': 'topology',
                              'dim': 2,
                              'tool': 'prevabs'},
                   'model': {'md1': {'tool': 'vabs'}},
                   'name': 'cs1',
                   'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 7882.099999999999,
                                 'ang_spar_2': -3829.499999999999,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 26.83,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'}}},
 'sgs_data': {},
 'sgs_temp': {}} 
INFO     [2023-07-12 11:02:52] core.findSGPropByParam :: - finding cs cs1 properties by parameters... 
INFO     [2023-07-12 11:02:52] core.findSGPropByParam :: - not found 
INFO     [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: [cs: cs1_set2] updating current design inputs... 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: design before substitution:
{'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: model before substitution:
{'tool': 'vabs'} 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: design after substitution:
{'base_file': 'airfoil_solid.xml.tmp', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: model after substitution:
{'tool': 'vabs'} 
INFO     [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: [cs: cs1_set2] checking if lower level cs properties exist... 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: parameters before preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1094r8.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 7882.099999999999,
 'ang_spar_2': -3829.499999999999,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 26.83,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_solid.xml.tmp'} 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: prepros =
[] 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: _fn_base = airfoil_solid.xml.tmp 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: _fn_spec = cs1_set2.xml 
DEBUG    [2023-07-12 11:02:52] core.runSGDesignAnalysisH :: parameters after preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1094r8.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 7882.099999999999,
 'ang_spar_2': -3829.499999999999,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 26.83,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_solid.xml.tmp'} 
INFO     [2023-07-12 11:02:53] core.runSGDesignAnalysisH :: [cs: cs1_set2] creating cs... 
INFO     [2023-07-12 11:02:53] main.buildSG :: building 2D SG: cs1_set2... 
CRITICAL [2023-07-12 11:02:53] execu.run :: prevabs -i cs1_set2.xml -vabs -ver 4.0 -h 
INFO     [2023-07-12 11:02:53] core.runSGDesignAnalysisH :: [cs: cs1_set2] writing cs input file... 
INFO     [2023-07-12 11:02:53] core.runSGDesignAnalysisH :: [cs: cs1_set2] running cs analysis... 
INFO     [2023-07-12 11:02:54] core.runSGDesignAnalysisH :: [cs: cs1_set2] reading cs analysis output file... 
INFO     [2023-07-12 11:02:54] core.runSGDesignAnalysisH :: [cs: cs1_set2] adding cs outputs to the database... 
CRITICAL [2023-07-12 11:02:54] analysis.analyze :: ==================== 
CRITICAL [2023-07-12 11:02:54] analysis.analyze :: [eval 0] running gebt step:  
DEBUG    [2023-07-12 11:02:54] analysis.analyze :: step config:
{'analysis': 1,
 'setting': {'output_file': 'struct_resp.csv'},
 'step': 'beam analysis',
 'type': 'gebt'} 
INFO     [2023-07-12 11:02:54] main.run :: running the gebt module... 
CRITICAL [2023-07-12 11:02:54] io.buildGEBTBeam :: creating the beam object for GEBT... 
DEBUG    [2023-07-12 11:02:54] io.buildGEBTBeam :: local variables:
{'fn': {'analysis': 1,
        'condition': [{'dofs': [1, 2, 3, 4, 5, 6],
                       'region': 'root',
                       'values': [0, 0, 0, 0, 0, 0]},
                      {'dofs': [7, 8, 9, 10, 11, 12],
                       'region': 'tip',
                       'values': [0, 0, 0, 0, 1, 0]}],
        'max_iteration': 1,
        'member': [{'division': 32, 'id': 1, 'points': [1, 2]}],
        'name': 'beam_design',
        'num_steps': 1,
        'point': [{'coordinates': [0, 0, 0], 'id': 1},
                  {'coordinates': [26.83, 0, 0], 'id': 2}],
        'section_assignment': [{'region': 'segment1', 'section': 'cs1'}],
        'section_property': [{'id': 1,
                              'name': 'cs1',
                              'parameter': {'a2p1': 0.8,
                                            'a2p3': 0.6,
                                            'airfoil': 'sc1095.txt',
                                            'airfoil_file_head': 1,
                                            'airfoil_point_order': 1,
                                            'airfoil_point_reverse': 1,
                                            'ang_spar_1': 70.5,
                                            'ang_spar_2': -67.5,
                                            'chord': 1.73,
                                            'gms': 0.004,
                                            'lam_back': 'T300 15k/976_0.0053',
                                            'lam_cap': 'Aluminum 8009_0.01',
                                            'lam_front': 'T300 15k/976_0.0053',
                                            'lam_skin': 'T300 15k/976_0.0053',
                                            'lam_spar_1': 'T300 15k/976_0.0053',
                                            'location': 0,
                                            'mat_fill_back': 'Plascore '
                                                             'PN2-3/16OX3.0',
                                            'mat_fill_front': 'Rohacell 70',
                                            'mat_fill_te': 'Plascore '
                                                           'PN2-3/16OX3.0',
                                            'mat_nsm': 'lead',
                                            'mdb_name': 'material_database_us_ft',
                                            'ply_spar_1': 10,
                                            'rnsm': 0.001,
                                            'topology': 'airfoil_gbox_uni.xml.tmp'},
                              'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                                   'cmp11r': 5.4844993952e-07,
                                                   'cmp12c': 1.0598978638e-09,
                                                   'cmp12r': 8.9235923897e-11,
                                                   'cmp13c': -6.3876314212e-08,
                                                   'cmp13r': -4.7643828627e-09,
                                                   'cmp14c': 4.4880988512e-07,
                                                   'cmp14r': 1.0598978638e-09,
                                                   'cmp15r': -6.3876314212e-08,
                                                   'cmp16r': 4.4880988512e-07,
                                                   'cmp21c': 1.0598978638e-09,
                                                   'cmp21r': 8.9235923897e-11,
                                                   'cmp22c': 1.8627229409e-05,
                                                   'cmp22r': 1.7667949994e-06,
                                                   'cmp23c': 1.6150669739e-09,
                                                   'cmp23r': -2.4678796711e-07,
                                                   'cmp24c': -6.4868398022e-09,
                                                   'cmp24r': 2.8947742359e-07,
                                                   'cmp25r': -7.4760283647e-09,
                                                   'cmp26r': -1.2065334963e-10,
                                                   'cmp31c': -6.3876314212e-08,
                                                   'cmp31r': -4.7643828627e-09,
                                                   'cmp32c': 1.6150669739e-09,
                                                   'cmp32r': -2.4678796711e-07,
                                                   'cmp33c': 8.3947049682e-06,
                                                   'cmp33r': 2.7325344108e-05,
                                                   'cmp34c': 3.7413165037e-08,
                                                   'cmp34r': -2.2054535366e-05,
                                                   'cmp35r': -1.7459754752e-09,
                                                   'cmp36r': 4.6260657341e-09,
                                                   'cmp41c': 4.4880988512e-07,
                                                   'cmp41r': 1.0598978638e-09,
                                                   'cmp42c': -6.4868398022e-09,
                                                   'cmp42r': 2.8947742359e-07,
                                                   'cmp43c': 3.7413165037e-08,
                                                   'cmp43r': -2.2054535366e-05,
                                                   'cmp44c': 3.9040038704e-07,
                                                   'cmp44r': 1.8627229409e-05,
                                                   'cmp45r': 1.6150669739e-09,
                                                   'cmp46r': -6.4868398022e-09,
                                                   'cmp51r': -6.3876314212e-08,
                                                   'cmp52r': -7.4760283647e-09,
                                                   'cmp53r': -1.7459754752e-09,
                                                   'cmp54r': 1.6150669739e-09,
                                                   'cmp55r': 8.3947049682e-06,
                                                   'cmp56r': 3.7413165037e-08,
                                                   'cmp61r': 4.4880988512e-07,
                                                   'cmp62r': -1.2065334963e-10,
                                                   'cmp63r': 4.6260657341e-09,
                                                   'cmp64r': -6.4868398022e-09,
                                                   'cmp65r': 3.7413165037e-08,
                                                   'cmp66r': 3.9040038704e-07,
                                                   'ea': 32123231.314,
                                                   'ei22': 119120.22795,
                                                   'ei33': 2562620.733,
                                                   'ga22': 562248.80781,
                                                   'ga33': 835685.78392,
                                                   'gj': 53684.849101,
                                                   'mc2': 1.1494335098,
                                                   'mc3': 0.012439183019,
                                                   'mmoi1': 0.0072632486361,
                                                   'mmoi2': 0.00026525481038,
                                                   'mmoi3': 0.0069979938258,
                                                   'ms11': 0.075463162134,
                                                   'ms12': 0.0,
                                                   'ms13': 0.0,
                                                   'ms14': 0.0,
                                                   'ms15': 0.00093870008494,
                                                   'ms16': -0.086739887313,
                                                   'ms21': 0.0,
                                                   'ms22': 0.075463162134,
                                                   'ms23': 0.0,
                                                   'ms24': -0.00093870008494,
                                                   'ms25': 0.0,
                                                   'ms26': 0.0,
                                                   'ms31': 0.0,
                                                   'ms32': 0.0,
                                                   'ms33': 0.075463162134,
                                                   'ms34': 0.086739887313,
                                                   'ms35': 0.0,
                                                   'ms36': 0.0,
                                                   'ms41': 0.0,
                                                   'ms42': -0.00093870008494,
                                                   'ms43': 0.086739887313,
                                                   'ms44': 0.10697665841,
                                                   'ms45': 0.0,
                                                   'ms46': 0.0,
                                                   'ms51': 0.00093870008494,
                                                   'ms52': 0.0,
                                                   'ms53': 0.0,
                                                   'ms54': 0.0,
                                                   'ms55': 0.00027700892854,
                                                   'ms56': -0.00110180938,
                                                   'ms61': -0.086739887313,
                                                   'ms62': 0.0,
                                                   'ms63': 0.0,
                                                   'ms64': 0.0,
                                                   'ms65': -0.00110180938,
                                                   'ms66': 0.10669964949,
                                                   'mu': 0.075463162134,
                                                   'sc2': 1.1839944031,
                                                   'sc3': 0.015540551804,
                                                   'stf11c': 32127277.521,
                                                   'stf11r': 32127277.572,
                                                   'stf12c': -14739.357462,
                                                   'stf12r': 10.197839899,
                                                   'stf13c': 409244.80607,
                                                   'stf13r': -204.38975585,
                                                   'stf14c': -36973443.08,
                                                   'stf14r': -14981.512851,
                                                   'stf15r': 409244.827,
                                                   'stf16r': -36973444.739,
                                                   'stf21c': -14739.357462,
                                                   'stf21r': 10.197839899,
                                                   'stf22c': 53691.923587,
                                                   'stf22r': 569898.38406,
                                                   'stf23c': -202.06359968,
                                                   'stf23r': -45085.363622,
                                                   'stf24c': 17856.078801,
                                                   'stf24r': -62237.532434,
                                                   'stf25r': 511.91968891,
                                                   'stf26r': -384.5445086,
                                                   'stf31c': 409244.80607,
                                                   'stf31r': -204.38975585,
                                                   'stf32c': -202.06359968,
                                                   'stf32r': -45085.363622,
                                                   'stf33c': 124386.66909,
                                                   'stf33r': 828054.88201,
                                                   'stf34c': -482397.38069,
                                                   'stf34r': 981115.35652,
                                                   'stf35r': -88.188473075,
                                                   'stf36r': 6719.495598,
                                                   'stf41c': -36973443.08,
                                                   'stf41r': -14981.512851,
                                                   'stf42c': 17856.078801,
                                                   'stf42r': -62237.532434,
                                                   'stf43c': -482397.38069,
                                                   'stf43r': 981115.35652,
                                                   'stf44c': 45113199.598,
                                                   'stf44r': 1216297.0163,
                                                   'stf45r': -314.434069,
                                                   'stf46r': 25817.919161,
                                                   'stf51r': 409244.827,
                                                   'stf52r': 511.91968891,
                                                   'stf53r': -88.188473075,
                                                   'stf54r': -314.434069,
                                                   'stf55r': 124387.13169,
                                                   'stf56r': -482398.11302,
                                                   'stf61r': -36973444.739,
                                                   'stf62r': -384.5445086,
                                                   'stf63r': 6719.495598,
                                                   'stf64r': 25817.919161,
                                                   'stf65r': -482398.11302,
                                                   'stf66r': 45113254.126,
                                                   'tc2': 1.1508350738,
                                                   'tc3': 0.012738112556}}}],
        'set': [{'name': 'root', 'objects': [1], 'type': 'point'},
                {'name': 'tip', 'objects': [2], 'type': 'point'},
                {'name': 'segment1', 'objects': [1], 'type': 'member'}]},
 'fn_base': '',
 'fn_dir': '.',
 'fn_ext': ''} 
DEBUG    [2023-07-12 11:02:54] io.buildGEBTBeam :: raw input:
{'analysis': 1,
 'condition': [{'dofs': [1, 2, 3, 4, 5, 6],
                'region': 'root',
                'values': [0, 0, 0, 0, 0, 0]},
               {'dofs': [7, 8, 9, 10, 11, 12],
                'region': 'tip',
                'values': [0, 0, 0, 0, 1, 0]}],
 'max_iteration': 1,
 'member': [{'division': 32, 'id': 1, 'points': [1, 2]}],
 'name': 'beam_design',
 'num_steps': 1,
 'point': [{'coordinates': [0, 0, 0], 'id': 1},
           {'coordinates': [26.83, 0, 0], 'id': 2}],
 'section_assignment': [{'region': 'segment1', 'section': 'cs1'}],
 'section_property': [{'id': 1,
                       'name': 'cs1',
                       'parameter': {'a2p1': 0.8,
                                     'a2p3': 0.6,
                                     'airfoil': 'sc1095.txt',
                                     'airfoil_file_head': 1,
                                     'airfoil_point_order': 1,
                                     'airfoil_point_reverse': 1,
                                     'ang_spar_1': 70.5,
                                     'ang_spar_2': -67.5,
                                     'chord': 1.73,
                                     'gms': 0.004,
                                     'lam_back': 'T300 15k/976_0.0053',
                                     'lam_cap': 'Aluminum 8009_0.01',
                                     'lam_front': 'T300 15k/976_0.0053',
                                     'lam_skin': 'T300 15k/976_0.0053',
                                     'lam_spar_1': 'T300 15k/976_0.0053',
                                     'location': 0,
                                     'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                     'mat_fill_front': 'Rohacell 70',
                                     'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                     'mat_nsm': 'lead',
                                     'mdb_name': 'material_database_us_ft',
                                     'ply_spar_1': 10,
                                     'rnsm': 0.001,
                                     'topology': 'airfoil_gbox_uni.xml.tmp'},
                       'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                            'cmp11r': 5.4844993952e-07,
                                            'cmp12c': 1.0598978638e-09,
                                            'cmp12r': 8.9235923897e-11,
                                            'cmp13c': -6.3876314212e-08,
                                            'cmp13r': -4.7643828627e-09,
                                            'cmp14c': 4.4880988512e-07,
                                            'cmp14r': 1.0598978638e-09,
                                            'cmp15r': -6.3876314212e-08,
                                            'cmp16r': 4.4880988512e-07,
                                            'cmp21c': 1.0598978638e-09,
                                            'cmp21r': 8.9235923897e-11,
                                            'cmp22c': 1.8627229409e-05,
                                            'cmp22r': 1.7667949994e-06,
                                            'cmp23c': 1.6150669739e-09,
                                            'cmp23r': -2.4678796711e-07,
                                            'cmp24c': -6.4868398022e-09,
                                            'cmp24r': 2.8947742359e-07,
                                            'cmp25r': -7.4760283647e-09,
                                            'cmp26r': -1.2065334963e-10,
                                            'cmp31c': -6.3876314212e-08,
                                            'cmp31r': -4.7643828627e-09,
                                            'cmp32c': 1.6150669739e-09,
                                            'cmp32r': -2.4678796711e-07,
                                            'cmp33c': 8.3947049682e-06,
                                            'cmp33r': 2.7325344108e-05,
                                            'cmp34c': 3.7413165037e-08,
                                            'cmp34r': -2.2054535366e-05,
                                            'cmp35r': -1.7459754752e-09,
                                            'cmp36r': 4.6260657341e-09,
                                            'cmp41c': 4.4880988512e-07,
                                            'cmp41r': 1.0598978638e-09,
                                            'cmp42c': -6.4868398022e-09,
                                            'cmp42r': 2.8947742359e-07,
                                            'cmp43c': 3.7413165037e-08,
                                            'cmp43r': -2.2054535366e-05,
                                            'cmp44c': 3.9040038704e-07,
                                            'cmp44r': 1.8627229409e-05,
                                            'cmp45r': 1.6150669739e-09,
                                            'cmp46r': -6.4868398022e-09,
                                            'cmp51r': -6.3876314212e-08,
                                            'cmp52r': -7.4760283647e-09,
                                            'cmp53r': -1.7459754752e-09,
                                            'cmp54r': 1.6150669739e-09,
                                            'cmp55r': 8.3947049682e-06,
                                            'cmp56r': 3.7413165037e-08,
                                            'cmp61r': 4.4880988512e-07,
                                            'cmp62r': -1.2065334963e-10,
                                            'cmp63r': 4.6260657341e-09,
                                            'cmp64r': -6.4868398022e-09,
                                            'cmp65r': 3.7413165037e-08,
                                            'cmp66r': 3.9040038704e-07,
                                            'ea': 32123231.314,
                                            'ei22': 119120.22795,
                                            'ei33': 2562620.733,
                                            'ga22': 562248.80781,
                                            'ga33': 835685.78392,
                                            'gj': 53684.849101,
                                            'mc2': 1.1494335098,
                                            'mc3': 0.012439183019,
                                            'mmoi1': 0.0072632486361,
                                            'mmoi2': 0.00026525481038,
                                            'mmoi3': 0.0069979938258,
                                            'ms11': 0.075463162134,
                                            'ms12': 0.0,
                                            'ms13': 0.0,
                                            'ms14': 0.0,
                                            'ms15': 0.00093870008494,
                                            'ms16': -0.086739887313,
                                            'ms21': 0.0,
                                            'ms22': 0.075463162134,
                                            'ms23': 0.0,
                                            'ms24': -0.00093870008494,
                                            'ms25': 0.0,
                                            'ms26': 0.0,
                                            'ms31': 0.0,
                                            'ms32': 0.0,
                                            'ms33': 0.075463162134,
                                            'ms34': 0.086739887313,
                                            'ms35': 0.0,
                                            'ms36': 0.0,
                                            'ms41': 0.0,
                                            'ms42': -0.00093870008494,
                                            'ms43': 0.086739887313,
                                            'ms44': 0.10697665841,
                                            'ms45': 0.0,
                                            'ms46': 0.0,
                                            'ms51': 0.00093870008494,
                                            'ms52': 0.0,
                                            'ms53': 0.0,
                                            'ms54': 0.0,
                                            'ms55': 0.00027700892854,
                                            'ms56': -0.00110180938,
                                            'ms61': -0.086739887313,
                                            'ms62': 0.0,
                                            'ms63': 0.0,
                                            'ms64': 0.0,
                                            'ms65': -0.00110180938,
                                            'ms66': 0.10669964949,
                                            'mu': 0.075463162134,
                                            'sc2': 1.1839944031,
                                            'sc3': 0.015540551804,
                                            'stf11c': 32127277.521,
                                            'stf11r': 32127277.572,
                                            'stf12c': -14739.357462,
                                            'stf12r': 10.197839899,
                                            'stf13c': 409244.80607,
                                            'stf13r': -204.38975585,
                                            'stf14c': -36973443.08,
                                            'stf14r': -14981.512851,
                                            'stf15r': 409244.827,
                                            'stf16r': -36973444.739,
                                            'stf21c': -14739.357462,
                                            'stf21r': 10.197839899,
                                            'stf22c': 53691.923587,
                                            'stf22r': 569898.38406,
                                            'stf23c': -202.06359968,
                                            'stf23r': -45085.363622,
                                            'stf24c': 17856.078801,
                                            'stf24r': -62237.532434,
                                            'stf25r': 511.91968891,
                                            'stf26r': -384.5445086,
                                            'stf31c': 409244.80607,
                                            'stf31r': -204.38975585,
                                            'stf32c': -202.06359968,
                                            'stf32r': -45085.363622,
                                            'stf33c': 124386.66909,
                                            'stf33r': 828054.88201,
                                            'stf34c': -482397.38069,
                                            'stf34r': 981115.35652,
                                            'stf35r': -88.188473075,
                                            'stf36r': 6719.495598,
                                            'stf41c': -36973443.08,
                                            'stf41r': -14981.512851,
                                            'stf42c': 17856.078801,
                                            'stf42r': -62237.532434,
                                            'stf43c': -482397.38069,
                                            'stf43r': 981115.35652,
                                            'stf44c': 45113199.598,
                                            'stf44r': 1216297.0163,
                                            'stf45r': -314.434069,
                                            'stf46r': 25817.919161,
                                            'stf51r': 409244.827,
                                            'stf52r': 511.91968891,
                                            'stf53r': -88.188473075,
                                            'stf54r': -314.434069,
                                            'stf55r': 124387.13169,
                                            'stf56r': -482398.11302,
                                            'stf61r': -36973444.739,
                                            'stf62r': -384.5445086,
                                            'stf63r': 6719.495598,
                                            'stf64r': 25817.919161,
                                            'stf65r': -482398.11302,
                                            'stf66r': 45113254.126,
                                            'tc2': 1.1508350738,
                                            'tc3': 0.012738112556}}}],
 'set': [{'name': 'root', 'objects': [1], 'type': 'point'},
         {'name': 'tip', 'objects': [2], 'type': 'point'},
         {'name': 'segment1', 'objects': [1], 'type': 'member'}]} 
CRITICAL [2023-07-12 11:02:54] execu.run :: gebt beam_design.dat 
INFO     [2023-07-12 11:02:54] io.readGEBTOut :: reading gebt output beam_design.dat.out... 
DEBUG    [2023-07-12 11:02:54] io.readGEBTOut :: reading step 1 output... 
DEBUG    [2023-07-12 11:02:54] io.readGEBTOutBeam :: reading point 1 result... 
DEBUG    [2023-07-12 11:02:54] io.readGEBTOutBeam :: reading point 2 result... 
DEBUG    [2023-07-12 11:02:54] io.readGEBTOutBeam :: reading memebr 1 result... 
CRITICAL [2023-07-12 11:02:54] analysis.analyze :: ==================== 
CRITICAL [2023-07-12 11:02:54] analysis.analyze :: [eval 0] running cs step:  
DEBUG    [2023-07-12 11:02:54] analysis.analyze :: step config:
{'analysis': 'd,fi',
 'input': {'load_case': {'data_form': 'file',
                         'file_name': 'struct_resp.csv',
                         'location_tags': 'coord',
                         'location_value_types': 'float'}},
 'step': 'recovery',
 'type': 'cs'} 
DEBUG    [2023-07-12 11:02:54] analysis.analyze :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {'cs1_set1': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1095.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 70.5,
                                         'ang_spar_2': -67.5,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_gbox_uni.xml.tmp'},
                           'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                                'cmp11r': 5.4844993952e-07,
                                                'cmp12c': 1.0598978638e-09,
                                                'cmp12r': 8.9235923897e-11,
                                                'cmp13c': -6.3876314212e-08,
                                                'cmp13r': -4.7643828627e-09,
                                                'cmp14c': 4.4880988512e-07,
                                                'cmp14r': 1.0598978638e-09,
                                                'cmp15r': -6.3876314212e-08,
                                                'cmp16r': 4.4880988512e-07,
                                                'cmp21c': 1.0598978638e-09,
                                                'cmp21r': 8.9235923897e-11,
                                                'cmp22c': 1.8627229409e-05,
                                                'cmp22r': 1.7667949994e-06,
                                                'cmp23c': 1.6150669739e-09,
                                                'cmp23r': -2.4678796711e-07,
                                                'cmp24c': -6.4868398022e-09,
                                                'cmp24r': 2.8947742359e-07,
                                                'cmp25r': -7.4760283647e-09,
                                                'cmp26r': -1.2065334963e-10,
                                                'cmp31c': -6.3876314212e-08,
                                                'cmp31r': -4.7643828627e-09,
                                                'cmp32c': 1.6150669739e-09,
                                                'cmp32r': -2.4678796711e-07,
                                                'cmp33c': 8.3947049682e-06,
                                                'cmp33r': 2.7325344108e-05,
                                                'cmp34c': 3.7413165037e-08,
                                                'cmp34r': -2.2054535366e-05,
                                                'cmp35r': -1.7459754752e-09,
                                                'cmp36r': 4.6260657341e-09,
                                                'cmp41c': 4.4880988512e-07,
                                                'cmp41r': 1.0598978638e-09,
                                                'cmp42c': -6.4868398022e-09,
                                                'cmp42r': 2.8947742359e-07,
                                                'cmp43c': 3.7413165037e-08,
                                                'cmp43r': -2.2054535366e-05,
                                                'cmp44c': 3.9040038704e-07,
                                                'cmp44r': 1.8627229409e-05,
                                                'cmp45r': 1.6150669739e-09,
                                                'cmp46r': -6.4868398022e-09,
                                                'cmp51r': -6.3876314212e-08,
                                                'cmp52r': -7.4760283647e-09,
                                                'cmp53r': -1.7459754752e-09,
                                                'cmp54r': 1.6150669739e-09,
                                                'cmp55r': 8.3947049682e-06,
                                                'cmp56r': 3.7413165037e-08,
                                                'cmp61r': 4.4880988512e-07,
                                                'cmp62r': -1.2065334963e-10,
                                                'cmp63r': 4.6260657341e-09,
                                                'cmp64r': -6.4868398022e-09,
                                                'cmp65r': 3.7413165037e-08,
                                                'cmp66r': 3.9040038704e-07,
                                                'ea': 32123231.314,
                                                'ei22': 119120.22795,
                                                'ei33': 2562620.733,
                                                'ga22': 562248.80781,
                                                'ga33': 835685.78392,
                                                'gj': 53684.849101,
                                                'mc2': 1.1494335098,
                                                'mc3': 0.012439183019,
                                                'mmoi1': 0.0072632486361,
                                                'mmoi2': 0.00026525481038,
                                                'mmoi3': 0.0069979938258,
                                                'ms11': 0.075463162134,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00093870008494,
                                                'ms16': -0.086739887313,
                                                'ms21': 0.0,
                                                'ms22': 0.075463162134,
                                                'ms23': 0.0,
                                                'ms24': -0.00093870008494,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.075463162134,
                                                'ms34': 0.086739887313,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00093870008494,
                                                'ms43': 0.086739887313,
                                                'ms44': 0.10697665841,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00093870008494,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 0.00027700892854,
                                                'ms56': -0.00110180938,
                                                'ms61': -0.086739887313,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00110180938,
                                                'ms66': 0.10669964949,
                                                'mu': 0.075463162134,
                                                'sc2': 1.1839944031,
                                                'sc3': 0.015540551804,
                                                'stf11c': 32127277.521,
                                                'stf11r': 32127277.572,
                                                'stf12c': -14739.357462,
                                                'stf12r': 10.197839899,
                                                'stf13c': 409244.80607,
                                                'stf13r': -204.38975585,
                                                'stf14c': -36973443.08,
                                                'stf14r': -14981.512851,
                                                'stf15r': 409244.827,
                                                'stf16r': -36973444.739,
                                                'stf21c': -14739.357462,
                                                'stf21r': 10.197839899,
                                                'stf22c': 53691.923587,
                                                'stf22r': 569898.38406,
                                                'stf23c': -202.06359968,
                                                'stf23r': -45085.363622,
                                                'stf24c': 17856.078801,
                                                'stf24r': -62237.532434,
                                                'stf25r': 511.91968891,
                                                'stf26r': -384.5445086,
                                                'stf31c': 409244.80607,
                                                'stf31r': -204.38975585,
                                                'stf32c': -202.06359968,
                                                'stf32r': -45085.363622,
                                                'stf33c': 124386.66909,
                                                'stf33r': 828054.88201,
                                                'stf34c': -482397.38069,
                                                'stf34r': 981115.35652,
                                                'stf35r': -88.188473075,
                                                'stf36r': 6719.495598,
                                                'stf41c': -36973443.08,
                                                'stf41r': -14981.512851,
                                                'stf42c': 17856.078801,
                                                'stf42r': -62237.532434,
                                                'stf43c': -482397.38069,
                                                'stf43r': 981115.35652,
                                                'stf44c': 45113199.598,
                                                'stf44r': 1216297.0163,
                                                'stf45r': -314.434069,
                                                'stf46r': 25817.919161,
                                                'stf51r': 409244.827,
                                                'stf52r': 511.91968891,
                                                'stf53r': -88.188473075,
                                                'stf54r': -314.434069,
                                                'stf55r': 124387.13169,
                                                'stf56r': -482398.11302,
                                                'stf61r': -36973444.739,
                                                'stf62r': -384.5445086,
                                                'stf63r': 6719.495598,
                                                'stf64r': 25817.919161,
                                                'stf65r': -482398.11302,
                                                'stf66r': 45113254.126,
                                                'tc2': 1.1508350738,
                                                'tc3': 0.012738112556}}},
              'cs1_set2': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1094r8.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 7882.099999999999,
                                         'ang_spar_2': -3829.499999999999,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 26.83,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_solid.xml.tmp'},
                           'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                                'cmp11r': 8.6878954609e-07,
                                                'cmp12c': 0.0,
                                                'cmp12r': 0.0,
                                                'cmp13c': -4.9180730583e-07,
                                                'cmp13r': 0.0,
                                                'cmp14c': 7.2917599121e-07,
                                                'cmp14r': 0.0,
                                                'cmp15r': -4.9180730583e-07,
                                                'cmp16r': 7.2917599121e-07,
                                                'cmp21c': 0.0,
                                                'cmp21r': 0.0,
                                                'cmp22c': 0.00027162192945,
                                                'cmp22r': 3.9379621974e-06,
                                                'cmp23c': 0.0,
                                                'cmp23r': -1.0747935643e-05,
                                                'cmp24c': 0.0,
                                                'cmp24r': 9.9217524276e-06,
                                                'cmp25r': 0.0,
                                                'cmp26r': 0.0,
                                                'cmp31c': -4.9180730583e-07,
                                                'cmp31r': 0.0,
                                                'cmp32c': 0.0,
                                                'cmp32r': -1.0747935643e-05,
                                                'cmp33c': 5.7758156681e-05,
                                                'cmp33r': 0.0003372421267,
                                                'cmp34c': 9.5286858883e-07,
                                                'cmp34r': -0.00029847292879,
                                                'cmp35r': 0.0,
                                                'cmp36r': 0.0,
                                                'cmp41c': 7.2917599121e-07,
                                                'cmp41r': 0.0,
                                                'cmp42c': 0.0,
                                                'cmp42r': 9.9217524276e-06,
                                                'cmp43c': 9.5286858883e-07,
                                                'cmp43r': -0.00029847292879,
                                                'cmp44c': 8.4546245969e-07,
                                                'cmp44r': 0.00027162192945,
                                                'cmp45r': 0.0,
                                                'cmp46r': 0.0,
                                                'cmp51r': -4.9180730583e-07,
                                                'cmp52r': 0.0,
                                                'cmp53r': 0.0,
                                                'cmp54r': 0.0,
                                                'cmp55r': 5.7758156681e-05,
                                                'cmp56r': 9.5286858883e-07,
                                                'cmp61r': 7.2917599121e-07,
                                                'cmp62r': 0.0,
                                                'cmp63r': 0.0,
                                                'cmp64r': 0.0,
                                                'cmp65r': 9.5286858883e-07,
                                                'cmp66r': 8.4546245969e-07,
                                                'ea': 4774098.1646,
                                                'ei22': 17308.792358,
                                                'ei33': 1205526.0405,
                                                'ga22': 107897.42799,
                                                'ga33': 280006.76136,
                                                'gj': 3681.5878674,
                                                'mc2': 1.0027721065,
                                                'mc3': 0.026707103412,
                                                'mmoi1': 0.006197578454,
                                                'mmoi2': 6.4318592849e-05,
                                                'mmoi3': 0.0061332598612,
                                                'ms11': 0.033471831002,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00089393565197,
                                                'ms16': -0.033564618483,
                                                'ms21': 0.0,
                                                'ms22': 0.033471831002,
                                                'ms23': 0.0,
                                                'ms24': -0.00089393565197,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.033471831002,
                                                'ms34': 0.033564618483,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00089393565197,
                                                'ms43': 0.033564618483,
                                                'ms44': 0.039879116067,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00089393565197,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 8.9734363542e-05,
                                                'ms56': -0.00099311905661,
                                                'ms61': -0.033564618483,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00099311905661,
                                                'ms66': 0.039789381703,
                                                'mu': 0.033471831002,
                                                'sc2': 1.0988543134,
                                                'sc3': 0.036527803361,
                                                'stf11c': 4774098.1646,
                                                'stf11r': 4774098.1646,
                                                'stf12c': 0.0,
                                                'stf12r': 0.0,
                                                'stf13c': 110636.30469,
                                                'stf13r': 0.0,
                                                'stf14c': -4242151.2389,
                                                'stf14r': 0.0,
                                                'stf15r': 110636.30469,
                                                'stf16r': -4242151.2389,
                                                'stf21c': 0.0,
                                                'stf21r': 0.0,
                                                'stf22c': 3681.5878674,
                                                'stf22r': 279879.86959,
                                                'stf23c': 0.0,
                                                'stf23r': -4671.5261463,
                                                'stf24c': 0.0,
                                                'stf24r': -15356.723497,
                                                'stf25r': 0.0,
                                                'stf26r': 0.0,
                                                'stf31c': 110636.30469,
                                                'stf31r': 0.0,
                                                'stf32c': 0.0,
                                                'stf32r': -4671.5261463,
                                                'stf33c': 20205.505805,
                                                'stf33r': 108024.31976,
                                                'stf34c': -118191.56225,
                                                'stf34r': 118873.63031,
                                                'stf35r': 0.0,
                                                'stf36r': 0.0,
                                                'stf41c': -4242151.2389,
                                                'stf41r': 0.0,
                                                'stf42c': 0.0,
                                                'stf42r': -15356.723497,
                                                'stf43c': -118191.56225,
                                                'stf43r': 118873.63031,
                                                'stf44c': 4974668.9678,
                                                'stf44r': 134867.33666,
                                                'stf45r': 0.0,
                                                'stf46r': 0.0,
                                                'stf51r': 110636.30469,
                                                'stf52r': 0.0,
                                                'stf53r': 0.0,
                                                'stf54r': 0.0,
                                                'stf55r': 20205.505805,
                                                'stf56r': -118191.56225,
                                                'stf61r': -4242151.2389,
                                                'stf62r': 0.0,
                                                'stf63r': 0.0,
                                                'stf64r': 0.0,
                                                'stf65r': -118191.56225,
                                                'stf66r': 4974668.9678,
                                                'tc2': 0.88857645835,
                                                'tc3': 0.023174283576}}}},
 'design': {'cs_assignment': [{'cs': 'cs1',
                               'model': 'b2',
                               'region': 'segment1'}],
            'dim': 1,
            'file': 'beam_design.yml',
            'section_locations': [0, 26.83],
            'tool': 'gebt'},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'xscale': 26.83,
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'xscale': 26.83,
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'xscale': 26.83,
                   'ytype': 'float'}],
 'name': 'blade1'} 
INFO     [2023-07-12 11:02:54] io.readStructuralGlobalResponses :: reading global structural responses... 
INFO     [2023-07-12 11:02:54] core.runSGenomeDesignAnalysis :: running cs design d,fi analysis... 
DEBUG    [2023-07-12 11:02:54] core.runSGenomeDesignAnalysis :: set_name = set1 
INFO     [2023-07-12 11:02:54] core.runSGDesignAnalysisDF :: running cs dehomogenization/failure analysis: cs1_set1... 
DEBUG    [2023-07-12 11:02:54] core.runSGDesignAnalysisDF :: local variables:
{'analysis': {'analysis': 'd,fi',
              'input': {'load_case': {'data_form': 'file',
                                      'file_name': 'struct_resp.csv',
                                      'location_tags': 'coord',
                                      'location_value_types': 'float'}},
              'step': 'recovery',
              'type': 'cs'},
 'case_name_config_index': {},
 'case_select': [],
 'design_config_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'glb_resp_cases': --------------------
Location:
  coord = 0.0
Condition:

Displacement
  u1 =     0.000000e+00
  u2 =     0.000000e+00
  u3 =     0.000000e+00
Rotation (directional cosine)
  c11 =     1.000000e+00, c12 =     0.000000e+00, c13 =     0.000000e+00
  c21 =     0.000000e+00, c22 =     1.000000e+00, c23 =     0.000000e+00
  c31 =     0.000000e+00, c32 =     0.000000e+00, c33 =     1.000000e+00
Load
  f1 =     0.000000e+00
  f2 =     0.000000e+00
  f3 =     0.000000e+00
  m1 =     0.000000e+00
  m2 =     1.000000e+00
  m3 =     0.000000e+00
--------------------,
 'mdao_data': {},
 'model_config_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set1',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 70.5,
                                 'ang_spar_2': -67.5,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                        'cmp11r': 5.4844993952e-07,
                                        'cmp12c': 1.0598978638e-09,
                                        'cmp12r': 8.9235923897e-11,
                                        'cmp13c': -6.3876314212e-08,
                                        'cmp13r': -4.7643828627e-09,
                                        'cmp14c': 4.4880988512e-07,
                                        'cmp14r': 1.0598978638e-09,
                                        'cmp15r': -6.3876314212e-08,
                                        'cmp16r': 4.4880988512e-07,
                                        'cmp21c': 1.0598978638e-09,
                                        'cmp21r': 8.9235923897e-11,
                                        'cmp22c': 1.8627229409e-05,
                                        'cmp22r': 1.7667949994e-06,
                                        'cmp23c': 1.6150669739e-09,
                                        'cmp23r': -2.4678796711e-07,
                                        'cmp24c': -6.4868398022e-09,
                                        'cmp24r': 2.8947742359e-07,
                                        'cmp25r': -7.4760283647e-09,
                                        'cmp26r': -1.2065334963e-10,
                                        'cmp31c': -6.3876314212e-08,
                                        'cmp31r': -4.7643828627e-09,
                                        'cmp32c': 1.6150669739e-09,
                                        'cmp32r': -2.4678796711e-07,
                                        'cmp33c': 8.3947049682e-06,
                                        'cmp33r': 2.7325344108e-05,
                                        'cmp34c': 3.7413165037e-08,
                                        'cmp34r': -2.2054535366e-05,
                                        'cmp35r': -1.7459754752e-09,
                                        'cmp36r': 4.6260657341e-09,
                                        'cmp41c': 4.4880988512e-07,
                                        'cmp41r': 1.0598978638e-09,
                                        'cmp42c': -6.4868398022e-09,
                                        'cmp42r': 2.8947742359e-07,
                                        'cmp43c': 3.7413165037e-08,
                                        'cmp43r': -2.2054535366e-05,
                                        'cmp44c': 3.9040038704e-07,
                                        'cmp44r': 1.8627229409e-05,
                                        'cmp45r': 1.6150669739e-09,
                                        'cmp46r': -6.4868398022e-09,
                                        'cmp51r': -6.3876314212e-08,
                                        'cmp52r': -7.4760283647e-09,
                                        'cmp53r': -1.7459754752e-09,
                                        'cmp54r': 1.6150669739e-09,
                                        'cmp55r': 8.3947049682e-06,
                                        'cmp56r': 3.7413165037e-08,
                                        'cmp61r': 4.4880988512e-07,
                                        'cmp62r': -1.2065334963e-10,
                                        'cmp63r': 4.6260657341e-09,
                                        'cmp64r': -6.4868398022e-09,
                                        'cmp65r': 3.7413165037e-08,
                                        'cmp66r': 3.9040038704e-07,
                                        'ea': 32123231.314,
                                        'ei22': 119120.22795,
                                        'ei33': 2562620.733,
                                        'ga22': 562248.80781,
                                        'ga33': 835685.78392,
                                        'gj': 53684.849101,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1839944031,
                                        'sc3': 0.015540551804,
                                        'stf11c': 32127277.521,
                                        'stf11r': 32127277.572,
                                        'stf12c': -14739.357462,
                                        'stf12r': 10.197839899,
                                        'stf13c': 409244.80607,
                                        'stf13r': -204.38975585,
                                        'stf14c': -36973443.08,
                                        'stf14r': -14981.512851,
                                        'stf15r': 409244.827,
                                        'stf16r': -36973444.739,
                                        'stf21c': -14739.357462,
                                        'stf21r': 10.197839899,
                                        'stf22c': 53691.923587,
                                        'stf22r': 569898.38406,
                                        'stf23c': -202.06359968,
                                        'stf23r': -45085.363622,
                                        'stf24c': 17856.078801,
                                        'stf24r': -62237.532434,
                                        'stf25r': 511.91968891,
                                        'stf26r': -384.5445086,
                                        'stf31c': 409244.80607,
                                        'stf31r': -204.38975585,
                                        'stf32c': -202.06359968,
                                        'stf32r': -45085.363622,
                                        'stf33c': 124386.66909,
                                        'stf33r': 828054.88201,
                                        'stf34c': -482397.38069,
                                        'stf34r': 981115.35652,
                                        'stf35r': -88.188473075,
                                        'stf36r': 6719.495598,
                                        'stf41c': -36973443.08,
                                        'stf41r': -14981.512851,
                                        'stf42c': 17856.078801,
                                        'stf42r': -62237.532434,
                                        'stf43c': -482397.38069,
                                        'stf43r': 981115.35652,
                                        'stf44c': 45113199.598,
                                        'stf44r': 1216297.0163,
                                        'stf45r': -314.434069,
                                        'stf46r': 25817.919161,
                                        'stf51r': 409244.827,
                                        'stf52r': 511.91968891,
                                        'stf53r': -88.188473075,
                                        'stf54r': -314.434069,
                                        'stf55r': 124387.13169,
                                        'stf56r': -482398.11302,
                                        'stf61r': -36973444.739,
                                        'stf62r': -384.5445086,
                                        'stf63r': 6719.495598,
                                        'stf64r': 25817.919161,
                                        'stf65r': -482398.11302,
                                        'stf66r': 45113254.126,
                                        'tc2': 1.1508350738,
                                        'tc3': 0.012738112556}}},
                  {'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 7882.099999999999,
                                 'ang_spar_2': -3829.499999999999,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 26.83,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'},
                   'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                        'cmp11r': 8.6878954609e-07,
                                        'cmp12c': 0.0,
                                        'cmp12r': 0.0,
                                        'cmp13c': -4.9180730583e-07,
                                        'cmp13r': 0.0,
                                        'cmp14c': 7.2917599121e-07,
                                        'cmp14r': 0.0,
                                        'cmp15r': -4.9180730583e-07,
                                        'cmp16r': 7.2917599121e-07,
                                        'cmp21c': 0.0,
                                        'cmp21r': 0.0,
                                        'cmp22c': 0.00027162192945,
                                        'cmp22r': 3.9379621974e-06,
                                        'cmp23c': 0.0,
                                        'cmp23r': -1.0747935643e-05,
                                        'cmp24c': 0.0,
                                        'cmp24r': 9.9217524276e-06,
                                        'cmp25r': 0.0,
                                        'cmp26r': 0.0,
                                        'cmp31c': -4.9180730583e-07,
                                        'cmp31r': 0.0,
                                        'cmp32c': 0.0,
                                        'cmp32r': -1.0747935643e-05,
                                        'cmp33c': 5.7758156681e-05,
                                        'cmp33r': 0.0003372421267,
                                        'cmp34c': 9.5286858883e-07,
                                        'cmp34r': -0.00029847292879,
                                        'cmp35r': 0.0,
                                        'cmp36r': 0.0,
                                        'cmp41c': 7.2917599121e-07,
                                        'cmp41r': 0.0,
                                        'cmp42c': 0.0,
                                        'cmp42r': 9.9217524276e-06,
                                        'cmp43c': 9.5286858883e-07,
                                        'cmp43r': -0.00029847292879,
                                        'cmp44c': 8.4546245969e-07,
                                        'cmp44r': 0.00027162192945,
                                        'cmp45r': 0.0,
                                        'cmp46r': 0.0,
                                        'cmp51r': -4.9180730583e-07,
                                        'cmp52r': 0.0,
                                        'cmp53r': 0.0,
                                        'cmp54r': 0.0,
                                        'cmp55r': 5.7758156681e-05,
                                        'cmp56r': 9.5286858883e-07,
                                        'cmp61r': 7.2917599121e-07,
                                        'cmp62r': 0.0,
                                        'cmp63r': 0.0,
                                        'cmp64r': 0.0,
                                        'cmp65r': 9.5286858883e-07,
                                        'cmp66r': 8.4546245969e-07,
                                        'ea': 4774098.1646,
                                        'ei22': 17308.792358,
                                        'ei33': 1205526.0405,
                                        'ga22': 107897.42799,
                                        'ga33': 280006.76136,
                                        'gj': 3681.5878674,
                                        'mc2': 1.0027721065,
                                        'mc3': 0.026707103412,
                                        'mmoi1': 0.006197578454,
                                        'mmoi2': 6.4318592849e-05,
                                        'mmoi3': 0.0061332598612,
                                        'ms11': 0.033471831002,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00089393565197,
                                        'ms16': -0.033564618483,
                                        'ms21': 0.0,
                                        'ms22': 0.033471831002,
                                        'ms23': 0.0,
                                        'ms24': -0.00089393565197,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.033471831002,
                                        'ms34': 0.033564618483,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00089393565197,
                                        'ms43': 0.033564618483,
                                        'ms44': 0.039879116067,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00089393565197,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 8.9734363542e-05,
                                        'ms56': -0.00099311905661,
                                        'ms61': -0.033564618483,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00099311905661,
                                        'ms66': 0.039789381703,
                                        'mu': 0.033471831002,
                                        'sc2': 1.0988543134,
                                        'sc3': 0.036527803361,
                                        'stf11c': 4774098.1646,
                                        'stf11r': 4774098.1646,
                                        'stf12c': 0.0,
                                        'stf12r': 0.0,
                                        'stf13c': 110636.30469,
                                        'stf13r': 0.0,
                                        'stf14c': -4242151.2389,
                                        'stf14r': 0.0,
                                        'stf15r': 110636.30469,
                                        'stf16r': -4242151.2389,
                                        'stf21c': 0.0,
                                        'stf21r': 0.0,
                                        'stf22c': 3681.5878674,
                                        'stf22r': 279879.86959,
                                        'stf23c': 0.0,
                                        'stf23r': -4671.5261463,
                                        'stf24c': 0.0,
                                        'stf24r': -15356.723497,
                                        'stf25r': 0.0,
                                        'stf26r': 0.0,
                                        'stf31c': 110636.30469,
                                        'stf31r': 0.0,
                                        'stf32c': 0.0,
                                        'stf32r': -4671.5261463,
                                        'stf33c': 20205.505805,
                                        'stf33r': 108024.31976,
                                        'stf34c': -118191.56225,
                                        'stf34r': 118873.63031,
                                        'stf35r': 0.0,
                                        'stf36r': 0.0,
                                        'stf41c': -4242151.2389,
                                        'stf41r': 0.0,
                                        'stf42c': 0.0,
                                        'stf42r': -15356.723497,
                                        'stf43c': -118191.56225,
                                        'stf43r': 118873.63031,
                                        'stf44c': 4974668.9678,
                                        'stf44r': 134867.33666,
                                        'stf45r': 0.0,
                                        'stf46r': 0.0,
                                        'stf51r': 110636.30469,
                                        'stf52r': 0.0,
                                        'stf53r': 0.0,
                                        'stf54r': 0.0,
                                        'stf55r': 20205.505805,
                                        'stf56r': -118191.56225,
                                        'stf61r': -4242151.2389,
                                        'stf62r': 0.0,
                                        'stf63r': 0.0,
                                        'stf64r': 0.0,
                                        'stf65r': -118191.56225,
                                        'stf66r': 4974668.9678,
                                        'tc2': 0.88857645835,
                                        'tc3': 0.023174283576}}}]},
 'sgs_data': {'cs1_set1': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1095.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 70.5,
                                         'ang_spar_2': -67.5,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_gbox_uni.xml.tmp'},
                           'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                                'cmp11r': 5.4844993952e-07,
                                                'cmp12c': 1.0598978638e-09,
                                                'cmp12r': 8.9235923897e-11,
                                                'cmp13c': -6.3876314212e-08,
                                                'cmp13r': -4.7643828627e-09,
                                                'cmp14c': 4.4880988512e-07,
                                                'cmp14r': 1.0598978638e-09,
                                                'cmp15r': -6.3876314212e-08,
                                                'cmp16r': 4.4880988512e-07,
                                                'cmp21c': 1.0598978638e-09,
                                                'cmp21r': 8.9235923897e-11,
                                                'cmp22c': 1.8627229409e-05,
                                                'cmp22r': 1.7667949994e-06,
                                                'cmp23c': 1.6150669739e-09,
                                                'cmp23r': -2.4678796711e-07,
                                                'cmp24c': -6.4868398022e-09,
                                                'cmp24r': 2.8947742359e-07,
                                                'cmp25r': -7.4760283647e-09,
                                                'cmp26r': -1.2065334963e-10,
                                                'cmp31c': -6.3876314212e-08,
                                                'cmp31r': -4.7643828627e-09,
                                                'cmp32c': 1.6150669739e-09,
                                                'cmp32r': -2.4678796711e-07,
                                                'cmp33c': 8.3947049682e-06,
                                                'cmp33r': 2.7325344108e-05,
                                                'cmp34c': 3.7413165037e-08,
                                                'cmp34r': -2.2054535366e-05,
                                                'cmp35r': -1.7459754752e-09,
                                                'cmp36r': 4.6260657341e-09,
                                                'cmp41c': 4.4880988512e-07,
                                                'cmp41r': 1.0598978638e-09,
                                                'cmp42c': -6.4868398022e-09,
                                                'cmp42r': 2.8947742359e-07,
                                                'cmp43c': 3.7413165037e-08,
                                                'cmp43r': -2.2054535366e-05,
                                                'cmp44c': 3.9040038704e-07,
                                                'cmp44r': 1.8627229409e-05,
                                                'cmp45r': 1.6150669739e-09,
                                                'cmp46r': -6.4868398022e-09,
                                                'cmp51r': -6.3876314212e-08,
                                                'cmp52r': -7.4760283647e-09,
                                                'cmp53r': -1.7459754752e-09,
                                                'cmp54r': 1.6150669739e-09,
                                                'cmp55r': 8.3947049682e-06,
                                                'cmp56r': 3.7413165037e-08,
                                                'cmp61r': 4.4880988512e-07,
                                                'cmp62r': -1.2065334963e-10,
                                                'cmp63r': 4.6260657341e-09,
                                                'cmp64r': -6.4868398022e-09,
                                                'cmp65r': 3.7413165037e-08,
                                                'cmp66r': 3.9040038704e-07,
                                                'ea': 32123231.314,
                                                'ei22': 119120.22795,
                                                'ei33': 2562620.733,
                                                'ga22': 562248.80781,
                                                'ga33': 835685.78392,
                                                'gj': 53684.849101,
                                                'mc2': 1.1494335098,
                                                'mc3': 0.012439183019,
                                                'mmoi1': 0.0072632486361,
                                                'mmoi2': 0.00026525481038,
                                                'mmoi3': 0.0069979938258,
                                                'ms11': 0.075463162134,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00093870008494,
                                                'ms16': -0.086739887313,
                                                'ms21': 0.0,
                                                'ms22': 0.075463162134,
                                                'ms23': 0.0,
                                                'ms24': -0.00093870008494,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.075463162134,
                                                'ms34': 0.086739887313,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00093870008494,
                                                'ms43': 0.086739887313,
                                                'ms44': 0.10697665841,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00093870008494,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 0.00027700892854,
                                                'ms56': -0.00110180938,
                                                'ms61': -0.086739887313,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00110180938,
                                                'ms66': 0.10669964949,
                                                'mu': 0.075463162134,
                                                'sc2': 1.1839944031,
                                                'sc3': 0.015540551804,
                                                'stf11c': 32127277.521,
                                                'stf11r': 32127277.572,
                                                'stf12c': -14739.357462,
                                                'stf12r': 10.197839899,
                                                'stf13c': 409244.80607,
                                                'stf13r': -204.38975585,
                                                'stf14c': -36973443.08,
                                                'stf14r': -14981.512851,
                                                'stf15r': 409244.827,
                                                'stf16r': -36973444.739,
                                                'stf21c': -14739.357462,
                                                'stf21r': 10.197839899,
                                                'stf22c': 53691.923587,
                                                'stf22r': 569898.38406,
                                                'stf23c': -202.06359968,
                                                'stf23r': -45085.363622,
                                                'stf24c': 17856.078801,
                                                'stf24r': -62237.532434,
                                                'stf25r': 511.91968891,
                                                'stf26r': -384.5445086,
                                                'stf31c': 409244.80607,
                                                'stf31r': -204.38975585,
                                                'stf32c': -202.06359968,
                                                'stf32r': -45085.363622,
                                                'stf33c': 124386.66909,
                                                'stf33r': 828054.88201,
                                                'stf34c': -482397.38069,
                                                'stf34r': 981115.35652,
                                                'stf35r': -88.188473075,
                                                'stf36r': 6719.495598,
                                                'stf41c': -36973443.08,
                                                'stf41r': -14981.512851,
                                                'stf42c': 17856.078801,
                                                'stf42r': -62237.532434,
                                                'stf43c': -482397.38069,
                                                'stf43r': 981115.35652,
                                                'stf44c': 45113199.598,
                                                'stf44r': 1216297.0163,
                                                'stf45r': -314.434069,
                                                'stf46r': 25817.919161,
                                                'stf51r': 409244.827,
                                                'stf52r': 511.91968891,
                                                'stf53r': -88.188473075,
                                                'stf54r': -314.434069,
                                                'stf55r': 124387.13169,
                                                'stf56r': -482398.11302,
                                                'stf61r': -36973444.739,
                                                'stf62r': -384.5445086,
                                                'stf63r': 6719.495598,
                                                'stf64r': 25817.919161,
                                                'stf65r': -482398.11302,
                                                'stf66r': 45113254.126,
                                                'tc2': 1.1508350738,
                                                'tc3': 0.012738112556}}},
              'cs1_set2': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1094r8.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 7882.099999999999,
                                         'ang_spar_2': -3829.499999999999,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 26.83,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_solid.xml.tmp'},
                           'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                                'cmp11r': 8.6878954609e-07,
                                                'cmp12c': 0.0,
                                                'cmp12r': 0.0,
                                                'cmp13c': -4.9180730583e-07,
                                                'cmp13r': 0.0,
                                                'cmp14c': 7.2917599121e-07,
                                                'cmp14r': 0.0,
                                                'cmp15r': -4.9180730583e-07,
                                                'cmp16r': 7.2917599121e-07,
                                                'cmp21c': 0.0,
                                                'cmp21r': 0.0,
                                                'cmp22c': 0.00027162192945,
                                                'cmp22r': 3.9379621974e-06,
                                                'cmp23c': 0.0,
                                                'cmp23r': -1.0747935643e-05,
                                                'cmp24c': 0.0,
                                                'cmp24r': 9.9217524276e-06,
                                                'cmp25r': 0.0,
                                                'cmp26r': 0.0,
                                                'cmp31c': -4.9180730583e-07,
                                                'cmp31r': 0.0,
                                                'cmp32c': 0.0,
                                                'cmp32r': -1.0747935643e-05,
                                                'cmp33c': 5.7758156681e-05,
                                                'cmp33r': 0.0003372421267,
                                                'cmp34c': 9.5286858883e-07,
                                                'cmp34r': -0.00029847292879,
                                                'cmp35r': 0.0,
                                                'cmp36r': 0.0,
                                                'cmp41c': 7.2917599121e-07,
                                                'cmp41r': 0.0,
                                                'cmp42c': 0.0,
                                                'cmp42r': 9.9217524276e-06,
                                                'cmp43c': 9.5286858883e-07,
                                                'cmp43r': -0.00029847292879,
                                                'cmp44c': 8.4546245969e-07,
                                                'cmp44r': 0.00027162192945,
                                                'cmp45r': 0.0,
                                                'cmp46r': 0.0,
                                                'cmp51r': -4.9180730583e-07,
                                                'cmp52r': 0.0,
                                                'cmp53r': 0.0,
                                                'cmp54r': 0.0,
                                                'cmp55r': 5.7758156681e-05,
                                                'cmp56r': 9.5286858883e-07,
                                                'cmp61r': 7.2917599121e-07,
                                                'cmp62r': 0.0,
                                                'cmp63r': 0.0,
                                                'cmp64r': 0.0,
                                                'cmp65r': 9.5286858883e-07,
                                                'cmp66r': 8.4546245969e-07,
                                                'ea': 4774098.1646,
                                                'ei22': 17308.792358,
                                                'ei33': 1205526.0405,
                                                'ga22': 107897.42799,
                                                'ga33': 280006.76136,
                                                'gj': 3681.5878674,
                                                'mc2': 1.0027721065,
                                                'mc3': 0.026707103412,
                                                'mmoi1': 0.006197578454,
                                                'mmoi2': 6.4318592849e-05,
                                                'mmoi3': 0.0061332598612,
                                                'ms11': 0.033471831002,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00089393565197,
                                                'ms16': -0.033564618483,
                                                'ms21': 0.0,
                                                'ms22': 0.033471831002,
                                                'ms23': 0.0,
                                                'ms24': -0.00089393565197,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.033471831002,
                                                'ms34': 0.033564618483,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00089393565197,
                                                'ms43': 0.033564618483,
                                                'ms44': 0.039879116067,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00089393565197,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 8.9734363542e-05,
                                                'ms56': -0.00099311905661,
                                                'ms61': -0.033564618483,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00099311905661,
                                                'ms66': 0.039789381703,
                                                'mu': 0.033471831002,
                                                'sc2': 1.0988543134,
                                                'sc3': 0.036527803361,
                                                'stf11c': 4774098.1646,
                                                'stf11r': 4774098.1646,
                                                'stf12c': 0.0,
                                                'stf12r': 0.0,
                                                'stf13c': 110636.30469,
                                                'stf13r': 0.0,
                                                'stf14c': -4242151.2389,
                                                'stf14r': 0.0,
                                                'stf15r': 110636.30469,
                                                'stf16r': -4242151.2389,
                                                'stf21c': 0.0,
                                                'stf21r': 0.0,
                                                'stf22c': 3681.5878674,
                                                'stf22r': 279879.86959,
                                                'stf23c': 0.0,
                                                'stf23r': -4671.5261463,
                                                'stf24c': 0.0,
                                                'stf24r': -15356.723497,
                                                'stf25r': 0.0,
                                                'stf26r': 0.0,
                                                'stf31c': 110636.30469,
                                                'stf31r': 0.0,
                                                'stf32c': 0.0,
                                                'stf32r': -4671.5261463,
                                                'stf33c': 20205.505805,
                                                'stf33r': 108024.31976,
                                                'stf34c': -118191.56225,
                                                'stf34r': 118873.63031,
                                                'stf35r': 0.0,
                                                'stf36r': 0.0,
                                                'stf41c': -4242151.2389,
                                                'stf41r': 0.0,
                                                'stf42c': 0.0,
                                                'stf42r': -15356.723497,
                                                'stf43c': -118191.56225,
                                                'stf43r': 118873.63031,
                                                'stf44c': 4974668.9678,
                                                'stf44r': 134867.33666,
                                                'stf45r': 0.0,
                                                'stf46r': 0.0,
                                                'stf51r': 110636.30469,
                                                'stf52r': 0.0,
                                                'stf53r': 0.0,
                                                'stf54r': 0.0,
                                                'stf55r': 20205.505805,
                                                'stf56r': -118191.56225,
                                                'stf61r': -4242151.2389,
                                                'stf62r': 0.0,
                                                'stf63r': 0.0,
                                                'stf64r': 0.0,
                                                'stf65r': -118191.56225,
                                                'stf66r': 4974668.9678,
                                                'tc2': 0.88857645835,
                                                'tc3': 0.023174283576}}}}} 
DEBUG    [2023-07-12 11:02:54] core.runSGDesignAnalysisDF :: ls_resp_cases:
[{'coord': 0.0,
  'response': Displacement
  u1 =     0.000000e+00
  u2 =     0.000000e+00
  u3 =     0.000000e+00
Rotation (directional cosine)
  c11 =     1.000000e+00, c12 =     0.000000e+00, c13 =     0.000000e+00
  c21 =     0.000000e+00, c22 =     1.000000e+00, c23 =     0.000000e+00
  c31 =     0.000000e+00, c32 =     0.000000e+00, c33 =     1.000000e+00
Load
  f1 =     0.000000e+00
  f2 =     0.000000e+00
  f3 =     0.000000e+00
  m1 =     0.000000e+00
  m2 =     1.000000e+00
  m3 =     0.000000e+00}] 
DEBUG    [2023-07-12 11:02:54] core.runSGDesignAnalysisDF :: case_name = case1 
INFO     [2023-07-12 11:02:54] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:02:54] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:02:54] execu.run :: prevabs -i cs1_set1.xml -vabs -d 
INFO     [2023-07-12 11:02:54] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:02:54] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:03:02] presg2d.solve :: reading results cs1_set1.sg... 
INFO     [2023-07-12 11:03:02] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:03:02] main.buildSG :: building 2D SG: cs1_set1.xml... 
CRITICAL [2023-07-12 11:03:02] execu.run :: prevabs -i cs1_set1.xml -vabs -fi 
INFO     [2023-07-12 11:03:02] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:03:02] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:03:03] presg2d.solve :: reading results cs1_set1.sg.fi... 
INFO     [2023-07-12 11:03:03] core.runSGenomeDesignAnalysis :: running cs design d,fi analysis... 
DEBUG    [2023-07-12 11:03:03] core.runSGenomeDesignAnalysis :: set_name = set2 
INFO     [2023-07-12 11:03:03] core.runSGDesignAnalysisDF :: running cs dehomogenization/failure analysis: cs1_set2... 
DEBUG    [2023-07-12 11:03:03] core.runSGDesignAnalysisDF :: local variables:
{'analysis': {'analysis': 'd,fi',
              'input': {'load_case': {'data_form': 'file',
                                      'file_name': 'struct_resp.csv',
                                      'location_tags': 'coord',
                                      'location_value_types': 'float'}},
              'step': 'recovery',
              'type': 'cs'},
 'case_name_config_index': {},
 'case_select': [],
 'design_config_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'glb_resp_cases': --------------------
Location:
  coord = 26.83
Condition:

Displacement
  u1 =    -1.713801e-06
  u2 =     1.326533e-05
  u3 =    -3.021506e-03
Rotation (directional cosine)
  c11 =     1.000000e+00, c12 =     0.000000e+00, c13 =     0.000000e+00
  c21 =     0.000000e+00, c22 =     1.000000e+00, c23 =     0.000000e+00
  c31 =     0.000000e+00, c32 =     0.000000e+00, c33 =     1.000000e+00
Load
  f1 =     0.000000e+00
  f2 =     0.000000e+00
  f3 =     0.000000e+00
  m1 =     0.000000e+00
  m2 =     1.000000e+00
  m3 =     0.000000e+00
--------------------,
 'mdao_data': {'cs1_set1': {'sr_min_case1': 3277.5931583915954},
               'sr_min_case1': 3277.5931583915954},
 'model_config_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set2',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 70.5,
                                 'ang_spar_2': -67.5,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                        'cmp11r': 5.4844993952e-07,
                                        'cmp12c': 1.0598978638e-09,
                                        'cmp12r': 8.9235923897e-11,
                                        'cmp13c': -6.3876314212e-08,
                                        'cmp13r': -4.7643828627e-09,
                                        'cmp14c': 4.4880988512e-07,
                                        'cmp14r': 1.0598978638e-09,
                                        'cmp15r': -6.3876314212e-08,
                                        'cmp16r': 4.4880988512e-07,
                                        'cmp21c': 1.0598978638e-09,
                                        'cmp21r': 8.9235923897e-11,
                                        'cmp22c': 1.8627229409e-05,
                                        'cmp22r': 1.7667949994e-06,
                                        'cmp23c': 1.6150669739e-09,
                                        'cmp23r': -2.4678796711e-07,
                                        'cmp24c': -6.4868398022e-09,
                                        'cmp24r': 2.8947742359e-07,
                                        'cmp25r': -7.4760283647e-09,
                                        'cmp26r': -1.2065334963e-10,
                                        'cmp31c': -6.3876314212e-08,
                                        'cmp31r': -4.7643828627e-09,
                                        'cmp32c': 1.6150669739e-09,
                                        'cmp32r': -2.4678796711e-07,
                                        'cmp33c': 8.3947049682e-06,
                                        'cmp33r': 2.7325344108e-05,
                                        'cmp34c': 3.7413165037e-08,
                                        'cmp34r': -2.2054535366e-05,
                                        'cmp35r': -1.7459754752e-09,
                                        'cmp36r': 4.6260657341e-09,
                                        'cmp41c': 4.4880988512e-07,
                                        'cmp41r': 1.0598978638e-09,
                                        'cmp42c': -6.4868398022e-09,
                                        'cmp42r': 2.8947742359e-07,
                                        'cmp43c': 3.7413165037e-08,
                                        'cmp43r': -2.2054535366e-05,
                                        'cmp44c': 3.9040038704e-07,
                                        'cmp44r': 1.8627229409e-05,
                                        'cmp45r': 1.6150669739e-09,
                                        'cmp46r': -6.4868398022e-09,
                                        'cmp51r': -6.3876314212e-08,
                                        'cmp52r': -7.4760283647e-09,
                                        'cmp53r': -1.7459754752e-09,
                                        'cmp54r': 1.6150669739e-09,
                                        'cmp55r': 8.3947049682e-06,
                                        'cmp56r': 3.7413165037e-08,
                                        'cmp61r': 4.4880988512e-07,
                                        'cmp62r': -1.2065334963e-10,
                                        'cmp63r': 4.6260657341e-09,
                                        'cmp64r': -6.4868398022e-09,
                                        'cmp65r': 3.7413165037e-08,
                                        'cmp66r': 3.9040038704e-07,
                                        'ea': 32123231.314,
                                        'ei22': 119120.22795,
                                        'ei33': 2562620.733,
                                        'ga22': 562248.80781,
                                        'ga33': 835685.78392,
                                        'gj': 53684.849101,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1839944031,
                                        'sc3': 0.015540551804,
                                        'stf11c': 32127277.521,
                                        'stf11r': 32127277.572,
                                        'stf12c': -14739.357462,
                                        'stf12r': 10.197839899,
                                        'stf13c': 409244.80607,
                                        'stf13r': -204.38975585,
                                        'stf14c': -36973443.08,
                                        'stf14r': -14981.512851,
                                        'stf15r': 409244.827,
                                        'stf16r': -36973444.739,
                                        'stf21c': -14739.357462,
                                        'stf21r': 10.197839899,
                                        'stf22c': 53691.923587,
                                        'stf22r': 569898.38406,
                                        'stf23c': -202.06359968,
                                        'stf23r': -45085.363622,
                                        'stf24c': 17856.078801,
                                        'stf24r': -62237.532434,
                                        'stf25r': 511.91968891,
                                        'stf26r': -384.5445086,
                                        'stf31c': 409244.80607,
                                        'stf31r': -204.38975585,
                                        'stf32c': -202.06359968,
                                        'stf32r': -45085.363622,
                                        'stf33c': 124386.66909,
                                        'stf33r': 828054.88201,
                                        'stf34c': -482397.38069,
                                        'stf34r': 981115.35652,
                                        'stf35r': -88.188473075,
                                        'stf36r': 6719.495598,
                                        'stf41c': -36973443.08,
                                        'stf41r': -14981.512851,
                                        'stf42c': 17856.078801,
                                        'stf42r': -62237.532434,
                                        'stf43c': -482397.38069,
                                        'stf43r': 981115.35652,
                                        'stf44c': 45113199.598,
                                        'stf44r': 1216297.0163,
                                        'stf45r': -314.434069,
                                        'stf46r': 25817.919161,
                                        'stf51r': 409244.827,
                                        'stf52r': 511.91968891,
                                        'stf53r': -88.188473075,
                                        'stf54r': -314.434069,
                                        'stf55r': 124387.13169,
                                        'stf56r': -482398.11302,
                                        'stf61r': -36973444.739,
                                        'stf62r': -384.5445086,
                                        'stf63r': 6719.495598,
                                        'stf64r': 25817.919161,
                                        'stf65r': -482398.11302,
                                        'stf66r': 45113254.126,
                                        'tc2': 1.1508350738,
                                        'tc3': 0.012738112556}}},
                  {'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 7882.099999999999,
                                 'ang_spar_2': -3829.499999999999,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 26.83,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'},
                   'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                        'cmp11r': 8.6878954609e-07,
                                        'cmp12c': 0.0,
                                        'cmp12r': 0.0,
                                        'cmp13c': -4.9180730583e-07,
                                        'cmp13r': 0.0,
                                        'cmp14c': 7.2917599121e-07,
                                        'cmp14r': 0.0,
                                        'cmp15r': -4.9180730583e-07,
                                        'cmp16r': 7.2917599121e-07,
                                        'cmp21c': 0.0,
                                        'cmp21r': 0.0,
                                        'cmp22c': 0.00027162192945,
                                        'cmp22r': 3.9379621974e-06,
                                        'cmp23c': 0.0,
                                        'cmp23r': -1.0747935643e-05,
                                        'cmp24c': 0.0,
                                        'cmp24r': 9.9217524276e-06,
                                        'cmp25r': 0.0,
                                        'cmp26r': 0.0,
                                        'cmp31c': -4.9180730583e-07,
                                        'cmp31r': 0.0,
                                        'cmp32c': 0.0,
                                        'cmp32r': -1.0747935643e-05,
                                        'cmp33c': 5.7758156681e-05,
                                        'cmp33r': 0.0003372421267,
                                        'cmp34c': 9.5286858883e-07,
                                        'cmp34r': -0.00029847292879,
                                        'cmp35r': 0.0,
                                        'cmp36r': 0.0,
                                        'cmp41c': 7.2917599121e-07,
                                        'cmp41r': 0.0,
                                        'cmp42c': 0.0,
                                        'cmp42r': 9.9217524276e-06,
                                        'cmp43c': 9.5286858883e-07,
                                        'cmp43r': -0.00029847292879,
                                        'cmp44c': 8.4546245969e-07,
                                        'cmp44r': 0.00027162192945,
                                        'cmp45r': 0.0,
                                        'cmp46r': 0.0,
                                        'cmp51r': -4.9180730583e-07,
                                        'cmp52r': 0.0,
                                        'cmp53r': 0.0,
                                        'cmp54r': 0.0,
                                        'cmp55r': 5.7758156681e-05,
                                        'cmp56r': 9.5286858883e-07,
                                        'cmp61r': 7.2917599121e-07,
                                        'cmp62r': 0.0,
                                        'cmp63r': 0.0,
                                        'cmp64r': 0.0,
                                        'cmp65r': 9.5286858883e-07,
                                        'cmp66r': 8.4546245969e-07,
                                        'ea': 4774098.1646,
                                        'ei22': 17308.792358,
                                        'ei33': 1205526.0405,
                                        'ga22': 107897.42799,
                                        'ga33': 280006.76136,
                                        'gj': 3681.5878674,
                                        'mc2': 1.0027721065,
                                        'mc3': 0.026707103412,
                                        'mmoi1': 0.006197578454,
                                        'mmoi2': 6.4318592849e-05,
                                        'mmoi3': 0.0061332598612,
                                        'ms11': 0.033471831002,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00089393565197,
                                        'ms16': -0.033564618483,
                                        'ms21': 0.0,
                                        'ms22': 0.033471831002,
                                        'ms23': 0.0,
                                        'ms24': -0.00089393565197,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.033471831002,
                                        'ms34': 0.033564618483,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00089393565197,
                                        'ms43': 0.033564618483,
                                        'ms44': 0.039879116067,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00089393565197,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 8.9734363542e-05,
                                        'ms56': -0.00099311905661,
                                        'ms61': -0.033564618483,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00099311905661,
                                        'ms66': 0.039789381703,
                                        'mu': 0.033471831002,
                                        'sc2': 1.0988543134,
                                        'sc3': 0.036527803361,
                                        'stf11c': 4774098.1646,
                                        'stf11r': 4774098.1646,
                                        'stf12c': 0.0,
                                        'stf12r': 0.0,
                                        'stf13c': 110636.30469,
                                        'stf13r': 0.0,
                                        'stf14c': -4242151.2389,
                                        'stf14r': 0.0,
                                        'stf15r': 110636.30469,
                                        'stf16r': -4242151.2389,
                                        'stf21c': 0.0,
                                        'stf21r': 0.0,
                                        'stf22c': 3681.5878674,
                                        'stf22r': 279879.86959,
                                        'stf23c': 0.0,
                                        'stf23r': -4671.5261463,
                                        'stf24c': 0.0,
                                        'stf24r': -15356.723497,
                                        'stf25r': 0.0,
                                        'stf26r': 0.0,
                                        'stf31c': 110636.30469,
                                        'stf31r': 0.0,
                                        'stf32c': 0.0,
                                        'stf32r': -4671.5261463,
                                        'stf33c': 20205.505805,
                                        'stf33r': 108024.31976,
                                        'stf34c': -118191.56225,
                                        'stf34r': 118873.63031,
                                        'stf35r': 0.0,
                                        'stf36r': 0.0,
                                        'stf41c': -4242151.2389,
                                        'stf41r': 0.0,
                                        'stf42c': 0.0,
                                        'stf42r': -15356.723497,
                                        'stf43c': -118191.56225,
                                        'stf43r': 118873.63031,
                                        'stf44c': 4974668.9678,
                                        'stf44r': 134867.33666,
                                        'stf45r': 0.0,
                                        'stf46r': 0.0,
                                        'stf51r': 110636.30469,
                                        'stf52r': 0.0,
                                        'stf53r': 0.0,
                                        'stf54r': 0.0,
                                        'stf55r': 20205.505805,
                                        'stf56r': -118191.56225,
                                        'stf61r': -4242151.2389,
                                        'stf62r': 0.0,
                                        'stf63r': 0.0,
                                        'stf64r': 0.0,
                                        'stf65r': -118191.56225,
                                        'stf66r': 4974668.9678,
                                        'tc2': 0.88857645835,
                                        'tc3': 0.023174283576}}}]},
 'sgs_data': {'cs1_set1': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1095.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 70.5,
                                         'ang_spar_2': -67.5,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 0,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_gbox_uni.xml.tmp'},
                           'property': {'md1': {'cmp11c': 5.4844993952e-07,
                                                'cmp11r': 5.4844993952e-07,
                                                'cmp12c': 1.0598978638e-09,
                                                'cmp12r': 8.9235923897e-11,
                                                'cmp13c': -6.3876314212e-08,
                                                'cmp13r': -4.7643828627e-09,
                                                'cmp14c': 4.4880988512e-07,
                                                'cmp14r': 1.0598978638e-09,
                                                'cmp15r': -6.3876314212e-08,
                                                'cmp16r': 4.4880988512e-07,
                                                'cmp21c': 1.0598978638e-09,
                                                'cmp21r': 8.9235923897e-11,
                                                'cmp22c': 1.8627229409e-05,
                                                'cmp22r': 1.7667949994e-06,
                                                'cmp23c': 1.6150669739e-09,
                                                'cmp23r': -2.4678796711e-07,
                                                'cmp24c': -6.4868398022e-09,
                                                'cmp24r': 2.8947742359e-07,
                                                'cmp25r': -7.4760283647e-09,
                                                'cmp26r': -1.2065334963e-10,
                                                'cmp31c': -6.3876314212e-08,
                                                'cmp31r': -4.7643828627e-09,
                                                'cmp32c': 1.6150669739e-09,
                                                'cmp32r': -2.4678796711e-07,
                                                'cmp33c': 8.3947049682e-06,
                                                'cmp33r': 2.7325344108e-05,
                                                'cmp34c': 3.7413165037e-08,
                                                'cmp34r': -2.2054535366e-05,
                                                'cmp35r': -1.7459754752e-09,
                                                'cmp36r': 4.6260657341e-09,
                                                'cmp41c': 4.4880988512e-07,
                                                'cmp41r': 1.0598978638e-09,
                                                'cmp42c': -6.4868398022e-09,
                                                'cmp42r': 2.8947742359e-07,
                                                'cmp43c': 3.7413165037e-08,
                                                'cmp43r': -2.2054535366e-05,
                                                'cmp44c': 3.9040038704e-07,
                                                'cmp44r': 1.8627229409e-05,
                                                'cmp45r': 1.6150669739e-09,
                                                'cmp46r': -6.4868398022e-09,
                                                'cmp51r': -6.3876314212e-08,
                                                'cmp52r': -7.4760283647e-09,
                                                'cmp53r': -1.7459754752e-09,
                                                'cmp54r': 1.6150669739e-09,
                                                'cmp55r': 8.3947049682e-06,
                                                'cmp56r': 3.7413165037e-08,
                                                'cmp61r': 4.4880988512e-07,
                                                'cmp62r': -1.2065334963e-10,
                                                'cmp63r': 4.6260657341e-09,
                                                'cmp64r': -6.4868398022e-09,
                                                'cmp65r': 3.7413165037e-08,
                                                'cmp66r': 3.9040038704e-07,
                                                'ea': 32123231.314,
                                                'ei22': 119120.22795,
                                                'ei33': 2562620.733,
                                                'ga22': 562248.80781,
                                                'ga33': 835685.78392,
                                                'gj': 53684.849101,
                                                'mc2': 1.1494335098,
                                                'mc3': 0.012439183019,
                                                'mmoi1': 0.0072632486361,
                                                'mmoi2': 0.00026525481038,
                                                'mmoi3': 0.0069979938258,
                                                'ms11': 0.075463162134,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00093870008494,
                                                'ms16': -0.086739887313,
                                                'ms21': 0.0,
                                                'ms22': 0.075463162134,
                                                'ms23': 0.0,
                                                'ms24': -0.00093870008494,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.075463162134,
                                                'ms34': 0.086739887313,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00093870008494,
                                                'ms43': 0.086739887313,
                                                'ms44': 0.10697665841,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00093870008494,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 0.00027700892854,
                                                'ms56': -0.00110180938,
                                                'ms61': -0.086739887313,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00110180938,
                                                'ms66': 0.10669964949,
                                                'mu': 0.075463162134,
                                                'sc2': 1.1839944031,
                                                'sc3': 0.015540551804,
                                                'stf11c': 32127277.521,
                                                'stf11r': 32127277.572,
                                                'stf12c': -14739.357462,
                                                'stf12r': 10.197839899,
                                                'stf13c': 409244.80607,
                                                'stf13r': -204.38975585,
                                                'stf14c': -36973443.08,
                                                'stf14r': -14981.512851,
                                                'stf15r': 409244.827,
                                                'stf16r': -36973444.739,
                                                'stf21c': -14739.357462,
                                                'stf21r': 10.197839899,
                                                'stf22c': 53691.923587,
                                                'stf22r': 569898.38406,
                                                'stf23c': -202.06359968,
                                                'stf23r': -45085.363622,
                                                'stf24c': 17856.078801,
                                                'stf24r': -62237.532434,
                                                'stf25r': 511.91968891,
                                                'stf26r': -384.5445086,
                                                'stf31c': 409244.80607,
                                                'stf31r': -204.38975585,
                                                'stf32c': -202.06359968,
                                                'stf32r': -45085.363622,
                                                'stf33c': 124386.66909,
                                                'stf33r': 828054.88201,
                                                'stf34c': -482397.38069,
                                                'stf34r': 981115.35652,
                                                'stf35r': -88.188473075,
                                                'stf36r': 6719.495598,
                                                'stf41c': -36973443.08,
                                                'stf41r': -14981.512851,
                                                'stf42c': 17856.078801,
                                                'stf42r': -62237.532434,
                                                'stf43c': -482397.38069,
                                                'stf43r': 981115.35652,
                                                'stf44c': 45113199.598,
                                                'stf44r': 1216297.0163,
                                                'stf45r': -314.434069,
                                                'stf46r': 25817.919161,
                                                'stf51r': 409244.827,
                                                'stf52r': 511.91968891,
                                                'stf53r': -88.188473075,
                                                'stf54r': -314.434069,
                                                'stf55r': 124387.13169,
                                                'stf56r': -482398.11302,
                                                'stf61r': -36973444.739,
                                                'stf62r': -384.5445086,
                                                'stf63r': 6719.495598,
                                                'stf64r': 25817.919161,
                                                'stf65r': -482398.11302,
                                                'stf66r': 45113254.126,
                                                'tc2': 1.1508350738,
                                                'tc3': 0.012738112556}}},
              'cs1_set2': {'parameter': {'a2p1': 0.8,
                                         'a2p3': 0.6,
                                         'airfoil': 'sc1094r8.txt',
                                         'airfoil_file_head': 1,
                                         'airfoil_point_order': 1,
                                         'airfoil_point_reverse': 1,
                                         'ang_spar_1': 7882.099999999999,
                                         'ang_spar_2': -3829.499999999999,
                                         'chord': 1.73,
                                         'gms': 0.004,
                                         'lam_back': 'T300 15k/976_0.0053',
                                         'lam_cap': 'Aluminum 8009_0.01',
                                         'lam_front': 'T300 15k/976_0.0053',
                                         'lam_skin': 'T300 15k/976_0.0053',
                                         'lam_spar_1': 'T300 15k/976_0.0053',
                                         'location': 26.83,
                                         'mat_fill_back': 'Plascore '
                                                          'PN2-3/16OX3.0',
                                         'mat_fill_front': 'Rohacell 70',
                                         'mat_fill_te': 'Plascore '
                                                        'PN2-3/16OX3.0',
                                         'mat_nsm': 'lead',
                                         'mdb_name': 'material_database_us_ft',
                                         'ply_spar_1': 10,
                                         'rnsm': 0.001,
                                         'topology': 'airfoil_solid.xml.tmp'},
                           'property': {'md1': {'cmp11c': 8.6878954609e-07,
                                                'cmp11r': 8.6878954609e-07,
                                                'cmp12c': 0.0,
                                                'cmp12r': 0.0,
                                                'cmp13c': -4.9180730583e-07,
                                                'cmp13r': 0.0,
                                                'cmp14c': 7.2917599121e-07,
                                                'cmp14r': 0.0,
                                                'cmp15r': -4.9180730583e-07,
                                                'cmp16r': 7.2917599121e-07,
                                                'cmp21c': 0.0,
                                                'cmp21r': 0.0,
                                                'cmp22c': 0.00027162192945,
                                                'cmp22r': 3.9379621974e-06,
                                                'cmp23c': 0.0,
                                                'cmp23r': -1.0747935643e-05,
                                                'cmp24c': 0.0,
                                                'cmp24r': 9.9217524276e-06,
                                                'cmp25r': 0.0,
                                                'cmp26r': 0.0,
                                                'cmp31c': -4.9180730583e-07,
                                                'cmp31r': 0.0,
                                                'cmp32c': 0.0,
                                                'cmp32r': -1.0747935643e-05,
                                                'cmp33c': 5.7758156681e-05,
                                                'cmp33r': 0.0003372421267,
                                                'cmp34c': 9.5286858883e-07,
                                                'cmp34r': -0.00029847292879,
                                                'cmp35r': 0.0,
                                                'cmp36r': 0.0,
                                                'cmp41c': 7.2917599121e-07,
                                                'cmp41r': 0.0,
                                                'cmp42c': 0.0,
                                                'cmp42r': 9.9217524276e-06,
                                                'cmp43c': 9.5286858883e-07,
                                                'cmp43r': -0.00029847292879,
                                                'cmp44c': 8.4546245969e-07,
                                                'cmp44r': 0.00027162192945,
                                                'cmp45r': 0.0,
                                                'cmp46r': 0.0,
                                                'cmp51r': -4.9180730583e-07,
                                                'cmp52r': 0.0,
                                                'cmp53r': 0.0,
                                                'cmp54r': 0.0,
                                                'cmp55r': 5.7758156681e-05,
                                                'cmp56r': 9.5286858883e-07,
                                                'cmp61r': 7.2917599121e-07,
                                                'cmp62r': 0.0,
                                                'cmp63r': 0.0,
                                                'cmp64r': 0.0,
                                                'cmp65r': 9.5286858883e-07,
                                                'cmp66r': 8.4546245969e-07,
                                                'ea': 4774098.1646,
                                                'ei22': 17308.792358,
                                                'ei33': 1205526.0405,
                                                'ga22': 107897.42799,
                                                'ga33': 280006.76136,
                                                'gj': 3681.5878674,
                                                'mc2': 1.0027721065,
                                                'mc3': 0.026707103412,
                                                'mmoi1': 0.006197578454,
                                                'mmoi2': 6.4318592849e-05,
                                                'mmoi3': 0.0061332598612,
                                                'ms11': 0.033471831002,
                                                'ms12': 0.0,
                                                'ms13': 0.0,
                                                'ms14': 0.0,
                                                'ms15': 0.00089393565197,
                                                'ms16': -0.033564618483,
                                                'ms21': 0.0,
                                                'ms22': 0.033471831002,
                                                'ms23': 0.0,
                                                'ms24': -0.00089393565197,
                                                'ms25': 0.0,
                                                'ms26': 0.0,
                                                'ms31': 0.0,
                                                'ms32': 0.0,
                                                'ms33': 0.033471831002,
                                                'ms34': 0.033564618483,
                                                'ms35': 0.0,
                                                'ms36': 0.0,
                                                'ms41': 0.0,
                                                'ms42': -0.00089393565197,
                                                'ms43': 0.033564618483,
                                                'ms44': 0.039879116067,
                                                'ms45': 0.0,
                                                'ms46': 0.0,
                                                'ms51': 0.00089393565197,
                                                'ms52': 0.0,
                                                'ms53': 0.0,
                                                'ms54': 0.0,
                                                'ms55': 8.9734363542e-05,
                                                'ms56': -0.00099311905661,
                                                'ms61': -0.033564618483,
                                                'ms62': 0.0,
                                                'ms63': 0.0,
                                                'ms64': 0.0,
                                                'ms65': -0.00099311905661,
                                                'ms66': 0.039789381703,
                                                'mu': 0.033471831002,
                                                'sc2': 1.0988543134,
                                                'sc3': 0.036527803361,
                                                'stf11c': 4774098.1646,
                                                'stf11r': 4774098.1646,
                                                'stf12c': 0.0,
                                                'stf12r': 0.0,
                                                'stf13c': 110636.30469,
                                                'stf13r': 0.0,
                                                'stf14c': -4242151.2389,
                                                'stf14r': 0.0,
                                                'stf15r': 110636.30469,
                                                'stf16r': -4242151.2389,
                                                'stf21c': 0.0,
                                                'stf21r': 0.0,
                                                'stf22c': 3681.5878674,
                                                'stf22r': 279879.86959,
                                                'stf23c': 0.0,
                                                'stf23r': -4671.5261463,
                                                'stf24c': 0.0,
                                                'stf24r': -15356.723497,
                                                'stf25r': 0.0,
                                                'stf26r': 0.0,
                                                'stf31c': 110636.30469,
                                                'stf31r': 0.0,
                                                'stf32c': 0.0,
                                                'stf32r': -4671.5261463,
                                                'stf33c': 20205.505805,
                                                'stf33r': 108024.31976,
                                                'stf34c': -118191.56225,
                                                'stf34r': 118873.63031,
                                                'stf35r': 0.0,
                                                'stf36r': 0.0,
                                                'stf41c': -4242151.2389,
                                                'stf41r': 0.0,
                                                'stf42c': 0.0,
                                                'stf42r': -15356.723497,
                                                'stf43c': -118191.56225,
                                                'stf43r': 118873.63031,
                                                'stf44c': 4974668.9678,
                                                'stf44r': 134867.33666,
                                                'stf45r': 0.0,
                                                'stf46r': 0.0,
                                                'stf51r': 110636.30469,
                                                'stf52r': 0.0,
                                                'stf53r': 0.0,
                                                'stf54r': 0.0,
                                                'stf55r': 20205.505805,
                                                'stf56r': -118191.56225,
                                                'stf61r': -4242151.2389,
                                                'stf62r': 0.0,
                                                'stf63r': 0.0,
                                                'stf64r': 0.0,
                                                'stf65r': -118191.56225,
                                                'stf66r': 4974668.9678,
                                                'tc2': 0.88857645835,
                                                'tc3': 0.023174283576}}}}} 
DEBUG    [2023-07-12 11:03:03] core.runSGDesignAnalysisDF :: ls_resp_cases:
[{'coord': 26.83,
  'response': Displacement
  u1 =    -1.713801e-06
  u2 =     1.326533e-05
  u3 =    -3.021506e-03
Rotation (directional cosine)
  c11 =     1.000000e+00, c12 =     0.000000e+00, c13 =     0.000000e+00
  c21 =     0.000000e+00, c22 =     1.000000e+00, c23 =     0.000000e+00
  c31 =     0.000000e+00, c32 =     0.000000e+00, c33 =     1.000000e+00
Load
  f1 =     0.000000e+00
  f2 =     0.000000e+00
  f3 =     0.000000e+00
  m1 =     0.000000e+00
  m2 =     1.000000e+00
  m3 =     0.000000e+00}] 
DEBUG    [2023-07-12 11:03:03] core.runSGDesignAnalysisDF :: case_name = case1 
INFO     [2023-07-12 11:03:03] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:03:03] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:03:03] execu.run :: prevabs -i cs1_set2.xml -vabs -d 
INFO     [2023-07-12 11:03:03] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:03:03] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:03:07] presg2d.solve :: reading results cs1_set2.sg... 
INFO     [2023-07-12 11:03:07] presg2d.solve :: preprocessing... 
INFO     [2023-07-12 11:03:07] main.buildSG :: building 2D SG: cs1_set2.xml... 
CRITICAL [2023-07-12 11:03:07] execu.run :: prevabs -i cs1_set2.xml -vabs -fi 
INFO     [2023-07-12 11:03:07] presg2d.solve :: running analysis... 
DEBUG    [2023-07-12 11:03:07] presg2d.solve :: solver: vabs 
INFO     [2023-07-12 11:03:07] presg2d.solve :: reading results cs1_set2.sg.fi... 
INFO     [2023-07-12 11:03:08] _msgd.writeMDAOEvalOut :: writing output files... 
