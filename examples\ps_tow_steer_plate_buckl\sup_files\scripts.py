# import tensorflow as tf


def runNN(data, sname, *args, **kwargs):

    # Access the design variables from the main data
    l1v1 = data['main']['l1v1']
    l1v2 = data['main']['l1v2']
    l2v3 = data['main']['l2v3']
    l2v4 = data['main']['l2v4']
    l3v5 = data['main']['l3v5']
    l3v6 = data['main']['l3v6']

    # Access other arguments
    model_name = kwargs['model_name']

    # Load NN model
    # model = load(model_name)

    # Run NN model
    # params = [[l1v1, l1v2, l2v3, l2v4, l3v5, l3v6],]
    # output = model(params)
    # eig1, eig2, eig3, eig4, eig5 = output
    eig1, eig2, eig3, eig4, eig5 = 1, 2, 3, 4, 5

    # Save the result in the main data
    data['main']['eig1'] = eig1
    # data['main']['eig2'] = eig2
    # data['main']['eig3'] = eig3
    # data['main']['eig4'] = eig4
    # data['main']['eig5'] = eig5

    return


