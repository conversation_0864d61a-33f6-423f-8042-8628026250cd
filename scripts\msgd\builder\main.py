import copy
import json
import logging
import os
import shutil
import traceback
import xml.etree.ElementTree as et
from xml.dom import minidom
# import subprocess as sbp

import dakota.interfacing as di
import numpy as np
import sgio

import msgd._global as GLOBAL
import msgd.core as mcore
# import msgd.model.sg as mss
# import msgd.model.solid as mms
import msgd.utils as mutils
# import msgd.utils.container as muc
# import msgd.utils.io as mui
# import msgd.utils.execu as mue
# import msgd.utils.logger as mul
import msgd.ext.abaqus as mea



logger = logging.getLogger(__name__)


def readMaterialFromXMLElement(xem):
    """Read material data from XML elements.
    """
    type_flag = {
        'isotropic': 0,
        'orthotropic': 1,
        'anisotropic': 2
    }
    elastic_label = {
        0: ('e', 'nu'),
        1: (
            'e1', 'e2', 'e3',
            'g12', 'g13', 'g23',
            'nu12', 'nu13', 'nu23'
        )#,
        # 'anisotropic': (
        #     ('c11', 'c12', 'c13', 'c14', 'c15', 'c16'),
        #     ('c12', 'c22', 'c23', 'c24', 'c25', 'c26'),
        #     ('c13', 'c23', 'c33', 'c34', 'c35', 'c36'),
        #     ('c14', 'c24', 'c34', 'c44', 'c45', 'c46'),
        #     ('c15', 'c25', 'c35', 'c45', 'c55', 'c56'),
        #     ('c16', 'c26', 'c36', 'c46', 'c56', 'c66'),
        # )
    }

    m = sgio.MaterialSection()
    # m = mss.MaterialSection()
    m.name = xem.get('name').strip()

    m.eff_props[3]['type'] = type_flag[xem.get('type').strip()]
    m.eff_props[3]['density'] = float(xem.find('density').text.strip())

    ep = xem.find('elastic')
    if (m.eff_props[3]['type'] == 0) or (m.eff_props[3]['type'] == 1):
        ep_labels = elastic_label[m.eff_props[3]['type']]
        for tag in ep_labels:
            m.eff_props[3]['constants'][tag] = float(
                ep.find(tag).text.strip()
            )
    else:
        stf = np.zeros((6, 6))
        for i in range(6):
            rowtag = 'row' + str(i)
            row = list(map(float, ep.find(rowtag).text.strip().split()))
            stf[i, i:] = row
        for i in range(1, 6):
            for j in range(0, i):
                stf[i, j] = stf[j, i]
        m.eff_props[3]['stiffness'] = stf

    if xem.find('strength') is not None:
        xe_str = xem.find('strength')
        if xe_str.find('criterion') is not None:
            m.strength['criterion'] = int(xe_str.find('criterion').text.strip())
        m.strength['constants'] = list(map(float, xe_str.find('constants').text.strip().split()))

    return m



def strLayer(layer):
    s = '{0:8s}  {1:4d}  {2:4d}  {3}'.format(layer['material'], layer['in-plane_orientation'], layer['number_of_plies'], layer['ply_thickness'])
    return s


def printLayers(layers, title=''):
    print()
    print(title)
    for layer in layers:
        print(strLayer(layer))
    return









def buildSGModel(
    sg_model:mcore.StructureModel,
    db_sg_model:mcore.DataBase=None,
    analysis:str='', load_cases:list=[],
    timeout:float=30,
    work_dir:str='.',
    substitute:bool=True,
    sgdb=None,
    **kwargs
    ):
    """Build a structure gene model.

    Parameters
    ----------
    sg_model : :obj:`msgd.core.StructureModel`
        Structure Gene Model.
    sg_database : :obj:`msgd.core.DataBase`
        Structure Gene Database.
    analysis : {'h', 'dn', 'dl', 'd', 'l', 'fi', 'f', 'fe'}
        Analysis to be carried out.

        * 'h' - homogenization
        * 'dn' - (VABS) dehomogenization (nonlinear)
        * 'dl' or 'd' or 'l' - dehomogenization (linear)
        * 'fi' - initial failure indices and strength ratios
        * 'f' - (SwiftComp) initial failure strength
        * 'fe' - (SwiftComp) initial failure envelope
    load_cases : list
        Load cases.
    timeout : float
        Max running time.
    work_dir : str
        Working directory.

    """

    name = sg_model.name
    sgdim = sg_model.design.dimension
    builder = sg_model.design.builder

    logger.debug(f'building {sgdim}D SG ({builder}): {name}...')


    sg = None
    fn_sg = ''

    if builder == 'default':
        sg = buildSGFromDefault(
            name,
            design=sg_model.design.base,
            model=sg_model,
            # sgdim=sgdim,
            # smdim='',
            analysis=analysis,
            physics=sg_model.physics,
            db_sg_model=db_sg_model,
            sgdb_map={},
            load_cases=load_cases
        )
        # fn_sg = f'{name}.sg'
        fn_sg = os.path.join(work_dir, f'{name}.sg')
        # print(type(sg).__name__)
        sgio.write(
            sg, fn_sg,
            file_format=sg_model.data_format,
            format_version=sg_model.solver_version,
            analysis=analysis,
            model_type=sg_model.model_type,
            model_space='z'
        )

    elif 'prevabs' in builder.lower():
        fn_sg, fns_include = buildSGFromPrevabs(
            name,
            solver=sg_model.solver,
            analysis=analysis,
            fn_template=sg_model.design.base['base_file'],
            parameters=sg_model.design.parameters,
            prevabs_cmd=sg_model.design.builder_cmd,
            prevabs_version=sg_model.design.builder_version,
            solver_version=sg_model.solver_version,
            sgdb=sgdb,
            timeout=timeout,
            substitute=substitute,
            work_dir=work_dir,
            design_base=sg_model.design.base,
            **kwargs
        )

        if analysis == 'h':
            sg_model.fns_sup.extend(fns_include)
            sg = sgio.read(
                fn_sg,
                file_format=sg_model.data_format,
                format_version=sg_model.solver_version,
                model_type=sg_model.model_type
            )

    elif builder == 'abaqus':
        fn_sg, sg = buildSGFromAbaqus(
            name,
            sgdim,
            design=sg_model.design.base,
            params=sg_model.design.parameters,
            sgdb=sgdb,
            # sgdb=db_sg_model,
        )
        # sg = sgio.read(
        #     fn_sg,
        #     file_format=sg_model.data_format,
        #     format_version=sg_model.solver_version,
        #     model=sg_model.model_type
        # )

    if analysis == 'h':
        sg_model.sg_data = sg
        sg_model.fn_model = fn_sg

    return




# def buildSG(
#     name, design, smdim, model=None, params={}, sgdb={}, fn_input='', builder='default',
#     analysis='', physics='',
#     load_cases=[], solver='swiftcomp', version='',
#     integrated=False, fn_prevabs_mdb_xml='',
#     sgdb_map={},
#     dir_list=['.',],
#     write_input=True, timeout=30
#     # , logger=None
#     # , *args, **kwargs
#     ):
#     """ Preprocessor of a structure gene.

#     Parameters
#     ----------
#     name : str
#         Name of the SG.
#     design : dict
#         Input of SG design.
#     model : dict
#         Input of SG model.
#     fn_input : str
#         Name of SG input file.
#     builder : str, default 'prevabs'
#         Preprocessor command.
#     analysis : {0, 1, 2, 3, 4, 5, '', 'h', 'dn', 'dl', 'd', 'l', 'fi', 'f', 'fe'}
#         Analysis to be carried out.

#         * 0 or 'h' or '' - homogenization
#         * 1 or 'dn' - (VABS) dehomogenization (nonlinear)
#         * 2 or 'dl' or 'd' or 'l' - dehomogenization (linear)
#         * 3 or 'fi' - initial failure indices and strength ratios
#         * 4 or 'f' - (SwiftComp) initial failure strength
#         * 5 or 'fe' - (SwiftComp) initial failure envelope
#     solver : str, default 'swiftcomp'
#         Format of the generated input file ('vabs' or 'swiftcomp')
#     write_input : bool
#         Whether to write input file.
#     timeout : int
#         Max running time.
#     logger : msgd.utils.logger.Logger
#         Logger.

#     Returns
#     -------
#     str, int
#         (If write_input True) Input file name, dimension of the material/structural model.
#     msgpi.sg.StructureGene
#         (If write_input False) Structure Gene.
#     """

#     sgdim = design['dim']

#     logger.info('building {}D SG: {}...'.format(sgdim, name))

#     try:

#         if builder == 'default':
#             sg = buildSGFromDefault(
#                 name, design, model, sgdim, smdim, analysis, physics,
#                 sgdb, sgdb_map, load_cases
#             )
#             return sg

#         elif 'prevabs' in builder:
#             fn_sg_in = buildSGFromPrevabs(
#                 name, fn_prevabs_mdb_xml, sgdb, builder, solver,
#                 version, integrated, analysis, timeout
#             )
#             return fn_sg_in

#         elif builder == 'abaqus':
#             fn_sg_in = buildSGFromAbaqus(name, design, params, sgdb)
#             return fn_sg_in

#     except:
#         e = tb.format_exc()
#         print(e)









def buildSGFromDefault(
    name, design, model:mcore.StructureModel,
    # sgdim, smdim,
    analysis, physics,
    db_sg_model:mcore.DataBase, sgdb_map, load_cases
    ):
    logger.debug('building SG using default...')

    logger.debug(locals())

    # if model.model_type[2] == '1':
    #     submodel = 0
    # elif model.model_type[2] == '2':
    #     submodel = 1

    mesh_size = model.configs.get('mesh_size', -1)  # default: 1 element per layer
    k11 = model.configs.get('k11', 0.0)
    k22 = model.configs.get('k22', 0.0)
    lame1 = model.configs.get('lame1', 1.0)
    lame2 = model.configs.get('lame2', 1.0)

    dict_sg_model = {}
    for _sg_model in db_sg_model.data:
        dict_sg_model[_sg_model.name] = _sg_model.constitutive
    logger.debug(f'dict_sg_model = {dict_sg_model}')

    sg = sgio.buildSG1D(
        name=name,
        layup=design,
        sgdb=dict_sg_model,
        model=model.model_type,
        # smdim=model.smdim,
        mesh_size=mesh_size,
        k11=k11, k22=k22,
        lame1=lame1, lame2=lame2,
        load_cases=load_cases,
        analysis=analysis,
        sgdb_map=sgdb_map,
        # submodel=submodel,
        physics=physics
        )

    # print(sg)

    return sg









def substituteTemplateParams(
    fn_template:str, parameters:dict={},
    fn_output:str=''):
    """Substitute parameters in a template file.
    """
    try:
        di.dprepro(
            template=fn_template, output=fn_output,
            include=parameters
        )
    except:
        e = traceback.format_exc()
        logger.critical(e, exc_info=1)

    return fn_output




def extractIncludedFiles(fn_xml):
    """
    Extract included file names from the xml input file of prevabs.

    Parameters
    ----------
    fn_xml : str
        Name of the xml input file.

    Returns
    -------
    list
        List of included file names.
    """
    # logger.info('extracting included files from xml...')

    xtree, xroot = mutils.parseXML(fn_xml)
    # print(xroot)

    included_files = []

    # Files under 'include' tag
    xe_include = xroot.findall('.//include/*')
    for _xe in xe_include:
        included_files.append(f'{_xe.text}.xml')

    # Files under 'points' tag
    xe_points = xroot.findall(".//points[@data='file']")
    for _xe in xe_points:
        included_files.append(_xe.text)

    return included_files




def buildSGFromPrevabs(
    name, solver, analysis,
    fn_template='', parameters={},
    prevabs_cmd='prevabs', prevabs_version='',
    solver_version='',
    fn_prevabs_mdb_xml='', sgdb=None,
    integrated:bool=False, timeout:float=30,
    work_dir:str='.',
    substitute:bool=True,
    design_base:dict={},
    **kwargs
    ):
    """
    """

    logger.debug(f'building {GLOBAL.SG_KEY} using prevabs...')
    logger.debug(locals())

    if name[-4:] != '.xml':
        fn_xml_in = name + '.xml'
        fn_sg_in = name + '.sg'

    else:
        fn_xml_in = name
        fn_sg_in = name.replace('.xml', '.sg')

    fn_xml_in = os.path.join(work_dir, fn_xml_in)
    fn_sg_in = os.path.join(work_dir, fn_sg_in)

    logger.debug(f'parameters = {parameters}')

    # Substitute parameters
    if substitute:
        try:
            di.dprepro(
                template=fn_template, output=fn_xml_in,
                include=parameters)
        except:
            e = traceback.format_exc()
            logger.critical(e, exc_info=1)

    # Write material xml file
    fn_prevabs_mdb_xml = parameters.get('mdb', '')
    if fn_prevabs_mdb_xml != '':
        transform = design_base.get('transform', {})
        logger.debug(f'transform = {transform}')
        writeMaterialDbXml(sgdb, fn_prevabs_mdb_xml, transform)


    # Copy files to work directory
    if work_dir != '.':
        fns_include = extractIncludedFiles(fn_xml_in)
        logger.debug(f'fns_include = {fns_include}')
        for _fn in fns_include:
            # print(f'_fn = {_fn}')
            shutil.copy(_fn, work_dir)

    cli_ver = 0  # command line version

    if prevabs_version != '':
        ver_major = int(prevabs_version.split('.')[0])
        ver_minor = int(prevabs_version.split('.')[1])

        if ver_major > 1:
            cli_ver = 1
        elif ver_major == 1:
            if ver_minor >= 6:
                cli_ver = 1

    # Form command
    cmd = [prevabs_cmd, '-i', fn_xml_in]

    if 'vabs' in solver.lower():
        if cli_ver == 0:
            cmd.append('-vabs')
        elif cli_ver == 1:
            cmd.append('--vabs')

    elif ('swiftcomp' in solver.lower()) or ('sc' in solver.lower()):
        if cli_ver == 0:
            cmd.append('-sc')
        if cli_ver == 1:
            cmd.append('--sc')

    if solver_version != '':
        if cli_ver == 0:
            cmd.extend(['-ver', solver_version])
        elif cli_ver == 1:
            cmd.extend(['--ver', solver_version])
        # cmd.append('-ver')
        # cmd.append(solver_version)

    if integrated:
        cmd.append('-e')
        cmd.append('-int')

    # cmd.append('-' + analysis)
    if analysis == 'h':
        if cli_ver == 0:
            cmd.append('-h')
        elif cli_ver == 1:
            cmd.append('--hm')
    elif analysis == 'd':
        if cli_ver == 0:
            cmd.append('-d')
        elif cli_ver == 1:
            cmd.append('--dh')
    elif analysis == 'fi':
        if cli_ver == 0:
            cmd.append('-fi')
        elif cli_ver == 1:
            cmd.append('--fi')

    logger.debug(f'cmd = {cmd}')

    # Run prevabs
    mutils.run(cmd, timeout)

    return fn_sg_in, fns_include









def buildSGFromAbaqus(name, sgdim, design, params, sgdb={}):
    """
    """

    logger.debug(f'building {GLOBAL.SG_KEY} using abaqus...')
    logger.debug(locals())

    # Write materials to a file
    fn_materials = params.get('material_file', '')
    if fn_materials != '':
        transform = design.get('transform', {})
        logger.debug(f'transform = {transform}')
        writeMaterialDbJson(sgdb, fn_materials, transform)


    # Run Abaqus script
    fn_script = design['base_file']['script']
    arg_names = design['base_file'].get('args', [])
    logger.debug(f'arg_names = {arg_names}')

    cmd = ['abaqus', 'cae', f'nogui={fn_script}', '--', name]

    # Add arguments
    nargs = 1

    if arg_names:
        for _arg in arg_names:
            cmd.append(str(_arg))
            nargs += 1

    else:
        for _k, _v in params.items():
            if _k == 'matrix' or _k == 'inclusion':
                _m = sgdb[_v][0]
                _m_prop = _m['property']['md3']
                _m_density = _m_prop['density']
                _m_type = _m_prop['type']
                _m_elastic = _m_prop['elasticity']
                cmd.extend([
                    f'--name_{_k}', _v,
                    f'--density_{_k}', str(_m_density),
                    f'--type_{_k}', _m_type,
                    f'--elastic_{_k}', ','.join(list(map(str, _m_elastic)))
                ])
                nargs += 8
            else:
                cmd.extend([f'--{_k}', str(_v)])
                nargs += 2
    cmd.append(str(nargs))

    # print(f'cmd = {cmd}')

    # sbp.run(cmd)
    mea.run(cmd)

    # Convert .inp to .sg
    # fn_base = 'rve_inclusion_meshsize01'

    fn_rve_abq_inp = f'{name}.inp'
    fn_sg = f'{name}.sg'

    sg = sgio.read(fn_rve_abq_inp, file_format='abaqus')
    sg.sgdim = sgdim

    # print(sg)

    sgio.write(sg, fn_sg, 'swiftcomp')

    return fn_sg, sg









def writeMaterialDbJson(mdb, fn, transform={}):
    """
    """
    logger.debug('writing material database in json...')

    logger.debug('mdb')
    logger.debug(str(mdb))

    # Read/Create json object
    try:
        with open(fn, 'r') as f:
            mdb_json = json.load(f)
    except FileNotFoundError:
        mdb_json = []

    # Add materials
    for _name, _model in mdb.items():
        mdata = {}

        logger.debug(f'adding material {_model.name}...')

        _consti = _model.constitutive
        logger.debug('_consti')
        logger.debug(str(_consti))

        _mname = _model.name.split('_')
        if _mname[-1].startswith('set'):
            _mname = '_'.join(_mname[:-1])
        else:
            _mname = '_'.join(_mname)
        logger.debug(f'_mname = {_mname}')
        mdata['name'] = _mname

        _density = _consti.get('density')
        mdata['density'] = _density

        _type = _consti.get('isotropy')

        if _type == 0:
            mdata['type'] = 'isotropic'
        elif _type == 1:
            mdata['type'] = 'orthotropic'
        elif _type == 2:
            mdata['type'] = 'anisotropic'

        mdata['elastic'] = {}

        _stff = copy.deepcopy(_consti.get('c'))
        logger.debug(f'_stff = {_stff}')

        if _stff is not None:

            # Rotate stiffness matrix
            tf = transform.get(_mname, {})
            if tf:
                _stff = sgio.utils.rotate_stiffness_matrix(_stff, tf['axis'], np.deg2rad(tf['angle']))

            for i in range(6):
                for j in range(i, 6):
                    _key = f'c{i+1}{j+1}'
                    mdata['elastic'][_key] = _stff[i][j]

        _cte = _consti.get('cte')
        _sp_heat = _consti.get('specific_heat')

        mi = -1
        for i, m in enumerate(mdb_json):
            if m['name'] == _mname:
                mi = i
                break

        if mi == -1:
            mdb_json.append(mdata)
        else:
            mdb_json[mi] = mdata

    # Write to file
    with open(fn, 'w') as f:
        json.dump(mdb_json, f, indent=4)

    return







def writeMaterialDbXml(mdb, fn, transform={}):
    """
    Write material database to an XML file.

    Parameters
    ----------
    mdb : dict
        A dictionary containing material properties with the following structure:
        {
            'material_name': [
                {
                    'param': dict,  # Optional parameter dictionary
                    'prop': {
                        'md3': {
                            'type': str,  # Material type ('isotropic' or 'engineering')
                            'density': float,
                            'stiffness': list[list[float]],  # Optional 6x6 stiffness matrix for anisotropic materials
                            'elasticity': list[float],  # Elastic constants based on material type
                            'cte': list[float],  # Optional coefficient of thermal expansion values
                            'specific_heat': float,  # Optional specific heat value
                            'strength': dict,  # Optional strength properties
                            'failure_criterion': str  # Optional failure criterion
                        }
                    }
                }
            ]
        }
    fn : str
        Path to the output XML file
    transform : dict
        A dictionary containing transformation rules for material properties
        ..  code-block:: python
        {
            'material_name': {
                'axis': [1, 2, 3],
                'angle': 90
            },
            ...
        }

    Returns
    -------
    None

    Notes
    -----
    - The function creates or updates an XML file containing material properties
    - For each material, it writes properties such as density, elasticity constants,
      thermal expansion coefficients, and strength properties
    - The XML structure follows a specific format for material database storage
    """

    logger.debug('writing material database in xml...')

    logger.debug('mdb')
    logger.debug(str(mdb))

    # Read/Create xml object
    try:
        mdb_tree = et.parse(fn)
        mdb_root = mdb_tree.getroot()
        # mdb_doc = minidom.parse(fn)
        # mdb_root = mdb_doc.documentElement
    except FileNotFoundError:
        mdb_root = et.Element('materials')


    # Add materials
    for _name, _model in mdb.items():
        logger.debug(f'adding material {_model.name}...')

        _consti = _model.constitutive
        logger.debug('_consti')
        logger.debug(str(_consti))

        # if len(_pplist) == 1:
        # _param = _pplist[0]['parameter']
        # _props = _pplist[0]['property']
        # _param = mutils.getValueByKey(_pplist[0], 'param')
        # _props = mutils.getValueByKey(_pplist[0], 'prop')
        # if (_param == {}) and ('md3' in _props.keys()):
        # _prop = _props['md3']
        # _type = _prop['type']
        # _density = _prop['density']

        _mname = _model.name.split('_')
        if _mname[-1].startswith('set'):
            _mname = '_'.join(_mname[:-1])
        else:
            _mname = '_'.join(_mname)
        logger.debug(f'_mname = {_mname}')

        _type = _consti.get('isotropy')
        _density = _consti.get('density')

        # Check if the material is already in the file
        # se_material = None
        for _mdb_mat in mdb_root.findall('material'):
            if _mdb_mat.get('name') == _mname:
                mdb_root.remove(_mdb_mat)
                # se_material = _mdb_mat
                # se_density = se_material.find('density')
                # se_elastic = se_material.find('elastic')
                break
        # if not se_material:
        se_material = et.SubElement(mdb_root, 'material', name=_mname)
        se_density = et.SubElement(se_material, 'density')
        se_elastic = et.SubElement(se_material, 'elastic')

        se_material.set('type', _type)
        se_density.text = str(_density)

        _stff = copy.deepcopy(_consti.get('c'))

        # print('_stff =', _stff)
        logger.debug(f'_stff = {_stff}')

        if not _stff is None:

            tf = transform.get(_mname, {})
            if tf:
                _stff = sgio.utils.rotate_stiffness_matrix(_stff, tf['axis'], np.deg2rad(tf['angle']))

            se_material.set('type', 'anisotropic')
            for i in range(6):
                for j in range(i, 6):
                    _se = et.SubElement(se_elastic, 'c{}{}'.format(i+1, j+1))
                    _se.text = str(_stff[i][j])

        else:
            # _constants = _prop['elasticity']
            if _type == 'isotropic':
                _cnames = ['e', 'nu']
                for _i, _cname in enumerate(_cnames):
                    _se = et.SubElement(se_elastic, _cname)
                    # _se.text = str(_constants[_i])
                    _se.text = str(_consti.get(_cname))

            elif _type == 'engineering':
                _cnames = ['e1', 'e2', 'e3', 'g12', 'g13', 'g23', 'nu12', 'nu13', 'nu23']
                for _i, _cname in enumerate(_cnames):
                    _se = et.SubElement(se_elastic, _cname)
                    # _se.text = str(_constants[_i])
                    _se.text = str(_consti.get(_cname))

        _cte = _consti.get('cte')
        # try:
        #     _cte = _prop['cte']
        # except KeyError:
        #     _cte = None
        logger.debug(f'_cte = {_cte}')

        if not _cte is None:
            se_cte = et.SubElement(se_material, 'cte')
            if _type == 'isotropic':
                _se = et.SubElement(se_cte, 'a')
                _se.text = str(_cte[0])
            elif _type == 'engineering':
                _tags = ['a11', 'a22', 'a33']
                for _tag, _value in zip(_tags, _cte):
                    _se = et.SubElement(se_cte, _tag)
                    _se.text = str(_value)

        _sp_heat = _consti.get('specific_heat')
        # try:
        #     _sp_heat = _prop['specific_heat']
        # except KeyError:
        #     _sp_heat = None
        logger.debug(f'_sp_heat = {_sp_heat}')

        if not _sp_heat is None:
            se_sh = et.SubElement(se_material, 'specific_heat')
            se_sh.text = str(_sp_heat)

        _strg = _consti.get('strength')
        # try:
        #     _strg = _prop['strength']
        # except KeyError:
        #     _strg = None
        logger.debug(f'_strg = {_strg}')

        if not _strg is None:
            se_strength = et.SubElement(se_material, 'strength')
            for k, v in _strg.items():
                _se = et.SubElement(se_strength, k)
                _se.text = str(v)

            # _fc = _prop['failure_criterion']
            _fc = _consti.get('failure_criterion')
            se_fc = et.SubElement(se_material, 'failure_criterion')
            se_fc.text = str(_fc)


    # Write to file
    logger.debug(f'writing material database to {fn}...')
    xml_string = et.tostring(mdb_root)
    # print(xml_string)
    # xml_string = xml_string.replace(b'\n', b'')
    # xmlstr = minidom.parseString(xml_string).toprettyxml(indent="   ")
    reparsed = minidom.parseString(xml_string)
    xmlstr = '\n'.join([line for line in reparsed.toprettyxml(indent=' '*2).split('\n') if line.strip()])
    with open(fn, "w") as f:
        f.write(xmlstr)
        f.write('\n')

    return
