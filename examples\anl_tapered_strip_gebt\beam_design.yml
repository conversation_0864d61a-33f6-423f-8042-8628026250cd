# Main input for GEBT

# Overall analysis configuration block
analysis: 3

max_iteration: 1
num_steps: 1
num_eigens: 20

# Geometry block
point:
  - id: 1
    coordinates: [0, 0, 0]
  - id: 2
    coordinates: [10, 0, 0]

member:
  - id: 1
    points: [1, 2]

set:
  - name: "root"
    type: 'point'
    items: [1,]
  - name: "tip"
    type: 'point'
    items: [2,]
  - name: 'segment1'
    type: 'member'
    items: [1,]


# Boundary condition and load block
condition:
  - region: 'root'
    dofs: [1, 2, 3, 4, 5, 6]
    values: [0, 0, 0, 0, 0, 0]

  - region: 'tip'
    dofs: [7, 8, 9, 10, 11, 12]
    values: [0, 0, 0, 0, 0, 0]


# Meshing block
# ====================================================================

meshing:
  member_division:
    - member: 1
      division: 32
