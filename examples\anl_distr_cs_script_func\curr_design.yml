version: '0.10'
structure:
  name: blade1
  parameter: {}
  model:
    type: ''
    tool: ''
    tool_version: ''
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: blade1
    parameter: {}
    dim: 1
    builder: default
    design: null
    distribution:
    - name: w
      type: float
      coefficient:
        a: 0.2
        k: 0.5
        w0: 1
        phi: 0
      data_form: explicit
  cs_assignment:
  - region: all
    location: node
    cs: main_cs
  physics: elastic
functions:
- name: f1
  type: float
  module: users_function
  function: calcParameter
  coefficients: {}
cs:
- name: cs1
  parameter:
    w: 1
    t: 0.1
    lam_lyr_1: la_mat_1
    ang_lyr_1: 0
    gms: 0.01
  dim: 2
  builder: prevabs
  design:
    base_file: rect.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    output:
      value:
      - cmp11
      - cmp12
      - cmp13
      - cmp14
      - cmp15
      - cmp16
      - cmp22
      - cmp23
      - cmp24
      - cmp25
      - cmp26
      - cmp33
      - cmp34
      - cmp35
      - cmp36
      - cmp44
      - cmp45
      - cmp46
      - cmp55
      - cmp56
      - cmp66
      - ms11
      - ms12
      - ms13
      - ms14
      - ms15
      - ms16
      - ms22
      - ms23
      - ms24
      - ms25
      - ms26
      - ms33
      - ms34
      - ms35
      - ms36
      - ms44
      - ms45
      - ms46
      - ms55
      - ms56
      - ms66
    type: sg
    analysis: h
    work_dir: cs
