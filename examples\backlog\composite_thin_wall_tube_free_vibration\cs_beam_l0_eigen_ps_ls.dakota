# cs_beam_l0_eigen_ps_ls.dakota

environment
  output_file = 'cs_beam_l0_eigen_ps_ls.out'
  write_restart = 'cs_beam_l0_eigen_ps_ls.rst'
  error_file = 'cs_beam_l0_eigen_ps_ls.err'
  tabular_data
    tabular_data_file = 'cs_beam_l0_eigen_ps_ls_tabular.dat'
  results_output
    results_output_file = 'cs_beam_l0_eigen_ps_ls_results'


method
  output  normal
  list_parameter_study
    list_of_points =  45  -45  -45  45


model
  single

variables
  active = design

  continuous_design = 4
    descriptors = 'ang_1'  'ang_2'  'ang_3'  'ang_4'
    upper_bounds = 90  90  90  90
    lower_bounds = -90  -90  -90  -90




interface
  analysis_driver = 'msgd ./cs_beam_l0_eigen_ps_ls.yml --mode 1 --paramfile {PARAMETERS} --resultfile {RESULTS} --loglevelcmd info --loglevelfile info --logfile eval.log'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  './cs_beam_l0_eigen_ps_ls.yml'  'data/*'
      verbatim


responses
  descriptors =  'eig1'  'eig2'  'eig3'  'eig4'  'eig5'  'eig6'  'eig7'  'eig8'  'eig9'  'eig10'
  objective_functions = 10
    sense =  'max'  'max'  'max'  'max'  'max'  'max'  'max'  'max'  'max'  'max'
    weights = 1 1 1 1 1 1 1 1 1 1
  no_gradients
  no_hessians


