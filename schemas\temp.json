{"$schema": "http://json-schema.org/draft-07/schema#", "title": "MSGD Input Schema", "description": "Schema for MSG Design input files", "type": "object", "properties": {"version": {"type": "string", "description": "Version of the input format", "default": "0.7"}, "structure": {"type": "object", "description": "Global structure specification", "properties": {"name": {"type": "string"}, "parameter": {"type": "object", "additionalProperties": true}, "distribution": {"type": "array", "items": {"$ref": "#/definitions/distribution"}}, "design": {"type": "object"}, "model": {"type": "object", "properties": {"main_file": {"type": "string"}, "tool": {"type": "string"}, "config": {"type": "object", "properties": {"node_id_start": {"type": "integer"}}, "additionalProperties": true}}}, "cs_assignment": {"type": "array", "items": {"type": "object", "properties": {"region": {"type": "string"}, "location": {"type": "string"}, "cs": {"type": "string"}}}}, "cs": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "design": {"type": "string"}, "model": {"type": "object", "properties": {"type": {"type": "string"}, "solver": {"type": "string"}}}}}}}}, "function": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "interp_kind": {"type": "string"}}}}, "cs": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "builder": {"type": "string"}, "parameter": {"type": "object", "additionalProperties": true}, "design": {"type": "object", "properties": {"base_file": {"type": "string"}}}}}}, "analysis": {"type": "object", "properties": {"steps": {"type": "array", "items": {"type": "object", "properties": {"step": {"type": "string"}, "type": {"type": "string"}, "analysis": {"type": "string"}, "output": {"type": "object", "properties": {"file_name": {"type": "string"}, "file_format": {"type": "string"}, "value": {"type": "array", "items": {"type": "string"}}}}, "section_response": {"type": "object", "properties": {"data_form": {"type": "string"}, "file_name": {"type": "string"}, "file_format": {"type": "string"}}}}}}}}}, "definitions": {"distribution": {"type": "object", "properties": {"name": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "function": {"type": "string"}, "type": {"type": "string"}, "data_form": {"type": "string"}, "file_name": {"type": "string"}, "file_format": {"type": "string"}, "data_request": {"type": "string"}, "config": {"type": "object"}, "xscale": {"type": "string"}, "data": {"type": "string"}}}}}