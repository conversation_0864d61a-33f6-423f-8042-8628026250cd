from __future__ import annotations

import logging
from typing import Dict, List, Tuple, Any

import numpy as np

import msgd._global as GLO<PERSON>L
from msgd.core._data_classes import DataBase
from msgd.design.distribution import Distribution

logger = logging.getLogger(__name__)

def get_element_unique_nodes(eids, elements):
    """Get unique nodes from a list of elements."""
    unique_nodes = set()
    for eid in eids:
        unique_nodes.update(elements[eid])
    return unique_nodes

def get_element_regions(eid: int, element_regions: Dict) -> List[str]:
    """Get the regions containing the element."""
    return element_regions.get(eid, ['all'])

def get_sg_assignment(regions: List[str], region_sg_assigns: Dict) -> Tuple[Any, Any]:
    """Get the SG assignment for the given regions."""
    for region in regions:
        if region in region_sg_assigns:
            return region_sg_assigns[region], region_sg_assigns[region].sg_model
        elif 'all' in region_sg_assigns:
            return region_sg_assigns['all'], region_sg_assigns['all'].sg_model
    return None, None

def get_region_distributions(regions: List[str], region_distributions: Dict) -> List[Distribution]:
    """Get all distributions for the given regions."""
    distrs = []
    for region in regions:
        if region in region_distributions:
            distrs.extend(region_distributions[region])
        if 'all' in region_distributions:
            distrs.extend(region_distributions['all'])
    return distrs

def process_node_parameters(
    node_coords: np.ndarray,
    nid: int,
    distributions: List[Distribution],
    region_node_values: Dict,
    sg_model: Any
) -> Dict:
    """Process parameters for a single node."""
    nparam = {
        'sg': sg_model,
        'params': {}
    }
    
    for distr in distributions:
        pnames = [distr.name] if not isinstance(distr.name, list) else distr.name
        
        # Calculate new values
        pvalue = distr(node_coords, _id=nid)
        
        # Update parameters
        for pn, pv in pvalue.items():
            nparam['params'][pn] = pv
            if distr.region not in region_node_values:
                region_node_values[distr.region] = {}
            if nid not in region_node_values[distr.region]:
                region_node_values[distr.region][nid] = {}
            region_node_values[distr.region][nid][pn] = pv
            
    return nparam

def process_element(
    _eid: int,
    _enodes: List[int],
    _nodes: Dict,
    element_regions: Dict,
    region_sg_assigns: Dict,
    region_distributions: Dict,
    _region_node_values: Dict,
    _cache_lock: Any = None
) -> Dict:
    """Process a single element and its nodes.
    
    Parameters
    ----------
    _eid : int
        Element ID
    _enodes : list
        List of node IDs in the element
    _nodes : dict
        Dictionary of node coordinates
    element_regions : dict
        Mapping of elements to their regions
    region_sg_assigns : dict
        Mapping of regions to SG assignments
    region_distributions : dict
        Mapping of regions to distributions
    _region_node_values : dict
        Cache for node parameter values
    _cache_lock : Lock, optional
        Thread lock for cache access
    
    Returns
    -------
    dict
        Processed element data
    """
    element_data = {_eid: {}}
    
    # Get regions and assignments
    regions = get_element_regions(_eid, element_regions)
    logger.debug(f'element {_eid} in regions {regions}')
    
    sg_assign, sg_model = get_sg_assignment(regions, region_sg_assigns)
    if sg_assign is None:
        logger.warning(f'Element {_eid} is not assigned with any {GLOBAL.SG_KEY.upper()}.')
        sg_model = None
    elif sg_assign.location.startswith('element'):
        sg_assign.addRegionEntity(_eid)
    
    # Get distributions
    distributions = get_region_distributions(regions, region_distributions)
    
    # Process all nodes of the element at once using numpy
    node_coords = np.array([_nodes[_nid] for _nid in _enodes])
    if len(node_coords.shape) == 2 and node_coords.shape[1] == 1:
        node_coords = node_coords.reshape(-1, 1)
    
    # Process each node
    for nid_idx, nid in enumerate(_enodes):
        ncoords = node_coords[nid_idx:nid_idx+1]
        element_data[_eid][nid] = process_node_parameters(
            ncoords, nid, distributions, _region_node_values, sg_model
        )
        
        if sg_assign is not None and sg_assign.location.startswith('node'):
            if nid not in sg_assign.region_entities:
                sg_assign.addRegionEntity(nid)
    
    return element_data


def get_specific_sg_model_name(
    structure_model, sg_model_base_name, sg_params:dict,
    db_sg_model, db_sg_model_sets:DataBase=None):
    """
    Get or create a specific SG model name based on parameters.
    Uses a more efficient parameter comparison and caching strategy.
    """
    logger.debug('='*16)
    logger.debug(f'[{structure_model.name}] getting specific SG model name...')

    # Get base SG model parameters
    _sg_model = db_sg_model.getItemByName(sg_model_base_name)
    _sg_params = _sg_model.design.parameters
    _sg_params.update(_sg_model.parameters)

    # Filter parameters to only those related to the SG
    _curr_params = {k: v for k, v in sg_params.items() if k in _sg_params}

    # Create a hashable key for parameter comparison
    param_key = tuple(sorted(_curr_params.items()))

    # Check if we already have a set for these parameters
    if not hasattr(structure_model, '_param_set_map'):
        structure_model._param_set_map = {}
        structure_model._sg_param_sets = {}
        structure_model._sg_model_sets = {}

    if param_key in structure_model._param_set_map:
        _param_set_name = structure_model._param_set_map[param_key]
    else:
        # Create new parameter set
        _param_set_id = len(structure_model._sg_param_sets) + 1
        _param_set_name = f'set{_param_set_id}'
        structure_model._sg_param_sets[_param_set_name] = _curr_params
        structure_model._param_set_map[param_key] = _param_set_name
        logger.debug(f'new param set ({_param_set_name}): {_curr_params}')

    # Create specific SG model if needed
    sp_sg_model_name = f'{sg_model_base_name}_{_param_set_name}'
    if sp_sg_model_name not in structure_model._sg_model_sets:
        _sg_model = db_sg_model.getItemByName(sg_model_base_name).copy(sg_params)
        _sg_model.name = sp_sg_model_name
        structure_model._sg_model_sets[sp_sg_model_name] = _sg_model
        logger.debug(f'new sg model ({sp_sg_model_name})')

    return sp_sg_model_name


