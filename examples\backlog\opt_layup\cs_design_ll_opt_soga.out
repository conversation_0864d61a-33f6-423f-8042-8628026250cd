Writing new restart file 'cs_design_ll_opt_soga.rst'.

>>>>> Executing environment.

>>>>> Running soga iterator.

---------------------
Begin Evaluation    1
---------------------
Parameters for evaluation 1:
                     -7.1366618854e+01 ang_spar_1
                     -7.0685445723e+01 ang_spar_2
                      7.0232245857e+00 ang_spar_3
                     -4.3921933653e+01 ang_spar_4
                                    11 ply_spar_1
                                     9 ply_spar_2
                                    17 ply_spar_3
                                     5 ply_spar_4

blocking fork: msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 1:
Active set vector = { 1 1 1 }
                      1.4867719057e+00 diff_gj
                      2.4114427304e+00 diff_eiyy
                      4.0050117570e-01 diff_eizz



---------------------
Begin Evaluation    2
---------------------
Parameters for evaluation 2:
                     -4.6229132969e+01 ang_spar_1
                      2.5156712546e+01 ang_spar_2
                      4.0389721366e+01 ang_spar_3
                     -1.0456556902e+01 ang_spar_4
                                    10 ply_spar_1
                                    18 ply_spar_2
                                     3 ply_spar_3
                                    10 ply_spar_4

blocking fork: msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 2:
Active set vector = { 1 1 1 }
                      5.2338654000e+00 diff_gj
                      2.7897527442e+00 diff_eiyy
                      4.2596908639e-01 diff_eizz



---------------------
Begin Evaluation    3
---------------------
Parameters for evaluation 3:
                      2.6255378887e+01 ang_spar_1
                      2.5898312326e+01 ang_spar_2
                     -5.3244117557e+01 ang_spar_3
                     -6.4977874081e+01 ang_spar_4
                                     4 ply_spar_1
                                     4 ply_spar_2
                                    18 ply_spar_3
                                    19 ply_spar_4

blocking fork: msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 3:
Active set vector = { 1 1 1 }
                      3.6621165603e+00 diff_gj
                      1.3721271043e+00 diff_eiyy
                      2.5856824824e-01 diff_eizz



---------------------
Begin Evaluation    4
---------------------
Parameters for evaluation 4:
                      5.2299264504e+01 ang_spar_1
                     -4.0087588122e+01 ang_spar_2
                     -5.5729850154e+00 ang_spar_3
                      4.4586626789e+01 ang_spar_4
                                     9 ply_spar_1
                                     4 ply_spar_2
                                     5 ply_spar_3
                                    13 ply_spar_4

blocking fork: msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 4:
Active set vector = { 1 1 1 }
                      3.5320077734e+00 diff_gj
                      1.4491149090e+00 diff_eiyy
                      2.7271424930e-01 diff_eizz



---------------------
Begin Evaluation    5
---------------------
Parameters for evaluation 5:
                      5.7204809717e+01 ang_spar_1
                     -4.6042359691e+01 ang_spar_2
                      7.7428510392e+00 ang_spar_3
                     -4.7152012696e+01 ang_spar_4
                                    19 ply_spar_1
                                    12 ply_spar_2
                                     4 ply_spar_3
                                    18 ply_spar_4

blocking fork: msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Active response data for evaluation 5:
Active set vector = { 1 1 1 }
                      6.7222074109e+00 diff_gj
                      9.9433325357e-01 diff_eiyy
                      2.5890795693e-01 diff_eizz



---------------------
Begin Evaluation    6
---------------------
Parameters for evaluation 6:
                      2.6255378887e+01 ang_spar_1
                      2.5898312326e+01 ang_spar_2
                     -5.3244117557e+01 ang_spar_3
                     -6.4977874081e+01 ang_spar_4
                                    10 ply_spar_1
                                    18 ply_spar_2
                                     3 ply_spar_3
                                    19 ply_spar_4

blocking fork: msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile input.in --resultfile output.out --loglevelcmd info --loglevelfile info --logfile eval.log

Dakota caught signal 2
