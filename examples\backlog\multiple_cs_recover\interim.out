main: {}
structure:
  name: blade1
  design:
    dim: 1
    section_locations:
    - 0.2
    - 0.9
    cs_assignment:
    - region: all
      cs: cs1
      model: b2
  distribution:
  - name: topology
    ytype: str
    function: interpolation
    kind: previous
    data_form: compact
    data: '0.0, airfoil_gbox_uni.xml.tmp

      0.8, airfoil_solid.xml.tmp

      '
  - name: airfoil
    ytype: str
    function: interpolation
    kind: previous
    data_form: compact
    data: '0.0, sc1095.txt

      0.5, sc1094r8.txt

      '
  - name:
    - ang_spar_1
    - ang_spar_2
    ytype: float
    function: interpolation
    kind: linear
    data_form: compact
    data: '0.1, 46, -45

      0.3, -3, 0

      0.7, -44, 90

      1.0, 47, 45

      '
  css_data: {}
  cs:
    cs1:
      base: cs1
      model: md1
csdb: {}
