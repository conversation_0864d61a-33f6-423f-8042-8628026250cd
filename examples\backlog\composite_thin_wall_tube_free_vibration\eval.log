CRITICAL [2023-05-08 16:46:14] msgd.main ::  
CRITICAL [2023-05-08 16:46:14] msgd.main :: START 
CRITICAL [2023-05-08 16:46:14] io.readMSGDInput :: reading main input .\cs_beam_l0_eigen_ps_ls.yml... 
CRITICAL [2023-05-08 16:46:14] dkt.runDakota :: calling: dakota -i cs_beam_l0_eigen_ps_ls.dakota 
CRITICAL [2023-05-08 16:46:23] msgd.main :: FINISH 
CRITICAL [2023-05-08 16:46:23] msgd.main ::  
CRITICAL [2023-05-08 16:48:42] msgd.main ::  
CRITICAL [2023-05-08 16:48:42] msgd.main :: START 
CRITICAL [2023-05-08 16:48:42] io.readMSGDInput :: reading main input .\cs_beam_l0_eigen_ps_ls.yml... 
CRITICAL [2023-05-08 16:48:43] dkt.runDakota :: calling: dakota -i cs_beam_l0_eigen_ps_ls.dakota 
CRITICAL [2023-05-08 16:48:45] msgd.main :: FINISH 
CRITICAL [2023-05-08 16:48:45] msgd.main ::  
CRITICAL [2023-05-08 16:54:03] msgd.main ::  
CRITICAL [2023-05-08 16:54:03] msgd.main :: START 
CRITICAL [2023-05-08 16:54:03] io.readMSGDInput :: reading main input .\cs_beam_l0_eigen_ps_ls.yml... 
CRITICAL [2023-05-08 16:54:03] dkt.runDakota :: calling: dakota -i cs_beam_l0_eigen_ps_ls.dakota 
CRITICAL [2023-05-08 16:54:09] msgd.main :: FINISH 
CRITICAL [2023-05-08 16:54:09] msgd.main ::  
CRITICAL [2023-05-08 17:11:30] msgd.main ::  
CRITICAL [2023-05-08 17:11:30] msgd.main :: START 
CRITICAL [2023-05-08 17:11:30] io.readMSGDInput :: reading main input .\cs_beam_l0_eigen_ps_ls.yml... 
CRITICAL [2023-05-08 17:11:30] dkt.runDakota :: calling: dakota -i cs_beam_l0_eigen_ps_ls.dakota 
CRITICAL [2023-05-08 17:11:37] msgd.main :: FINISH 
CRITICAL [2023-05-08 17:11:37] msgd.main ::  
CRITICAL [2023-05-08 17:20:02] msgd.main ::  
CRITICAL [2023-05-08 17:20:02] msgd.main :: START 
CRITICAL [2023-05-08 17:20:02] io.readMSGDInput :: reading main input .\cs_beam_l0_eigen_ps_ls.yml... 
CRITICAL [2023-05-08 17:20:02] dkt.runDakota :: calling: dakota -i cs_beam_l0_eigen_ps_ls.dakota 
CRITICAL [2023-05-08 17:20:08] msgd.main :: FINISH 
CRITICAL [2023-05-08 17:20:08] msgd.main ::  
CRITICAL [2023-05-08 17:21:05] msgd.main ::  
CRITICAL [2023-05-08 17:21:05] msgd.main :: START 
CRITICAL [2023-05-08 17:21:05] io.readMSGDInput :: reading main input .\cs_beam_l0_eigen_ps_ls.yml... 
CRITICAL [2023-05-08 17:21:05] dkt.runDakota :: calling: dakota -i cs_beam_l0_eigen_ps_ls.dakota 
CRITICAL [2023-05-08 17:21:13] msgd.main :: FINISH 
CRITICAL [2023-05-08 17:21:13] msgd.main ::  
