version: "0.10"

structure:
  name: "blade"
  parameter:
    ilam_spar: 4
  cs:
    - name: 'main_cs'
      design: "airfoil"
      model:
        type: "bm1"
        solver: 'vabs'

cs:
  - name: "airfoil"
    builder: "prevabs"
    parameter:
      mdb_name: "material_database_us_ft"
      airfoil: 'sc1095.dat'
      airfoil_point_order: -1
      chord: 1.8
      rnsm: 0.001
      ilam_spar: 1
      mat_nsm: "lead"
      mat_fill_front: "Rohacell 70"
      mat_fill_back: "Plascore PN2-3/16OX3.0"
      mat_fill_te: "AS4 12k/E7K8"
      gms: 0.002
      fms: 0.04
    design:
      base_file: "airfoil_gbox_uni.xml.tmp"

analysis:
  steps:
    # - step: 'preprocess'
    #   type: 'script'
    #   module: 'data_proc_funcs'
    #   function: 'materialId2Name'

    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      preprocess:
        - module: "data_proc_funcs"
          function: "materialId2Name"
          kwargs:
      output:
        value: ["gj", "eiyy", "eizz"]

    - step: 'postprocess'
      type: 'script'
      module: 'data_proc_funcs'
      function: 'postProcess'
      kwargs:
        target:
          gj: 3000
          eiyy: 20000
          eizz: 3700000
