import logging
# import copy
# import pprint

# import msgd._global as GLOBAL
import msgd.utils as mutils
from msgd.core._analysis import AnalysisStep
from .io import readGEBTIn, readGEBTOut
# from msgd.core import MSGD

logger = logging.getLogger(__name__)



class GEBTAnalysisStep(AnalysisStep):
    step_type = 'gebt'
    def __init__(
        self, name='', activate:bool=True, parameters:dict={},
        setting:dict={}, step_args:list=[], step_kwargs:dict={},
        output_config=None
        ):
        super().__init__(
            name=name,
            activate=activate,
            parameters=parameters,
            setting=setting,
            step_args=step_args,
            step_kwargs=step_kwargs,
            output_config=output_config
        )

        self._cs_assign_loc = 'element'


    def toDictionary(self):
        _dict = super().toDictionary()
        _dict['type'] = type(self).step_type
        return _dict


    def run(self, structure_model, **kwargs):
        """
        """
        logger.info(f'[step: {self._name}] running gebt analysis...')

        beam = structure_model.model

        # beam_input = msgd.structure_data['design']
        # beam_data = msgd.getStructure()
        # beam_data = msgd.structure['model']
        # pprint.pprint(beam)

        # Update analysis configurations
        # _analysis_config = {
        #     'analysis': step_config.get('analysis', 1),
        #     'max_iteration': step_config.get('max_iteration', 1),
        #     'num_steps': step_config.get('num_steps', 1)
        # }
        # if _analysis_config['analysis'] == 3:
        #     _analysis_config['num_eigens'] = step_config.get('num_eigens', 20)

        # beam_data.update(_analysis_config)

        # Update velocities
        # _v = beam_input.get('velocity', {})
        # beam_data.update({'velocity': _v})

        # Update section assignment
        # if not 'section_assignment' in beam_data.keys():
        #     beam_data['section_assignment'] = []

        # for _i, _cs_assign in enumerate(structure_model.sg_assignments):
        #     beam_data['section_assignment'].append({
        #         'region': _cs_assign['region'],
        #         'section': _cs_assign[msgd.SG_KEY]
        #     })

        # Update CS data
        # --------------
        for _i, _cs_assign in enumerate(structure_model.sg_assignments):
            # print(_cs_assign.region)
            # print(_cs_assign.sg_model)
            # print(_cs_assign.location)
            for _memb_cs_assign in _cs_assign.discrete_sg_assigns:
                # print(_memb_cs_assign)
                _memb_id = _memb_cs_assign['region']

                _cs_names = []
                if _cs_assign.location == 'element':
                    self._cs_assign_loc = 'element'
                    _cs_name = _memb_cs_assign['sg_model']
                    _cs_names = [_cs_name, _cs_name]

                elif _cs_assign.location == 'element_node':
                    self._cs_assign_loc = 'element_node'
                    _cs_names = _memb_cs_assign['sg_model']
                    if not isinstance(_cs_names, list):
                        _cs_names = [_cs_names, _cs_names]

                elif _cs_assign.location == 'node':
                    self._cs_assign_loc = 'node'
                    # _cs_names = _memb_cs_assign['sg_model']
                    # if not isinstance(_cs_names, list):
                    #     _cs_names = [_cs_names, _cs_names]

                _sids = []
                for _cs_name in _cs_names:
                    _sid = beam.getSectionIdByName(_cs_name)
                    if not _sid:
                        _sid = beam.num_sections + 1
                        # print(structure_model.sg_model_sets)
                        # print(f'_cs_name: {_cs_name}')
                        for _set_name, _sg in structure_model.sg_model_sets.items():
                            # print(_sg.name)
                            # print(_sg.constitutive)
                            if _cs_name == _sg.name:
                                _cs_prop = _sg.constitutive
                        # _cs_prop = db_sg_model.getItemByName(_cs_name).constitutive
                        beam.addSection(_sid, _cs_prop)
                        beam.addSectionIdToName(_sid, _cs_name)
                    logger.debug(f'assigning section {_sid} to member {_memb_id}')
                    _sids.append(_sid)

                beam.assignMemberSections(
                    _memb_id, _sids[0], _sids[1])

        logger.debug(beam.section_assignments)

        # mutils.dumpData(beam_data, f'{beam_data["name"]}_curr_design')

        # Build beam
        # beam = buildGEBTBeam(beam_data)


        # Write input
        # -----------
        fn_gebt = beam.writeGEBTIn()


        # Run GEBT
        # --------
        cmd = ['gebt', fn_gebt]
        timeout = 60
        mutils.run(cmd, timeout)
        fn_gebt_out = fn_gebt + '.out'


        # Output
        # -----------
        readGEBTOut(fn_gebt_out, beam)

        for _output_config in self._output_config:

            _output_value = _output_config.get('value', [])
            if (not isinstance(_output_value, list)) and (_output_value != 'all'):
                _output_value = [_output_value,]

            _output_to = _output_config.get('to', 'main')
            # _output_file = _output_config.get('file_name', '')

            # _output_names = []
            _output_data = None

            # Get desired output data
            # -----------------------

            # Get all output data generated by gebt
            if _output_value == 'all':

                # Static analysis
                # Get all quantities (displacements, forces, etc.)
                # of all nodes (points and member elements)
                if beam.analysis == 1:

                    # Get step output
                    _step = _output_config.get('step', -1)
                    # _result_step_last = beam.results_steps[_step]
                    _result_step_last = beam.getResultStep(_step)

                    if self._cs_assign_loc == 'element':
                        _output_data = _result_step_last.getOutputMemberFlattened()
                    elif self._cs_assign_loc == 'element_node':
                        pass
                    elif self._cs_assign_loc == 'node':
                        _output_data = _result_step_last.getOutputNodeFlattened()

            # Get specific output data
            elif isinstance(_output_value, list):
                _output_data = {}

                # Static analysis
                if beam.analysis == 1:
                    _entity_request = _output_config.get('entity', 'point')
                    _entity_name = _output_config.get('entity_name', None)

                    if not _entity_name is None:
                        # Get step output
                        _step = _output_config.get('step', -1)
                        _beam_out_step_last = beam.getResultStep(_step)
                        _node_id = beam.getEntitySetByName(_entity_request, _entity_name)[0]

                        # Get node output
                        _node_out = _beam_out_step_last.getResultById(_entity_request, _node_id)


                for _name in _output_value:
                    if _name == 'mass':
                        _value = beam.mass

                    # Static analysis
                    elif beam.analysis == 1:
                        _value = _node_out.get(_name)

                    # Eigen analysis
                    elif beam.analysis == 3:
                        _value = beam.getResult(_name)

                    _output_data[_name] = _value

            # Store output data
            # -----------------

            # Store output data to main data storage
            if _output_to == 'main':
                _output_names = _output_config.get('name', _output_value)
                if not isinstance(_output_names, list):
                    _output_names = [_output_names,]

                for _request_name, _output_name in zip(_output_value, _output_names):
                    self._step_output[_output_name] = _output_data[_request_name]

            # Store output data to file
            elif _output_to == 'file':

                _fn_response = _output_config.get('file_name', 'struct_resp.csv')

                col_names = []

                if self._cs_assign_loc == 'element':
                    col_names.append('element')
                elif self._cs_assign_loc == 'node':
                    col_names.append('node')

                col_names.extend([
                    'x1', 'x2', 'x3',
                    'u1', 'u2', 'u3', 'r1', 'r2', 'r3',
                    'f1', 'f2', 'f3', 'm1', 'm2', 'm3',
                    'c11', 'c12', 'c13', 'c21', 'c22', 'c23', 'c31', 'c32', 'c33',
                ])

                _c = [1, 0, 0, 0, 1, 0, 0, 0, 1]  # default direction cosine matrix

                with open(_fn_response, 'w') as file:

                    # Write header
                    file.write(','.join(col_names))
                    file.write('\n')

                    for _out in _output_data:
                        _row = _out + _c
                        file.write(','.join(list(map(str, _row))))
                        file.write('\n')


        # # Store final outputs
        # # -------------------
        # if self._output_config:
        #     # step_out = self._output_config.get('output', [])
        #     # for _out in step_out:
        #     _value_names = self._output_config.get('value', [])
        #     for _name in _value_names:
        #         _value = beam.getResult(_name)
        #         self._step_output[_name] = _value

        return
