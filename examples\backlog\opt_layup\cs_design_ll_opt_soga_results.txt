soga/NO_METHOD_ID/1/"Best Continuous Variables"
  Array Spans: "Best Sets"
  Row Labels: "ang_spar_1" "ang_spar_2" "ang_spar_3" "ang_spar_4"
  Data (vector<vector<double>>):
      Array Entry 1:
                      5.7204809717e+01
                     -4.6042359691e+01
                     -5.3244117557e+01
                     -4.7152012696e+01
soga/NO_METHOD_ID/1/"Best Discrete Integer Variables"
  Array Spans: "Best Sets"
  Row Labels: "ply_spar_1" "ply_spar_2" "ply_spar_3" "ply_spar_4"
Warning: unknown type of any: class std::vector<class Teuchos::SerialDenseVector<int,int>,class std::allocator<class Teuchos::SerialDenseVector<int,int> > >
soga/NO_METHOD_ID/1/"Best Functions"
  Array Spans: "Best Sets"
  Row Labels: "diff_gj" "diff_eiyy" "diff_eizz"
  Data (vector<vector<double>>):
      Array Entry 1:
                      4.0095805926e+00
                      4.2230364281e-01
                      1.4236595607e-01
soga/NO_METHOD_ID/1/"Continuous Variable Labels"
  Data (vector<string>):
      "ang_spar_1" "ang_spar_2" "ang_spar_3" "ang_spar_4"
soga/NO_METHOD_ID/1/"Discrete Integer Variable Labels"
  Data (vector<string>):
      "ply_spar_1" "ply_spar_2" "ply_spar_3" "ply_spar_4"
soga/NO_METHOD_ID/1/"Function Labels"
  Data (vector<string>):
      "diff_gj" "diff_eiyy" "diff_eizz"

