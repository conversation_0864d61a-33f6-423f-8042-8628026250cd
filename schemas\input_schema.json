{"$schema": "http://json-schema.org/draft-07/schema#", "title": "MSGD Input Schema", "description": "Schema for MSG Design input files", "type": "object", "properties": {"version": {"type": "string", "description": "Version of the input format", "default": "0.7"}, "name": {"type": "string", "description": "Name of the design"}, "include": {"type": "array", "description": "List of files to include", "items": {"type": "string"}}, "structure": {"type": "object", "description": "Global structure specification", "properties": {"name": {"type": "string"}, "dim": {"type": "integer", "minimum": 1, "maximum": 3}, "physics": {"type": "string", "enum": ["elastic", "thermoelastic"], "default": "elastic"}, "builder": {"type": "string"}, "builder_cmd": {"type": "string"}, "builder_version": {"type": "string"}, "parameter": {"type": "object", "additionalProperties": true}, "distribution": {"type": "array", "items": {"$ref": "#/definitions/distribution"}}, "design": {"type": "object", "properties": {"file": {"type": "string"}, "section_prop_file": {"type": "string"}, "sg_assignment": {"type": "array", "items": {"$ref": "#/definitions/sg_assignment"}}}}, "model": {"type": "object", "properties": {"tool": {"type": "string"}, "main_file": {"type": "string"}, "prop_file": {"type": "string"}, "config": {"type": "object"}}}, "sg": {"type": "array", "items": {"$ref": "#/definitions/sg_model_ref"}}, "cs": {"type": "array", "items": {"$ref": "#/definitions/sg_model_ref"}}}}, "sg": {"type": "array", "description": "Subgenome (SG) design specifications", "items": {"$ref": "#/definitions/sg_design"}}, "cs": {"type": "array", "description": "Cross-section design specifications", "items": {"$ref": "#/definitions/sg_design"}}, "function": {"type": "array", "description": "Function specifications", "items": {"$ref": "#/definitions/function"}}, "analysis": {"type": "object", "description": "Analysis configuration", "properties": {"steps": {"type": "array", "items": {"$ref": "#/definitions/analysis_step"}}}}, "study": {"type": "object", "description": "Design study specification", "properties": {"method": {"type": "string"}, "variables": {"$ref": "#/definitions/study_variables"}, "interface": {"$ref": "#/definitions/study_interface"}, "responses": {"$ref": "#/definitions/study_responses"}}}}, "definitions": {"sg_design": {"type": "object", "properties": {"name": {"type": "string"}, "dim": {"type": "integer", "minimum": 1, "maximum": 3}, "builder": {"type": "string", "default": "default"}, "solver": {"type": "string"}, "parameter": {"type": "object", "additionalProperties": true}, "design": {"type": "object", "properties": {"base_file": {"type": "string"}, "file": {"type": "string"}, "symmetry": {"type": "integer"}, "layers": {"type": "array", "items": {"type": "object", "properties": {"material": {"type": "string"}, "ply_thickness": {"type": "number"}, "number_of_plies": {"type": "integer"}, "in-plane_orientation": {"type": ["number", "string"]}}}}}, "additionalProperties": true}, "model": {"oneOf": [{"$ref": "#/definitions/sg_model"}, {"type": "array", "items": {"$ref": "#/definitions/sg_model"}}]}, "sg": {"type": "array", "items": {"$ref": "#/definitions/sg_model_ref"}}, "sg_assignment": {"type": "array", "items": {"$ref": "#/definitions/sg_assignment"}}}, "required": ["name"]}, "sg_model": {"type": "object", "properties": {"type": {"type": "string"}, "tool": {"type": "string"}, "physics": {"type": "string"}, "data_format": {"type": "string"}, "config": {"type": "object"}, "property": {"$ref": "#/definitions/material_property"}, "sg_assignment": {"type": "array", "items": {"$ref": "#/definitions/sg_assignment"}}}}, "sg_model_ref": {"type": "object", "properties": {"name": {"type": "string"}, "design": {"type": "string"}, "model": {"$ref": "#/definitions/sg_model"}}, "required": ["name"]}, "sg_assignment": {"type": "object", "properties": {"region": {"type": "string", "default": "all"}, "location": {"type": "string", "default": "element"}, "sg": {"type": "string"}, "cs": {"type": "string"}}}, "material_property": {"type": "object", "properties": {"density": {"type": "number"}, "temperature": {"type": "number", "default": 0}, "anisotropy": {"type": "string", "enum": ["isotropic", "engineering", "orthotropic"], "default": "isotropic"}, "elasticity": {"type": "array", "items": {"type": "number"}}, "elastic": {"type": "array", "items": {"type": "number"}}, "cte": {"type": "array", "items": {"type": "number"}, "description": "Coefficient of thermal expansion"}, "specific_heat": {"type": "number", "default": 0}, "failure_criterion": {"type": "integer", "default": 0}, "strength": {"type": "array", "items": {"type": "number"}}, "char_len": {"type": "number", "default": 0}}}, "distribution": {"type": "object", "properties": {"name": {"type": "string"}, "region": {"type": "string", "default": "all"}, "domain": {"type": "string", "default": "none"}, "function": {"type": "string"}, "type": {"type": "string", "default": "float"}, "xdim": {"type": "integer", "default": 1}, "xscale": {"type": "number", "default": 1.0}, "coefficient": {"type": "object"}, "data": {"type": "array"}, "data_form": {"type": "string", "default": "explicit"}, "file_name": {"type": "string"}, "file_format": {"type": "string"}, "data_request": {"type": "string"}, "config": {"type": "object"}}, "required": ["name"]}, "function": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["expression", "interpolation", "python"]}, "return_type": {"type": "string", "default": "float"}, "expression": {"type": "string"}, "coefficient": {"type": "object"}, "coefficients": {"type": "array", "items": {"type": "string"}}, "interp_data_x": {"type": "array"}, "interp_data_y": {"type": "array"}, "interp_kind": {"type": "string", "default": "linear"}, "interp_fill_value": {"type": "string", "default": "extrapolate"}, "py_mod_name": {"type": "string"}, "py_function_name": {"type": "string"}}, "required": ["name", "type"]}, "analysis_step": {"type": "object", "properties": {"step": {"type": "string"}, "type": {"type": "string", "enum": ["sg", "cs", "script", "custom", "gebt", "<PERSON><PERSON><PERSON><PERSON>"]}, "activate": {"type": "boolean", "default": true}, "analysis": {"type": "string"}, "output": {"oneOf": [{"type": "array"}, {"type": "object"}]}, "work_dir": {"type": "string"}, "section_response": {"type": "object"}, "setting": {"type": "object"}, "args": {"type": "array"}, "kwargs": {"type": "object"}, "pre_process": {"type": "array"}, "post_process": {"type": "array"}, "module": {"type": "string"}, "function": {"type": "string"}, "step_result_file": {"type": "string"}}, "required": ["step", "type"]}, "study_variables": {"type": "object", "properties": {"data_form": {"type": "string", "enum": ["data", "explicit"]}, "data": {"type": "string"}, "list": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["continuous", "discrete"]}, "bounds": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}}, "required": ["name", "type"]}}, "design": {"type": "object", "properties": {"continuous": {"type": "array", "items": {"type": "object", "properties": {"descriptor": {"type": "string"}, "upper_bound": {"type": ["number", "string"]}, "lower_bound": {"type": ["number", "string"]}, "initial": {"type": ["number", "string"]}}}}}}}}, "study_interface": {"type": "object", "additionalProperties": true}, "study_responses": {"type": "object", "properties": {"data_form": {"type": "string", "enum": ["data", "explicit"]}, "data": {"type": "string"}, "response_functions": {"type": "array", "items": {"type": "object", "properties": {"descriptor": {"type": "string"}}}}, "objective_functions": {"type": "array", "items": {"type": "object", "properties": {"descriptor": {"type": "string"}, "sense": {"type": "string", "enum": ["min", "max"], "default": "min"}, "weight": {"type": "number", "default": 1}}}}, "inequality_constraints": {"type": "array", "items": {"type": "object", "properties": {"descriptor": {"type": "string"}}}}}}}}