import math


def analysis_postpro(
    data, structure, db_sg_model,
    *args,
    **kwargs
    ):
    """ Post-process results from previous analysis steps.

    This is the last step in a Dakota evaluation.
    The goal is to calculate response/objective/constraint function values
    and store them in data['dakota']:

        data['main'] = {
            'descriptor1': value1,
            'descriptor2': value2,
            ...
        }
    """

    # cs_name = 'main_cs_set1'

    prop_names = kwargs['beam_properties']
    prop_targets = list(map(float, kwargs['target']))

    prop_calc = structure.getSGenomeProperty(
        prop_names, db_sg_model
    )
    print(f'prop_calc: {prop_calc}')

    for i, n in enumerate(prop_names):
        _name = f'diff_{n}'
        _value = math.fabs((prop_calc[i] - prop_targets[i]) / prop_targets[i])
        data[_name] = _value
        # p = data['structure']['css_data'][cs_name]['property']['md1'][n]
        # calc_props.append(p)

    # data['diff_gj'] = math.fabs((calc_props[0] - target_props[0]) / target_props[0])
    # data['diff_eiyy'] = math.fabs((calc_props[1] - target_props[1]) / target_props[1])
    # data['diff_eizz'] = math.fabs((calc_props[2] - target_props[2]) / target_props[2])
    # data['diff_sc2'] = math.fabs((calc_props[3] - target_props[3]) / target_props[3])
    # data['diff_mc2'] = math.fabs((calc_props[4] - target_props[4]) / target_props[4])

    print('data')
    print(data)

    return

