[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "MSGD"
version = "0.8"
description = "MSG-based Multiscale Structural Design Tool"
authors = [
    {name = "<PERSON> Tian", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "numpy>=1.26.0",
    "PyYAML>=6.0.1",
    "scipy>=1.11.0",
    "sympy==1.11.1",
    "tqdm==4.65.0",
    "pydantic>=2.0.0",
    "sgio",
    "pytest>=8.4.1",
    "icecream>=2.1.7",
]

[project.scripts]
datc = "bin.run:main"
ivabs = "bin.run:main"
msgd = "bin.run:main"
swiftcomp = "bin.swiftcomp:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["sg_library*", "bin*", "scripts*"]

[tool.uv]
dev-dependencies = []
