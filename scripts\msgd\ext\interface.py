import logging
import os
# from typing import Protocol
# from msgd.core import MSGDStructure

# import sgio.meshio as meshio

import msgd.utils as mutils
# from .abaqus import *
from . import (
    abaqus,
    rcas,
    gebt
)
# import gebt


# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)

# class StructureImporter(Protocol):
#     def importFromFile(fn:str) -> MSGDStructure:
#         ...


# class StructureExporter(Protocol):
#     ...



# importers = {
#     'abaqus': AbaqusStructureImporter
# }


# def getStructureImporter(format:str) -> StructureImporter:
#     return importers[format]


def importStructureMesh(
    file_name:str, data_format:str,
    **kwargs
    ):
    """Import mesh data from an external tool.
    """

    # print(f'data_format = {data_format}')

    model = None

    if data_format == 'gebt':
        model = gebt.readGEBTIn(file_name, **kwargs)

    elif data_format == 'abaqus':
        model = abaqus.readAbaqusInput(file_name, **kwargs)

    elif data_format == 'rcas':
        model = rcas.readRcasInput(file_name, **kwargs)

    return model


def writeSGPropertyFile(
    file_name:str, data_format:str, sg_sets:dict,
    elem_sets:dict={},
    physics:str='elastic',
    **kwargs
    ):
    """Write SG properties to a file.
    """

    if data_format == 'gebt':
        ...

    elif data_format == 'abaqus':
        abaqus.writeAbaqusSectionsInp(
            fn=file_name, prop_sets=sg_sets,
            elem_sets=elem_sets,
            physics=physics,
            **kwargs
            )

    elif data_format == 'rcas':
        rcas.writeRcasPropsFile(
            file_name=file_name
        )

    return


def importStructure(format, structure_input):
    """Import structural data from an external tool.
    """

    model = None

    fn = structure_input.get('file', '')
    _fn_dir, _fn_base, _fn_ext = mutils.splitFileName(fn)

    if format == 'abaqus':
        model = abaqus.readAbaqusInput(fn)

        _orient_name = structure_input.get('orient_name', None)
        if _orient_name: model.orient[_orient_name] = {}

        model.fn_section = structure_input.get('section_prop_file', 'sg_sections.inp')

    elif format == 'gebt':
        # _model = meb.buildGEBTBeam(fn_structure_model)
        model = mutils.parseDataFile(fn)
        model['name'] = structure_input.get('name', _fn_base)

    return model





def loadDataFromFile(
    file_name, file_format,
    yname, xscale=1.0,
    **kwargs):
    """
    """

    logger.info(f'loading data from {file_name} ({file_format})...')

    data = {}
    """
    {
        'data1': {
            'x': [...],
            'y': [...],
        },
        ...
    }
    """

    # xs = []
    # ys = []

    # if file_format == 'galaxy_oml' or file_format == 'rcas_oml':
    if file_format.startswith('rcas') or file_format.startswith('galaxy'):
        _file = 'main'

        _rcas_model = kwargs.get('rcas_model', None)
        if _rcas_model is None:
            _rcas_model = rcas.RCASModel()

            if _file == 'main':
                _oml = rcas.readRcasOml(file_name)
                _rcas_model.fe_nodes = _oml['fenode']
                _rcas_model.aero_nodes = _oml['aeronode']
                _rcas_model.aero_segs = _oml['aeroseg']
                _rcas_model.airfoil_interp = _oml['airfoilinterp']

        if not isinstance(yname, list):
            yname = [yname,]

        for _name in yname:
            logger.info(f'loading data {_name}...')

            if _name == 'airfoilinterp':
                _ext = kwargs.get('airfoil_file_ext', 'xy')
                # try:
                #     ext = kwargs['airfoil_file_ext']
                # except KeyError:
                #     ext = 'xy'
                _xy = _rcas_model.getAirfoilInterpNames(_ext)
                # x = airfoil_data['x']
                # yj = airfoil_data['y']

                # Convert to actual file names
                dir = '.'
                for k, fn in enumerate(_xy['y']):
                    for dirpath, dirnames, filenames in os.walk(dir):
                        for dfn in filenames:
                            if (dfn.upper() == fn.upper()):
                                _xy['y'][k] = dfn

            elif _name == 'chord_structure':
                _xy = _rcas_model.getChordStruct()
                # x = chord_struct['x']
                # yj = chord_struct['y']
                # for segi in range(1, len(oml['aeroseg'])+1):
                #     pnid = oml['aeroseg'][segi]['aero_nodes'][0]
                #     x.append(oml['aeronode'][pnid][0])
                #     segi_chord = oml['aeroseg'][segi]['chord']
                #     segi_shear = oml['aeroseg'][segi]['shear']
                #     # print('seg {}: chord = {}, shear = {}'.format(segi, segi_chord, segi_shear))
                #     yj.append(segi_chord*math.cos(segi_shear))
                # seg_last = oml['aeroseg'][len(oml['aeroseg'])]
                # pnid = seg_last['aero_nodes'][1]
                # x.append(oml['aeronode'][pnid][0])
                # yj.append(yj[-1])

            if xscale == 'nondim':
                s = _xy['x'][-1]
                _xy['x'] = [xi / s for xi in _xy['x']]

            # xs.append(x)
            # ys.append(yj)
            data[_name] = _xy

    return data


