import sys
from PySide6.QtWidgets import (
    <PERSON>A<PERSON><PERSON>, QMainWindow, QDockWidget, QTextEdit, QPushButton, QWidget, QVBoxLayout
)

class DockableExample(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Floating Windows with QDockWidget")

        # Button to create floating windows
        self.add_window_button = QPushButton("Open Floating Window")
        self.add_window_button.clicked.connect(self.create_dock_window)

        # Set up the main layout
        container_widget = QWidget()
        layout = QVBoxLayout(container_widget)
        layout.addWidget(self.add_window_button)
        self.setCentralWidget(container_widget)

    def create_dock_window(self):
        dock_widget = QDockWidget("Floating Window", self)
        dock_widget.setWidget(QTextEdit())  # Add a QTextEdit inside the floating window
        dock_widget.setFloating(True)  # Start as a floating window
        self.addDockWidget(0x2, dock_widget)  # Default position (can be left, right, etc.)
        dock_widget.show()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    main_window = DockableExample()
    main_window.show()
    sys.exit(app.exec())
