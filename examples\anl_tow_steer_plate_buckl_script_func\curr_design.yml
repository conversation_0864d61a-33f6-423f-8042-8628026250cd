version: '0.10'
structure:
  name: square_plate
  parameter: {}
  model:
    type: ''
    tool: abaqus
    tool_version: ''
    main_file: plate_sq2_ss_nfx_bck_s4r_40x40_si.inp
    prop_file: shellsections.inp
    config:
      orient_name: Ori-1
  design:
    name: square_plate
    parameter:
      l1v1: 0
      l1v2: 30
      l2v3: 45
      l2v4: 0
      l3v5: 0
      l3v6: 0
    dim: null
    builder: default
    design: null
    distribution:
    - name: a1
      type: float
      coefficient:
        v1: 0
        v2: 30
      data_form: explicit
  sg_assignment:
  - region: all
    location: element
    sg: mainsg
  physics: elastic
functions:
- name: f1
  type: float
  module: users_function
  function: calcFiberAngle
  coefficients: {}
sg:
- name: lv1_layup
  parameter:
    a1: 0
    a2: 0
    a3: 0
  dim: 1
  builder: default
  design:
    symmetry: 1
    layers:
    - material: m2
      ply_thickness: 0.00015
      number_of_plies: 1
      in-plane_orientation: a1
    - material: m2
      ply_thickness: 0.00015
      number_of_plies: 1
      in-plane_orientation: a2
    - material: m2
      ply_thickness: 0.00015
      number_of_plies: 1
      in-plane_orientation: a3
- name: m2
  parameter: {}
  dim: null
  builder: default
  design: null
analysis:
  steps:
  - step: homogenization
    activate: true
    type: sg
    analysis: h
    work_dir: sg
  - step: buckling analysis
    activate: true
    setting:
      timeout: 300
    kwargs:
      exec:
        args:
        - interactive
        kwargs:
          ask_delete: 'OFF'
    type: abaqus
    post_process:
    - script: abq_get_result.py
      args:
      - plate_sq2_ss_nfx_bck_s4r_40x40_si.odb
      - abq_result.dat
    step_output_file: abq_result.dat
