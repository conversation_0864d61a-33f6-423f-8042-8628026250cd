# unit system
# m, N, Pa

version: "0.10"


# --------------------------------------------------------------------
structure:
  name: "square_plate"
  # type: null

  parameter:
    l1v1: 0
    l1v2: 30
    l2v3: 45
    l2v4: 0
    l3v5: 0
    l3v6: 0

  distribution:
    - name: a1
      function: f1
      coefficient:
        v1: l1v1
        v2: l1v2
    # - name: a2
    #   function: f2
    #   coefficient:
    #     v3: l2v3
    #     v4: l2v4
    # - name: a3
    #   function: f3
    #   coefficient:
    #     v5: l3v5
    #     v6: l3v6

  model:
    tool: "abaqus"
    main_file: "plate_sq2_ss_nfx_bck_s4r_40x40_si.inp"
    prop_file: "shellsections.inp"
    config:
      orient_name: 'Ori-1'

  sg_assignment:
    - region: 'all'
      sg: "mainsg"
  sg:
    - name: 'mainsg'
      design: "lv1_layup"
      model:
        type: "pl1"

# --------------------------------------------------------------------
function:
  - name: "f1"
    type: 'script'
    module: 'users_function'
    function: 'calcFiberAngle'
  # - name: "f1"
  #   type: "expression"
  #   expression: "2*(v2-v1)*abs(x)/2+v1"
  #   coefficient: 
  #     "v1": 0
  #     "v2": 1
  # - name: "f2"
  #   type: "expression"
  #   expression: "2*(v4-v3)*abs(y)/2+v3"
  #   coefficient: 
  #     "v3": 0
  #     "v4": 1
  # - name: "f3"
  #   type: "expression"
  #   expression: "(v6-v5)*abs(x)/2+v5"
  #   coefficient: 
  #     "v5": 0
  #     "v6": 1

# --------------------------------------------------------------------
sg:
  - name: "lv1_layup"
    parameter:
      a1: 0
      a2: 0
      a3: 0
    dim: 1
    builder: "default"
    design:
      # type: 'gd1'
      # type: "descriptive"
      symmetry: 1
      layers:
        - material: "m2"
          ply_thickness: 1.5e-4
          number_of_plies: 1
          in-plane_orientation: a1
        - material: "m2"
          ply_thickness: 1.5e-4
          number_of_plies: 1
          in-plane_orientation: a2
        - material: "m2"
          ply_thickness: 1.5e-4
          number_of_plies: 1
          in-plane_orientation: a3

    model:
      - type: "pl1"
        tool: "swiftcomp"
        version: "2.1"
        config:
          mesh_size: -1

  - name: "m2"
    # type: "material"
    model:
      type: "sd1"
      property:
        density: 1.0
        anisotropy: "engineering"
        elasticity:
          [
            181e9, 8.96e9, 8.96e9,
            7.2e9, 7.2e9, 7.2e9,
            0.3, 0.28, 0.28
          ]


# --------------------------------------------------------------------
analysis:
  steps:
    - step: "homogenization"
      type: "sg"
      analysis: "h"
      # setting:
      #   solver: "swiftcomp"

    - step: "buckling analysis"
      type: "abaqus"
      # job_file: "plate_sq2_ss_nfx_bck_s4r_40x40_si.inp"
      setting:
        timeout: 300
      kwargs:
        exec:
          args:
            - "interactive"
          kwargs:
            ask_delete: "OFF"
      post_process:
        - script: "abq_get_result.py"
          args:
            - "plate_sq2_ss_nfx_bck_s4r_40x40_si.odb"
            - "abq_result.dat"
      step_result_file: "abq_result.dat"

