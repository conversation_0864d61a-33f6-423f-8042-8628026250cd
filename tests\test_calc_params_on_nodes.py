import numpy as np
import pytest
from msgd.design.distribution import Distribution
from msgd.design.discretize import calc_params_on_nodes
# from msgd.design.transformation import Domain
import msgd.utils.function as mutils


class TestCalcParamsOnNodes:
    @pytest.fixture
    def linear_function(self):
        """Create a simple linear function for testing"""
        class LinearFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "linear"
                self.vtype = "float"
            
            def __call__(self, x):
                return 2 * x + 1
        
        return LinearFunction()

    @pytest.fixture
    def linear_function_2d(self):
        """Create a simple linear function for testing"""
        class LinearFunction2D(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "linear"
                self.vtype = "float"
            
            def __call__(self, x):
                return 2*x[:,0] + 3*x[:,1] + 1
        
        return LinearFunction2D()

    @pytest.fixture
    def quadratic_function(self):
        """Create a quadratic function for testing"""
        class QuadraticFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "quadratic"
                self.vtype = "float"
            
            def __call__(self, x):
                return x**2
        
        return QuadraticFunction()

    @pytest.fixture
    def quadratic_function_2d(self):
        """Create a quadratic function for testing"""
        class QuadraticFunction2D(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "quadratic"
                self.vtype = "float"
            
            def __call__(self, x):
                return x[:,0]**2 + 2*x[:,1]**2
        
        return QuadraticFunction2D()

    # @pytest.fixture
    # def constant_function(self):
    #     """Create a constant function for testing"""
    #     class ConstantFunction(mutils.MSGDFunction):
    #         def __init__(self):
    #             super().__init__()
    #             self.name = "constant"
    #             self.vtype = "float"
            
    #         def __call__(self, x):
    #             return 5.0  # Always returns constant
        
    #     return ConstantFunction()

    @pytest.fixture
    def single_param_distribution(self, linear_function):
        """Create a distribution with single parameter"""
        dist = Distribution(
            name="thickness",
            region="test_region",
            func_base=linear_function,
            vtype="float"
        )
        dist._function = [linear_function]
        return dist

    @pytest.fixture
    def single_param_distribution_2d(self, linear_function_2d):
        """Create a distribution with single parameter"""
        dist = Distribution(
            name="thickness",
            region="test_region",
            func_base=linear_function_2d,
            vtype="float"
        )
        dist._function = [linear_function_2d]
        return dist

    @pytest.fixture
    def multi_param_distribution(self, linear_function, quadratic_function):
        """Create a distribution with multiple parameters"""
        dist = Distribution(
            name=["thickness", "density"],
            region="test_region",
            func_base=None,
            vtype="float"
        )
        dist._function = [linear_function, quadratic_function]
        return dist

    @pytest.fixture
    def multi_param_distribution_2d(self, linear_function_2d, quadratic_function_2d):
        """Create a distribution with multiple parameters"""
        dist = Distribution(
            name=["thickness", "density"],
            region="test_region",
            func_base=None,
            vtype="float"
        )
        dist._function = [linear_function_2d, quadratic_function_2d]
        return dist

    def test_single_node_single_param(self, single_param_distribution):
        """Test calculation for single node with single parameter"""
        nodes = np.array([[1.0]])
        result = calc_params_on_nodes(single_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (1, 1)  # 1 node, 1 parameter
        assert np.allclose(result, [[3.0]])  # 2*1 + 1 = 3

    def test_multiple_nodes_single_param(self, single_param_distribution):
        """Test calculation for multiple nodes with single parameter"""
        nodes = np.array([[1.0], [2.0], [3.0]])
        result = calc_params_on_nodes(single_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (3, 1)  # 3 nodes, 1 parameter
        expected = np.array([[3.0], [5.0], [7.0]])  # 2*x + 1
        assert np.allclose(result, expected)

    def test_single_node_multiple_params(self, multi_param_distribution):
        """Test calculation for single node with multiple parameters"""
        nodes = np.array([[2.0]])
        result = calc_params_on_nodes(multi_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (1, 2)  # 1 node, 2 parameters
        expected = np.array([[5.0, 4.0]])  # [2*2+1, 2^2] = [5, 4]
        assert np.allclose(result, expected)

    def test_multiple_nodes_multiple_params(self, multi_param_distribution):
        """Test calculation for multiple nodes with multiple parameters"""
        nodes = np.array([[1.0], [2.0], [3.0]])
        result = calc_params_on_nodes(multi_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (3, 2)  # 3 nodes, 2 parameters
        expected = np.array([
            [3.0, 1.0],  # [2*1+1, 1^2] = [3, 1]
            [5.0, 4.0],  # [2*2+1, 2^2] = [5, 4]
            [7.0, 9.0]   # [2*3+1, 3^2] = [7, 9]
        ])
        assert np.allclose(result, expected)

    def test_2d_coordinates(self, single_param_distribution_2d):
        """Test with 2D coordinate nodes"""
        nodes = np.array([[1.0, 0.5], [2.0, 1.0], [3.0, 1.5]])
        result = calc_params_on_nodes(single_param_distribution_2d, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (3, 1)
        # Function should use first coordinate only for linear function
        expected = np.array([[4.5], [8.0], [11.5]])
        assert np.allclose(result, expected)

    def test_multiple_nodes_multiple_params_2d(self, multi_param_distribution_2d):
        """Test calculation for multiple nodes with multiple parameters"""
        nodes = np.array([[1.0, 0.5], [2.0, 1.0], [3.0, 1.5]])
        result = calc_params_on_nodes(multi_param_distribution_2d, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (3, 2)  # 3 nodes, 2 parameters
        expected = np.array([
            [4.5, 1.5],  # [2*1+1, 1^2] = [3, 1]
            [8.0, 6.0],  # [2*2+1, 2^2] = [5, 4]
            [11.5, 13.5]   # [2*3+1, 3^2] = [7, 9]
        ])
        assert np.allclose(result, expected)

    # def test_constant_function_distribution(self, constant_function):
    #     """Test with distribution that returns constant values"""
    #     dist = Distribution(
    #         name="constant_param",
    #         region="test_region",
    #         func_base=constant_function,
    #         vtype="float"
    #     )
    #     dist._function = [constant_function]
        
    #     nodes = np.array([[1.0], [2.0], [3.0]])
    #     result = calc_params_on_nodes(dist, nodes)
        
    #     assert isinstance(result, np.ndarray)
    #     assert result.shape == (3, 1)
    #     expected = np.array([[5.0], [5.0], [5.0]])  # All constant
    #     assert np.allclose(result, expected)

    def test_empty_nodes_array(self, single_param_distribution):
        """Test with empty nodes array"""
        nodes = np.array([]).reshape(0, 1)
        result = calc_params_on_nodes(single_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (0, 1)

    def test_1d_input_nodes(self, single_param_distribution):
        """Test with 1D input nodes array"""
        nodes = np.array([1.0, 2.0, 3.0])  # 1D array
        with pytest.raises(ValueError, match="Input nodes must be 2D array"):
            calc_params_on_nodes(single_param_distribution, nodes)

    def test_invalid_input_dimensions(self, single_param_distribution):
        """Test error handling for invalid input dimensions"""
        # Test 3D input
        nodes = np.array([[[1.0]]])
        with pytest.raises(ValueError, match="Input nodes must be 2D array"):
            calc_params_on_nodes(single_param_distribution, nodes)

    # def test_distribution_with_domain_transformation(self, linear_function):
    #     """Test distribution with domain transformation"""
    #     # Create a simple domain transformation that only uses first coordinate
    #     class XOnlyDomain:
    #         def transform(self, x, _id=0, **kwargs):
    #             return x[:, :1]  # Only keep first coordinate

    #     dist = Distribution(
    #         name="thickness",
    #         region="test_region",
    #         domain=XOnlyDomain(),
    #         func_base=linear_function,
    #         vtype="float"
    #     )
    #     dist._function = [linear_function]

    #     # Test with 2D coordinates - should only use first coordinate
    #     nodes = np.array([[1.0, 100.0], [2.0, 200.0]])
    #     result = calc_params_on_nodes(dist, nodes)

    #     assert isinstance(result, np.ndarray)
    #     assert result.shape == (2, 1)
    #     # Should only use first coordinate: 2*1+1=3, 2*2+1=5
    #     expected = np.array([[3.0], [5.0]])
    #     assert np.allclose(result, expected)

    def test_list_input_conversion(self, single_param_distribution):
        """Test that list inputs are properly converted to numpy arrays"""
        nodes = [[1.0], [2.0], [3.0]]  # List instead of numpy array
        result = calc_params_on_nodes(single_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (3, 1)
        expected = np.array([[3.0], [5.0], [7.0]])
        assert np.allclose(result, expected)

    def test_single_point_as_1d_array(self, single_param_distribution):
        """Test single point provided as 1D array"""
        nodes = np.array([[2.0]])  # Single point as 1D array
        result = calc_params_on_nodes(single_param_distribution, nodes)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (1, 1)
        expected = np.array([[5.0]])  # 2*2 + 1 = 5
        assert np.allclose(result, expected)

    def test_parameter_order_consistency(self, multi_param_distribution):
        """Test that parameter order is consistent with distribution name order"""
        nodes = np.array([[2.0]])
        result = calc_params_on_nodes(multi_param_distribution, nodes)
        
        # Distribution has names ["thickness", "density"] with [linear, quadratic] functions
        # So result[:, 0] should be thickness (linear: 2*2+1=5)
        # And result[:, 1] should be density (quadratic: 2^2=4)
        assert result[0, 0] == 5.0  # thickness
        assert result[0, 1] == 4.0  # density

    def test_large_number_of_nodes(self, single_param_distribution_2d):
        """Test performance with large number of nodes"""
        # Create a large array of nodes
        nodes = np.random.rand(1000, 2) * 10  # 1000 random nodes
        result = calc_params_on_nodes(single_param_distribution_2d, nodes)

        assert isinstance(result, np.ndarray)
        assert result.shape == (1000, 1)
        # Verify a few random results
        for i in [0, 100, 500, 999]:
            expected = 2 * nodes[i, 0] + 3 * nodes[i, 1] + 1
            assert np.allclose(result[i, 0], expected)

    # def test_zero_coordinates(self, single_param_distribution):
    #     """Test with zero coordinates"""
    #     nodes = np.array([[0.0], [0.0], [0.0]])
    #     result = calc_params_on_nodes(single_param_distribution, nodes)

    #     assert isinstance(result, np.ndarray)
    #     assert result.shape == (3, 1)
    #     expected = np.array([[1.0], [1.0], [1.0]])  # 2*0 + 1 = 1
    #     assert np.allclose(result, expected)

    # def test_negative_coordinates(self, single_param_distribution):
    #     """Test with negative coordinates"""
    #     nodes = np.array([[-1.0], [-2.0], [-3.0]])
    #     result = calc_params_on_nodes(single_param_distribution, nodes)

    #     assert isinstance(result, np.ndarray)
    #     assert result.shape == (3, 1)
    #     expected = np.array([[-1.0], [-3.0], [-5.0]])  # 2*x + 1
    #     assert np.allclose(result, expected)
