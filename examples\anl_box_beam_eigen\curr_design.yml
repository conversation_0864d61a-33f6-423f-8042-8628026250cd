version: '0.10'
structure:
  name: blade1
  parameter: {}
  model:
    type: ''
    tool: gebt
    tool_version: ''
    main_file: beam_design.yml
    prop_file: ''
    config: {}
  design:
    name: blade1
    parameter:
      lyr_ang_sta1: 0
      lyr_ang_sta2: 90
      lyr_ply: 6
    dim: 1
    builder: default
    design: {}
    distribution:
    - name: lyr_ang
      type: float
      data_form: explicit
      data:
      - coordinate: 0
        value: 0
      - coordinate: 10
        value: 90
  cs_assignment:
  - region: segment1
    location: element_node
    cs: main_cs
  physics: elastic
functions:
- name: f_interp
  type: float
  kind: linear
  fill_value: extrapolate
cs:
- name: box
  parameter:
    w: 0.953
    h: 0.53
    lyr_ang: 0
    lyr_ply: 6
  dim: 2
  builder: prevabs
  design:
    base_file: box.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    type: sg
    analysis: h
    work_dir: cs
  - step: beam analysis
    activate: true
    output:
    - value:
      - eig1
      - eig2
      - eig3
      - eig4
      - eig5
      - eig6
      - eig7
      - eig8
      - eig9
      - eig10
    type: gebt
