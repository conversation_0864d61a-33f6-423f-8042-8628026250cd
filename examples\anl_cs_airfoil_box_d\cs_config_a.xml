<cross_section name="cs" format="1">
    <include>
        <material>materials</material>
    </include>

    <analysis>
        <model>1</model>
    </analysis>

    <general>
        <translate>0 0</translate>
        <scale>0.12</scale>
        
        <rotate>0</rotate>
        <mesh_size>2e-4</mesh_size>
        <element_type>linear</element_type>
        <track_interface>0</track_interface>
    </general>



    <baselines>
        <line name="ln_af" type="airfoil">
            <points data="file" format="1" direction="1" header="1">naca0012.dat</points>
            
            
        </line>
        <point name="p1" on="ln_af" by="x2" which="top">0.1</point>
        <point name="p2" on="ln_af" by="x2" which="bottom">0.1</point>
        <point name="p3" on="ln_af" by="x2" which="top">0.9</point>
        <point name="p4" on="ln_af" by="x2" which="bottom">0.9</point>

        <line name="ln_skin_top">
            <points>p3:p1</points>
        </line>
        <line name="ln_skin_le">
            <points>p1:p2</points>
        </line>
        <line name="ln_skin_bottom">
            <points>p2:p4</points>
        </line>
        <line name="ln_skin_te">
            <points>p4:p3</points>
        </line>

        <point name="pw1">0.205  0</point>
        <line name="ln_web_1">
            <point>pw1</point>
            <angle>90</angle>
        </line>

        <point name="pw2">0.404  0</point>
        <line name="ln_web_2">
            <point>pw2</point>
            <angle>90</angle>
        </line>

        <point name="pw3">0.603  0</point>
        <line name="ln_web_3">
            <point>pw3</point>
            <angle>90</angle>
        </line>

    </baselines>



    <layups>
        <layup name="layup_skin">
            <layer lamina="la_onyx_1">0</layer>
        </layup>
        <layup name="layup_spar">
            <layer lamina="la_onyx_0.76">0</layer>
        </layup>
    </layups>



    <component name="skin">
        <segment>
            <baseline>ln_skin_top</baseline>
            <layup>layup_skin</layup>
        </segment>
        <segment>
            <baseline>ln_skin_le</baseline>
            <layup>layup_skin</layup>
        </segment>
        <segment>
            <baseline>ln_skin_bottom</baseline>
            <layup>layup_skin</layup>
        </segment>
        <segment>
            <baseline>ln_skin_te</baseline>
            <layup>layup_skin</layup>
        </segment>
    </component>

    <component name="spar_1" depend="skin">
        <segment>
            <baseline>ln_web_1</baseline>
            <layup>layup_spar</layup>
        </segment>
    </component>
    <component name="spar_2" depend="skin">
        <segment>
            <baseline>ln_web_2</baseline>
            <layup>layup_spar</layup>
        </segment>
    </component>
    <component name="spar_3" depend="skin">
        <segment>
            <baseline>ln_web_3</baseline>
            <layup>layup_spar</layup>
        </segment>
    </component>

    <global measure="stress">
        
        <displacements>6.369422289e-04  9.120472206e-05  2.269319424e-02</displacements>
        <rotations>9.987679237e-01  6.461786567e-05  4.962489684e-02  -1.886794093e-04  9.999968692e-01  2.495192188e-03  -4.962523732e-02  -2.501511963e-03  9.987647762e-01</rotations>
        <loads>2.324449797e-02  4.512233201e-03  3.023056040e-02  -2.359598071e-04  -3.368918595e-05  7.032256462e-04</loads>
    </global>

</cross_section>