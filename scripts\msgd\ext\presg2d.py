import logging

import sgio

import msgd.builder.main as mbp

# import msgd._global as GLOBAL


logger = logging.getLogger(__name__)

def solve(
    sg_xml, analysis, ppcmd, solver, integrated=False,
    aperiodic=False, output_gmsh_format=True, reduced_integration=False,
    timeout=30, scrnout=True, timer=None
    ):
    """Solve

    Parameters
    ----------
    sg_xml : str
        File name of SG design parameters (XML format).
    analysis : str
        Analysis to be carried out.

        * h - homogenization
        * d - dehomogenization/localization/recover
        * f - initial failure strength
        * fe - initial failure envelope
        * fi - initial failure indices and strength ratios
    ppcmd : str
        Preprocessor command.
    solver : str
        Command of the solver.
    integrated : bool, optional
        Use integrated solver or not (standalone), by default False.
    aperiodic : bool, optional
        (SwiftComp) If the structure gene is periodic, by default False.
    output_gmsh_format : bool, optional
        (SwiftComp) If output dehomogenization results in Gmsh format, by default True
    reduced_integration : bool, optional
        (SwiftComp) If reduced integration is used for certain elements, by default False.
    timeout : int, optional
        Time to wait before stop, by default 30.
    scrnout : bool, optional
        Switch of printing solver messages, by default True.
    logger : logging.Logger, optional
        Logger object, by default None.

    Returns
    -------
    various
        Different analyses return different types of results.
    """

    # if logger is None:
    #     logger = mul.initLogger(__name__)

    # t = mtime.Timer(logger=logger.info)

    # Preprocess
    logger.info('preprocessing...')

    design = {'dim': 2}
    smdim = 1

    if timer:
        timer.start()
    sg_in = mbp.buildSG(
        sg_xml, design, smdim,
        builder=ppcmd, analysis=analysis, solver=solver, integrated=integrated,
        timeout=timeout
    )
    if timer:
        timer.stop()


    # Solve
    if not integrated:
        logger.info('running analysis...')
        logger.debug('solver: ' + solver)
        if timer:
            timer.start()
        sgio.run(
            solver, sg_in, analysis, smdim,
            aperiodic, output_gmsh_format, reduced_integration
        )
        if timer:
            timer.stop()


    # Parse results
    results = None

    if timer:
        timer.start()

    sg_out = sg_in
    if analysis == 'h':
        sg_out = f'{sg_in}.k'
    elif analysis == 'fi':
        sg_out = f'{sg_in}.fi'

    logger.info(f'reading results {sg_out}...')

    results = sgio.readOutput(sg_out, solver.lower(), analysis, smdim)

    # if 'vabs' in solver.lower():
    #     results = msi.readVABSOut(sg_in, analysis, scrnout, logger=logger)

    # elif 'swiftcomp' in solver.lower():
    #     results = msi.readSCOut(sg_in, smdim, analysis, scrnout)

    
    if timer:
        timer.stop()
    
    return results

