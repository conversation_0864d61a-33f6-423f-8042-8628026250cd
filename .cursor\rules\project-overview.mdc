---
description: 
globs: 
alwaysApply: true
---
# MSG Design Project Overview

This is a mechanics of structure genome (MSG) based framework for multiscale constitutive modeling and structural analysis of composite structures.
The core concept is structure gene (SG).

## Main Components

- **MSG Design Core**: Main implementation in [scripts/msgd/](mdc:scripts/msgd)
  - Core framework implementation
  - Structure genome calculations
  - Analysis and optimization tools
  - Dakota integration

- **SG Library**: Core functionality for structure gene calculations
  - Located in [sg_library/](mdc:sg_library)
  - Contains material database and templates

- **Scripts**: Utility scripts and tools
  - Located in [scripts/](mdc:scripts)
  - Contains helper functions and utilities

- **Examples**: Example configurations and use cases
  - Located in [examples/](mdc:examples)
  - Contains sample input files and configurations

- **Documentation**: Project documentation
  - Located in [doc/](mdc:doc)
  - Contains detailed documentation and guides

## Key Files

- [setup.py](mdc:setup.py): Project setup and installation configuration
- [requirements.txt](mdc:requirements.txt): Python package dependencies
- [requirements.yml](mdc:requirements.yml): Additional project requirements
- [CHANGELOG.md](mdc:CHANGELOG.md): Project version history and changes

## Running the Project

The main entry point is through the `datc` command with a YAML input file:

```bash
datc <input>.yml
```

For help and command line options:
```bash
datc -h
```

## Development

The main development work is done in the [scripts/msgd/](mdc:scripts/msgd) directory, which contains:
- Core framework implementation
- Analysis tools
- Design optimization
- External tool integrations
- Utility functions

See the [msgd-module.mdc](mdc:.cursor/rules/msgd-module.mdc) rule for detailed information about the main code structure.



