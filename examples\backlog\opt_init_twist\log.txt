CRITICAL [2023-03-27 21:16:57] io.readMSGDInput :: reading main input cs_design_twist_opt_soga.yml... 
INFO     [2023-03-27 21:16:57] msgd.run :: file name path:  
INFO     [2023-03-27 21:16:57] msgd.run :: file name base: cs_design_twist_opt_soga 
INFO     [2023-03-27 21:16:57] msgd.run :: file name mdao: cs_design_twist_opt_soga.dakota 
CRITICAL [2023-03-27 21:19:04] io.readMSGDInput :: reading main input cs_design_twist_opt_soga.yml... 
INFO     [2023-03-27 21:19:04] msgd.run :: file name path:  
INFO     [2023-03-27 21:19:04] msgd.run :: file name base: cs_design_twist_opt_soga 
INFO     [2023-03-27 21:19:04] msgd.run :: file name mdao: cs_design_twist_opt_soga.dakota 
CRITICAL [2023-03-27 21:25:50] io.readMSGDInput :: reading main input cs_design_twist_opt_soga.yml... 
INFO     [2023-03-27 21:25:50] msgd.run :: file name path:  
INFO     [2023-03-27 21:25:50] msgd.run :: file name base: cs_design_twist_opt_soga 
INFO     [2023-03-27 21:25:50] msgd.run :: file name mdao: cs_design_twist_opt_soga.dakota 
CRITICAL [2023-03-27 21:27:35] io.readMSGDInput :: reading main input cs_design_rotate_opt_soga.yml... 
CRITICAL [2023-03-27 21:27:55] io.readMSGDInput :: reading main input cs_design_rotate_opt_soga.yml... 
INFO     [2023-03-27 21:27:55] msgd.run :: file name path:  
INFO     [2023-03-27 21:27:55] msgd.run :: file name base: cs_design_rotate_opt_soga 
INFO     [2023-03-27 21:27:55] msgd.run :: file name mdao: cs_design_rotate_opt_soga.dakota 
CRITICAL [2023-03-27 21:28:23] io.readMSGDInput :: reading main input cs_design_rotate_opt_soga.yml... 
INFO     [2023-03-27 21:28:23] msgd.run :: file name path:  
INFO     [2023-03-27 21:28:23] msgd.run :: file name base: cs_design_rotate_opt_soga 
INFO     [2023-03-27 21:28:23] msgd.run :: file name mdao: cs_design_rotate_opt_soga.dakota 
