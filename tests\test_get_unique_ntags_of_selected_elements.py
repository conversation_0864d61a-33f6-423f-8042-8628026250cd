import pytest
import numpy as np
from msgd.core._helpers import get_unique_ntags_of_selected_elements


class TestGetUniqueNtagsOfSelectedElements:
    """Test suite for the get_unique_ntags_of_selected_elements function."""

    def test_empty_elements(self):
        """Test with empty elements array."""
        elements = np.array([])
        mask = np.array([], dtype=bool)

        result = get_unique_ntags_of_selected_elements(elements, mask)

        expected = np.array([], dtype=int)
        np.testing.assert_array_equal(result, expected)

    def test_empty_mask_all_false(self):
        """Test with mask that selects no elements."""
        elements = np.array([
            [0, 1, 2],
            [1, 2, 3],
            [2, 3, 4]
        ])
        mask = np.array([False, False, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        expected = np.array([], dtype=int)
        np.testing.assert_array_equal(result, expected)

    def test_single_element_selected(self):
        """Test with a single element selected."""
        elements = np.array([
            [0, 1, 2],
            [1, 2, 3],
            [2, 3, 4]
        ])
        mask = np.array([False, True, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        expected = np.array([1, 2, 3])
        np.testing.assert_array_equal(result, expected)

    def test_multiple_elements_selected(self):
        """Test with multiple elements selected."""
        elements = np.array([
            [0, 1, 2],
            [1, 2, 3],
            [2, 3, 4],
            [4, 5, 6]
        ])
        mask = np.array([True, False, True, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Elements [0, 1, 2] and [2, 3, 4] are selected
        # Unique nodes: 0, 1, 2, 3, 4
        expected = np.array([0, 1, 2, 3, 4])
        np.testing.assert_array_equal(result, expected)

    def test_all_elements_selected(self):
        """Test with all elements selected."""
        elements = np.array([
            [0, 1, 2],
            [1, 2, 3],
            [2, 3, 4]
        ])
        mask = np.array([True, True, True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        expected = np.array([0, 1, 2, 3, 4])
        np.testing.assert_array_equal(result, expected)

    def test_overlapping_nodes(self):
        """Test with elements that share nodes."""
        elements = np.array([
            [0, 1, 2],
            [2, 3, 4],
            [4, 5, 6],
            [1, 7, 8]
        ])
        mask = np.array([True, True, False, True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [0, 1, 2], [2, 3, 4], [1, 7, 8]
        # Unique nodes: 0, 1, 2, 3, 4, 7, 8
        expected = np.array([0, 1, 2, 3, 4, 7, 8])
        np.testing.assert_array_equal(result, expected)

    def test_quad_elements(self):
        """Test with quadrilateral elements (4 nodes per element)."""
        elements = np.array([
            [0, 1, 2, 3],
            [4, 5, 6, 7],
            [8, 9, 10, 11]
        ])
        mask = np.array([True, False, True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [0, 1, 2, 3], [8, 9, 10, 11]
        expected = np.array([0, 1, 2, 3, 8, 9, 10, 11])
        np.testing.assert_array_equal(result, expected)

    def test_line_elements(self):
        """Test with line elements (2 nodes per element)."""
        elements = np.array([
            [0, 1],
            [1, 2],
            [2, 3],
            [3, 4]
        ])
        mask = np.array([True, False, True, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [0, 1], [2, 3]
        expected = np.array([0, 1, 2, 3])
        np.testing.assert_array_equal(result, expected)

    def test_large_node_indices(self):
        """Test with large node indices."""
        elements = np.array([
            [1000, 1001, 1002],
            [2000, 2001, 2002],
            [3000, 3001, 3002]
        ])
        mask = np.array([False, True, True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [2000, 2001, 2002], [3000, 3001, 3002]
        expected = np.array([2000, 2001, 2002, 3000, 3001, 3002])
        np.testing.assert_array_equal(result, expected)

    def test_single_element_array(self):
        """Test with a single element in the array."""
        elements = np.array([[10, 20, 30]])
        mask = np.array([True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        expected = np.array([10, 20, 30])
        np.testing.assert_array_equal(result, expected)

    def test_duplicate_nodes_in_element(self):
        """Test with duplicate nodes within an element (edge case)."""
        elements = np.array([
            [1, 1, 2],  # Duplicate node 1
            [2, 3, 3],  # Duplicate node 3
            [4, 5, 6]
        ])
        mask = np.array([True, True, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [1, 1, 2], [2, 3, 3]
        # Unique nodes: 1, 2, 3
        expected = np.array([1, 2, 3])
        np.testing.assert_array_equal(result, expected)

    def test_return_type_and_structure(self):
        """Test that the return type and structure match the specification."""
        elements = np.array([
            [0, 1, 2],
            [3, 4, 5]
        ])
        mask = np.array([True, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Check that we get a numpy array
        assert isinstance(result, np.ndarray)
        assert result.dtype == int or result.dtype == np.int32 or result.dtype == np.int64

        # Check that the array is 1D
        assert result.ndim == 1

        # Check that values are sorted and unique
        assert np.array_equal(result, np.unique(result))

        # Check specific content
        expected = np.array([0, 1, 2])
        np.testing.assert_array_equal(result, expected)

    def test_mismatched_lengths_error(self):
        """Test error handling when elements and mask have mismatched lengths."""
        elements = np.array([
            [0, 1, 2],
            [1, 2, 3]
        ])
        mask = np.array([True, False, True])  # More mask values than elements

        with pytest.raises((ValueError, IndexError)):
            get_unique_ntags_of_selected_elements(elements, mask)

    def test_docstring_example(self):
        """Test the exact example from the docstring."""
        elements = np.array([
            [0, 1, 2],
            [1, 2, 3],
            [2, 3, 4]
        ])
        mask = np.array([False, True, True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [1, 2, 3], [2, 3, 4]
        # Unique nodes: 1, 2, 3, 4
        expected = np.array([1, 2, 3, 4])
        np.testing.assert_array_equal(result, expected)

    def test_non_sequential_node_ids(self):
        """Test with non-sequential node IDs."""
        elements = np.array([
            [10, 25, 30],
            [5, 15, 35],
            [20, 40, 50]
        ])
        mask = np.array([True, False, True])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [10, 25, 30], [20, 40, 50]
        # Unique nodes: 10, 20, 25, 30, 40, 50
        expected = np.array([10, 20, 25, 30, 40, 50])
        np.testing.assert_array_equal(result, expected)

    def test_zero_node_ids(self):
        """Test with node IDs including zero."""
        elements = np.array([
            [0, 1, 2],
            [0, 3, 4],
            [5, 6, 7]
        ])
        mask = np.array([True, True, False])

        result = get_unique_ntags_of_selected_elements(elements, mask)

        # Selected elements: [0, 1, 2], [0, 3, 4]
        # Unique nodes: 0, 1, 2, 3, 4
        expected = np.array([0, 1, 2, 3, 4])
        np.testing.assert_array_equal(result, expected)


if __name__ == "__main__":
    pytest.main([__file__])