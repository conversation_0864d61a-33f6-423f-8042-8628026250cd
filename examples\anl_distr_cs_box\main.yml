version: "0.10"

# Design parameters/variables of the structure
# ====================================================================
structure:
  name: "beam"
  parameter:
    lyr_ang_sta1: 0
    lyr_ang_sta2: 90
    lyr_ply_sta1: 1
    lyr_ply_sta2: 9
  distribution:
    - name: 'lyr_ang'
      function: 'f_interp'
      data:
        - coordinate: 0
          value: lyr_ang_sta1
        - coordinate: 10
          value: lyr_ang_sta2
    - name: 'lyr_ply'
      function: 'f_interp'
      data:
        - coordinate: 0
          value: lyr_ply_sta1
        - coordinate: 10
          value: lyr_ply_sta2
  design:
  model:
    section_locations:
      type: 'curvilinear'
      coordinates: [0, 2, 5, 7, 9, 10]
  cs_assignment:
    - region: 'all'
      location: 'node'
      cs: 'main_cs'
  cs:
    - name: 'main_cs'
      design: "box"
      model:
        type: "bm2"
        solver: 'vabs'


function:
  - name: 'f_interp'
    type: 'interpolation'
    kind: 'linear'


# CS base design
# ====================================================================
cs:
  - name: "box"
    builder: "prevabs"
    parameter:
      w: 0.953
      h: 0.53
      lyr_ang: 0
      lyr_ply: 6
    #   lma_thk: 0.005
    #   gms: 0.01
    #   elem_type: 'linear'
    design:
      base_file: "box.xml.tmp"
    # model:
    #   md1:
    #     tool: "vabs"

# Analysis process
# ====================================================================
analysis:
  steps:
    - step: "cs analysis"
      type: "cs"
      analysis: "h"
      # setting:
      #   timeout: 60
      output:
        value: [
          'ea', 'gayy', 'gazz', "gj", "eiyy", "eizz",
        ]
