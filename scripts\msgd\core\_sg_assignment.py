from __future__ import annotations
from typing import Union, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict

class SGAssignment(BaseModel):
    """Assignment of an SG model to a global/macro structural region.

    Parameters
    ----------
    name:str
        Name of the assignment.
    region:str
        Name of the region.
    sg_model:str
        Name of the SG model.
    """

    # Pydantic configuration
    # model_config = ConfigDict(
    #     arbitrary_types_allowed=True,
    #     validate_assignment=True
    # )

    # Field definitions
    name: str = Field(default='', description="Name of the assignment")
    region: str = Field(default='', description="Name of the region")
    sg_model: str = Field(default='', description="Name of the SG model")
    # location: str = Field(default='element', description="Location where SG properties are mapped")

    # Private fields with default factories
    # _region_entities: List[Any] = Field(default_factory=list, alias='region_entities')
    # _discrete_sg_assigns: List[Dict[str, Any]] = Field(
    #     default_factory=list,
    #     alias='discrete_sg_assigns',
    #     description="""
    #     Discrete SG assignments.

    #     ..  code-block::

    #         [
    #             {
    #                 'region': [],  # eid or nid
    #                 'sg_model': 'sg1'  # or ['sg1', 'sg2', ...]
    #             },
    #             ...
    #         ]
    #     """
    # )

    def __repr__(self) -> str:
        _str = [
            f'sg assignment of {self.sg_model} to region {self.region}',
        ]
        return '\n'.join(_str)

    def to_dict(self, sg_key: str = 'sg') -> Dict[str, Any]:
        """Convert the object to dictionary.
        """
        _dict = {
            'region': self.region,
            # 'location': self.location,
        }
        if hasattr(self.sg_model, 'name'):
            _dict[sg_key] = self.sg_model.name
        elif isinstance(self.sg_model, str):
            _dict[sg_key] = self.sg_model

        return _dict

    # @property
    # def region_entities(self) -> List[Any]:
    #     """Get region entities."""
    #     return self._region_entities

    # @property
    # def discrete_sg_assigns(self) -> List[Dict[str, Any]]:
    #     """Get discrete SG assignments."""
    #     return self._discrete_sg_assigns

    # def add_region_entity(self, entity: Any) -> None:
    #     """Add an entity to the region entities list."""
    #     self._region_entities.append(entity)

    # def add_discrete_sg_assign(self, region: Any, set_name: str, location: str = '') -> None:
    #     """Add a discrete SG assignment."""
    #     self._discrete_sg_assigns.append({
    #         'region': region,
    #         'location': self.location if location == '' else location,
    #         'sg_model': set_name
    #     })

