# Main input file

version: '0.10'  # Required.


# Design parameters/variables of the structure
# ====================================================================
structure:
  name: 'blade1'

  parameter:
    L: 10
    w_root: 1.5
    w_tip: 0.5

  distribution:
    - name: 'w'
      function: 'f_interp_linear'
      xscale: L
      data_form: 'compact'
      data: |
        0, w_root
        1, w_tip

  # Basic design inputs of the blade
  model:
    main_file: 'beam_design.yml'
    tool: 'gebt'
    # section_locations: [0,]

  cs_assignment:
    - region: 'segment1'
      cs: 'main_cs'
      location: 'element_node'

  cs:
    - name: 'main_cs'
      design: 'cs1'
      model:
        type: 'bm2'
        solver: 'vabs'

  # The 'distribution' block defines how c/s parameters vary along the blade span


function:
  - name: 'f_interp_linear'
    type: 'interpolation'
    interp_kind: 'linear'


# Base (default/template) design of the cross-section
# ====================================================================
cs:
  - name: 'cs1'
    builder: 'prevabs'
    parameter:
      # mdb_name: 'material_database_us_ft'
      w: 1
      t: 0.1
      lam_lyr_1: 'la_mat_1'
      ang_lyr_1: 0
      gms: 0.01

    design:
      base_file: 'rect.xml.tmp'

    # model:
    #   md1:
    #     tool: 'vabs'
    #     # Required.
    #     # Cross-sectional analysis tool.
    #     # This is the actual command that will be called.



# Analysis process
# ====================================================================
analysis:
  steps:
    - step: 'cs analysis'
      type: 'cs'
      analysis: 'h'
      # setting:
      #   timeout: 60

    - step: 'beam analysis'
      type: 'gebt'
      analysis: 3
      output:
        value: [
          'eig1', 'eig2', 'eig3', 'eig4', 'eig5',
          'eig6', 'eig7', 'eig8', 'eig9', 'eig10'
          ]


# # ====================================================================
# study:
#   method:
#     format: "keyword"
#     list_parameter_study:
#       list_of_points: [1, 0.1]
#     # multidim_parameter_study:
#     #   partitions: [12, 7]
#   variables:
#     data_form: "compact"
#     data: |
#       w,  design, continuous, 1:2
#       t,  design, continuous, 0.1:0.2
#   responses:
#     data_form: "explicit"
#     response_functions:
#       - descriptor: 'eig1'
#       - descriptor: 'eig2'
#       - descriptor: 'eig3'
#       - descriptor: 'eig4'
#       - descriptor: 'eig5'
#       - descriptor: 'eig6'
#       - descriptor: 'eig7'
#       - descriptor: 'eig8'
#       - descriptor: 'eig9'
#       - descriptor: 'eig10'
#   interface:
#     fork:
#       parameters_file: "input.in"
#       results_file: "output.out"
#       file_save: on
#       work_directory:
#         directory_tag: on
#         directory_save: on
#     required_files:
#       - "support/*"
#     # asynchronous:
#     #   evaluation_concurrency: 20
#     failure_capture:
#       recover: [nan, nan, nan, nan, nan, nan, nan, nan, nan, nan]

