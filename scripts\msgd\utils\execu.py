import logging
# import os
# import platform
# import signal
import subprocess as sbp
# import multiprocessing as mp
# from tqdm import tqdm

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)


def run(cmd, timeout):

    logger.debug(' '.join(cmd))
    # proc = sbp.Popen(cmd, stdout=sbp.PIPE, stderr=sbp.PIPE)
    # print('command: ', cmd)

    try:
        out = sbp.run(
            cmd,
            capture_output=True, timeout=timeout, encoding='UTF-8'
        )

    except sbp.TimeoutExpired as e:

        print(out.stdout)

        logger.critical('TimeoutExpired')
        # os.killpg(os.getpgid(proc.pid), signal.SIGTERM)
        logger.critical('Process killed')

    # if platform.system() == 'Windows':
    #     # if scrnout:
    #     #     # sbp.call(cmd)
    #     #     proc = sbp.<PERSON>n(cmd)
    #     # else:
    #     #     # proc = sbp.Popen(cmd, stdout=sbp.DEVNULL, stderr=sbp.STDOUT)
    #     #     proc = sbp.Popen(cmd, stdout=sbp.PIPE, stderr=sbp.STDOUT)

    #     try:
    #         # stdout, stderr = proc.communicate(timeout=timeout)
    #         out = sbp.run(
    #             cmd,
    #             capture_output=True, timeout=timeout, encoding='UTF-8'
    #         )
    #         print(out.stdout)
    #         # print('exit code:', proc.returncode)
    #         # print(stdout)
    #         # if logger:
    #             # logger.debug(f'exit code: {proc.returncode}')
    #         logger.debug('exit code: {0}'.format(out.returncode))
    #         # logger.info(stdout)
    #             # logger.debug(stdout.decode())
    #     except sbp.TimeoutExpired as e:
    #         # print('exit code:', proc.returncode)
    #         # print(stderr)
    #         # if logger:
    #             # logger.debug(f'exit code: {proc.returncode}')
    #         logger.debug('exit code: {0}'.format(out.returncode))
    #             # logger.debug(stdout.decode())
    #             # logger.debug(stderr.decode())

    #         logger.critical('TimeoutExpired')
    #         # proc.kill()
    #         logger.critical('Process killed')


    # elif platform.system() == 'Linux':
    #     # sbp.run(["$PATH"])

    #     # if scrnout:
    #     #     # sbp.call(cmd)
    #     #     proc = sbp.Popen(cmd)
    #     # else:
    #     #     # proc = sbp.Popen(cmd, stdout=sbp.DEVNULL, stderr=sbp.STDOUT, preexec_fn=os.setsid)
    #     #     proc = sbp.Popen(cmd, stdout=sbp.PIPE, stderr=sbp.STDOUT, preexec_fn=os.setsid)

    #     try:
    #         sbp.run(cmd, timeout=timeout)
    #         # stdout, stderr = proc.communicate(timeout=timeout)
    #         # print('exit code:', proc.returncode)
    #         # print(stdout)
    #         # if logger:
    #         #     # logger.debug(f'exit code: {proc.returncode}')
    #         #     logger.debug('exit code: {0}'.format(proc.returncode))
    #         #     logger.debug(stdout)
    #         #     # logger.debug(stdout.decode())
    #     except sbp.TimeoutExpired as e:
    #         # print('exit code:', proc.returncode)
    #         # print(stderr)
    #         # if logger:
    #         #     # logger.debug(f'exit code: {proc.returncode}')
    #         #     logger.debug('exit code: {0}'.format(proc.returncode))
    #         #     # logger.debug(stdout.decode())
    #         #     # logger.debug(stderr.decode())

    #         logger.critical('TimeoutExpired')
    #         # os.killpg(os.getpgid(proc.pid), signal.SIGTERM)
    #         logger.critical('Process killed')




def importFunction(module_name, func_name):
    """
    """

    try:
        import_str = f'import {module_name}'
        logger.info(import_str)
        exec(import_str)
        func_str = f'{module_name}.{func_name}'
        # logger.info('evaluating user function: {}'.format(func_str))
        func_obj = eval(func_str)

    except ImportError:
        try:
            import_str = f'from {module_name} import {func_name}'
            logger.info(import_str)
            exec(import_str)
            # logger.info('evaluating user function: {}'.format(func_name))
            func_obj = eval(func_str)

        except ImportError:
            logger.error(f'something wrong when importing module: {module_name}')


    return func_obj









# def runApplyAsyncMultiprocessing(func, arg_list, num_process, progress_bar=True):

#     pool = mp.Pool(processes=num_process)

#     jobs = [pool.apply_async(func=func, args=(*arg,)) if isinstance(arg, tuple) else pool.apply_async(func=func, args=(arg,)) for arg in arg_list]
#     pool.close()
#     result_list_tqdm = []
#     if progress_bar:
#         for job in tqdm(jobs):
#             result_list_tqdm.append(job.get())
#     else:
#         for job in jobs:
#             result_list_tqdm.append(job.get())

#     return result_list_tqdm
