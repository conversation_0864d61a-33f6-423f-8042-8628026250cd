.. _section-ivabs-start:

Quick Start
============

..  note::

    In this documentation, ``IVABS_ROOT`` refers to the root directory of iVABS after installation.




Run iVABS with a simple example
-------------------------------

Start the shell/terminal that is configured during the installation.
Please refer to :ref:`section-ivabs_install` for more information.

    If you are using Python virtual environment managed by Anaconda (see :doc:`/install/python_setup`), you need to activate it first.
    For example, if Python 3.6 and all dependencies are installed in a virtual environment named ``py3ivabs``, then activate it using

    ..  code-block:: shell

        conda activate py3ivabs

Go to the directory ``IVABS_ROOT/examples/anl_cs_airfoil_box``

..  code-block:: shell

    cd IVABS_ROOT/examples/anl_cs_airfoil_box

Run the example using the following command.


..  code-block:: shell

    ivabs main.yml --mode 1

If iVABS has been installed properly, a single cross-sectional analysis will be carried out and the result can be found in the output file ``main.out``.

.. If iVABS has been installed properly, it will generate a complete list of output files shown in the section below (:ref:`section-start_file_out`).
.. In the file ``cs_param_study.out``, the following message should appear at the end of the file.

.. ..  code-block:: none

..     <<<<< Iterator multidim_parameter_study completed.
..     <<<<< Environment execution completed.
..     DAKOTA execution time in seconds:
..     Total CPU        =     34.003 [parent =     34.003, child =          0]
..     Total wall clock =     34.003

.. Running time usually takes less than one minute depending on the computer.

.. Please refer to :ref:`section-ivabs_example_quickstart_param_study` for a detailed explanation of this example. 



iVABS command line interface
----------------------------

Type ``ivabs -h`` to see the list of arguments and options.

..  code-block:: shell

    usage: ivabs [-h] [--loglevelcmd {debug,info,warning,error,critical}]
                 [--loglevelfile {debug,info,warning,error,critical}] [--logfile LOGFILE]
                 {analyze,iter,map,plot} ...

    iVABS

    positional arguments:
      {analyze,iter,map,plot}
                            sub-command help
        analyze             Analyze a design
        iter                Iterative analysis
        map                 Create a mapping of meshes between the physcial and parametric domains
        plot                Plot all CS/SG

    options:
      -h, --help            show this help message and exit
      --loglevelcmd {debug,info,warning,error,critical}
                            Command line logging level
      --loglevelfile {debug,info,warning,error,critical}
                            File logging level
      --logfile LOGFILE     Logging file name

.. The general command syntax to run an iVABS study is

.. ..  code-block:: shell

..     ivabs <main_input_file> [options]

.. Use the option ``-h`` to see the list of available options.

Single analysis
^^^^^^^^^^^^^^^^

..  code-block:: shell

    ivabs analyze <main_input_file> [options]



Iterative analysis
^^^^^^^^^^^^^^^^^^^

..  code-block:: shell

    ivabs iter <main_input_file> [options]









Other examples
--------------

More examples can be found in Section :ref:`section-ivabs-examples`.
