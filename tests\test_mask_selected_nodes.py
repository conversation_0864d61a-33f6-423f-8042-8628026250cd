import pytest
import numpy as np
from msgd.core._helpers import mask_selected_nodes


class TestMaskSelectedNodes:
    """Test suite for the mask_selected_nodes function."""

    def test_empty_nodes(self):
        """Test with empty nodes array."""
        nodes = np.array([]).reshape(0, 3)  # Empty 2D array with 3 columns
        selected_node_ids = np.array([])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([], dtype=bool)
        np.testing.assert_array_equal(result, expected)

    def test_empty_selected_node_ids(self):
        """Test with empty selected_node_ids array."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([False, False, False])
        np.testing.assert_array_equal(result, expected)

    def test_single_node_selected(self):
        """Test with a single node selected."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [1.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([1])  # Select node at index 1

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([False, True, False, False])
        np.testing.assert_array_equal(result, expected)

    def test_multiple_nodes_selected(self):
        """Test with multiple nodes selected."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [1.0, 1.0, 0.0],
            [0.0, 0.0, 1.0]
        ])
        selected_node_ids = np.array([0, 2, 4])  # Select nodes at indices 0, 2, 4

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, False, True, False, True])
        np.testing.assert_array_equal(result, expected)

    def test_all_nodes_selected(self):
        """Test with all nodes selected."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([0, 1, 2])  # Select all nodes

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, True, True])
        np.testing.assert_array_equal(result, expected)

    def test_no_nodes_selected(self):
        """Test with no valid nodes selected (out of range indices)."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([5, 10])  # Indices out of range

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([False, False, False])
        np.testing.assert_array_equal(result, expected)

    def test_duplicate_selected_node_ids(self):
        """Test with duplicate node IDs in selected_node_ids."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([0, 0, 1, 1])  # Duplicate indices

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, True, False])  # Should still work correctly
        np.testing.assert_array_equal(result, expected)

    def test_non_sequential_node_ids(self):
        """Test with non-sequential node indices."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [1.0, 1.0, 0.0],
            [0.0, 0.0, 1.0],
            [1.0, 0.0, 1.0]
        ])
        selected_node_ids = np.array([5, 1, 3])  # Non-sequential order

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([False, True, False, True, False, True])
        np.testing.assert_array_equal(result, expected)

    def test_large_node_indices(self):
        """Test with large node arrays and indices."""
        # Create a larger array of nodes
        num_nodes = 1000
        nodes = np.random.rand(num_nodes, 3)  # Random 3D coordinates
        selected_node_ids = np.array([0, 100, 500, 999])  # Select specific indices

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.zeros(num_nodes, dtype=bool)
        expected[[0, 100, 500, 999]] = True
        np.testing.assert_array_equal(result, expected)

    def test_2d_nodes(self):
        """Test with 2D nodes (only x, y coordinates)."""
        nodes = np.array([
            [0.0, 0.0],
            [1.0, 0.0],
            [0.0, 1.0],
            [1.0, 1.0]
        ])
        selected_node_ids = np.array([1, 3])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([False, True, False, True])
        np.testing.assert_array_equal(result, expected)

    def test_1d_nodes(self):
        """Test with 1D nodes (only x coordinates)."""
        nodes = np.array([
            [0.0],
            [1.0],
            [2.0],
            [3.0]
        ])
        selected_node_ids = np.array([0, 2])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, False, True, False])
        np.testing.assert_array_equal(result, expected)

    def test_single_node_array(self):
        """Test with a single node in the array."""
        nodes = np.array([[1.0, 2.0, 3.0]])
        selected_node_ids = np.array([0])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True])
        np.testing.assert_array_equal(result, expected)

    def test_negative_node_ids_ignored(self):
        """Test that negative node IDs are ignored."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([-1, 0, 1, -5])  # Negative indices should be ignored

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, True, False])  # Only indices 0 and 1 are valid
        np.testing.assert_array_equal(result, expected)

    def test_return_type_and_structure(self):
        """Test that the return type and structure match the specification."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([0, 2])

        result = mask_selected_nodes(nodes, selected_node_ids)

        # Check that we get a numpy array
        assert isinstance(result, np.ndarray)
        assert result.dtype == bool

        # Check that the array is 1D
        assert result.ndim == 1

        # Check that the length matches the number of nodes
        assert len(result) == len(nodes)

        # Check specific content
        expected = np.array([True, False, True])
        np.testing.assert_array_equal(result, expected)

    def test_order_independence_of_selected_node_ids(self):
        """Test that the order of selected_node_ids doesn't affect the result."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [1.0, 1.0, 0.0]
        ])
        selected_node_ids_1 = np.array([0, 2, 3])
        selected_node_ids_2 = np.array([3, 0, 2])  # Different order

        result_1 = mask_selected_nodes(nodes, selected_node_ids_1)
        result_2 = mask_selected_nodes(nodes, selected_node_ids_2)

        # Results should be identical regardless of order
        np.testing.assert_array_equal(result_1, result_2)

    def test_mixed_valid_invalid_indices(self):
        """Test with a mix of valid and invalid indices."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([0, 5, 1, 10, 2])  # Mix of valid (0,1,2) and invalid (5,10) indices

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, True, True])  # Only valid indices should be considered
        np.testing.assert_array_equal(result, expected)

    def test_docstring_example(self):
        """Test an example that matches the docstring format."""
        nodes = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [1.0, 1.0, 0.0]
        ])
        selected_node_ids = np.array([1, 2, 3])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([False, True, True, True])
        np.testing.assert_array_equal(result, expected)

    def test_zero_dimensional_edge_case(self):
        """Test edge case with zero-dimensional inputs."""
        nodes = np.array([]).reshape(0, 2)  # Empty 2D array
        selected_node_ids = np.array([])

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([], dtype=bool)
        np.testing.assert_array_equal(result, expected)

    def test_different_input_types(self):
        """Test with different input types (list vs numpy array)."""
        nodes = [
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0]
        ]  # Regular list instead of numpy array
        selected_node_ids = [0, 2]  # Regular list instead of numpy array

        result = mask_selected_nodes(nodes, selected_node_ids)

        expected = np.array([True, False, True])
        np.testing.assert_array_equal(result, expected)


if __name__ == "__main__":
    pytest.main([__file__])