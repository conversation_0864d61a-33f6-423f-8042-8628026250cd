DEBUG    [2023-07-12 11:08:10] msgd.main :: {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} 
CRITICAL [2023-07-12 11:08:10] io.readMSGDInput :: reading main input main.yml... 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: local variables:
{'fn': 'main.yml',
 'fn_run': 'run.py',
 'mode': '1',
 'msgd': <msgd.core._msgd.MSGD object at 0x0000022C59081A00>,
 'py_cmd': 'python',
 'variant': 'ivabs'} 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: currect working directory:  
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: input file name: main 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: input file extension: .yml 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: output file name: main.out 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: variant: ivabs 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: version: 0.9 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {},
 'design': {'cs_assignment': [{'cs': 'cs1', 'region': 'all'}],
            'dim': 1,
            'section_locations': [0.2, 0.9]},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'ytype': 'float'}],
 'name': 'blade1'} 
DEBUG    [2023-07-12 11:08:10] io.readMSGDInput :: msgd.analysis =
{'steps': [{'analysis': 'h',
            'output': [{'value': ['gj', 'eiyy', 'eizz']}],
            'setting': {'timeout': 60},
            'step': 'cs analysis',
            'type': 'cs'}]} 
INFO     [2023-07-12 11:08:10] _msgd.readMDAOEvalIn :: reading mdao input... 
DEBUG    [2023-07-12 11:08:10] _msgd.readMDAOEvalIn :: mdao_tool: dakota 
DEBUG    [2023-07-12 11:08:10] _msgd.readMDAOEvalIn :: fn_dakota_params:  
DEBUG    [2023-07-12 11:08:10] _msgd.readMDAOEvalIn :: fn_dakota_results:  
INFO     [2023-07-12 11:08:10] _msgd.updateData :: updating current design... 
DEBUG    [2023-07-12 11:08:10] _msgd.updateData :: cs1 
DEBUG    [2023-07-12 11:08:10] _msgd.updateData :: step 1 
DEBUG    [2023-07-12 11:08:10] _msgd.writeInput :: writing input file: curr_design.yml... 
CRITICAL [2023-07-12 11:08:10] analysis.analyze :: [eval 0] analysis start 
INFO     [2023-07-12 11:08:10] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:08:10] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                         '0.8, airfoil_solid.xml.tmp\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'topology',
                 'ytype': 'str'},
 'dname': 'topology',
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: reading distribution of parameter ['topology']... 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.0, airfoil_gbox_uni.xml.tmp\n0.8, airfoil_solid.xml.tmp\n',
 'interp_kind': 'previous',
 'name': None,
 'other_input': {'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                         '0.8, airfoil_solid.xml.tmp\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'topology',
                 'ytype': 'str'},
 'xndim': 1,
 'xscale': 1.0,
 'yname': ['topology'],
 'yndim': 1,
 'ytype': ['str']} 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: interp_data =
0.0, airfoil_gbox_uni.xml.tmp
0.8, airfoil_solid.xml.tmp
 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: topology: msgd.utils.function, xdim=1, ydim=24, ytype=str, kind=previous, fill=extrapolate 
INFO     [2023-07-12 11:08:10] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:08:10] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'airfoil',
                 'ytype': 'str'},
 'dname': 'airfoil',
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: reading distribution of parameter ['airfoil']... 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
 'interp_kind': 'previous',
 'name': None,
 'other_input': {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'previous',
                 'name': 'airfoil',
                 'ytype': 'str'},
 'xndim': 1,
 'xscale': 1.0,
 'yname': ['airfoil'],
 'yndim': 1,
 'ytype': ['str']} 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: interp_data =
0.0, sc1095.txt
0.5, sc1094r8.txt
 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: airfoil: msgd.utils.function, xdim=1, ydim=10, ytype=str, kind=previous, fill=extrapolate 
INFO     [2023-07-12 11:08:10] distribution.loadDistribution :: loading parameter distributions... 
DEBUG    [2023-07-12 11:08:10] distribution.loadDistribution :: local variables:
{'distr_input': {'data': '0.1, 46, -45\n'
                         '0.3, -3, 0\n'
                         '0.7, -44, 90\n'
                         '1.0, 47, 45\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'linear',
                 'name': ['ang_spar_1', 'ang_spar_2'],
                 'ytype': 'float'},
 'dname': ['ang_spar_1', 'ang_spar_2'],
 'dobj': None,
 'func_lib': {},
 'function': 'interpolation',
 'kwargs': {},
 'params': {}} 
INFO     [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: reading distribution of parameter ['ang_spar_1', 'ang_spar_2']... 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: local variables:
{'dobjs': [],
 'fill_value': ['extrapolate', 'extrapolate'],
 'input_form': 'compact',
 'interp_data': '0.1, 46, -45\n0.3, -3, 0\n0.7, -44, 90\n1.0, 47, 45\n',
 'interp_kind': 'linear',
 'name': None,
 'other_input': {'data': '0.1, 46, -45\n'
                         '0.3, -3, 0\n'
                         '0.7, -44, 90\n'
                         '1.0, 47, 45\n',
                 'data_form': 'compact',
                 'function': 'interpolation',
                 'kind': 'linear',
                 'name': ['ang_spar_1', 'ang_spar_2'],
                 'ytype': 'float'},
 'xndim': 1,
 'xscale': 1.0,
 'yname': ['ang_spar_1', 'ang_spar_2'],
 'yndim': 2,
 'ytype': ['float', 'float']} 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: interp_data =
0.1, 46, -45
0.3, -3, 0
0.7, -44, 90
1.0, 47, 45
 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: ang_spar_1: msgd.utils.function, xdim=1, ydim=1, ytype=float, kind=linear, fill=extrapolate 
DEBUG    [2023-07-12 11:08:10] distribution.createInterpolationDistribution :: ang_spar_2: msgd.utils.function, xdim=1, ydim=1, ytype=float, kind=linear, fill=extrapolate 
INFO     [2023-07-12 11:08:10] _msgd.discretize :: discretizing the design... 
DEBUG    [2023-07-12 11:08:10] _msgd.discretize :: structure model:
None 
DEBUG    [2023-07-12 11:08:10] _msgd.discretize :: parameter distributions:
{'airfoil': <msgd.utils.function.InterpolationFunction object at 0x0000022C557537F0>,
 'ang_spar_1': <msgd.utils.function.InterpolationFunction object at 0x0000022C557536A0>,
 'ang_spar_2': <msgd.utils.function.InterpolationFunction object at 0x0000022C52957D30>,
 'topology': <msgd.utils.function.InterpolationFunction object at 0x0000022C52624250>} 
INFO     [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: local variables:
{'abs_tol': 1e-12,
 'distributions': {'airfoil': <msgd.utils.function.InterpolationFunction object at 0x0000022C557537F0>,
                   'ang_spar_1': <msgd.utils.function.InterpolationFunction object at 0x0000022C557536A0>,
                   'ang_spar_2': <msgd.utils.function.InterpolationFunction object at 0x0000022C52957D30>,
                   'topology': <msgd.utils.function.InterpolationFunction object at 0x0000022C52624250>},
 'locations': [0.2, 0.9],
 'model_elems': None,
 'model_nodes': None,
 'rel_tol': 1e-09,
 'sg_assignment': [{'cs': 'cs1', 'region': 'all'}],
 'sg_key': 'cs',
 'trans_func': None} 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction topology (str) at 0.2... 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = airfoil_gbox_uni.xml.tmp (str) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction airfoil (str) at 0.2... 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = sc1095.txt (str) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_1 (float) at 0.2... 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: 0.2 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = 21.499999999999996 (float) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_2 (float) at 0.2... 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: 0.2 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = -22.499999999999996 (float) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction topology (str) at 0.9... 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = airfoil_solid.xml.tmp (str) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction airfoil (str) at 0.9... 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = sc1094r8.txt (str) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_1 (float) at 0.9... 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: 0.9 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = 16.666666666666686 (float) 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: eval InterpolationFunction ang_spar_2 (float) at 0.9... 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: x 
DEBUG    [2023-07-12 11:08:10] function.__call__ :: 0.9 
DEBUG    [2023-07-12 11:08:10] distribution.calcParamsFromDistr :: result = 60.0 (float) 
INFO     [2023-07-12 11:08:10] analysis.analyze :: going through steps... 
CRITICAL [2023-07-12 11:08:11] analysis.analyze :: ==================== 
CRITICAL [2023-07-12 11:08:11] analysis.analyze :: [eval 0] running cs step:  
DEBUG    [2023-07-12 11:08:11] analysis.analyze :: step config:
{'analysis': 'h',
 'output': [{'value': ['gj', 'eiyy', 'eizz']}],
 'setting': {'timeout': 60},
 'step': 'cs analysis',
 'type': 'cs'} 
DEBUG    [2023-07-12 11:08:11] analysis.analyze :: msgd.structure_data =
{'cs': {'cs1': {'base': 'cs1', 'model': 'md1'}},
 'css_data': {},
 'design': {'cs_assignment': [{'cs': 'cs1', 'region': 'all'}],
            'dim': 1,
            'section_locations': [0.2, 0.9]},
 'distribution': [{'data': '0.0, airfoil_gbox_uni.xml.tmp\n'
                           '0.8, airfoil_solid.xml.tmp\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'topology',
                   'ytype': 'str'},
                  {'data': '0.0, sc1095.txt\n0.5, sc1094r8.txt\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'previous',
                   'name': 'airfoil',
                   'ytype': 'str'},
                  {'data': '0.1, 46, -45\n'
                           '0.3, -3, 0\n'
                           '0.7, -44, 90\n'
                           '1.0, 47, 45\n',
                   'data_form': 'compact',
                   'function': 'interpolation',
                   'kind': 'linear',
                   'name': ['ang_spar_1', 'ang_spar_2'],
                   'ytype': 'float'}],
 'name': 'blade1'} 
INFO     [2023-07-12 11:08:11] core.runSGenomeDesignAnalysis :: running cs design h analysis... 
DEBUG    [2023-07-12 11:08:11] core.runSGenomeDesignAnalysis :: cs: cs1, model: md1 
INFO     [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: ---------------- 
INFO     [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: running cs design analysis: cs1_set1... 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: local variables:
{'analysis': {'analysis': 'h',
              'output': [{'value': ['gj', 'eiyy', 'eizz']}],
              'setting': {'timeout': 60},
              'step': 'cs analysis',
              'type': 'cs'},
 'calculate_strength': False,
 'design_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'design_name': 'cs1',
 'dir_list': ['.'],
 'mdao_data': {},
 'model_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set1',
 'params': {'a2p1': 0.8,
            'a2p3': 0.6,
            'airfoil': 'sc1095.txt',
            'airfoil_file_head': 1,
            'airfoil_point_order': 1,
            'airfoil_point_reverse': 1,
            'ang_spar_1': 21.499999999999996,
            'ang_spar_2': -22.499999999999996,
            'chord': 1.73,
            'gms': 0.004,
            'lam_back': 'T300 15k/976_0.0053',
            'lam_cap': 'Aluminum 8009_0.01',
            'lam_front': 'T300 15k/976_0.0053',
            'lam_skin': 'T300 15k/976_0.0053',
            'lam_spar_1': 'T300 15k/976_0.0053',
            'location': 0.2,
            'mat_fill_back': 'Plascore PN2-3/16OX3.0',
            'mat_fill_front': 'Rohacell 70',
            'mat_fill_te': 'Plascore PN2-3/16OX3.0',
            'mat_nsm': 'lead',
            'mdb_name': 'material_database_us_ft',
            'ply_spar_1': 10,
            'rnsm': 0.001,
            'topology': 'airfoil_gbox_uni.xml.tmp'},
 'physics': 'elastic',
 'returns': None,
 'sg_id': 'set1',
 'sg_key': 'cs',
 'sgdb': {},
 'sgdb_map': {},
 'sglib': {'cs1': {'design': {'base_file': 'topology',
                              'dim': 2,
                              'tool': 'prevabs'},
                   'model': {'md1': {'tool': 'vabs'}},
                   'name': 'cs1',
                   'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 21.499999999999996,
                                 'ang_spar_2': -22.499999999999996,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.2,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'}}},
 'sgs_data': {},
 'sgs_temp': {}} 
INFO     [2023-07-12 11:08:11] core.findSGPropByParam :: - finding cs cs1 properties by parameters... 
INFO     [2023-07-12 11:08:11] core.findSGPropByParam :: - not found 
INFO     [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: [cs: cs1_set1] updating current design inputs... 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: design before substitution:
{'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: model before substitution:
{'tool': 'vabs'} 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: design after substitution:
{'base_file': 'airfoil_gbox_uni.xml.tmp', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: model after substitution:
{'tool': 'vabs'} 
INFO     [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: [cs: cs1_set1] checking if lower level cs properties exist... 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: parameters before preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1095.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 21.499999999999996,
 'ang_spar_2': -22.499999999999996,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.2,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_gbox_uni.xml.tmp'} 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: prepros =
[] 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: _fn_base = airfoil_gbox_uni.xml.tmp 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: _fn_spec = cs1_set1.xml 
DEBUG    [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: parameters after preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1095.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 21.499999999999996,
 'ang_spar_2': -22.499999999999996,
 'chord': 1.73,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.2,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_gbox_uni.xml.tmp'} 
INFO     [2023-07-12 11:08:11] core.runSGDesignAnalysisH :: [cs: cs1_set1] creating cs... 
INFO     [2023-07-12 11:08:11] main.buildSG :: building 2D SG: cs1_set1... 
CRITICAL [2023-07-12 11:08:11] execu.run :: prevabs -i cs1_set1.xml -vabs -ver 4.0 -h 
INFO     [2023-07-12 11:08:12] core.runSGDesignAnalysisH :: [cs: cs1_set1] writing cs input file... 
INFO     [2023-07-12 11:08:12] core.runSGDesignAnalysisH :: [cs: cs1_set1] running cs analysis... 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: [cs: cs1_set1] reading cs analysis output file... 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: [cs: cs1_set1] adding cs outputs to the database... 
INFO     [2023-07-12 11:08:13] core.runSGenomeDesignAnalysis :: running cs design h analysis... 
DEBUG    [2023-07-12 11:08:13] core.runSGenomeDesignAnalysis :: cs: cs1, model: md1 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: ---------------- 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: running cs design analysis: cs1_set2... 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: local variables:
{'analysis': {'analysis': 'h',
              'output': [{'value': ['gj', 'eiyy', 'eizz']}],
              'setting': {'timeout': 60},
              'step': 'cs analysis',
              'type': 'cs'},
 'calculate_strength': False,
 'design_base': {'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'},
 'design_name': 'cs1',
 'dir_list': ['.'],
 'mdao_data': {'cs1_set1': {'eiyy': 182531.84636,
                            'eizz': 2808826.6659,
                            'gj': 56848.707959},
               'eiyy': 182531.84636,
               'eizz': 2808826.6659,
               'gj': 56848.707959},
 'model_base': {'tool': 'vabs'},
 'model_type': 'md1',
 'name': 'cs1_set2',
 'params': {'a2p1': 0.8,
            'a2p3': 0.6,
            'airfoil': 'sc1094r8.txt',
            'airfoil_file_head': 1,
            'airfoil_point_order': 1,
            'airfoil_point_reverse': 1,
            'ang_spar_1': 16.666666666666686,
            'ang_spar_2': 60.0,
            'chord': 1.73,
            'cs1_set1': {'eiyy': 182531.84636,
                         'eizz': 2808826.6659,
                         'gj': 56848.707959},
            'eiyy': 182531.84636,
            'eizz': 2808826.6659,
            'gj': 56848.707959,
            'gms': 0.004,
            'lam_back': 'T300 15k/976_0.0053',
            'lam_cap': 'Aluminum 8009_0.01',
            'lam_front': 'T300 15k/976_0.0053',
            'lam_skin': 'T300 15k/976_0.0053',
            'lam_spar_1': 'T300 15k/976_0.0053',
            'location': 0.9,
            'mat_fill_back': 'Plascore PN2-3/16OX3.0',
            'mat_fill_front': 'Rohacell 70',
            'mat_fill_te': 'Plascore PN2-3/16OX3.0',
            'mat_nsm': 'lead',
            'mdb_name': 'material_database_us_ft',
            'ply_spar_1': 10,
            'rnsm': 0.001,
            'topology': 'airfoil_solid.xml.tmp'},
 'physics': 'elastic',
 'returns': None,
 'sg_id': 'set2',
 'sg_key': 'cs',
 'sgdb': {'cs1': [{'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1095.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 21.499999999999996,
                                 'ang_spar_2': -22.499999999999996,
                                 'chord': 1.73,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.2,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_gbox_uni.xml.tmp'},
                   'property': {'md1': {'cmp11c': 5.0878057051e-07,
                                        'cmp11r': 5.0878057051e-07,
                                        'cmp12c': -1.5401286717e-09,
                                        'cmp12r': -7.530958881e-11,
                                        'cmp13c': -4.3595365276e-08,
                                        'cmp13r': 2.8556613473e-09,
                                        'cmp14c': 4.1606368247e-07,
                                        'cmp14r': -1.5401286717e-09,
                                        'cmp15r': -4.3595365276e-08,
                                        'cmp16r': 4.1606368247e-07,
                                        'cmp21c': -1.5401286717e-09,
                                        'cmp21r': -7.530958881e-11,
                                        'cmp22c': 1.75905493e-05,
                                        'cmp22r': 1.5539551764e-06,
                                        'cmp23c': -1.0894250155e-09,
                                        'cmp23r': -2.5828925164e-07,
                                        'cmp24c': 5.9281787424e-09,
                                        'cmp24r': 2.7187260737e-07,
                                        'cmp25r': 5.9677090988e-09,
                                        'cmp26r': 1.1403413773e-10,
                                        'cmp31c': -4.3595365276e-08,
                                        'cmp31r': 2.8556613473e-09,
                                        'cmp32c': -1.0894250155e-09,
                                        'cmp32r': -2.5828925164e-07,
                                        'cmp33c': 5.47838635e-06,
                                        'cmp33r': 2.6105890461e-05,
                                        'cmp34c': 2.3705357301e-08,
                                        'cmp34r': -2.1004911352e-05,
                                        'cmp35r': 1.1050021026e-09,
                                        'cmp36r': -6.2115945295e-09,
                                        'cmp41c': 4.1606368247e-07,
                                        'cmp41r': -1.5401286717e-09,
                                        'cmp42c': 5.9281787424e-09,
                                        'cmp42r': 2.7187260737e-07,
                                        'cmp43c': 2.3705357301e-08,
                                        'cmp43r': -2.1004911352e-05,
                                        'cmp44c': 3.5613024885e-07,
                                        'cmp44r': 1.75905493e-05,
                                        'cmp45r': -1.0894250155e-09,
                                        'cmp46r': 5.9281787424e-09,
                                        'cmp51r': -4.3595365276e-08,
                                        'cmp52r': 5.9677090988e-09,
                                        'cmp53r': 1.1050021026e-09,
                                        'cmp54r': -1.0894250155e-09,
                                        'cmp55r': 5.47838635e-06,
                                        'cmp56r': 2.3705357301e-08,
                                        'cmp61r': 4.1606368247e-07,
                                        'cmp62r': 1.1403413773e-10,
                                        'cmp63r': -6.2115945295e-09,
                                        'cmp64r': 5.9281787424e-09,
                                        'cmp65r': 2.3705357301e-08,
                                        'cmp66r': 3.5613024885e-07,
                                        'ea': 45936361.817,
                                        'ei22': 182531.84636,
                                        'ei33': 2808826.6659,
                                        'ga22': 641849.92559,
                                        'ga33': 984601.87622,
                                        'gj': 56848.707959,
                                        'mc2': 1.1494335098,
                                        'mc3': 0.012439183019,
                                        'mmoi1': 0.0072632486361,
                                        'mmoi2': 0.00026525481038,
                                        'mmoi3': 0.0069979938258,
                                        'ms11': 0.075463162134,
                                        'ms12': 0.0,
                                        'ms13': 0.0,
                                        'ms14': 0.0,
                                        'ms15': 0.00093870008494,
                                        'ms16': -0.086739887313,
                                        'ms21': 0.0,
                                        'ms22': 0.075463162134,
                                        'ms23': 0.0,
                                        'ms24': -0.00093870008494,
                                        'ms25': 0.0,
                                        'ms26': 0.0,
                                        'ms31': 0.0,
                                        'ms32': 0.0,
                                        'ms33': 0.075463162134,
                                        'ms34': 0.086739887313,
                                        'ms35': 0.0,
                                        'ms36': 0.0,
                                        'ms41': 0.0,
                                        'ms42': -0.00093870008494,
                                        'ms43': 0.086739887313,
                                        'ms44': 0.10697665841,
                                        'ms45': 0.0,
                                        'ms46': 0.0,
                                        'ms51': 0.00093870008494,
                                        'ms52': 0.0,
                                        'ms53': 0.0,
                                        'ms54': 0.0,
                                        'ms55': 0.00027700892854,
                                        'ms56': -0.00110180938,
                                        'ms61': -0.086739887313,
                                        'ms62': 0.0,
                                        'ms63': 0.0,
                                        'ms64': 0.0,
                                        'ms65': -0.00110180938,
                                        'ms66': 0.10669964949,
                                        'mu': 0.075463162134,
                                        'sc2': 1.1941020712,
                                        'sc3': 0.015455606458,
                                        'stf11c': 45945000.559,
                                        'stf11r': 45945000.559,
                                        'stf12c': 22162.951903,
                                        'stf12r': -5.5750261835,
                                        'stf13c': 598059.49438,
                                        'stf13r': -3.0120584186,
                                        'stf14c': -53717298.054,
                                        'stf14r': 22159.441361,
                                        'stf15r': 598059.50033,
                                        'stf16r': -53717298.047,
                                        'stf21c': 22162.951903,
                                        'stf21r': -5.5750261835,
                                        'stf22c': 56859.719142,
                                        'stf22r': 647062.21773,
                                        'stf23c': 303.89610305,
                                        'stf23r': -41934.168158,
                                        'stf24c': -26859.493003,
                                        'stf24r': -60074.499223,
                                        'stf25r': -708.88756699,
                                        'stf26r': 115.10153036,
                                        'stf31c': 598059.49438,
                                        'stf31r': -3.0120584186,
                                        'stf32c': 303.89610305,
                                        'stf32r': -41934.168158,
                                        'stf33c': 190372.95062,
                                        'stf33r': 979394.40393,
                                        'stf34c': -711384.37902,
                                        'stf34r': 1170145.8133,
                                        'stf35r': 91.123412413,
                                        'stf36r': -2384.9633539,
                                        'stf41c': -53717298.054,
                                        'stf41r': 22159.441361,
                                        'stf42c': -26859.493003,
                                        'stf42r': -60074.499223,
                                        'stf43c': -711384.37902,
                                        'stf43r': 1170145.8133,
                                        'stf44c': 65613184.403,
                                        'stf44r': 1455062.7137,
                                        'stf45r': 423.66317411,
                                        'stf46r': -29709.163618,
                                        'stf51r': 598059.50033,
                                        'stf52r': -708.88756699,
                                        'stf53r': 91.123412413,
                                        'stf54r': 423.66317411,
                                        'stf55r': 190373.72933,
                                        'stf56r': -711384.61511,
                                        'stf61r': -53717298.047,
                                        'stf62r': 115.10153036,
                                        'stf63r': -2384.9633539,
                                        'stf64r': -29709.163618,
                                        'stf65r': -711384.61511,
                                        'stf66r': 65613190.211,
                                        'tc2': 1.1691572113,
                                        'tc3': 0.013016726123}}}]},
 'sgdb_map': {},
 'sglib': {'cs1': {'design': {'base_file': 'topology',
                              'dim': 2,
                              'tool': 'prevabs'},
                   'model': {'md1': {'tool': 'vabs'}},
                   'name': 'cs1',
                   'parameter': {'a2p1': 0.8,
                                 'a2p3': 0.6,
                                 'airfoil': 'sc1094r8.txt',
                                 'airfoil_file_head': 1,
                                 'airfoil_point_order': 1,
                                 'airfoil_point_reverse': 1,
                                 'ang_spar_1': 16.666666666666686,
                                 'ang_spar_2': 60.0,
                                 'chord': 1.73,
                                 'cs1_set1': {'eiyy': 182531.84636,
                                              'eizz': 2808826.6659,
                                              'gj': 56848.707959},
                                 'eiyy': 182531.84636,
                                 'eizz': 2808826.6659,
                                 'gj': 56848.707959,
                                 'gms': 0.004,
                                 'lam_back': 'T300 15k/976_0.0053',
                                 'lam_cap': 'Aluminum 8009_0.01',
                                 'lam_front': 'T300 15k/976_0.0053',
                                 'lam_skin': 'T300 15k/976_0.0053',
                                 'lam_spar_1': 'T300 15k/976_0.0053',
                                 'location': 0.9,
                                 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
                                 'mat_fill_front': 'Rohacell 70',
                                 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
                                 'mat_nsm': 'lead',
                                 'mdb_name': 'material_database_us_ft',
                                 'ply_spar_1': 10,
                                 'rnsm': 0.001,
                                 'topology': 'airfoil_solid.xml.tmp'}}},
 'sgs_data': {},
 'sgs_temp': {}} 
INFO     [2023-07-12 11:08:13] core.findSGPropByParam :: - finding cs cs1 properties by parameters... 
INFO     [2023-07-12 11:08:13] core.findSGPropByParam :: - not found 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: [cs: cs1_set2] updating current design inputs... 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: design before substitution:
{'base_file': 'topology', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: model before substitution:
{'tool': 'vabs'} 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: design after substitution:
{'base_file': 'airfoil_solid.xml.tmp', 'dim': 2, 'tool': 'prevabs'} 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: model after substitution:
{'tool': 'vabs'} 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: [cs: cs1_set2] checking if lower level cs properties exist... 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: parameters before preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1094r8.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 16.666666666666686,
 'ang_spar_2': 60.0,
 'chord': 1.73,
 'cs1_set1': {'eiyy': 182531.84636, 'eizz': 2808826.6659, 'gj': 56848.707959},
 'eiyy': 182531.84636,
 'eizz': 2808826.6659,
 'gj': 56848.707959,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.9,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_solid.xml.tmp'} 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: prepros =
[] 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: _fn_base = airfoil_solid.xml.tmp 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: _fn_spec = cs1_set2.xml 
DEBUG    [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: parameters after preprocessing:
{'a2p1': 0.8,
 'a2p3': 0.6,
 'airfoil': 'sc1094r8.txt',
 'airfoil_file_head': 1,
 'airfoil_point_order': 1,
 'airfoil_point_reverse': 1,
 'ang_spar_1': 16.666666666666686,
 'ang_spar_2': 60.0,
 'chord': 1.73,
 'cs1_set1': {'eiyy': 182531.84636, 'eizz': 2808826.6659, 'gj': 56848.707959},
 'eiyy': 182531.84636,
 'eizz': 2808826.6659,
 'gj': 56848.707959,
 'gms': 0.004,
 'lam_back': 'T300 15k/976_0.0053',
 'lam_cap': 'Aluminum 8009_0.01',
 'lam_front': 'T300 15k/976_0.0053',
 'lam_skin': 'T300 15k/976_0.0053',
 'lam_spar_1': 'T300 15k/976_0.0053',
 'location': 0.9,
 'mat_fill_back': 'Plascore PN2-3/16OX3.0',
 'mat_fill_front': 'Rohacell 70',
 'mat_fill_te': 'Plascore PN2-3/16OX3.0',
 'mat_nsm': 'lead',
 'mdb_name': 'material_database_us_ft',
 'ply_spar_1': 10,
 'rnsm': 0.001,
 'topology': 'airfoil_solid.xml.tmp'} 
INFO     [2023-07-12 11:08:13] core.runSGDesignAnalysisH :: [cs: cs1_set2] creating cs... 
INFO     [2023-07-12 11:08:13] main.buildSG :: building 2D SG: cs1_set2... 
CRITICAL [2023-07-12 11:08:13] execu.run :: prevabs -i cs1_set2.xml -vabs -ver 4.0 -h 
INFO     [2023-07-12 11:08:14] core.runSGDesignAnalysisH :: [cs: cs1_set2] writing cs input file... 
INFO     [2023-07-12 11:08:14] core.runSGDesignAnalysisH :: [cs: cs1_set2] running cs analysis... 
INFO     [2023-07-12 11:08:14] core.runSGDesignAnalysisH :: [cs: cs1_set2] reading cs analysis output file... 
INFO     [2023-07-12 11:08:14] core.runSGDesignAnalysisH :: [cs: cs1_set2] adding cs outputs to the database... 
INFO     [2023-07-12 11:08:14] _msgd.writeMDAOEvalOut :: writing output files... 
