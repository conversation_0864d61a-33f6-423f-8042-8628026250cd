CRITICAL [2023-05-08 09:59:05] msgd.main ::  
CRITICAL [2023-05-08 09:59:05] msgd.main :: START 
CRITICAL [2023-05-08 09:59:05] io.readMSGDInput :: reading main input .\cs_design_ll_opt_soga.yml... 
CRITICAL [2023-05-08 09:59:05] dkt.runDakota :: calling: dakota -i cs_design_ll_opt_soga.dakota 
CRITICAL [2023-05-08 09:59:18] msgd.main :: FINISH 
CRITICAL [2023-05-08 09:59:18] msgd.main ::  
CRITICAL [2023-05-08 09:59:40] msgd.main ::  
CRITICAL [2023-05-08 09:59:40] msgd.main :: START 
CRITICAL [2023-05-08 09:59:40] io.readMSGDInput :: reading main input .\cs_design_ll_opt_soga.yml... 
CRITICAL [2023-05-08 09:59:40] dkt.runDakota :: calling: dakota -i cs_design_ll_opt_soga.dakota 
CRITICAL [2023-05-08 10:00:05] msgd.main :: FINISH 
CRITICAL [2023-05-08 10:00:05] msgd.main ::  
