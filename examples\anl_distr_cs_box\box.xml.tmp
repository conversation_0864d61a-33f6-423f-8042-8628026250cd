<cross_section name="box" format="1">

  <include>
    <!-- <baseline>baselines</baseline> -->
    <!-- <layup>layups</layup> -->
    <!-- <material>materials</material> -->
  </include>

  <analysis>
    <model>{model_type=1}</model>
    <trapeze>{trapeze=0}</trapeze>
  </analysis>

  <general>
    <scale>{scale=1}</scale>
    <mesh_size>{gms=0.01}</mesh_size>
    <element_type>{elem_type="linear"}</element_type>
  </general>

  <baselines>
    <!-- <point name="bl">{x2l=-1*0.4765}  -0.265</point> -->
    <point name="bl">{-1*w/2}  {-1*h/2}</point>
    <point name="tl">{-1*w/2}   {h/2}</point>
    <point name="tr">{w/2}   {h/2}</point>
    <point name="br">{w/2}  {-1*h/2}</point>
    <line name="bl_all" type="straight">
      <points>tl,tr,br,bl,tl</points>
    </line>
  </baselines>

  <materials>
    <material name="mat_1" type="orthotropic">
      <density>1.353e-04</density> <!-- lb * sec^2 / inch^4 -->
      <elastic>
        <e1>20.59e+06</e1> <!-- psi -->
        <e2>1.42e+06</e2>
        <e3>1.42e+06</e3>
        <g12>0.87e+06</g12>
        <g13>0.87e+06</g13>
        <g23>0.696e+06</g23>
        <nu12>0.30</nu12>
        <nu13>0.30</nu13>
        <nu23>0.34</nu23>
      </elastic>
    </material>
    <lamina name="la_mat_1">
      <material>mat_1</material>
      <thickness>{lma_thk=0.005}</thickness> <!-- inch -->
    </lamina>
  </materials>


  <layups>
    <layup name="layup_1">
      <layer lamina="la_mat_1">{lyr_ang=0}:{lyr_ply=6}</layer>
    </layup>
  </layups>


  <component name="main">
    <segment name="sg_all">
      <baseline>bl_all</baseline>
      <layup direction="right">layup_1</layup>
    </segment>
  </component>


</cross_section>
