from __future__ import annotations

import copy
import logging

from pydantic import BaseModel, Field

import msgd.utils as mutils


logger = logging.getLogger(__name__)



class StructureParameters(BaseModel):
    """
    """
    base_design_name:str = Field(default='', description="Name of the base design")
    parameters:dict = Field(default_factory=dict, description="Parameters")



class StructureDesign():
    """General class for the structural design of both global structures and SGs.

    Parameters
    ----------
    name:str
        Name of the design.
    base:dict
        Base design inputs.
    builder:str
        Name of the builder.
    parameters:dict
        Design parameters.
    distributions:list
        List of distributions.
    domains:list
        List of domain transformations.
    sgdim:int
        Dimension of the SG.
    """

    def __init__(
        self, name:str='', base:dict={}, builder:str='',
        builder_cmd:str='', builder_version:str='',
        parameters={}, distributions=[], domains=[],
        sg_assignments:list=[],
        sgdim:int=1):

        # For both global structure and SG
        self._name = name
        self._base = base
        self._parameters = parameters
        self._distributions = distributions
        self._domains = domains
        self._sg_assignments = sg_assignments

        # Builder
        self._builder = builder
        self._builder_cmd = builder_cmd
        self._builder_version = builder_version

        # For SG only
        self._dim = sgdim

    def __repr__(self):
        s = [
            f'Structure design (name: {self._name})',
            f'  - dim: {self._dim}',
            f'  - builder: {self._builder}',
        ]
        s.append('  - base:')
        for _n, _v in self._base.items():
            s.append(f'    - {_n}: {_v}')
        s.append('  - parameters:')
        for _n, _v in self._parameters.items():
            s.append(f'    - {_n}: {_v}')

        return '\n'.join(s)

    def toDictionary(self, **kwargs) -> dict:
        """Convert the object to a dictionary.
        """
        _dict = {
            'name': self._name,
            'parameter': self._parameters,
            'dim': self._dim,
            'builder': self._builder,
            'design': self._base
        }

        if len(self._distributions) > 0:
            _dict['distribution'] = [d.toDictionary() for d in self._distributions]

        return _dict

    @property
    def name(self): return self._name
    @property
    def base(self): return self._base
    @property
    def builder(self): return self._builder
    @property
    def builder_cmd(self):
        if self._builder_cmd == '':
            return self._builder
        return self._builder_cmd
    @property
    def builder_version(self): return self._builder_version
    @property
    def tool(self): return self._builder
    @property
    def parameters(self): return self._parameters
    @property
    def distributions(self): return self._distributions
    @property
    def domains(self): return self._domains
    @property
    def sg_assignments(self): return self._sg_assignments
    @sg_assignments.setter
    def sg_assignments(self, value): self._sg_assignments = value
    @property
    def sgdim(self): return self._dim
    @property
    def dimension(self): return self._dim

    def setParameter(self, name:str, value):
        self._parameters[name] = value

    def getParameter(self, name:str):
        return self._parameters[name]

    def copy(self, parameters:dict={}):
        """Make a copy of this object
        and optionally substitute new values to parameters.

        Parameters
        ----------
        parameters:dict
            New parameters.

        Returns
        -------
        StructureDesign
            Copied object.
        """
        new = copy.deepcopy(self)
        for _name, _value in new.parameters.items():
            try:
                new.setParameter(name=_name, value=parameters[_name])
            except KeyError:
                pass
        return new


    def updateParameters(self, parameters:dict, direct=True) -> None:
        """Update design parameters.

        Parameters
        ----------
        parameters:dict
            New parameters.
        """
        logger.debug(f'[{self.name}] updating parameters...')

        logger.debug('input parameters')
        logger.debug(parameters)

        # self._parameters.update(parameters)
        if self._parameters is None:
           self._parameters = {}

        _tmp_params = copy.deepcopy(parameters)
        for k, v in self._parameters.items():
            logger.debug(f'[{self.name}]  updating {k} = {v}')

            _vnew = None

            # Direct substitution
            if direct and k in parameters.keys():
                _vnew = parameters[k]

            elif v in parameters.keys():
                _vnew = parameters[v]

            # Evaluate string expressions
            elif isinstance(v, str):
                _str = v.split(':')
                if len(_str) > 1 and _str[0] == 'f':
                    _vnew = eval(_str[1], _tmp_params)
                # try:
                #     _vnew = eval(v, _tmp_params)
                # except NameError:
                #     pass

            if not _vnew is None:
                logger.debug(f'[{self.name}]           {k} = {_vnew}')
                self._parameters[k] = _vnew
                # _tmp_params[k] = _vnew

            _tmp_params[k] = copy.deepcopy(self._parameters[k])


    def substituteParameters(self, additional_params:dict={}) -> None:
        """Substitute parameters in the design.
        """
        logger.debug(f'[{self.name}] substituting parameters...')
        mutils.substituteParams(self._base, self._parameters)
        for _distr in self._distributions:
            _distr.updateCoefficient(self._parameters)


    def implementDistributionFunctions(self, db_function, db_domain) -> None:
        """Implement distributions.

        Parameters
        ----------
        db_function:DataBase
            Database of functions.
        db_domain:DataBase
            Database of domain transformations.
        """
        logger.info(f'[{self.name}] implementing distribution functions...')
        for _distr in self._distributions:
            _distr.implement(db_function, db_domain, self.parameters)

    def implementDomainTransformations(self, db_function):
        ...





















# class StructureData():
#     """Class containing all necessary and specific data for analysis.
#     """
#     def __init__(
#         self, name:str='',
#         design:StructureDesign=None,
#         model:StructureModel=None,
#         mesh=None,
#         parameters:dict={}
#         ):
#         self._name:str = name
#         self._parameters:dict = parameters
#         self._design:StructureDesign = design
#         self._model:StructureModel = model
#         self._mesh = mesh
#         self._sg:sgio.StructureGene = None

#     def __repr__(self):
#         _str = [
#             f'SG Data (name: {self._name})',
#         ]

#         _str.append('  - parameters:')
#         for _n, _v in self._parameters.items():
#             _str.append(f'    - {_n}: {_v}')

#         _str.append(f'  - model: {self._model}')

#         return '\n'.join(_str)

#     @property
#     def name(self) -> str: return self._name
#     @property
#     def sgdim(self): return self._model.design.sgdim
#     @property
#     def smdim(self): return self._model.smdim
#     @property
#     def parameters(self): return self._parameters
#     @property
#     def model(self): return self._model
#     @property
#     def sg(self) -> sgio.StructureGene: return self._sg

#     def toDictionary(self, **kwargs) -> dict:
#         _dict = {
#             'name': self._name,
#             'parameter': self._parameters,
#         }
#         _dict.update(self._model.toDictionary())
#         return _dict

#     def getParameter(self, name:str):
#         return self._parameters[name]
    
#     def setSG(self, sg:sgio.StructureGene):
#         self._sg = sg


# # @dataclass
# # class StructureDesignConcept():
# #     """Class for storing structure design concepts."""
# #     name : str = None



# # class StructureAnalysisRepresentation(ABC):
# #     """An abstract class for structure representation for analysis."""

# #     @abstractmethod
# #     def __repr__(self):
# #         pass










# class MSGDFEModel():
#     r"""
#     """

#     def __init__(self, name=''):
#         self.name = name

#         self.mesh:Mesh = None

#         self.nodes = {}
#         self.elements = {}
#         self.node_sets = {}
#         self.element_sets = {}


#     # @property
#     # def nnodes(self):
#     #     return len(self.mesh.points)


#     # @property
#     # def nelems(self):
#     #     return sum([len(cell.data) for cell in self.mesh.cells])


#     # def __repr__(self):
#     #     lines = []

#     #     lines += [
#     #         '-'*30,
#     #         'MESH',
#     #         'Number of nodes: {}'.format(self.nnodes),
#     #         'Number of elements: {}'.format(self.nelems),
#     #         str(self.mesh),
#     #         '',
#     #     ]

#     #     return '\n'.join(lines)


#     def summary(self):
#         print('Nodes')
#         print(self.nodes)
#         print()
#         print('Node sets')
#         print(self.node_sets)
#         print()
#         print('Elements')
#         print(self.elements)
#         print()
#         print('Element sets')
#         print(self.element_sets)









