@ECHO OFF

pushd %~dp0

REM Command file for Sphinx documentation

if "%SPHINXBUILD%" == "" (
	set SPHINXBUILD=sphinx-build
)
set SOURCEDIR=source
set BUILDDIR=build
set CONFDIR=source
set TAGS=msg

if "%1" == "" goto help

if "%2" == "ivabs" (
	set BUILDDIR=build_ivabs
	set TAGS=ivabs
)

if "%2" == "dev" (
	set BUILDDIR=build_dev
	set TAGS=dev
	@REM set CONFDIR=source\dev
)

%SPHINXBUILD% >NUL 2>NUL
if errorlevel 9009 (
	echo.
	echo.The 'sphinx-build' command was not found. Make sure you have Sphinx
	echo.installed, then set the SPHINXBUILD environment variable to point
	echo.to the full path of the 'sphinx-build' executable. Alternatively you
	echo.may add the Sphinx directory to PATH.
	echo.
	echo.If you don't have Sphinx installed, grab it from
	echo.http://sphinx-doc.org/
	exit /b 1
)

%SPHINXBUILD% -b %1 -c %CONFDIR% %SOURCEDIR% %BUILDDIR% %SPHINXOPTS% %O% -t %TAGS%
goto end

:help
%SPHINXBUILD% -M help %SOURCEDIR% %BUILDDIR% %SPHINXOPTS% %O%

:end
popd
