# unit system
# mm, N, MPa

version: "0.10"


# --------------------------------------------------------------------
structure:
  name: "square_plate"
  # type: null

  parameter:
    l1v1: 0
    l1v2: 90

  distribution:
    - name: a1
      function: f1
      coefficient:
        v1: l1v1
        v2: l1v2
  # coordinate_transformation:
  #   function: tf
  #   coefficient:
  #     wx: 200
  #     wy: 200
  design:

  model:
    tool: "abaqus"
    # type: 'discrete'
    main_file: "plate400_s4r_10x10.inp"
    prop_file: "shellsections.inp"
    # file: "sq_1ply_cccc_s4r_20x20.inp"
    config:
      orient_name: 'Ori-1'

  physics: "thermoelastic"

  sg_assignment:
    - region: 'all'
      location: 'element'
      sg: "mainsg"

  sg:
    - name: 'mainsg'
      design: "lv1_layup"
      model:
        type: "pl1"
        tool: "swiftcomp"
        tool_version: "2.1"
        config:
          mesh_size: -1


# --------------------------------------------------------------------
function:
  - name: 'ff_nds'
    type: 'expression'
    expression: '(v2-v1)*abs(x)+v1'
    coefficients: ['v1', 'v2']

  - name: 'tf'
    type: 'script'
    file_name: 'data_proc_funcs'
    function_name: 'trans'

  - name: "f1"
    type: "expression"
    expression: "2*(v2-v1)*abs(x)/400+v1"
    coefficients: ["v1", "v2"]

# --------------------------------------------------------------------
sg:
  - name: "lv1_layup"
    parameter:
      a1: 0

    design:
      dim: 1
      # type: 'gd1'
      type: "descriptive"
      tool: "default"
      symmetry: 2
      layers:
        - material: "m2"
          ply_thickness: 1.27
          number_of_plies: 1
          in-plane_orientation: a1

    # model:
    #   md2:
    #     tool: "swiftcomp"
    #     version: "2.1"
    #     mesh_size: -1
    #     # k11: _k11
    #     # k22: _k22


  - name: "m2"
    type: "material"
    model:
      type: 'sd1'
      property:
        density: 1.0
        anisotropy: "engineering"
        elasticity:
          [
            181e3, 8.96e3, 8.96e3,
            7.2e3, 7.2e3, 7.2e3,
            0.3, 0.28, 0.28
          ]
        cte: [22.5e-6, 2e-6, 2e-6]
        specific_heat: 0


# --------------------------------------------------------------------
analysis:
  # physics: "thermoelastic"
  steps:
    - name: "homogenization"
      type: "sg"
      analysis: "h"
      # setting:
      #   solver: "swiftcomp"
        # version: '2.2'
      # outputs:
      #   final:
      #     e1: "e1"

    - name: "buckling analysis"
      type: "abaqus"
      # job_file: "plate400_s4r_10x10.inp"
      setting:
        timeout: 300
      kwargs:
        exec:
          args:
            - "interactive"
          kwargs:
            ask_delete: "OFF"
      post_process:
        - script: "abq_get_result.py"
          args:
            - "plate400_s4r_10x10.odb"
            - "abq_result.dat"
      step_result_file: "abq_result.dat"


