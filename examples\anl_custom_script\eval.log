INFO     [2024-05-17 15:08:27] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-17 15:08:27] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-17 15:08:27] _msgd.updateData :: updating current design... 
INFO     [2024-05-17 15:08:27] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-17 15:08:27] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-17 15:08:27] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-17 15:08:27] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-17 15:08:27] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-17 15:08:27] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-17 15:08:27] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-17 15:08:27] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-17 15:08:27] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-17 15:08:27] custom.run :: running custom script: materialId2Name... 
INFO     [2024-05-17 15:08:27] custom.implement :: import data_proc_funcs as user_mod 
CRITICAL [2024-05-17 15:08:27] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\custom.py", line 66, in run
    self.function(
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\data_proc_funcs.py", line 11, in materialId2Name
    data['lam_spar'] = mdb[data['ilam_spar']]
                           ~~~~^^^^^^^^^^^^^
KeyError: 'ilam_spar'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\custom.py", line 66, in run
    self.function(
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\data_proc_funcs.py", line 11, in materialId2Name
    data['lam_spar'] = mdb[data['ilam_spar']]
                           ~~~~^^^^^^^^^^^^^
KeyError: 'ilam_spar'
INFO     [2024-05-17 15:08:27] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-17 15:08:55] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-17 15:08:55] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-17 15:08:55] _msgd.updateData :: updating current design... 
INFO     [2024-05-17 15:08:55] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-17 15:08:55] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-17 15:08:55] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-17 15:08:55] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-17 15:08:55] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-17 15:08:55] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-17 15:08:55] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-17 15:08:55] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-17 15:08:55] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-17 15:08:55] custom.run :: running custom script: materialId2Name... 
INFO     [2024-05-17 15:08:55] custom.implement :: import data_proc_funcs as user_mod 
CRITICAL [2024-05-17 15:08:55] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\custom.py", line 66, in run
    self.function(
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\data_proc_funcs.py", line 12, in materialId2Name
    data['lam_spar'] = mdb[data['ilam_spar']]
                           ~~~~^^^^^^^^^^^^^
KeyError: 'ilam_spar'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\custom.py", line 66, in run
    self.function(
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\data_proc_funcs.py", line 12, in materialId2Name
    data['lam_spar'] = mdb[data['ilam_spar']]
                           ~~~~^^^^^^^^^^^^^
KeyError: 'ilam_spar'
INFO     [2024-05-17 15:08:55] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-18 10:40:26] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-18 10:40:26] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-18 10:40:26] _msgd.updateData :: updating current design... 
INFO     [2024-05-18 10:40:26] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-18 10:40:26] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-18 10:40:26] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-18 10:40:26] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-18 10:40:26] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-18 10:40:26] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-18 10:40:26] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-18 10:40:26] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-18 10:40:26] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-18 10:40:26] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-18 10:40:26] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-18 10:40:26] _structure.updateParameters :: [airfoil] updating parameters... 
INFO     [2024-05-18 10:40:26] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-18 10:40:26] _structure.substituteParameters :: [airfoil] substituting parameters... 
INFO     [2024-05-18 10:40:26] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-18 10:40:26] main.buildSGFromPrevabs :: building cs using prevabs... 
CRITICAL [2024-05-18 10:40:26] main.buildSGFromPrevabs :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 455, in buildSGFromPrevabs
    di.dprepro(
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\interfacing.py", line 1084, in dprepro
    output_string = dprepro_mod.dprepro(include=env, template=template, fmt=fmt,
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1240, in dprepro
    output_string = pyprepro(tpl=use_template,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 255, in pyprepro
    txtout = _template(tpl,env=env)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1787, in _template
    rendered,env =  tpl_obj.render(env)
                    ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1550, in render
    env = self.execute(stdout, env)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1536, in execute
    exec_(self.co,env)
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\airfoil_gbox_uni.xml.tmp", line 172, in <module>
    <layup>lyp_te</layup>
    ^^^^^^^^
NameError: name 'lam_spar' is not defined
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 455, in buildSGFromPrevabs
    di.dprepro(
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\interfacing.py", line 1084, in dprepro
    output_string = dprepro_mod.dprepro(include=env, template=template, fmt=fmt,
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1240, in dprepro
    output_string = pyprepro(tpl=use_template,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 255, in pyprepro
    txtout = _template(tpl,env=env)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1787, in _template
    rendered,env =  tpl_obj.render(env)
                    ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1550, in render
    env = self.execute(stdout, env)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1536, in execute
    exec_(self.co,env)
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\airfoil_gbox_uni.xml.tmp", line 172, in <module>
    <layup>lyp_te</layup>
    ^^^^^^^^
NameError: name 'lam_spar' is not defined
CRITICAL [2024-05-18 10:40:26] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 408, in run
    self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 164, in runH
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 779, in run
    output = self.runH(
             ^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 602, in runH
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 568, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 180, in buildSGModel
    fn_sg, fns_include = buildSGFromPrevabs(
                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 482, in buildSGFromPrevabs
    fns_include = extractIncludedFiles(fn_xml_in)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 403, in extractIncludedFiles
    xtree, xroot = mutils.parseXML(fn_xml)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 569, in parse
    source = open(source, "rb")
             ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'cs\\main_cs_set1.xml'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 408, in run
    self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 164, in runH
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 779, in run
    output = self.runH(
             ^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 602, in runH
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 568, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 180, in buildSGModel
    fn_sg, fns_include = buildSGFromPrevabs(
                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 482, in buildSGFromPrevabs
    fns_include = extractIncludedFiles(fn_xml_in)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 403, in extractIncludedFiles
    xtree, xroot = mutils.parseXML(fn_xml)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 569, in parse
    source = open(source, "rb")
             ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'cs\\main_cs_set1.xml'
INFO     [2024-05-18 10:40:26] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-18 10:42:01] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-18 10:42:01] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-18 10:42:01] _msgd.updateData :: updating current design... 
INFO     [2024-05-18 10:42:01] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-18 10:42:01] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-18 10:42:01] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-18 10:42:01] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-18 10:42:01] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-18 10:42:01] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-18 10:42:01] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-18 10:42:01] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-18 10:42:01] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-18 10:42:01] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-18 10:42:01] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-18 10:42:01] _structure.updateParameters :: [airfoil] updating parameters... 
INFO     [2024-05-18 10:42:01] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-18 10:42:01] _structure.substituteParameters :: [airfoil] substituting parameters... 
INFO     [2024-05-18 10:42:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-18 10:42:01] main.buildSGFromPrevabs :: building cs using prevabs... 
CRITICAL [2024-05-18 10:42:01] main.buildSGFromPrevabs :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 455, in buildSGFromPrevabs
    di.dprepro(
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\interfacing.py", line 1084, in dprepro
    output_string = dprepro_mod.dprepro(include=env, template=template, fmt=fmt,
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1240, in dprepro
    output_string = pyprepro(tpl=use_template,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 255, in pyprepro
    txtout = _template(tpl,env=env)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1787, in _template
    rendered,env =  tpl_obj.render(env)
                    ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1550, in render
    env = self.execute(stdout, env)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1536, in execute
    exec_(self.co,env)
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\airfoil_gbox_uni.xml.tmp", line 172, in <module>
    <layup>lyp_te</layup>
    ^^^^^^^^
NameError: name 'lam_spar' is not defined
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 455, in buildSGFromPrevabs
    di.dprepro(
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\interfacing.py", line 1084, in dprepro
    output_string = dprepro_mod.dprepro(include=env, template=template, fmt=fmt,
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1240, in dprepro
    output_string = pyprepro(tpl=use_template,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 255, in pyprepro
    txtout = _template(tpl,env=env)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1787, in _template
    rendered,env =  tpl_obj.render(env)
                    ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1550, in render
    env = self.execute(stdout, env)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\program\dakota-6.18\share\dakota\Python\dakota\interfacing\dprepro.py", line 1536, in execute
    exec_(self.co,env)
  File "C:\Users\<USER>\work\dev\msg-design\examples\anl_custom_script\airfoil_gbox_uni.xml.tmp", line 172, in <module>
    <layup>lyp_te</layup>
    ^^^^^^^^
NameError: name 'lam_spar' is not defined
CRITICAL [2024-05-18 10:42:01] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 408, in run
    self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 164, in runH
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 779, in run
    output = self.runH(
             ^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 602, in runH
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 568, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 180, in buildSGModel
    fn_sg, fns_include = buildSGFromPrevabs(
                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 482, in buildSGFromPrevabs
    fns_include = extractIncludedFiles(fn_xml_in)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 403, in extractIncludedFiles
    xtree, xroot = mutils.parseXML(fn_xml)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 569, in parse
    source = open(source, "rb")
             ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'cs\\main_cs_set1.xml'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 235, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 529, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 408, in run
    self.runH(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 164, in runH
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 779, in run
    output = self.runH(
             ^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 602, in runH
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 568, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 180, in buildSGModel
    fn_sg, fns_include = buildSGFromPrevabs(
                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 482, in buildSGFromPrevabs
    fns_include = extractIncludedFiles(fn_xml_in)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 403, in extractIncludedFiles
    xtree, xroot = mutils.parseXML(fn_xml)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\io.py", line 334, in parseXML
    tree = et.parse(fn_xml)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 1218, in parse
    tree.parse(source, parser)
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\xml\etree\ElementTree.py", line 569, in parse
    source = open(source, "rb")
             ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'cs\\main_cs_set1.xml'
INFO     [2024-05-18 10:42:01] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-18 11:05:14] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-18 11:05:14] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-18 11:05:14] _msgd.updateData :: updating current design... 
INFO     [2024-05-18 11:05:14] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-18 11:05:14] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-18 11:05:14] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-18 11:05:14] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-18 11:05:14] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-18 11:05:14] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-18 11:05:14] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-18 11:05:14] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-18 11:05:14] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-18 11:05:14] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-05-18 11:05:14] execu.importFunction :: import data_proc_funcs 
INFO     [2024-05-18 11:05:14] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-18 11:05:14] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-18 11:05:14] _structure.updateParameters :: [airfoil] updating parameters... 
INFO     [2024-05-18 11:05:14] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-18 11:05:14] _structure.substituteParameters :: [airfoil] substituting parameters... 
INFO     [2024-05-18 11:05:14] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-18 11:05:14] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-18 11:05:14] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-18 11:05:17] execu.run :: VABS cs\main_cs_set1.sg 
INFO     [2024-05-18 11:05:20] execu.run :: VABS finished successfully 
INFO     [2024-05-18 11:05:20] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-18 14:23:45] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-18 14:23:45] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-18 14:23:45] _msgd.updateData :: updating current design... 
INFO     [2024-05-18 14:23:45] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-18 14:23:45] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-18 14:23:45] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-18 14:23:45] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-18 14:23:45] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-18 14:23:45] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-18 14:23:45] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-18 14:23:45] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-18 14:23:45] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-18 14:23:45] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-05-18 14:23:45] execu.importFunction :: import data_proc_funcs 
INFO     [2024-05-18 14:23:45] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-18 14:23:45] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-18 14:23:45] _structure.updateParameters :: [airfoil] updating parameters... 
INFO     [2024-05-18 14:23:45] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-18 14:23:45] _structure.substituteParameters :: [airfoil] substituting parameters... 
INFO     [2024-05-18 14:23:45] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-18 14:23:45] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-18 14:23:46] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-18 14:23:47] execu.run :: VABS cs\main_cs_set1.sg 
INFO     [2024-05-18 14:23:50] execu.run :: VABS finished successfully 
INFO     [2024-05-18 14:23:50] custom.run :: running custom script: postProcess... 
INFO     [2024-05-18 14:23:50] execu.importFunction :: import data_proc_funcs 
INFO     [2024-05-18 14:23:50] custom.run :: msgd_data = {'gj': 3406.0352758, 'eiyy': 20239.783833, 'eizz': 3728655.9416} 
INFO     [2024-05-18 14:23:50] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-18 14:26:13] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-18 14:26:13] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-18 14:26:13] _msgd.updateData :: updating current design... 
INFO     [2024-05-18 14:26:13] _structure.updateParameters :: [blade] updating parameters... 
INFO     [2024-05-18 14:26:13] _structure.substituteParameters :: [blade] substituting parameters... 
INFO     [2024-05-18 14:26:13] _structure.loadStructureMesh :: [blade] loading structural mesh data... 
INFO     [2024-05-18 14:26:13] _structure.implementDomainTransformations :: [blade] implementing domain transformations... 
INFO     [2024-05-18 14:26:13] _structure.implementDistributionFunctions :: [blade] implementing distribution functions... 
INFO     [2024-05-18 14:26:13] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-18 14:26:13] _structure.discretizeDesign :: [blade] discretizing the design... 
INFO     [2024-05-18 14:26:13] _structure.writeMeshData :: writing mesh data to file blade_mesh.msh... 
INFO     [2024-05-18 14:26:13] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-18 14:26:13] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-05-18 14:26:13] execu.importFunction :: import data_proc_funcs 
INFO     [2024-05-18 14:26:13] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-18 14:26:13] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-18 14:26:13] _structure.updateParameters :: [airfoil] updating parameters... 
INFO     [2024-05-18 14:26:13] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-18 14:26:13] _structure.substituteParameters :: [airfoil] substituting parameters... 
INFO     [2024-05-18 14:26:13] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-18 14:26:14] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-18 14:26:14] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-18 14:26:16] execu.run :: VABS cs\main_cs_set1.sg 
INFO     [2024-05-18 14:26:18] execu.run :: VABS finished successfully 
INFO     [2024-05-18 14:26:18] custom.run :: running custom script: postProcess... 
INFO     [2024-05-18 14:26:18] execu.importFunction :: import data_proc_funcs 
INFO     [2024-05-18 14:26:18] _msgd.writeAnalysisOut :: [main] writing output to file ... 
