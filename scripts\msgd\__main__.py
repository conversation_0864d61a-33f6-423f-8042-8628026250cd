# import os
import argparse
import logging
import sys
import traceback as tb
# import textwrap

import sgio
import sgio.utils
from icecream import install, ic

# from ._global import *
import msgd._global as GLOBAL
# import msgd.core as mc
# import msgd.core.io as mi
# import msgd.utils.logger as mul
import msgd.utils as mutils
from msgd._global import (
    logger,
    configure_logging,
    console
    )

# import msgd.core.analysis as mca
from msgd._msgd import MSGD
from msgd.io import readMSGDInput
from msgd.design.transformation import createNodeMapFromThree

install()

# console = GLOBAL.console

# logger = logging.getLogger()
# print(f'logger name: {logger.name}')
# ic(logger)


def cli(*args):
    console.print(f'raw args = {args}')

    largs = []

    if len(args) == 1:
        parser = argparse.ArgumentParser(
            prog='msgd',
            description='MSG-based design tool',
            formatter_class=argparse.ArgumentDefaultsHelpFormatter)
        variant = 'none'
        executable = 'msgd'
    else:
        if args[1] == 'ivabs':
            parser = argparse.ArgumentParser(
                prog='ivabs',
                description='iVABS',
                formatter_class=argparse.ArgumentDefaultsHelpFormatter)
            variant = 'ivabs'
            executable = 'ivabs'
            largs = args[2:]
        elif args[1] == 'datc':
            parser = argparse.ArgumentParser(
                prog='datc',
                description='Design Tool for Advanced Tailorable Composites',
                formatter_class=argparse.ArgumentDefaultsHelpFormatter)
            variant = 'none'
            executable = 'datc'
            largs = args[2:]
        else:
            parser = argparse.ArgumentParser(
                prog='msgd',
                description='MSG-based design tool',
                formatter_class=argparse.ArgumentDefaultsHelpFormatter)
            variant = 'none'
            executable = 'msgd'
            largs = args[1:]

    console.print(f'list args = {largs}')

    subparsers = parser.add_subparsers(
        help='sub-command help', dest='subparser_name')

    # Add logging arguments to each subparser
    logging_args = argparse.ArgumentParser(add_help=False)
    logging_args.add_argument(
        '--loglevelcmd', help='Command line logging level',
        default='info', choices=['debug', 'info', 'warning', 'error', 'critical'])
    logging_args.add_argument(
        '--loglevelfile', help='File logging level',
        default='info', choices=['debug', 'info', 'warning', 'error', 'critical'])
    logging_args.add_argument(
        '--logfile', help='Logging file name',
        default='log.txt')

    # Analyze
    # -------
    parser_analyze = subparsers.add_parser(
        'analyze', help='Analyze a design',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        parents=[logging_args])
    parser_analyze.add_argument(
        'inputfile', type=str, help='Main input file')

    # parser.add_argument(
    #     '--mode', help='Mode of running',
    #     default='0', choices=['0', '1'])

    parser_analyze.add_argument(
        '--paramfile', help='File containing parameters', default='')
    parser_analyze.add_argument(
        '--resultfile', help='File for storing analysis results', default='')


    # Iterative analysis
    # ------------------
    parser_iter = subparsers.add_parser(
        'iter', help='Iterative analysis',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        parents=[logging_args])
    parser_iter.add_argument(
        'inputfile', type=str, help='Main input file')


    # Three node mapping
    # ------------------
    parser_map = subparsers.add_parser(
        'map', help='Create a mapping of meshes between the physcial and parametric domains',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        parents=[logging_args])
    parser_map.add_argument(
        '--physcmesh', type=str, help='Mesh file of the physical domain'
    )
    parser_map.add_argument(
        '--parammesh', type=str, help='Mesh file of the parametric domain'
    )
    parser_map.add_argument(
        '--physcmeshfmt', type=str, help='Format of the physical domain mesh'
    )
    parser_map.add_argument(
        '--parammeshfmt', type=str, help='Format of the parametric domain mesh'
    )
    parser_map.add_argument(
        '--mapfile', type=str, help='File for storing the mapping'
    )


    # Plotting
    # --------
    parser_plot = subparsers.add_parser(
        'plot', help='Plot all CS/SG',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        parents=[logging_args])
    parser_plot.add_argument(
        'inputfile', type=str, help='Main input file')
    # parser_plot.add_argument(
    #     '-p', '--plot', help='Plot all CS/SG',
    #     action='store_true')
    parser_plot.add_argument(
        '--model', help='Global structural model',
        default='BM1', choices=['SD1', 'PL1', 'PL2', 'BM1', 'BM2']
    )
    parser_plot.add_argument(
        '--sgtool', help='SG/CS modeling tool',
        default='vabs', choices=['vabs', 'swiftcomp']
    )
    parser_plot.add_argument(
        '--sgdir', help='Directory for storing SG/CS files',
        default='.'
    )

    pargs = parser.parse_args(args=largs)

    console.print(f'parsed args = {pargs}')
    console.print(f'variant = {variant}')


    if pargs.subparser_name == 'analyze':
        return main(
            fn_main=pargs.inputfile,
            mode='1',
            variant=variant,
            executable=executable,
            fn_dakota_params=pargs.paramfile,
            fn_dakota_results=pargs.resultfile,
            log_level_cmd=pargs.loglevelcmd,
            log_level_file=pargs.loglevelfile,
            log_file_name=pargs.logfile
        )

    elif pargs.subparser_name == 'iter':
        return main(
            fn_main=pargs.inputfile,
            mode='0',
            variant=variant,
            executable=executable,
            # fn_dakota_params=pargs.paramfile,
            # fn_dakota_results=pargs.resultfile,
            log_level_cmd=pargs.loglevelcmd,
            log_level_file=pargs.loglevelfile,
            log_file_name=pargs.logfile
        )

    elif pargs.subparser_name == 'map':
        return createMeshMap(
            fn_mesh_phys=pargs.physcmesh,
            fn_mesh_param=pargs.parammesh,
            phys_mesh_fmt=pargs.physcmeshfmt,
            param_mesh_fmt=pargs.parammeshfmt,
            fn_map=pargs.mapfile,
            log_level_cmd=pargs.loglevelcmd,
            log_level_file=pargs.loglevelfile,
            log_file_name=pargs.logfile
        )

    elif pargs.subparser_name == 'plot':
        return plot(
            fn_main=pargs.inputfile,
            variant=variant,
            sg_tool=pargs.sgtool,
            model=pargs.model,
            sgdir=pargs.sgdir
        )

    else:
        parser.print_help()








def printTitle(text=None):

    default_name = 'MSG Design'

    default_text = [
        '{:^70s}'.format(default_name),
        '{:^70s}'.format('-'*len(default_name)),
        '{:^70s}'.format('Purdue University')
    ]
    default_text = '\n'.join(default_text)

    if not text:
        text = default_text

    print()
    print('='*70)
    print()
    print(text)
    print()
    print('='*70)
    print()

    return




def plot(
    fn_main='', variant='none',
    sg_tool='vabs', tool_version='4', model='BM1', sgdir='.',
    **kwargs
    ) -> None:
    """
    """

    msgd = MSGD()

    readMSGDInput(
        fn_main, mode='1', msgd=msgd, variant=variant,
        # fn_run=fn_run
    )

    mutils.plotSGenome(
        structure_name=msgd.global_structure.name,
        sg_tool=sg_tool,
        tool_version=tool_version,
        model=model,
        sgdir=sgdir
    )

    return



def createMeshMap(
    fn_mesh_phys='', fn_mesh_param='',
    phys_mesh_fmt='', param_mesh_fmt='',
    log_level_cmd='info', log_level_file='info', log_file_name='eval.log',
    fn_map=''
    ):

    # logger = mutils.initLogger(
    #     GLOBAL.LOGGER_NAME,
    #     cout_level=log_level_cmd,
    #     fout_level=log_level_file,
    #     filename=log_file_name
    # )

    mesh_physc = sgio.meshio.read(
        filename=fn_mesh_phys,
        file_format=phys_mesh_fmt
        )

    mesh_param = sgio.meshio.read(
        filename=fn_mesh_param,
        file_format=param_mesh_fmt
        )

    three_node_map = []
    with open(fn_map, 'r') as file:
        lines = file.readlines()
        for line in lines:
            line = line.strip()
            if line:
                three_node_map.append(list(map(int, line.split(','))))

    node_map = createNodeMapFromThree(
        mesh_physc=mesh_physc,
        mesh_param=mesh_param,
        three_node_map=three_node_map,
        fn_node_id_map=fn_map
    )

    return




def main(
    fn_main='', mode='0', fn_dakota_params='', fn_dakota_results='',
    variant='none', executable='msgd',
    log_level_cmd='info', log_level_file='info', log_file_name='log.txt',
    **kwargs):

    configure_logging(
        cout_level=log_level_cmd,
        fout_level=log_level_file,
        filename=log_file_name
    )

    sgio.configure_logging(
        cout_level=log_level_cmd,
        fout_level=log_level_file,
        filename=log_file_name
    )

    # print(f'logger name: {logger.name}')
    # print(f"Logger has handlers: {bool(logger.handlers)}")
    # print(f"Logger level: {logger.level}")

    # for handler in logger.handlers:
    #     print(handler)
    #     if isinstance(handler, logging.StreamHandler):
    #         print(f"Console log level: {handler.level}")
    #     elif isinstance(handler, logging.FileHandler):
    #         print(f"File log level: {handler.level}")

    # logger.info('main')

    GLOBAL.addFuncNameBegin('main')

    # Initialize the logger
    # ---------------------
    # sgio.utils.initLogger(
    #     __name__,
    #     cout_level=log_level_cmd,
    #     fout_level=log_level_file,
    #     filename=log_file_name
    # )

    # logger = mutils.initLogger(
    #     __name__,
    #     # GLOBAL.LOGGER_NAME,
    #     cout_level=log_level_cmd,
    #     fout_level=log_level_file,
    #     filename=log_file_name
    # )

    # logger.setLevel(log_level_cmd.upper())


    logger.debug(str(locals()))

    GLOBAL.VARIANT = variant
    GLOBAL.EXECUTABLE = executable

    if mode != '1':
        if variant == 'ivabs':
            printTitle(GLOBAL.IVABS_TITLE)
        else:
            printTitle()
        # try:
        #     printTitle(text=kwargs['title_text'])
        # except KeyError:

    msgd = MSGD()

    msgd.log_level_cmd = log_level_cmd
    msgd.log_level_file = log_level_file
    msgd.log_file_name = log_file_name

    # fn_run = kwargs.get('fn_run', 'run.py')

    # logger.info('\n\n'+'*'*70)
    # console.rule("")

    readMSGDInput(
        fn_main, mode, msgd=msgd, variant=variant,
        # fn_run=fn_run
    )

    if mode == '1':
        msgd.fn_dakota_params = fn_dakota_params
        msgd.fn_dakota_results = fn_dakota_results


    if mode == '1':
        # Run a single analysis

        try:
            msgd.readMDAOEvalIn()

            msgd.evaluate()

            # msgd.updateData()

            # msgd.writeInput('curr_design.yml')

            # msgd.analyze()

            msgd.writeAnalysisOut(0)

        except:
            e = tb.format_exc()
            # logger.critical(e, exc_info=1)
            logger.critical(str(e))
            msgd.writeAnalysisOut(1)

    else:

        msgd.study.writeMDAOInput()
        if mode == '0':
            msgd.runMDAO()

    # logger.critical('FINISH')
    # logger.critical('')

    GLOBAL.addFuncNameEnd('main')

    with open(GLOBAL.DEBUG_FUNC_CALL_FILENAME, 'w') as file:
        for _n in GLOBAL.DEBUG_FUNC_CALL_PROCESS:
            file.write(f'{_n}\n')

    return msgd.data









if __name__ == "__main__":
    cli(*sys.argv)

