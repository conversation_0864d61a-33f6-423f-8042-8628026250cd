import numpy as np
import pytest
from msgd.design.distribution import Distribution
from msgd.design.transformation import Domain
import msgd.utils.function as mutils

class TestDistribution:
    @pytest.fixture
    def simple_function(self):
        """Create a simple linear function for testing"""
        class LinearFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "linear"
                self.vtype = "float"
            
            def __call__(self, x):
                return 2 * x + 1
        
        return LinearFunction()

    @pytest.fixture
    def distribution(self, simple_function):
        """Create a basic distribution for testing"""
        dist = Distribution(
            name="test_dist",
            region="test_region",
            func_base=simple_function,
            vtype="float"
        )
        dist._function = [simple_function]
        return dist

    def test_single_point_evaluation(self, distribution):
        """Test evaluation at a single point"""
        x = np.array([1.0])
        result = distribution(x)
        
        assert isinstance(result, dict)
        assert "test_dist" in result
        assert np.allclose(result["test_dist"], 3.0)  # 2*1 + 1 = 3

    def test_multiple_points_evaluation(self, distribution):
        """Test evaluation at multiple points"""
        x = np.array([1.0, 2.0, 3.0])
        result = distribution(x)
        
        assert isinstance(result, dict)
        assert "test_dist" in result
        expected = np.array([3.0, 5.0, 7.0])  # 2*x + 1
        assert np.allclose(result["test_dist"], expected)

    def test_2d_input_evaluation(self, distribution):
        """Test evaluation with 2D input"""
        x = np.array([[1.0], [2.0], [3.0]])
        result = distribution(x)
        
        assert isinstance(result, dict)
        assert "test_dist" in result
        expected = np.array([3.0, 5.0, 7.0])
        assert np.allclose(result["test_dist"], expected)

    def test_array_output(self, distribution):
        """Test array output format"""
        x = np.array([1.0, 2.0, 3.0])
        result = distribution(x, return_dict=False)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == (3, 1)  # 3 points, 1 function
        expected = np.array([[3.0], [5.0], [7.0]])
        assert np.allclose(result, expected)

    def test_multiple_functions(self):
        """Test distribution with multiple functions"""
        class SquareFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "square"
                self.vtype = "float"
            
            def __call__(self, x):
                return x**2

        class CubeFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "cube"
                self.vtype = "float"
            
            def __call__(self, x):
                return x**3

        dist = Distribution(
            name=["square", "cube"],
            region="test_region",
            func_base=None,
            vtype="float"
        )
        dist._function = [SquareFunction(), CubeFunction()]

        x = np.array([1.0, 2.0, 3.0])
        result = dist(x)
        
        assert isinstance(result, dict)
        assert "square" in result
        assert "cube" in result
        assert np.allclose(result["square"], np.array([1.0, 4.0, 9.0]))
        assert np.allclose(result["cube"], np.array([1.0, 8.0, 27.0]))

    def test_domain_transformation(self):
        """Test distribution with domain transformation"""
        class XYDomain(Domain):
            def transform(self, x, _id=0, **kwargs):
                return x[:, :2]  # Only keep first two coordinates

        dist = Distribution(
            name="test_dist",
            region="test_region",
            domain=XYDomain(),
            func_base=None,
            vtype="float"
        )
        
        class SumFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "sum"
                self.vtype = "float"
            
            def __call__(self, x):
                return np.sum(x, axis=1)

        dist._function = [SumFunction()]

        # Test with 3D coordinates
        x = np.array([
            [1.0, 2.0, 3.0],
            [4.0, 5.0, 6.0],
            [7.0, 8.0, 9.0]
        ])
        result = dist(x)
        
        assert isinstance(result, dict)
        assert "test_dist" in result
        expected = np.array([3.0, 9.0, 15.0])  # Sum of first two coordinates
        assert np.allclose(result["test_dist"], expected)

    def test_invalid_input(self, distribution):
        """Test handling of invalid input"""
        # Test 3D input
        x = np.array([[[1.0]]])
        with pytest.raises(ValueError):
            distribution(x)

        # Test non-numeric input
        x = np.array(["invalid"])
        with pytest.raises(ValueError):
            distribution(x)

    def test_empty_input(self, distribution):
        """Test handling of empty input"""
        x = np.array([])
        result = distribution(x)
        
        assert isinstance(result, dict)
        assert "test_dist" in result
        assert len(result["test_dist"]) == 0

    def test_scalar_function_output(self):
        """Test distribution with function that returns scalar"""
        class ScalarFunction(mutils.MSGDFunction):
            def __init__(self):
                super().__init__()
                self.name = "scalar"
                self.vtype = "float"
            
            def __call__(self, x):
                return 42.0  # Always returns scalar

        dist = Distribution(
            name="test_dist",
            region="test_region",
            func_base=None,
            vtype="float"
        )
        dist._function = [ScalarFunction()]

        x = np.array([1.0, 2.0, 3.0])
        result = dist(x)
        
        assert isinstance(result, dict)
        assert "test_dist" in result
        expected = np.array([42.0, 42.0, 42.0])
        assert np.allclose(result["test_dist"], expected) 