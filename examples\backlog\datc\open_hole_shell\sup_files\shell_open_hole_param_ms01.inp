*Heading
** Job name: shell_open_hole_param_ms01 Model name: param_ms01
** Generated by: Abaqus/CAE 2023
*Preprint, echo=NO, model=NO, history=NO, contact=NO
**
** PARTS
**
*Part, name=Part-1
*Node
      1,           0.,          -1.
      2,           0.,         -0.5
      3,         -0.5,           0.
      4,          -1.,           0.
      5,          -1.,          -1.
      6,           0.,          0.5
      7,           0.,           1.
      8,          -1.,           1.
      9,           1.,          -1.
     10,           1.,           0.
     11,          0.5,           0.
     12,           1.,           1.
     13, -0.800000012,          -1.
     14, -0.600000024,          -1.
     15, -0.400000006,          -1.
     16, -0.200000003,          -1.
     17,           0.,       -0.875
     18,           0.,        -0.75
     19,           0.,       -0.625
     20, -0.191341713, -0.461939752
     21, -0.353553385, -0.353553385
     22, -0.461939752, -0.191341713
     23,       -0.625,           0.
     24,        -0.75,           0.
     25,       -0.875,           0.
     26,          -1., -0.200000003
     27,          -1., -0.400000006
     28,          -1., -0.600000024
     29,          -1., -0.800000012
     30, -0.461939752,  0.191341713
     31, -0.353553385,  0.353553385
     32, -0.191341713,  0.461939752
     33,           0.,        0.625
     34,           0.,         0.75
     35,           0.,        0.875
     36, -0.200000003,           1.
     37, -0.400000006,           1.
     38, -0.600000024,           1.
     39, -0.800000012,           1.
     40,          -1.,  0.800000012
     41,          -1.,  0.600000024
     42,          -1.,  0.400000006
     43,          -1.,  0.200000003
     44,  0.461939752, -0.191341713
     45,  0.353553385, -0.353553385
     46,  0.191341713, -0.461939752
     47,  0.200000003,          -1.
     48,  0.400000006,          -1.
     49,  0.600000024,          -1.
     50,  0.800000012,          -1.
     51,           1., -0.800000012
     52,           1., -0.600000024
     53,           1., -0.400000006
     54,           1., -0.200000003
     55,        0.875,           0.
     56,         0.75,           0.
     57,        0.625,           0.
     58,  0.800000012,           1.
     59,  0.600000024,           1.
     60,  0.400000006,           1.
     61,  0.200000003,           1.
     62,  0.191341713,  0.461939752
     63,  0.353553385,  0.353553385
     64,  0.461939752,  0.191341713
     65,           1.,  0.200000003
     66,           1.,  0.400000006
     67,           1.,  0.600000024
     68,           1.,  0.800000012
     69, -0.451799482, -0.450930625
     70, -0.220460758, -0.566009641
     71, -0.424398154, -0.650571227
     72, -0.410425067, -0.828893661
     73, -0.566607118, -0.220374838
     74,  -0.82714808, -0.409399241
     75, -0.651438117, -0.423657119
     76,  -0.20570612, -0.851781845
     77, -0.212641269, -0.704590678
     78, -0.704548001, -0.212489814
     79, -0.851673961, -0.205472261
     80, -0.813640833, -0.608701646
     81, -0.805790484, -0.805434346
     82, -0.624749601,  -0.62399137
     83, -0.610241294, -0.814579844
     84, -0.451391101,  0.452101618
     85, -0.220476478,  0.566580236
     86, -0.409399241,   0.82714808
     87, -0.423915207,  0.651489258
     88, -0.827612996,  0.410290539
     89,  -0.65124613,  0.424482405
     90,  -0.56676048,  0.221196711
     91, -0.212579742,  0.704554081
     92, -0.205494747,   0.85167551
     93, -0.608844578,  0.813616455
     94,  -0.80517894,  0.805786192
     95, -0.623969316,  0.624779105
     96, -0.814190269,  0.610213935
     97,  -0.85165906,   0.20580034
     98, -0.704916418,  0.212869868
     99,  0.451391101, -0.452101618
    100,  0.220476478, -0.566580236
    101,  0.409399241,  -0.82714808
    102,  0.423915207, -0.651489258
    103,  0.827612996, -0.410290539
    104,   0.65124613, -0.424482405
    105,   0.56676048, -0.221196711
    106,  0.212579742, -0.704554081
    107,  0.205494747,  -0.85167551
    108,  0.608844578, -0.813616455
    109,   0.80517894, -0.805786192
    110,  0.623969316, -0.624779105
    111,  0.814190269, -0.610213935
    112,   0.85165906,  -0.20580034
    113,  0.704916418, -0.212869868
    114,  0.451799482,  0.450930625
    115,  0.220460758,  0.566009641
    116,  0.424398154,  0.650571227
    117,  0.410425067,  0.828893661
    118,  0.566607118,  0.220374838
    119,   0.82714808,  0.409399241
    120,  0.651438117,  0.423657119
    121,   0.20570612,  0.851781845
    122,  0.212641269,  0.704590678
    123,  0.704548001,  0.212489814
    124,  0.851673961,  0.205472261
    125,  0.813640833,  0.608701646
    126,  0.805790484,  0.805434346
    127,  0.624749601,   0.62399137
    128,  0.610241294,  0.814579844
*Element, type=CPS4R
 1, 15, 16, 76, 72
 2, 16,  1, 17, 76
 3, 72, 76, 77, 71
 4, 76, 17, 18, 77
 5, 71, 77, 70, 69
 6, 77, 18, 19, 70
 7, 19,  2, 20, 70
 8, 70, 20, 21, 69
 9, 21, 22, 73, 69
10, 22,  3, 23, 73
11, 23, 24, 78, 73
12, 24, 25, 79, 78
13, 25,  4, 26, 79
14, 73, 78, 75, 69
15, 78, 79, 74, 75
16, 79, 26, 27, 74
17, 27, 28, 80, 74
18, 28, 29, 81, 80
19, 29,  5, 13, 81
20, 74, 80, 82, 75
21, 80, 81, 83, 82
22, 81, 13, 14, 83
23, 75, 82, 71, 69
24, 82, 83, 72, 71
25, 83, 14, 15, 72
26, 31, 32, 85, 84
27, 32,  6, 33, 85
28, 33, 34, 91, 85
29, 34, 35, 92, 91
30, 35,  7, 36, 92
31, 85, 91, 87, 84
32, 91, 92, 86, 87
33, 92, 36, 37, 86
34, 37, 38, 93, 86
35, 38, 39, 94, 93
36, 39,  8, 40, 94
37, 86, 93, 95, 87
38, 93, 94, 96, 95
39, 94, 40, 41, 96
40, 87, 95, 89, 84
41, 95, 96, 88, 89
42, 96, 41, 42, 88
43, 42, 43, 97, 88
44, 43,  4, 25, 97
45, 88, 97, 98, 89
46, 97, 25, 24, 98
47, 89, 98, 90, 84
48, 98, 24, 23, 90
49, 23,  3, 30, 90
50, 90, 30, 31, 84
51,  45,  46, 100,  99
52,  46,   2,  19, 100
53,  19,  18, 106, 100
54,  18,  17, 107, 106
55,  17,   1,  47, 107
56, 100, 106, 102,  99
57, 106, 107, 101, 102
58, 107,  47,  48, 101
59,  48,  49, 108, 101
60,  49,  50, 109, 108
61,  50,   9,  51, 109
62, 101, 108, 110, 102
63, 108, 109, 111, 110
64, 109,  51,  52, 111
65, 102, 110, 104,  99
66, 110, 111, 103, 104
67, 111,  52,  53, 103
68,  53,  54, 112, 103
69,  54,  10,  55, 112
70, 103, 112, 113, 104
71, 112,  55,  56, 113
72, 104, 113, 105,  99
73, 113,  56,  57, 105
74,  57,  11,  44, 105
75, 105,  44,  45,  99
 76,  60,  61, 121, 117
 77,  61,   7,  35, 121
 78, 117, 121, 122, 116
 79, 121,  35,  34, 122
 80, 116, 122, 115, 114
 81, 122,  34,  33, 115
 82,  33,   6,  62, 115
 83, 115,  62,  63, 114
 84,  63,  64, 118, 114
 85,  64,  11,  57, 118
 86,  57,  56, 123, 118
 87,  56,  55, 124, 123
 88,  55,  10,  65, 124
 89, 118, 123, 120, 114
 90, 123, 124, 119, 120
 91, 124,  65,  66, 119
 92,  66,  67, 125, 119
 93,  67,  68, 126, 125
 94,  68,  12,  58, 126
 95, 119, 125, 127, 120
 96, 125, 126, 128, 127
 97, 126,  58,  59, 128
 98, 120, 127, 116, 114
 99, 127, 128, 117, 116
100, 128,  59,  60, 117
*End Part
**  
**
** ASSEMBLY
**
*Assembly, name=Assembly
**  
*Instance, name=Part-1-1, part=Part-1
*End Instance
**  
*End Assembly
