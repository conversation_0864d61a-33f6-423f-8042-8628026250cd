import logging

from msgd.core import *

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)


class ScriptAnalysisStep(AnalysisStep):
    def __init__(
        self, name='',
        mod_name='', func_name='', args=[], kwargs={},
        activate=True, parameters={}, setting={}, output_config=[]
        ):
        super().__init__(
            name=name,
            activate=activate,
            parameters=parameters,
            setting=setting,
            output_config=output_config
            )

        self.mod_name = mod_name
        self.func_name = func_name
        self.function = None
        self.args = args
        self.kwargs = kwargs


    # def implement(self, **kwargs):
    #     try:
    #         import_str = f'import {self.mod_name} as user_mod'
    #         logger.info(import_str)
    #         exec(import_str)
    #         func_str = f'user_mod.{self.func_name}'
    #         # logger.info(f'evaluating user function: {func_str}')
    #         func_obj = eval(func_str)

    #     except ImportError:
    #         try:
    #             import_str = f'from {self.mod_name} import {self.func_name}'
    #             logger.info(import_str)
    #             exec(import_str)
    #             # logger.info(f'evaluating user function: {self.func_name}')
    #             func_obj = eval(func_str)

    #         except ImportError:
    #             logger.error(f'something wrong when importing module: {self.mod_name}')

    #     self.function = func_obj

    #     return


    def run(
        self, structure_model:StructureModel,
        db_sg_model:DataBase=None, db_sg_design:DataBase=None,
        msgd_data={},
        **kwargs
        ):

        logger.info(f'running custom script: {self.func_name}...')

        # self.implement()

        from msgd.utils import importFunction
        self.function = importFunction(self.mod_name, self.func_name)

        output = self.function(
            msgd_data,
            structure=structure_model,
            sgs=db_sg_model,
            **self.kwargs
            )
        
        self.step_output.update(output)

        # logger.info(f'msgd_data = {msgd_data}')

        return output

