name: blade
parameter: {}
model:
  type: ''
  tool: rcas
  tool_version: ''
  main_file: rcas_input.dat
  prop_file: ''
  config:
    node_id_start: 2
design:
  name: blade
  parameter:
    radius: 8.178
    ang_front_r0: 0
    ang_front_r1: 90
    ang_back_r0: 0
    ang_back_r1: 90
    r_ply: 0.5
    ply_spar_l1_root: 8
    ply_spar_l1_r1: 6
    ply_spar_l1_tip: 2
    ply_front_root: 4
    ply_front_r1: 3
    ply_front_tip: 1
    ply_back_root: 4
    ply_back_r1: 3
    ply_back_tip: 1
  dim: 1
  builder: default
  design: null
  distribution:
  - name: airfoil
    type: str
    data_form: file
  - name: chord
    type: float
    data_form: file
  - name:
    - ply_spar_1
    - ply_front
    - ply_back
    type: int
    xscale: 8.178
    data_form: compact
    data: '0.1,8,4,4

      0.5,6,3,3

      1.0,2,1,1

      '
  - name:
    - ang_front
    - ang_back
    type: int
    xscale: 8.178
    data_form: compact
    data: '0,0,0

      1,90,90

      '
cs_assignment:
- region: all
  location: node
  cs: main_cs
physics: elastic
