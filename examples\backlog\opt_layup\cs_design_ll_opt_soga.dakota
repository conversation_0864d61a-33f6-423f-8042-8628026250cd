# cs_design_ll_opt_soga.dakota

environment
  output_file = 'cs_design_ll_opt_soga.out'
  write_restart = 'cs_design_ll_opt_soga.rst'
  error_file = 'cs_design_ll_opt_soga.err'
  tabular_data
    tabular_data_file = 'cs_design_ll_opt_soga_tabular.dat'
  results_output
    results_output_file = 'cs_design_ll_opt_soga_results'


method
  output  normal
  soga
    max_function_evaluations =  10
    population_size =  5
    seed =  1027
    print_each_pop


model
  single

variables
  active = design

  continuous_design = 4
    descriptors = 'ang_spar_1'  'ang_spar_2'  'ang_spar_3'  'ang_spar_4'
    upper_bounds = 90  90  90  90
    lower_bounds = -90  -90  -90  -90


  discrete_design_range = 4
    descriptors = 'ply_spar_1'  'ply_spar_2'  'ply_spar_3'  'ply_spar_4'
    upper_bounds = 20  20  20  20
    lower_bounds = 1  1  1  1




interface
  analysis_driver = 'msgd ./cs_design_ll_opt_soga.yml --mode 1 --paramfile {PARAMETERS} --resultfile {RESULTS} --loglevelcmd info --loglevelfile info --logfile eval.log'
    fork
      parameters_file =  'input.in'
      results_file =  'output.out'
      file_save
      work_directory
        directory_tag
        directory_save
        named =  'evals/eval'
        copy_file =  './cs_design_ll_opt_soga.yml'  'design/*'  'scripts/*'
      verbatim


responses
  descriptors =  'diff_gj'  'diff_eiyy'  'diff_eizz'
  objective_functions = 3
    sense =  'min'  'min'  'min'
    weights = 0.5 0.8 0.8
  no_gradients
  no_hessians


