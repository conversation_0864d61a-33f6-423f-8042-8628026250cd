analysis:
- parent: "root"
  description: "Specification of the analysis process."
  specifications:
    arguments: "None"
    required: "Yes"
    default: "None"
    snippet: |
      analysis:
        steps:
          - step: "step 1"
            ...
          - step: "step 2"
            ...

- parent: "steps"
  description: "Specification of the |structure genetic| analysis type."
  specifications:
    arguments: "Choose one from h,fi"
    options:
      - option: "h"
        description: "Homogenization."
      - option: "fi"
        description: "Initial failure index and strength ratio analysis."
    required: "Yes"
    default: "None"
    snippet: |
      analysis:
        steps:
          - step: "..."
            type: "cs"
            analysis: "..."

# ====================================================================
args:
- parent: "steps"
  description: "Positional arguments for the function."

# ====================================================================
base_file:
- parent: "design"
  description: "File name of the |structure genetic| base design, such as a PreVABS template."
  specifications:
    arguments: "String"
    required: "Yes"
    default: "None"
    snippet: |
      cs:
        - name: "my_cs"
          design:
            base_file: "cs_base_design"
            ...
          ...

# ====================================================================
builder:
- parent: "|sg|"
  description: "Method or tool to create the |structure gene|."
  specifications:
    arguments: "String"
    required: "Yes"
    default: "None"
    snippet: |
      cs:
        - name: "my_cs"
          builder: "prevabs"
          ...

# ====================================================================
data_form:
- parent: "structure > distribution"
  description: "Input form of the interpolated data."
  specifications:
    arguments: "Choose one from ``explicit``, ``compact``."
    required: "No"
    default: "explicit"
    snippet: |
      structure:
        distribution:
          - name: "my_param"
            function: "my_func"
            data_form: "explicit"
            data:
              ...
            ...

# ====================================================================
data:
- parent: "structure > distribution"
  description: ""
  specifications:
  - condition: ":ref:`kw-data_form` is ``explicit``."
    arguments: "List"
    required: "Yes"
    default: "None"

# ====================================================================
design:
- parent: "|sg|"
  description: "Basic design setup of the structure."
  specifications:
    arguments: "None"
    required: "Yes"
    default: "None"
    snippet: |
      cs:
        - name: "my_cs"
          design:
            ...
          ...

- parent: "structure > |sg|"
  description: "Specification of the name of the |sg| design."
  specifications:
    arguments: "String"
    required: "Yes"
    default: "None"
    snippet: |
      structure:
        cs:
          - name: "my_cs_model"
            design: "my_cs"
            ...

# ====================================================================
name:
- parent: 'structure'
  description: "Name of the structure. Used only in logging messages."
  specifications:
    arguments: "String"
    required: "No"
    default: "structure_0"
    snippet: |
      structure:
        name: "my_structure"

# ====================================================================
structure:
- parent: "root"
  description: "Specification of the global structure."
  specifications:
    arguments: "None"
    required: "Yes"
    default: "None"
    snippet: |
      structure:
        name:
        parameter:
          ...
        distribution:
          ...
        design:
          ...
        model:
          ...

# ====================================================================
# version:
