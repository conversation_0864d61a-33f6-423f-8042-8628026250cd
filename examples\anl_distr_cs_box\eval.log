DEBUG    [2024-02-21 22:12:28] msgd.main :: {'fn_main': '.\\main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} 
INFO     [2024-02-21 22:12:28] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-21 22:12:28] io.readMSGDInput :: reading main input .\main.yml... 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: local variables:
{'fn': '.\\main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x000001B6BBC2F950>,
 'variant': 'ivabs'} 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: currect working directory: . 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: input file name: main 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: input file extension: .yml 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: output file name: .\main.out 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: variant: ivabs 
DEBUG    [2024-02-21 22:12:28] io.readMSGDInput :: version: 0.10 
INFO     [2024-02-21 22:12:28] _msgd.readMDAOEvalIn :: reading mdao input... 
DEBUG    [2024-02-21 22:12:28] _msgd.readMDAOEvalIn :: mdao_tool: dakota 
DEBUG    [2024-02-21 22:12:28] _msgd.readMDAOEvalIn :: fn_dakota_params:  
DEBUG    [2024-02-21 22:12:28] _msgd.readMDAOEvalIn :: fn_dakota_results:  
INFO     [2024-02-21 22:12:28] _msgd.updateData :: updating current design... 
INFO     [2024-02-21 22:12:28] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-02-21 22:12:28] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-02-21 22:12:28] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-02-21 22:12:28] distribution.implementFunction :: implementing parameter distribution... 
DEBUG    [2024-02-21 22:12:28] _msgd.writeInput :: writing input file: curr_design.yml... 
CRITICAL [2024-02-21 22:12:28] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-02-21 22:12:28] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-02-21 22:12:28] _msgd.discretize :: [main] discretizing the structure... 
DEBUG    [2024-02-21 22:12:28] _msgd.discretize :: structure model:
{'cs_assignment': [{'cs': 'main_cs', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': None,
            'dim': 2,
            'distribution': [{'coefficient': {},
                              'data': [{'coordinate': 0, 'value': 0},
                                       {'coordinate': 10, 'value': 90}],
                              'function': 'f_interp',
                              'name': 'lyr_ang'}],
            'name': 'beam',
            'parameter': {'lyr_ang_sta1': 0, 'lyr_ang_sta2': 90, 'lyr_ply': 6}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'beam',
 'parameter': {},
 'physics': 'elastic'} 
INFO     [2024-02-21 22:12:28] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-02-21 22:12:28] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
DEBUG    [2024-02-21 22:12:28] distribution.calcParamsFromDistr :: local variables:
{'abs_tol': 1e-12,
 'distributions': {'lyr_ang': <msgd.design.distribution.Distribution object at 0x000001B6BB912650>},
 'model_elems': {},
 'model_nodes': {1: 0, 2: 2, 3: 5, 4: 7, 5: 10},
 'rel_tol': 1e-09,
 'sg_assignment': [sg assignment of main_cs to region all],
 'sg_key': 'cs'} 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 0... 
DEBUG    [2024-02-21 22:12:28] function.__call__ :: x = 0 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: result = 0.0 (float) 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 2... 
DEBUG    [2024-02-21 22:12:28] function.__call__ :: x = 2 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: result = 18.0 (float) 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 5... 
DEBUG    [2024-02-21 22:12:28] function.__call__ :: x = 5 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: result = 45.0 (float) 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 7... 
DEBUG    [2024-02-21 22:12:28] function.__call__ :: x = 7 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: result = 63.0 (float) 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 10... 
DEBUG    [2024-02-21 22:12:28] function.__call__ :: x = 10 
DEBUG    [2024-02-21 22:12:28] distribution.__call__ :: result = 90.0 (float) 
INFO     [2024-02-21 22:12:28] _msgd.analyze :: [main] going through steps... 
DEBUG    [2024-02-21 22:12:28] _msgd.analyze :: step config:
{'activate': True,
 'analysis': 'h',
 'output': {'value': ['ea', 'gayy', 'gazz', 'gj', 'eiyy', 'eizz']},
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': '.'} 
INFO     [2024-02-21 22:12:28] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-02-21 22:12:28] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-02-21 22:12:28] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-21 22:12:28] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-02-21 22:12:28] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-21 22:12:28] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-02-21 22:12:28] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 933, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 142, in run
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 261, in run
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 239, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 177, in buildSGModel
    fn_sg = buildSGFromPrevabs(
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 463, in buildSGFromPrevabs
    mutils.run(cmd, timeout)
    ^^^^^^^^^^
AttributeError: module 'msgd.utils.io' has no attribute 'run'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 933, in analyze
    step.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 142, in run
    _sg_analysis.run(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 261, in run
    self.build(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\analysis\sg.py", line 239, in build
    mbp.buildSGModel(
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 177, in buildSGModel
    fn_sg = buildSGFromPrevabs(
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\builder\main.py", line 463, in buildSGFromPrevabs
    mutils.run(cmd, timeout)
    ^^^^^^^^^^
AttributeError: module 'msgd.utils.io' has no attribute 'run'
INFO     [2024-02-21 22:12:28] _msgd.writeMDAOEvalOut :: [main] writing output files... 
DEBUG    [2024-02-21 22:12:58] msgd.main :: {'fn_main': '.\\main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} 
INFO     [2024-02-21 22:12:58] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-21 22:12:58] io.readMSGDInput :: reading main input .\main.yml... 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: local variables:
{'fn': '.\\main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x000001BD46414BD0>,
 'variant': 'ivabs'} 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: currect working directory: . 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: input file name: main 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: input file extension: .yml 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: output file name: .\main.out 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: variant: ivabs 
DEBUG    [2024-02-21 22:12:58] io.readMSGDInput :: version: 0.10 
INFO     [2024-02-21 22:12:58] _msgd.readMDAOEvalIn :: reading mdao input... 
DEBUG    [2024-02-21 22:12:58] _msgd.readMDAOEvalIn :: mdao_tool: dakota 
DEBUG    [2024-02-21 22:12:58] _msgd.readMDAOEvalIn :: fn_dakota_params:  
DEBUG    [2024-02-21 22:12:58] _msgd.readMDAOEvalIn :: fn_dakota_results:  
INFO     [2024-02-21 22:12:58] _msgd.updateData :: updating current design... 
INFO     [2024-02-21 22:12:58] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-02-21 22:12:58] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-02-21 22:12:58] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-02-21 22:12:58] distribution.implementFunction :: implementing parameter distribution... 
DEBUG    [2024-02-21 22:12:58] _msgd.writeInput :: writing input file: curr_design.yml... 
CRITICAL [2024-02-21 22:12:58] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-02-21 22:12:58] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-02-21 22:12:58] _msgd.discretize :: [main] discretizing the structure... 
DEBUG    [2024-02-21 22:12:58] _msgd.discretize :: structure model:
{'cs_assignment': [{'cs': 'main_cs', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': None,
            'dim': 2,
            'distribution': [{'coefficient': {},
                              'data': [{'coordinate': 0, 'value': 0},
                                       {'coordinate': 10, 'value': 90}],
                              'function': 'f_interp',
                              'name': 'lyr_ang'}],
            'name': 'beam',
            'parameter': {'lyr_ang_sta1': 0, 'lyr_ang_sta2': 90, 'lyr_ply': 6}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'beam',
 'parameter': {},
 'physics': 'elastic'} 
INFO     [2024-02-21 22:12:58] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-02-21 22:12:58] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
DEBUG    [2024-02-21 22:12:58] distribution.calcParamsFromDistr :: local variables:
{'abs_tol': 1e-12,
 'distributions': {'lyr_ang': <msgd.design.distribution.Distribution object at 0x000001BD45D7D710>},
 'model_elems': {},
 'model_nodes': {1: 0, 2: 2, 3: 5, 4: 7, 5: 10},
 'rel_tol': 1e-09,
 'sg_assignment': [sg assignment of main_cs to region all],
 'sg_key': 'cs'} 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 0... 
DEBUG    [2024-02-21 22:12:58] function.__call__ :: x = 0 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: result = 0.0 (float) 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 2... 
DEBUG    [2024-02-21 22:12:58] function.__call__ :: x = 2 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: result = 18.0 (float) 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 5... 
DEBUG    [2024-02-21 22:12:58] function.__call__ :: x = 5 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: result = 45.0 (float) 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 7... 
DEBUG    [2024-02-21 22:12:58] function.__call__ :: x = 7 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: result = 63.0 (float) 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: evaluating Distribution lyr_ang (float) at 10... 
DEBUG    [2024-02-21 22:12:58] function.__call__ :: x = 10 
DEBUG    [2024-02-21 22:12:58] distribution.__call__ :: result = 90.0 (float) 
INFO     [2024-02-21 22:12:58] _msgd.analyze :: [main] going through steps... 
DEBUG    [2024-02-21 22:12:58] _msgd.analyze :: step config:
{'activate': True,
 'analysis': 'h',
 'output': {'value': ['ea', 'gayy', 'gazz', 'gj', 'eiyy', 'eizz']},
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': '.'} 
INFO     [2024-02-21 22:12:58] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-02-21 22:12:58] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-02-21 22:12:58] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-21 22:12:58] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-02-21 22:12:58] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-21 22:12:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-02-21 22:12:58] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-02-21 22:13:00] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-02-21 22:13:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-21 22:13:00] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-02-21 22:13:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-21 22:13:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-02-21 22:13:00] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-02-21 22:13:00] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-02-21 22:13:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-21 22:13:00] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-02-21 22:13:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-21 22:13:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-02-21 22:13:00] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-02-21 22:13:01] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-02-21 22:13:01] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-21 22:13:01] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-02-21 22:13:01] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-21 22:13:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-02-21 22:13:01] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-02-21 22:13:01] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-02-21 22:13:01] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-21 22:13:01] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-02-21 22:13:01] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-21 22:13:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-02-21 22:13:01] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-02-21 22:13:02] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-02-26 15:12:31] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-26 15:12:31] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-02-26 15:12:31] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-02-26 15:12:31] _msgd.updateData :: updating current design... 
INFO     [2024-02-26 15:12:31] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-02-26 15:12:31] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-02-26 15:12:31] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-02-26 15:12:31] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-02-26 15:12:31] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-02-26 15:12:31] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-02-26 15:12:31] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-02-26 15:12:31] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-02-26 15:12:31] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
INFO     [2024-02-26 15:12:31] _msgd.analyze :: [main] going through steps... 
INFO     [2024-02-26 15:12:31] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-02-26 15:12:31] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-02-26 15:12:31] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:12:31] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-02-26 15:12:31] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:12:31] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-02-26 15:12:31] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:12:32] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-02-26 15:12:32] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:12:32] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-02-26 15:12:32] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:12:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-02-26 15:12:32] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:12:32] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-02-26 15:12:32] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:12:32] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-02-26 15:12:32] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:12:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-02-26 15:12:32] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:12:32] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-02-26 15:12:32] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:12:32] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-02-26 15:12:32] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:12:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-02-26 15:12:32] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:12:33] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-02-26 15:12:33] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:12:33] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-02-26 15:12:33] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:12:33] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-02-26 15:12:33] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:12:33] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-02-26 15:13:54] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-26 15:13:54] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-02-26 15:13:54] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-02-26 15:13:54] _msgd.updateData :: updating current design... 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-02-26 15:13:54] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-02-26 15:13:54] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-02-26 15:13:54] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-02-26 15:13:54] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-02-26 15:13:54] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-02-26 15:13:54] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-02-26 15:13:54] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-02-26 15:13:54] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
INFO     [2024-02-26 15:13:54] _msgd.analyze :: [main] going through steps... 
INFO     [2024-02-26 15:13:54] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:13:54] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-02-26 15:13:54] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:13:54] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-02-26 15:13:54] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:13:54] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-02-26 15:13:54] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:13:54] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-02-26 15:13:54] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-02-26 15:13:54] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:13:54] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-02-26 15:13:54] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:13:54] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-02-26 15:13:54] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:13:55] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-02-26 15:13:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:13:55] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-02-26 15:13:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:13:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-02-26 15:13:55] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:13:55] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-02-26 15:13:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:13:55] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-02-26 15:13:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:13:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-02-26 15:13:55] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:13:55] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-02-26 15:13:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:13:55] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-02-26 15:13:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:13:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-02-26 15:13:55] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:13:56] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-02-26 15:21:11] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-26 15:21:11] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-02-26 15:21:11] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-02-26 15:21:11] _msgd.updateData :: updating current design... 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-02-26 15:21:11] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-02-26 15:21:11] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-02-26 15:21:11] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-02-26 15:21:11] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-02-26 15:21:11] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-02-26 15:21:11] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-02-26 15:21:11] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-02-26 15:21:11] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-02-26 15:21:11] distribution.calcParamsFromDistr :: calculating parameters from distribution... 
INFO     [2024-02-26 15:21:11] _msgd.analyze :: [main] going through steps... 
INFO     [2024-02-26 15:21:11] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:21:11] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-02-26 15:21:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:21:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-02-26 15:21:11] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:21:11] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-02-26 15:21:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:21:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-02-26 15:21:11] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:21:11] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-02-26 15:21:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:21:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-02-26 15:21:11] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-02-26 15:21:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:21:11] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-02-26 15:21:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:21:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-02-26 15:21:11] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:21:12] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-02-26 15:21:12] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:21:12] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-02-26 15:21:12] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:21:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-02-26 15:21:12] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:21:12] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-02-26 15:21:12] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-02-26 15:21:12] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-02-26 15:21:12] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-02-26 15:21:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-02-26 15:21:12] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-02-26 15:21:12] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 08:51:13] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 08:51:13] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 08:51:13] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 08:51:13] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 08:51:13] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 08:51:13] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 08:51:13] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 08:51:13] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 08:51:13] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 08:51:13] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 08:51:13] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 08:51:13] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 08:51:13] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 08:51:13] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 08:51:13] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 08:51:13] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 08:51:13] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 10:56:26] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 10:56:26] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 10:56:26] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 10:56:26] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 10:56:26] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 10:56:26] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 10:56:26] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 10:56:26] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 10:56:26] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 10:56:26] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 10:56:26] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 10:56:26] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 10:56:26] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 10:56:26] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 10:56:26] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 832, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 734, in calcParamsFromDistributions
    _elements = self.getMeshElements()
                ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 533, in getMeshElements
    _eid_data = self.mesh.cell_data['element_id']
                ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'element_id'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 832, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 734, in calcParamsFromDistributions
    _elements = self.getMeshElements()
                ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 533, in getMeshElements
    _eid_data = self.mesh.cell_data['element_id']
                ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'element_id'
INFO     [2024-03-05 10:56:26] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 11:01:36] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 11:01:36] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 11:01:36] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 11:01:36] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 11:01:36] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 11:01:36] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 11:01:36] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 11:01:36] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 11:01:36] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 11:01:36] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 11:01:36] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 11:01:36] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 11:01:36] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 11:01:36] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 11:01:36] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 836, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 760, in calcParamsFromDistributions
    if _sg_assign.location.startswith('element'):
       ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'location'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 836, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 760, in calcParamsFromDistributions
    if _sg_assign.location.startswith('element'):
       ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'location'
INFO     [2024-03-05 11:01:36] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 11:04:02] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 11:04:02] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 11:04:02] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 11:04:02] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 11:04:02] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 11:04:02] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 11:04:02] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 11:04:02] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 11:04:02] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 11:04:02] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 11:04:02] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 11:04:02] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 11:04:02] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 11:04:02] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 11:04:02] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 836, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 760, in calcParamsFromDistributions
    if _sg_assign.location.startswith('element'):
       ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'location'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 836, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 760, in calcParamsFromDistributions
    if _sg_assign.location.startswith('element'):
       ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'location'
INFO     [2024-03-05 11:04:02] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 11:10:23] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 11:10:23] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 11:10:23] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 11:10:23] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 11:10:23] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 11:10:23] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 11:10:23] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 11:10:23] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 11:10:23] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 11:10:23] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 11:10:23] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 11:10:23] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 11:10:23] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 11:10:23] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 11:10:23] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 842, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 781, in calcParamsFromDistributions
    _ncoords = _nodes[_nid]
               ~~~~~~^^^^^^
KeyError: 0
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 842, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 781, in calcParamsFromDistributions
    _ncoords = _nodes[_nid]
               ~~~~~~^^^^^^
KeyError: 0
INFO     [2024-03-05 11:10:23] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 11:11:34] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 11:11:34] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 11:11:34] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 11:11:34] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 11:11:34] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 11:11:34] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 11:11:34] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 11:11:34] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 11:11:34] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 11:11:34] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 11:11:34] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 11:11:34] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 11:11:34] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 11:11:34] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 11:11:34] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 844, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 783, in calcParamsFromDistributions
    _ncoords = _nodes[_nid]
               ~~~~~~^^^^^^
KeyError: 0
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 844, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 783, in calcParamsFromDistributions
    _ncoords = _nodes[_nid]
               ~~~~~~^^^^^^
KeyError: 0
INFO     [2024-03-05 11:11:34] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 11:12:33] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 11:12:33] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 11:12:33] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 11:12:33] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 11:12:33] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 11:12:33] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 11:12:33] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 11:12:33] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 11:12:33] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 11:12:33] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 11:12:33] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 11:12:33] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 11:12:33] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 11:12:33] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 11:12:33] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 845, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 784, in calcParamsFromDistributions
    _ncoords = _nodes[_nid]
               ~~~~~~^^^^^^
KeyError: 0
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 845, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 784, in calcParamsFromDistributions
    _ncoords = _nodes[_nid]
               ~~~~~~^^^^^^
KeyError: 0
INFO     [2024-03-05 11:12:33] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 11:16:45] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 11:16:45] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 11:16:45] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 11:16:45] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 11:16:45] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 11:16:45] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 11:16:45] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 11:16:45] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 11:16:45] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 11:16:45] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 11:16:45] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 11:16:45] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 11:16:45] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 11:16:45] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 11:16:45] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 845, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 787, in calcParamsFromDistributions
    _pvalue = _distr(_ncoords)
              ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\design\distribution.py", line 97, in __call__
    y = self._function(x)
        ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\function.py", line 322, in __call__
    y = y[0][0]
        ~~~~^^^
TypeError: 'float' object is not subscriptable
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 845, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 787, in calcParamsFromDistributions
    _pvalue = _distr(_ncoords)
              ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\design\distribution.py", line 97, in __call__
    y = self._function(x)
        ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\utils\function.py", line 322, in __call__
    y = y[0][0]
        ~~~~^^^
TypeError: 'float' object is not subscriptable
INFO     [2024-03-05 11:16:45] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 15:34:01] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 15:34:01] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 15:34:02] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 15:34:02] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 15:34:02] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 15:34:02] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 15:34:02] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 15:34:02] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 15:34:02] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 15:34:02] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 15:34:02] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
CRITICAL [2024-03-05 15:34:02] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 476, in analyze
    self._global_structure.loadStructureMesh()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 594, in loadStructureMesh
    _model = meshio.Mesh(
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\sgio\sgio\meshio\_mesh.py", line 138, in __init__
    self.points = np.asarray(points)
                  ^^^^^^^^^^^^^^^^^^
ValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (6, 3) + inhomogeneous part.
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 476, in analyze
    self._global_structure.loadStructureMesh()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 594, in loadStructureMesh
    _model = meshio.Mesh(
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\work\dev\sgio\sgio\meshio\_mesh.py", line 138, in __init__
    self.points = np.asarray(points)
                  ^^^^^^^^^^^^^^^^^^
ValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (6, 3) + inhomogeneous part.
INFO     [2024-03-05 15:34:02] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 15:35:11] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 15:35:11] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 15:35:11] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 15:35:11] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 15:35:11] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 15:35:11] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 15:35:11] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 15:35:11] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 15:35:11] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 15:35:11] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 15:35:11] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 15:35:11] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 15:35:11] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 15:35:11] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 15:35:11] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:35:11] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 15:35:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:35:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 15:35:11] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:35:11] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 15:35:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:35:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 15:35:11] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 15:35:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:35:11] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 15:35:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:35:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 15:35:11] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:35:12] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 15:35:12] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:35:12] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 15:35:12] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:35:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 15:35:12] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:35:13] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 15:35:13] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:35:13] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 15:35:13] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:35:13] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 15:35:13] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:35:14] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 15:39:34] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 15:39:34] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 15:39:34] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 15:39:34] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 15:39:34] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 15:39:34] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 15:39:34] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 15:39:34] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 15:39:34] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 15:39:34] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 15:39:34] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 15:39:34] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 15:39:34] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 15:39:34] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 15:39:34] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 15:39:34] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 15:39:34] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 15:39:34] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:39:34] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 15:39:34] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:39:34] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 15:39:34] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:39:34] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 15:39:34] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:39:34] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 15:39:34] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:39:34] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 15:39:34] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:39:35] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 15:39:35] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:39:35] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 15:39:35] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:39:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 15:39:35] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:39:35] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 15:39:35] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:39:35] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 15:39:35] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:39:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 15:39:35] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:39:35] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 15:39:35] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 15:39:35] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 15:39:35] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 15:39:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 15:39:35] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 15:39:36] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:09:40] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:09:40] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:09:40] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:09:40] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:09:40] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:09:40] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:09:40] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:09:40] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:09:40] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:09:40] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:09:40] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:09:40] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:09:40] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:09:40] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 16:09:40] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 855, in discretizeDesign
    _en_sg_params = self._discrete_sg_params[_eid]
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
KeyError: 6
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 855, in discretizeDesign
    _en_sg_params = self._discrete_sg_params[_eid]
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
KeyError: 6
INFO     [2024-03-05 16:09:40] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:11:14] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:11:14] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:11:14] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:11:14] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:11:14] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:11:14] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:11:14] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:11:14] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:11:14] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:11:14] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:11:14] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:11:14] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:11:14] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:11:14] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
CRITICAL [2024-03-05 16:11:14] msgd.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 856, in discretizeDesign
    _en_sg_params = self._discrete_sg_params[_eid]
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
KeyError: 6
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\msgd.py", line 183, in main
    msgd.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 482, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 459, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 856, in discretizeDesign
    _en_sg_params = self._discrete_sg_params[_eid]
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
KeyError: 6
INFO     [2024-03-05 16:11:14] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:20:00] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:20:00] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:20:00] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:20:00] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:20:00] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:20:00] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:20:00] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:20:00] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:20:00] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:20:00] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:20:00] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:20:00] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:20:00] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 16:20:00] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 16:20:00] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:20:00] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 16:20:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:20:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 16:20:00] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:20:00] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 16:20:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:20:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 16:20:00] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 16:20:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:20:00] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 16:20:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:20:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 16:20:00] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:20:01] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 16:20:01] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:20:01] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 16:20:01] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:20:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 16:20:01] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:20:01] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 16:20:01] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:20:01] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 16:20:01] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:20:01] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 16:20:01] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:20:02] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-05 16:20:02] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:20:02] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-05 16:20:02] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:20:02] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-03-05 16:20:02] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:20:02] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:24:10] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:24:10] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:24:10] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:24:10] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:24:10] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:24:10] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:24:10] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:24:10] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:24:10] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:24:10] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:24:10] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:24:10] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:24:10] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 16:24:10] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 16:24:10] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:24:10] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 16:24:10] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:24:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 16:24:10] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:24:10] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 16:24:10] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:24:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 16:24:10] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 16:24:10] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:24:10] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 16:24:10] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:24:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 16:24:10] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:24:11] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 16:24:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:24:11] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 16:24:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:24:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 16:24:11] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:24:11] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 16:24:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:24:11] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 16:24:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:24:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 16:24:11] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:24:12] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-05 16:24:12] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:24:12] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-05 16:24:12] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:24:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-03-05 16:24:12] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:24:12] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:26:34] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:26:34] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:26:34] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:26:34] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:26:34] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:26:34] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:26:34] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:26:34] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:26:34] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:26:34] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:26:34] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:26:34] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:26:34] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:26:34] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 16:26:34] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 16:26:34] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 16:26:34] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 16:26:34] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:26:34] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 16:26:34] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:26:34] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 16:26:34] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:26:34] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 16:26:34] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:26:34] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 16:26:34] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:26:34] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 16:26:34] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:26:35] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 16:26:35] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:26:35] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 16:26:35] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:26:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 16:26:35] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:26:35] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 16:26:35] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:26:35] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 16:26:35] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:26:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 16:26:35] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:26:35] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 16:26:35] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:26:35] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 16:26:35] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:26:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 16:26:35] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:26:36] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-05 16:26:36] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:26:36] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-05 16:26:36] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:26:36] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-03-05 16:26:36] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:26:37] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:27:44] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:27:44] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:27:44] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:27:44] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:27:44] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:27:44] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:27:44] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:27:44] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:27:44] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:27:44] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:27:44] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:27:44] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:27:44] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 16:27:44] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 16:27:44] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:27:44] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 16:27:44] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:27:44] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 16:27:44] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:27:44] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 16:27:44] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:27:44] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 16:27:44] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 16:27:44] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:27:44] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 16:27:44] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:27:44] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 16:27:44] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:27:45] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 16:27:45] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:27:45] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 16:27:45] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:27:45] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 16:27:45] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:27:45] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 16:27:45] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:27:45] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 16:27:45] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:27:45] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 16:27:45] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:27:46] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-05 16:27:46] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:27:46] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-05 16:27:46] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:27:46] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-03-05 16:27:46] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:27:47] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-05 16:29:10] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-05 16:29:10] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-05 16:29:10] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-05 16:29:10] _msgd.updateData :: updating current design... 
INFO     [2024-03-05 16:29:10] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-05 16:29:10] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-05 16:29:10] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-05 16:29:10] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-05 16:29:10] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-05 16:29:10] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-05 16:29:10] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-05 16:29:10] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-05 16:29:10] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-05 16:29:10] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-05 16:29:10] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-05 16:29:10] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-05 16:29:10] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-05 16:29:10] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:29:10] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-05 16:29:10] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:29:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-05 16:29:11] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:29:11] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-05 16:29:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:29:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-05 16:29:11] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:29:11] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-05 16:29:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:29:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-05 16:29:11] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:29:11] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-05 16:29:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:29:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-05 16:29:11] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-05 16:29:11] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:29:11] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-05 16:29:11] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:29:11] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-05 16:29:12] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:29:12] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-05 16:29:12] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-05 16:29:12] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-05 16:29:12] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-05 16:29:12] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-03-05 16:29:12] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-05 16:29:13] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-06 14:50:37] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-06 14:50:37] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-06 14:50:37] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-06 14:50:37] _msgd.updateData :: updating current design... 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-06 14:50:37] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-06 14:50:37] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-06 14:50:37] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-06 14:50:37] distribution.implementFunction :: implementing parameter distribution... 
CRITICAL [2024-03-06 14:50:37] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-06 14:50:37] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-06 14:50:37] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-06 14:50:37] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-06 14:50:37] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-06 14:50:37] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-06 14:50:37] sg.run :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-06 14:50:37] sg.run :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-06 14:50:37] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-06 14:50:37] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
CRITICAL [2024-03-06 14:50:37] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-06 14:50:37] sg.run :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-06 14:50:37] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-06 14:50:37] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
CRITICAL [2024-03-06 14:50:37] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-06 14:50:37] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-06 14:50:37] sg.run :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-06 14:50:37] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-06 14:50:37] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
CRITICAL [2024-03-06 14:50:37] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-06 14:50:38] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-06 14:50:38] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-06 14:50:38] sg.run :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-06 14:50:38] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-06 14:50:38] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
CRITICAL [2024-03-06 14:50:38] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-06 14:50:38] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-06 14:50:38] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-06 14:50:38] sg.run :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-06 14:50:38] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-06 14:50:38] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
CRITICAL [2024-03-06 14:50:38] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-06 14:50:39] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-06 14:50:39] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-06 14:50:39] sg.run :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-06 14:50:39] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-06 14:50:39] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
CRITICAL [2024-03-06 14:50:39] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-06 14:50:39] _msgd.writeMDAOEvalOut :: [main] writing output files... 
INFO     [2024-03-31 17:20:54] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-03-31 17:20:54] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-03-31 17:20:54] _msgd.updateData :: updating current design... 
INFO     [2024-03-31 17:20:54] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-03-31 17:20:54] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-03-31 17:20:54] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-03-31 17:20:54] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-31 17:20:54] distribution.loadData :: loading data (explicit)... 
INFO     [2024-03-31 17:20:54] distribution.implementFunction :: implementing parameter distribution... 
INFO     [2024-03-31 17:20:54] distribution.loadData :: loading data (explicit)... 
INFO     [2024-03-31 17:20:54] _msgd.analyze :: [main] [eval 0] analysis start 
INFO     [2024-03-31 17:20:54] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-03-31 17:20:54] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-03-31 17:20:54] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-03-31 17:20:54] _structure.calcParamsFromDistributions :: calculating parameters from distributions... 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[0, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[0, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[0.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 0.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[0, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[0, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[1.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 1.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[2, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[2, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[18.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 18.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[2, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[2, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[2.6]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 2.6 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[2, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[2, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[18.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 18.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[2, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[2, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[2.6]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 2.6 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[5, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[5, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[45.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 45.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[5, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[5, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[5.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 5.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[5, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[5, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[45.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 45.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[5, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[5, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[5.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 5.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[7, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[7, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[63.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 63.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[7, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[7, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 6.6000000000000005 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[7, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[7, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[63.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 63.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[7, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[7, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 6.6000000000000005 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[9, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[9, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[81.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 81.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[9, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[9, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[8.2]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 8.2 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[9, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[9, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[81.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 81.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[9, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[9, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[8.2]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 8.2 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ang (float) at [[10, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[10, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[90.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 90.0 (float) 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: evaluating lyr_ply (float) at [[10, 0, 0]]... 
INFO     [2024-03-31 17:20:54] function.__call__ :: x = [[10, 0, 0]] 
INFO     [2024-03-31 17:20:54] function.__call__ :: y = [[9.0]] 
INFO     [2024-03-31 17:20:54] distribution.__call__ :: result = 9.0 (float) 
INFO     [2024-03-31 17:20:54] _msgd.analyze :: [main] going through steps... 
INFO     [2024-03-31 17:20:54] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-31 17:20:55] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-03-31 17:20:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-31 17:20:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-03-31 17:20:55] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-31 17:20:55] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-03-31 17:20:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-31 17:20:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-03-31 17:20:55] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-31 17:20:55] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-03-31 17:20:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-31 17:20:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-03-31 17:20:55] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-03-31 17:20:55] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-31 17:20:55] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-03-31 17:20:55] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-31 17:20:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-03-31 17:20:55] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-03-31 17:20:56] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-03-31 17:20:56] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-31 17:20:56] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-03-31 17:20:56] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-31 17:20:56] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-03-31 17:20:56] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-03-31 17:20:56] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-03-31 17:20:56] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-03-31 17:20:56] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-03-31 17:20:56] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-03-31 17:20:56] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-03-31 17:20:56] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-03-31 17:20:57] _msgd.writeAnalysisOut :: [main] writing output files... 
INFO     [2024-04-16 17:36:54] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 17:36:54] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-16 17:36:54] _msgd.updateData :: updating current design... 
INFO     [2024-04-16 17:36:54] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-16 17:36:54] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-16 17:36:54] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-16 17:36:54] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-16 17:36:54] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-16 17:36:54] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-16 17:36:54] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-16 17:36:54] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-16 17:36:54] distribution.loadData :: loading data (explicit)... 
CRITICAL [2024-04-16 17:36:54] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    if isinstance(_sg_model.design, str):
                  ^^^^^^^^^^^^^^^^
AttributeError: 'InterpolationFunction' object has no attribute 'design'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    if isinstance(_sg_model.design, str):
                  ^^^^^^^^^^^^^^^^
AttributeError: 'InterpolationFunction' object has no attribute 'design'
INFO     [2024-04-16 17:36:54] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-16 17:41:45] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 17:41:45] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-16 17:41:45] _msgd.updateData :: updating current design... 
INFO     [2024-04-16 17:41:45] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-16 17:41:45] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-16 17:41:45] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-16 17:41:45] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-16 17:41:45] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-16 17:41:45] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-16 17:41:45] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-16 17:41:45] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-16 17:41:45] distribution.loadData :: loading data (explicit)... 
CRITICAL [2024-04-16 17:41:45] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    if isinstance(_sg_model.design, str):
                  ^^^^^^^^^^^^^^^^
AttributeError: 'InterpolationFunction' object has no attribute 'design'
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    if isinstance(_sg_model.design, str):
                  ^^^^^^^^^^^^^^^^
AttributeError: 'InterpolationFunction' object has no attribute 'design'
INFO     [2024-04-16 17:41:45] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-16 17:44:38] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 17:44:38] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-16 17:44:38] _msgd.updateData :: updating current design... 
INFO     [2024-04-16 17:44:38] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-16 17:44:38] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-16 17:44:38] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-16 17:44:38] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-16 17:44:38] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-16 17:44:38] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-16 17:44:38] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-16 17:44:38] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-16 17:44:38] distribution.loadData :: loading data (explicit)... 
CRITICAL [2024-04-16 17:56:36] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    for _sg_model in self._db_sg_model.data:
                     ^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 90, in trace_dispatch
    return self.dispatch_line(frame)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 115, in dispatch_line
    if self.quitting: raise BdbQuit
                      ^^^^^^^^^^^^^
bdb.BdbQuit
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    for _sg_model in self._db_sg_model.data:
                     ^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 90, in trace_dispatch
    return self.dispatch_line(frame)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 115, in dispatch_line
    if self.quitting: raise BdbQuit
                      ^^^^^^^^^^^^^
bdb.BdbQuit
INFO     [2024-04-16 17:56:36] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-16 17:57:42] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 17:58:44] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 17:59:36] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 18:00:48] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 18:06:58] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 18:08:02] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 23:38:57] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 23:42:21] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 23:42:21] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-16 23:42:21] _msgd.updateData :: updating current design... 
INFO     [2024-04-16 23:42:21] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-16 23:42:21] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-16 23:42:21] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-16 23:42:21] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-16 23:42:21] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-16 23:42:21] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-16 23:42:21] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-16 23:42:21] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-16 23:42:21] distribution.loadData :: loading data (explicit)... 
CRITICAL [2024-04-16 23:42:37] __main__.main :: Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    for _sg_model in self._db_sg_model.data:
                     ^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 90, in trace_dispatch
    return self.dispatch_line(frame)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 115, in dispatch_line
    if self.quitting: raise BdbQuit
                      ^^^^^^^^^^^^^
bdb.BdbQuit
 
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 224, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 428, in evaluate
    self.updateData()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 466, in updateData
    for _sg_model in self._db_sg_model.data:
                     ^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 90, in trace_dispatch
    return self.dispatch_line(frame)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\rnd_py38\Lib\bdb.py", line 115, in dispatch_line
    if self.quitting: raise BdbQuit
                      ^^^^^^^^^^^^^
bdb.BdbQuit
INFO     [2024-04-16 23:42:37] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-16 23:42:40] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-16 23:42:40] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-16 23:42:41] _msgd.updateData :: updating current design... 
INFO     [2024-04-16 23:42:41] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-16 23:42:41] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-16 23:42:41] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-16 23:42:41] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-16 23:42:41] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-16 23:42:41] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-16 23:42:41] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-16 23:42:41] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-16 23:42:41] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-16 23:42:41] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-16 23:42:41] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-04-16 23:42:41] _structure.calcParamsFromDistributions :: [beam] calculating parameters from distributions... 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[0 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[0.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[0 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[18.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[2.6]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[18.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[2.6]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[45.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[5.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[45.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[5.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[63.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[63.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[81.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[8.2]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[81.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[8.2]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[10  0  0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: x = [[10  0  0]] 
INFO     [2024-04-16 23:42:41] function.__call__ :: y = [[9.0]] 
INFO     [2024-04-16 23:42:41] _structure.writeMeshData :: writing mesh data to file beam_mesh.msh... 
INFO     [2024-04-16 23:42:41] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-16 23:42:41] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-16 23:42:41] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-16 23:42:41] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-16 23:42:41] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-16 23:42:41] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-16 23:42:41] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-16 23:42:41] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-16 23:42:42] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-16 23:42:42] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-16 23:42:42] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-16 23:42:42] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-16 23:42:42] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-16 23:42:42] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-16 23:42:42] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-04-16 23:42:42] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-16 23:42:42] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-04-16 23:42:42] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-16 23:42:42] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-04-16 23:42:42] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-04-16 23:42:43] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-04-16 23:42:43] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-16 23:42:43] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-04-16 23:42:43] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-16 23:42:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-04-16 23:42:43] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-04-16 23:42:43] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-04-16 23:42:43] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-16 23:42:43] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-04-16 23:42:43] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-16 23:42:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-04-16 23:42:43] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-04-16 23:42:43] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-04-16 23:42:43] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-16 23:42:43] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-04-16 23:42:43] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-16 23:42:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-04-16 23:42:43] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-04-16 23:42:44] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-17 17:59:23] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-17 17:59:23] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-17 17:59:23] _msgd.updateData :: updating current design... 
INFO     [2024-04-17 17:59:23] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-17 17:59:23] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-17 17:59:23] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-17 17:59:23] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-17 17:59:23] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-17 17:59:23] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-17 17:59:23] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-17 17:59:23] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-17 17:59:23] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-17 17:59:23] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-17 17:59:23] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-04-17 17:59:23] _structure.calcParamsFromDistributions :: [beam] calculating parameters from distributions... 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[0 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[0.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[0 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[18.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[2.6]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[18.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[2.6]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[45.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[5.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[45.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[5.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[63.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[63.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[81.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[8.2]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[81.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[8.2]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[10  0  0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: x = [[10  0  0]] 
INFO     [2024-04-17 17:59:23] function.__call__ :: y = [[9.0]] 
INFO     [2024-04-17 17:59:23] _structure.writeMeshData :: writing mesh data to file beam_mesh.msh... 
INFO     [2024-04-17 17:59:23] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-17 17:59:23] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-17 17:59:23] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-17 17:59:23] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-17 17:59:23] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-17 17:59:23] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-17 17:59:23] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-17 17:59:23] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:59:23] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-17 17:59:23] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-17 17:59:23] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-17 17:59:23] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-17 17:59:23] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-17 17:59:23] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:59:24] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-04-17 17:59:24] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-17 17:59:24] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-04-17 17:59:24] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-17 17:59:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-04-17 17:59:24] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:59:24] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-04-17 17:59:24] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-17 17:59:24] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-04-17 17:59:24] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-17 17:59:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-04-17 17:59:24] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:59:25] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-04-17 17:59:25] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-17 17:59:25] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-04-17 17:59:25] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-17 17:59:25] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-04-17 17:59:25] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:59:26] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-04-17 17:59:26] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-17 17:59:26] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-04-17 17:59:26] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-17 17:59:26] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-04-17 17:59:26] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:59:26] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-23 16:46:32] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-23 16:46:32] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-23 16:46:32] _msgd.updateData :: updating current design... 
INFO     [2024-04-23 16:46:32] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-04-23 16:46:32] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-04-23 16:46:32] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-04-23 16:46:32] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-04-23 16:46:32] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-04-23 16:46:32] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-04-23 16:46:32] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-23 16:46:32] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-04-23 16:46:32] distribution.loadData :: loading data (explicit)... 
INFO     [2024-04-23 16:46:32] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-23 16:46:32] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-04-23 16:46:32] _structure.calcParamsFromDistributions :: [beam] calculating parameters from distributions... 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[0 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[0.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[0 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[18.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[2.6]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[18.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[2 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[2.6]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[45.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[5.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[45.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[5 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[5.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[63.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[63.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[7 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[6.6000000000000005]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[81.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[8.2]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[81.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[9 0 0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[8.2]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[10  0  0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: x = [[10  0  0]] 
INFO     [2024-04-23 16:46:32] function.__call__ :: y = [[9.0]] 
INFO     [2024-04-23 16:46:32] _structure.writeMeshData :: writing mesh data to file beam_mesh.msh... 
INFO     [2024-04-23 16:46:32] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-23 16:46:32] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-23 16:46:32] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-23 16:46:32] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-23 16:46:32] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-23 16:46:32] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-23 16:46:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-23 16:46:32] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-23 16:46:32] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-23 16:46:32] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-23 16:46:32] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-23 16:46:32] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-23 16:46:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-23 16:46:32] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-23 16:46:33] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-04-23 16:46:33] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-23 16:46:33] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-04-23 16:46:33] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-23 16:46:33] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-04-23 16:46:33] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-04-23 16:46:33] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-04-23 16:46:33] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-23 16:46:33] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-04-23 16:46:33] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-23 16:46:33] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-04-23 16:46:33] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-04-23 16:46:34] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-04-23 16:46:34] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-23 16:46:34] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-04-23 16:46:34] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-23 16:46:34] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-04-23 16:46:34] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-04-23 16:46:34] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-04-23 16:46:34] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-04-23 16:46:34] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-04-23 16:46:34] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-04-23 16:46:34] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-04-23 16:46:34] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-04-23 16:46:35] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-15 16:53:58] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-15 16:53:58] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-15 16:53:58] _msgd.updateData :: updating current design... 
INFO     [2024-05-15 16:53:58] _structure.updateParameters :: [beam] updating parameters... 
INFO     [2024-05-15 16:53:58] _structure.substituteParameters :: [beam] substituting parameters... 
INFO     [2024-05-15 16:53:58] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-05-15 16:53:58] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-05-15 16:53:58] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-05-15 16:53:58] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-05-15 16:53:58] distribution.loadData :: loading data (explicit)... 
INFO     [2024-05-15 16:53:58] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-05-15 16:53:58] distribution.loadData :: loading data (explicit)... 
INFO     [2024-05-15 16:53:58] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-15 16:53:58] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-05-15 16:53:58] _structure.calcParamsFromDistributions :: [beam] calculating parameters from distributions... 
INFO     [2024-05-15 16:53:58] _structure.writeMeshData :: writing mesh data to file beam_mesh.msh... 
INFO     [2024-05-15 16:53:58] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-15 16:53:58] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-15 16:53:58] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-15 16:53:58] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-05-15 16:53:58] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 16:53:58] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-05-15 16:53:58] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 16:53:58] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:53:58] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:53:59] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-15 16:53:59] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-05-15 16:53:59] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 16:53:59] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-05-15 16:53:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 16:53:59] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:53:59] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:53:59] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-15 16:53:59] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-05-15 16:53:59] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 16:53:59] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-05-15 16:53:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 16:53:59] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:53:59] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:53:59] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-15 16:53:59] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-05-15 16:53:59] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 16:53:59] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-05-15 16:53:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 16:53:59] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:53:59] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:54:00] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-15 16:54:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-05-15 16:54:00] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 16:54:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-05-15 16:54:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 16:54:00] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:54:00] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:54:00] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-15 16:54:00] _structure.updateParameters :: [box] updating parameters... 
INFO     [2024-05-15 16:54:00] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 16:54:00] _structure.substituteParameters :: [box] substituting parameters... 
INFO     [2024-05-15 16:54:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 16:54:00] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 16:54:00] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 16:54:01] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-02 17:04:04] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-02 17:04:04] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-02 17:04:04] _msgd.updateData :: updating current design... 
INFO     [2024-06-02 17:04:04] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-06-02 17:04:04] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-06-02 17:04:04] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-06-02 17:04:04] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-06-02 17:04:04] distribution.loadData :: loading data (explicit)... 
INFO     [2024-06-02 17:04:04] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-06-02 17:04:04] distribution.loadData :: loading data (explicit)... 
INFO     [2024-06-02 17:04:04] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-02 17:04:04] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-06-02 17:04:04] _structure.calcParamsFromDistributions :: [beam] calculating parameters from distributions... 
INFO     [2024-06-02 17:04:04] _structure.writeMeshData :: writing mesh data to file beam_mesh.msh... 
INFO     [2024-06-02 17:04:04] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-02 17:04:04] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-02 17:04:04] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-02 17:04:06] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-06-20 15:56:43] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-06-20 15:56:43] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-06-20 15:56:43] _msgd.updateData :: updating current design... 
INFO     [2024-06-20 15:56:43] _structure.loadStructureMesh :: [beam] loading structural mesh data... 
INFO     [2024-06-20 15:56:43] _structure.implementDomainTransformations :: [beam] implementing domain transformations... 
INFO     [2024-06-20 15:56:43] _structure.implementDistributionFunctions :: [beam] implementing distribution functions... 
INFO     [2024-06-20 15:56:43] distribution.implement :: [lyr_ang] implementing parameter distribution... 
INFO     [2024-06-20 15:56:43] distribution.loadData :: loading data (explicit)... 
INFO     [2024-06-20 15:56:43] distribution.implement :: [lyr_ply] implementing parameter distribution... 
INFO     [2024-06-20 15:56:43] distribution.loadData :: loading data (explicit)... 
INFO     [2024-06-20 15:56:43] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-06-20 15:56:43] _structure.discretizeDesign :: [beam] discretizing the design... 
INFO     [2024-06-20 15:56:43] _structure.calcParamsFromDistributions :: [beam] calculating parameters from distributions... 
INFO     [2024-06-20 15:56:43] _structure.writeMeshData :: writing mesh data to file beam_mesh.msh... 
INFO     [2024-06-20 15:56:43] _msgd.analyze :: [main] going through steps... 
INFO     [2024-06-20 15:56:43] _analysis.importPrePostProcFuncs :: importing pre/post processing functions... 
INFO     [2024-06-20 15:56:43] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-06-20 15:56:46] _msgd.writeAnalysisOut :: [main] writing output to file ... 
