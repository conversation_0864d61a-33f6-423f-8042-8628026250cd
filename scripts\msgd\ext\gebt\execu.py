import os
import sys
import subprocess as sbp
import traceback as tb
import numpy as np

import msgd.utils.execu as mue


def run(fn_input, scrnout=True):
    """Run GEBT analysis.

    Parameters
    ----------
    fn_input : str
        File name of the GEBT input.
    scrnout : bool
        Switch for printing GEBT cmd outputs.

    Returns
    -------
    str
        File name of the GEBT output.
    """
    cmd = ['gebt', fn_input]
    # cmd = ' '.join(cmd)

    mue.run(cmd)

    # try:
    #     sys.stdout.write('    - running gebt...\n')
    #     sys.stdout.flush()
    #     if scrnout:
    #         sbp.call(cmd)
    #     else:
    #         FNULL = open(os.devnull, 'w')
    #         sbp.call(cmd, stdout=FNULL, stderr=sbp.STDOUT)
    # except:
    #     # sys.stdout.write('failed\n')
    #     # sys.stdout.flush()
    #     print(tb.format_exc())
    #     return

    return fn_input + '.out'




def solvePLECS(length, compliance, x1, f1=0, f2=0, f3=0, m1=0, m2=0, m3=0):
    """ Solve the static problem of a prismatic, linearly elastic,
    cantilever beam. Find the three displacements and three rotations
    of a point x1 given loads f1, f2, f3, m1, m2, m3 applied at the
    tip.

    Equations (5.59) and (5.61) from the book
    Nonlinear Composite Beam Theory by D. H. Hodges
    are used.

    Parameters
    ----------
    length : float
        Total length of the beam.
    compliance : list of lists of floats
        The 6x6 compliance matrix of the beam cross-section.
    x1 : float
        The location where the result is wanted.

    Returns
    -------
    list of lists of floats
        Results (displacement, rotations, forces, moments).
    """

    F = np.array([[f1, f2, f3, m1, m2, m3]]).T
    e1tilde = np.array([
        [0.0, 0.0, 0.0],
        [0.0, 0.0, -1.0],
        [0.0, 1.0, 0.0]
    ])

    Fx = np.array([[f1, f2, f3]]).T
    Mx = np.array([[m1, m2, m3]]).T + (length - x1) * np.dot(e1tilde, F[:3, :])

    R = compliance[:3, :3]
    Z = compliance[:3, 3:]
    T = compliance[3:, 3:]

    K = np.zeros((6, 6))

    # Equation (5.61)
    K[:3, :3] = (
        x1 * R +
        (x1 * length - x1**2 / 2.0) * np.dot(Z, e1tilde) -
        x1**2 / 2.0 * np.dot(e1tilde, Z.T) -
        (x1**2 * length / 2.0 - x1**3 / 6.0) * np.dot(e1tilde, np.dot(T, e1tilde))
    )
    K[:3, 3:] = x1 * Z - x1**2 / 2.0 * np.dot(e1tilde, T)

    # Equation (5.59)
    K[3:, :3] = x1 * Z.T + (x1 * length - x1**2 / 2.0) * np.dot(T, e1tilde)
    K[3:, 3:] = x1 * T

    ur = np.dot(K, F)
    result = np.vstack((ur, Fx, Mx))

    return result

