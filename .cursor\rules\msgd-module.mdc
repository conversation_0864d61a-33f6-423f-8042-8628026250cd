---
description: 
globs: 
alwaysApply: true
---
# MSGD Module Structure

The `msgd` module is the core implementation of the MSG-based design framework, providing functionality for multiscale constitutive modeling and structural analysis.

## Main Components

### Core Module ([core/](mdc:scripts/msgd/core))
- [_structure.py](mdc:scripts/msgd/core/_structure.py): Structure model implementation
- [_analysis.py](mdc:scripts/msgd/core/_analysis.py): Analysis framework
- [_database.py](mdc:scripts/msgd/core/_database.py): Database management
- [_msgd_study.py](mdc:scripts/msgd/core/_msgd_study.py): Study configuration and execution

### Main Implementation
- [_msgd.py](mdc:scripts/msgd/_msgd.py): Main MSGD class implementation
- [io.py](mdc:scripts/msgd/io.py): Input/output operations
- [_global.py](mdc:scripts/msgd/_global.py): Global configurations and utilities

### Key Submodules
- [analysis/](mdc:scripts/msgd/analysis): Analysis implementations
- [builder/](mdc:scripts/msgd/builder): Structure building tools
- [design/](mdc:scripts/msgd/design): Design optimization
- [dakota/](mdc:scripts/msgd/dakota): Dakota integration
- [utils/](mdc:scripts/msgd/utils): Utility functions
- [ext/](mdc:scripts/msgd/ext): External tool integrations

## Main Classes

### MSGD Class
The main entry point for the framework is the `MSGD` class in [_msgd.py](mdc:scripts/msgd/_msgd.py), which provides:
- Study configuration and execution
- Structure model management
- Analysis workflow control
- Dakota integration for optimization

### Structure Model
The structure model implementation in [_structure.py](mdc:scripts/msgd/core/_structure.py) handles:
- Structure representation
- Material properties
- Cross-section definitions
- Analysis configurations

## Usage

The framework is typically used through the `MSGD` class:

```python
from msgd import MSGD

# Create a new study
study = MSGD(name='my_study', fn_main='input.yml')

# Run the study
study.evaluate()
```

## Key Features
- Multiscale constitutive modeling
- Structure genome calculations
- Integration with Dakota for optimization
- Support for various analysis types
- Flexible input/output handling

