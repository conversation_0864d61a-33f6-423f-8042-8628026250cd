Mesh class
=============

.. currentmodule:: msgd.pkg.meshio._mesh

.. highlight:: python

::

    msgd.pkg.meshio._mesh.Mesh


.. ..  autoclass:: Mesh
..     :members:
..     :undoc-members:
..     :inherited-members:

Class attributes
----------------

..  autosummary::
    :toctree: _temp

    Mesh.points
    Mesh.cells
    Mesh.point_data
    Mesh.cell_data
    Mesh.field_data
    Mesh.point_sets
    Mesh.cell_sets
    Mesh.gmsh_periodic
    Mesh.info


Class methods
--------------

..  autosummary::
    :toctree: _temp

    Mesh.__init__
    Mesh.copy
    Mesh.write
    Mesh.get_cells_type
    Mesh.get_cell_data
    Mesh.cells_dict
    Mesh.cells_data_dict
    Mesh.cells_sets_dict
    Mesh.cells_sets_to_data
    Mesh.points_sets_to_data
    Mesh.cells_data_to_sets
    Mesh.points_data_to_sets
