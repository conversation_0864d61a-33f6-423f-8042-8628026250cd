# main.dakota

environment
  output_file = 'main.out'
  write_restart = 'main.rest'
  error_file = 'main.err'
  tabular_data
    tabular_data_file = 'main_tabular.dat'
  results_output
    results_output_file = 'main_results'


method
  sampling
    sample_type
      lhs
    samples =  10
    seed =  1027


model
  single

variables
  active = design

  continuous_design = 8
    descriptors = 'l1v1'  'l1v2'  'l1v3'  'l1v4'  'l1v5'  'l1v6'  'l1v7'  'l1v8'
    upper_bounds = 90  90  90  90  90  90  90  90
    lower_bounds = -90  -90  -90  -90  -90  -90  -90  -90




interface
  analysis_driver = 'datc --loglevelcmd info --loglevelfile info --logfile eval.log analyze main.yml --paramfile {PARAMETERS} --resultfile {RESULTS}'
    fork
      parameters_file =  'params.in'
      results_file =  'results.out'
      file_save
      work_directory
        named =  'evals/eval'
        directory_tag
        directory_save
        copy_file =  'main.yml'  'sup_files/*'
      verbatim


responses
  descriptors =  'u1'  'u2'  'u3'  'sr_min'
  response_functions = 4
  no_gradients
  no_hessians


