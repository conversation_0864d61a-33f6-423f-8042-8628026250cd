.. _kw-structure:


structure
===========================

Definition of the global structure.


..  code-block:: yaml
    :linenos:

    structure:
      name:
      parameter:
        ...
      distribution:
        ...
      design:
        ...
      model:
        ...


Specification
-------------

:Parent keyword: None
:Arguments: None
:Default: None


Child keywords
--------------

..  list-table::
    :header-rows: 1

    * - Keyword
      - Requirements
      - Description
    * - :ref:`kw-name`
      - Required
      - Name of the structure.
    * - :ref:`kw-parameter`
      - Optional
      - Parameters of the structure.
    * - :ref:`kw-distribution`
      - Optional
      - Distributions of parameters.
    * - :ref:`kw-model`
      - Optional
      - Model of the structure.
    * - :ref:`kw-sg_assign`
      - Required
      - Assignments of |sg| to regions.
    * - :ref:`kw-sg`
      - Required
      - Specification of |sg| models.


