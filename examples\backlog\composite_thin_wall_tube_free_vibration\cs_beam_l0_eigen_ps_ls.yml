# Ref: hodges1991free

version: "0.9"


# ====================================================================
setting:
  data_process_functions_file: "data_proc_funcs"


# ====================================================================
structure:
  name: "uh60_blade_1"
  # parameter:
    # k1: 0.01
    # r1: 0
  design:
    # type: 'discrete'
    dim: 1
    file: 'beam_design.yml'
    solver: 'gebt'
    # section_locations: [0,]
    cs_assignment:
      - region: 'segment1'
        cs: 'cs1'
      # all: 'main_cs'
  cs:
    cs1:
      base: 'circular'
      model: 'md1'


cs:
  - name: "circular"
    parameter:
      radius: 2
      layup: 'l0'
      lam_1: 't300/5208_00055'
      ang_1: 0
      ang_2: 0
      ang_3: 0
      ang_4: 0
      gms: 0.005
    design:
      dim: 2
      tool: "prevabs"
      base_file: "circle.xml.tmp"
    model:
      md1:
        tool: "vabs"



# ====================================================================
analysis:
  setting:
  steps:
    - step: "cs analysis"
      type: 'cs'
      analysis: 'h'
    - step: 'beam analysis'
      type: 'gebt'
      output:
        - value: ['eig1', 'eig2', 'eig3', 'eig4', 'eig5', 'eig6', 'eig7', 'eig8', 'eig9', 'eig10']
    # - step: "calc diff"
    #   type: "script"
    #   file: 'data_proc_funcs'
    #   function: "dakota_postpro"
    #   kwargs:
    #     beam_properties: ["gj", "ei22", "ei33"]
    #     target: [2.29e3, 3.98e3, 2.44e5]


# ====================================================================
study:
  method:
    format: "keyword"
    output: "normal"
    list_parameter_study:
      list_of_points: [45, -45, -45, 45]
  variables:
    data_form: "compact"
    data: |
      ang_1, design, continuous, -90:90
      ang_2, design, continuous, -90:90
      ang_3, design, continuous, -90:90
      ang_4, design, continuous, -90:90
  responses:
    data_form: "compact"
    data: |
      eig1,   objective, max
      eig2,   objective, max
      eig3,   objective, max
      eig4,   objective, max
      eig5,   objective, max
      eig6,   objective, max
      eig7,   objective, max
      eig8,   objective, max
      eig9,   objective, max
      eig10,   objective, max
  interface:
    fork:
      parameters_file: "input.in"
      results_file: "output.out"
      file_save: on
      work_directory:
        directory_tag: on
        directory_save: on
    required_files:
      - "data/*"
    # asynchronous:
    #   evaluation_concurrency: 20
    # failure_capture:
    #   recover: [1e12,]

