*Heading
** Job name: shell_open_hole_physc_ms01 Model name: physc_ms01
** Generated by: Abaqus/CAE 2023
*Preprint, echo=NO, model=NO, history=NO, contact=NO
**
** PARTS
**
*Part, name=Part-1
*Node
      1, -0.200000003, 0.0833030269,          0.5
      2, -0.433012635, 4.16777119e-08,          0.5
      3, -0.433012635, 4.16777119e-08,           0.
      4,           0.,  0.100000001,           0.
      5,           0.,  0.100000001,  0.300000012
      6,  0.433012635, 4.16783408e-08,          0.5
      7,  0.200000003, 0.0833030269,          0.5
      8,  0.433012635, 4.16783408e-08,           0.
      9,           0.,  0.100000001,  0.699999988
     10,           0.,  0.100000001,           1.
     11, -0.433012635, 4.16777119e-08,           1.
     12,  0.433012635, 4.16783408e-08,           1.
     13, -0.0766206309, 0.0976377577,   0.31525889
     14, -0.141428441, 0.0918324292,  0.358585715
     15, -0.184743866, 0.0858471245,  0.423385978
     16, -0.260986775, 0.0705923289,          0.5
     17, -0.320855379, 0.0533893444,          0.5
     18, -0.378791988, 0.0305475369,          0.5
     19, -0.433012635, 4.16777119e-08,  0.400000006
     20, -0.433012635, 4.16777119e-08,  0.300000012
     21, -0.433012635, 4.16777119e-08,  0.200000003
     22, -0.433012635, 4.16777119e-08,  0.100000001
     23, -0.353328854, 0.0415111147,           0.
     24, -0.267639279, 0.0689351186,           0.
     25, -0.179404989, 0.0866821259,           0.
     26, -0.0899564028, 0.0967365131,           0.
     27,           0.,  0.100000001,  0.075000003
     28,           0.,  0.100000001,  0.150000006
     29,           0.,  0.100000001,  0.224999994
     30,  0.433012635, 4.16783408e-08,  0.100000001
     31,  0.433012635, 4.16783408e-08,  0.200000003
     32,  0.433012635, 4.16783408e-08,  0.300000012
     33,  0.433012635, 4.16783408e-08,  0.400000006
     34,  0.378791988, 0.0305475369,          0.5
     35,  0.320855379, 0.0533893444,          0.5
     36,  0.260986775, 0.0705923289,          0.5
     37,  0.184743747, 0.0858471394,   0.42338571
     38,  0.141428009, 0.0918324739,  0.358585298
     39, 0.0766203403,   0.09763778,  0.315258771
     40, 0.0899564028, 0.0967365131,           0.
     41,  0.179404989, 0.0866821259,           0.
     42,  0.267639279, 0.0689351186,           0.
     43,  0.353328854, 0.0415111147,           0.
     44, -0.433012635, 4.16777119e-08,  0.899999976
     45, -0.433012635, 4.16777119e-08,  0.800000012
     46, -0.433012635, 4.16777119e-08,  0.699999988
     47, -0.433012635, 4.16777119e-08,  0.600000024
     48, -0.184743747, 0.0858471394,   0.57661432
     49, -0.141428009, 0.0918324739,  0.641414702
     50, -0.0766203403,   0.09763778,  0.684741259
     51,           0.,  0.100000001,  0.774999976
     52,           0.,  0.100000001,  0.850000024
     53,           0.,  0.100000001,  0.925000012
     54, -0.0899564028, 0.0967365131,           1.
     55, -0.179404989, 0.0866821259,           1.
     56, -0.267639279, 0.0689351186,           1.
     57, -0.353328854, 0.0415111147,           1.
     58, 0.0766206309, 0.0976377577,  0.684741139
     59,  0.141428441, 0.0918324292,  0.641414285
     60,  0.184743866, 0.0858471245,  0.576614022
     61,  0.433012635, 4.16783408e-08,  0.600000024
     62,  0.433012635, 4.16783408e-08,  0.699999988
     63,  0.433012635, 4.16783408e-08,  0.800000012
     64,  0.433012635, 4.16783408e-08,  0.899999976
     65,  0.353328854, 0.0415111147,           1.
     66,  0.267639279, 0.0689351186,           1.
     67,  0.179404989, 0.0866821259,           1.
     68, 0.0899564028, 0.0967365131,           1.
     69, -0.199098289,  0.083460018,  0.294755548
     70,  -0.24290289, 0.0748133585,  0.400819987
     71, -0.368503332,  0.035177093,  0.300475866
     72, -0.292167664,  0.062302459,  0.298666686
     73, -0.186627001, 0.0855458379, 0.0941539705
     74, -0.192445144,  0.084592469,  0.189104021
     75, -0.0977809206, 0.0961382762,  0.247582316
     76, -0.309939802, 0.0569393858,  0.399997741
     77, -0.375971735, 0.0318455175,  0.400118381
     78, -0.365062863, 0.0366625674,  0.199936479
     79, -0.361783326, 0.0380509011, 0.0997833312
     80, -0.284690589, 0.0644147322,  0.196726009
     81, -0.278394371, 0.0661308318,  0.097665824
     82, -0.0939768255, 0.0964355767,  0.084501341
     83, -0.097005643, 0.0961998627,   0.16779691
     84,  0.198223352, 0.0836115479,   0.29472059
     85,  0.242715642, 0.0748549625,  0.401000381
     86,  0.290997446, 0.0626384839,  0.298609853
     87,  0.369824678, 0.0345984921,  0.299581945
     88, 0.0967505276, 0.0962200165,  0.247985572
     89,  0.185607225, 0.0857094303, 0.0943708643
     90,  0.191482455, 0.0847525746,  0.189139768
     91,  0.375896096, 0.0318800211,  0.400118649
     92,  0.310055703, 0.0569027513,  0.399932235
     93, 0.0963433757, 0.0962520614,  0.168126062
     94, 0.0938295498,  0.096446842, 0.0843742341
     95,  0.275844485, 0.0668099821, 0.0983057171
     96,   0.36141935, 0.0382033624, 0.0995269716
     97,  0.283746243, 0.0646757185,  0.196464375
     98,  0.366518527, 0.0360377356,  0.198893324
     99, -0.198223174, 0.0836115777,  0.705279291
    100, -0.242715731, 0.0748549476,  0.598999619
    101, -0.290997356, 0.0626385137,  0.701390147
    102, -0.369824618, 0.0345985144,  0.700418055
    103, -0.0967504531, 0.0962200165,  0.752014518
    104, -0.185607255, 0.0857094303,  0.905629158
    105, -0.191482395, 0.0847525895,  0.810860157
    106, -0.375896126, 0.0318800099,  0.599881351
    107, -0.310055703, 0.0569027551,  0.600067735
    108, -0.0963433012, 0.0962520689,  0.831873953
    109, -0.0938296467, 0.0964468345,  0.915625811
    110, -0.275844485, 0.0668099821,  0.901694298
    111,  -0.36141935, 0.0382033587,  0.900473058
    112, -0.283746183,  0.064675726,  0.803535581
    113, -0.366518497, 0.0360377468,  0.801106632
    114,  0.199098364, 0.0834600106,  0.705244482
    115,  0.242902845, 0.0748133659,  0.599180043
    116,  0.368503332, 0.0351770967,  0.699524164
    117,  0.292167664, 0.0623024628,  0.701333284
    118,  0.186627015, 0.0855458379,     0.905846
    119,  0.192445159, 0.0845924616,  0.810896039
    120, 0.0977808759, 0.0961382762,  0.752417743
    121,  0.309939772,  0.056939397,  0.600002289
    122,  0.375971735, 0.0318455137,  0.599881649
    123,  0.365062863,   0.03666256,  0.800063491
    124,  0.361783355,   0.03805089,  0.900216639
    125,  0.284690648, 0.0644147173,  0.803273916
    126,   0.27839449, 0.0661307946,  0.902334094
    127, 0.0939767882, 0.0964355841,  0.915498674
    128, 0.0970055386, 0.0961998701,   0.83220309
*Element, type=S4R
 1, 69, 70, 15, 14
 2, 70, 16,  1, 15
 3, 70, 76, 17, 16
 4, 76, 77, 18, 17
 5, 77, 19,  2, 18
 6, 69, 72, 76, 70
 7, 72, 71, 77, 76
 8, 71, 20, 19, 77
 9, 71, 78, 21, 20
10, 78, 79, 22, 21
11, 79, 23,  3, 22
12, 72, 80, 78, 71
13, 80, 81, 79, 78
14, 81, 24, 23, 79
15, 69, 74, 80, 72
16, 74, 73, 81, 80
17, 73, 25, 24, 81
18, 73, 82, 26, 25
19, 82, 27,  4, 26
20, 74, 83, 82, 73
21, 83, 28, 27, 82
22, 69, 75, 83, 74
23, 75, 29, 28, 83
24, 75, 13,  5, 29
25, 69, 14, 13, 75
26, 87, 91, 33, 32
27, 91, 34,  6, 33
28, 86, 92, 91, 87
29, 92, 35, 34, 91
30, 84, 85, 92, 86
31, 85, 36, 35, 92
32, 85, 37,  7, 36
33, 84, 38, 37, 85
34, 84, 88, 39, 38
35, 88, 29,  5, 39
36, 88, 93, 28, 29
37, 93, 94, 27, 28
38, 94, 40,  4, 27
39, 84, 90, 93, 88
40, 90, 89, 94, 93
41, 89, 41, 40, 94
42, 89, 95, 42, 41
43, 95, 96, 43, 42
44, 96, 30,  8, 43
45, 90, 97, 95, 89
46, 97, 98, 96, 95
47, 98, 31, 30, 96
48, 84, 86, 97, 90
49, 86, 87, 98, 97
50, 87, 32, 31, 98
51, 102, 106,  47,  46
52, 106,  18,   2,  47
53, 101, 107, 106, 102
54, 107,  17,  18, 106
55,  99, 100, 107, 101
56, 100,  16,  17, 107
57, 100,  48,   1,  16
58,  99,  49,  48, 100
59,  99, 103,  50,  49
60, 103,  51,   9,  50
61, 103, 108,  52,  51
62, 108, 109,  53,  52
63, 109,  54,  10,  53
64,  99, 105, 108, 103
65, 105, 104, 109, 108
66, 104,  55,  54, 109
67, 104, 110,  56,  55
68, 110, 111,  57,  56
69, 111,  44,  11,  57
70, 105, 112, 110, 104
71, 112, 113, 111, 110
72, 113,  45,  44, 111
73,  99, 101, 112, 105
74, 101, 102, 113, 112
75, 102,  46,  45, 113
 76, 114, 115,  60,  59
 77, 115,  36,   7,  60
 78, 115, 121,  35,  36
 79, 121, 122,  34,  35
 80, 122,  61,   6,  34
 81, 114, 117, 121, 115
 82, 117, 116, 122, 121
 83, 116,  62,  61, 122
 84, 116, 123,  63,  62
 85, 123, 124,  64,  63
 86, 124,  65,  12,  64
 87, 117, 125, 123, 116
 88, 125, 126, 124, 123
 89, 126,  66,  65, 124
 90, 114, 119, 125, 117
 91, 119, 118, 126, 125
 92, 118,  67,  66, 126
 93, 118, 127,  68,  67
 94, 127,  53,  10,  68
 95, 119, 128, 127, 118
 96, 128,  52,  53, 127
 97, 114, 120, 128, 119
 98, 120,  51,  52, 128
 99, 120,  58,   9,  51
100, 114,  59,  58, 120
*Nset, nset=Set-1, generate
   1,  128,    1
*Elset, elset=Set-1, generate
   1,  100,    1
*Elset, elset=_Surf-1_SNEG, internal, generate
   1,  100,    1
*Surface, type=ELEMENT, name=Surf-1
_Surf-1_SNEG, SNEG
*Distribution, name=Ori-1-DiscOrient, location=ELEMENT, Table=Ori-1-DiscOrient_Table
** Description: Distribution generated from Discrete Orientation
,           1.,           0.,           0.,           0.,           1.,           0.
1,           0.,           0.,           1., 0.986419200897217, 0.164247140288353,           0.
2,           0.,           0.,           1., 0.9808629155159, 0.194699570536613,           0.
3,           0.,           0.,           1., 0.963992536067963, 0.265929341316223,           0.
4,           0.,           0.,           1., 0.933220386505127, 0.359304517507553,           0.
5,           0.,           0.,           1., 0.874006986618042, 0.48591336607933,           0.
6,           0.,           0.,           1., 0.97123110294342, 0.238139018416405,           0.
7,           0.,           0.,           1., 0.939355909824371, 0.34294381737709,           0.
8,           0.,           0.,           1., 0.877618849277496, 0.479359060525894,           0.
9,           0.,           0.,           1., 0.881300747394562, 0.472555786371231,           0.
10,           0.,           0.,           1., 0.883490085601807, 0.468449890613556,           0.
11,           0.,           0.,           1., 0.887176990509033, 0.461429238319397,           0.
12,           0.,           0.,           1., 0.94451504945755, 0.328468233346939,           0.
13,           0.,           0.,           1., 0.947228610515594, 0.320558845996857,           0.
14,           0.,           0.,           1., 0.950817227363586, 0.309752583503723,           0.
15,           0.,           0.,           1., 0.976297438144684, 0.21643328666687,           0.
16,           0.,           0.,           1., 0.977876305580139, 0.209183886647224,           0.
17,           0.,           0.,           1., 0.979583382606506, 0.201038435101509,           0.
18,           0.,           0.,           1., 0.993508756160736, 0.113755762577057,           0.
19,           0.,           0.,           1., 0.999317348003387, 0.0369436778128147,           0.
20,           0.,           0.,           1., 0.99298769235611, 0.118217766284943,           0.
21,           0.,           0.,           1., 0.999263525009155, 0.0383723936975002,           0.
22,           0.,           0.,           1., 0.992546856403351, 0.121863603591919,           0.
23,           0.,           0.,           1., 0.999233603477478, 0.0391439981758595,           0.
24,           0.,           0.,           1., 0.999386787414551, 0.0350146889686584,           0.
25,           0.,           0.,           1., 0.994358718395233, 0.106069520115852,           0.
26,           0.,           0.,           1., 0.877190768718719, -0.480142027139664,           0.
27,           0.,           0.,           1., 0.874034106731415, -0.485864609479904,           0.
28,           0.,           0.,           1., 0.939318656921387, -0.343045890331268,           0.
29,           0.,           0.,           1., 0.933214366436005, -0.359320163726807,           0.
30,           0.,           0.,           1., 0.971383929252625, -0.237514704465866,           0.
31,           0.,           0.,           1., 0.96399849653244, -0.265907645225525,           0.
32,           0.,           0.,           1., 0.980872750282288, -0.194650128483772,           0.
33,           0.,           0.,           1., 0.986462473869324, -0.163987144827843,           0.
34,           0.,           0.,           1., 0.994403064250946, -0.105653129518032,           0.
35,           0.,           0.,           1., 0.999394059181213, -0.0348061174154282,           0.
36,           0.,           0.,           1., 0.999246954917908, -0.0388006046414375,           0.
37,           0.,           0.,           1., 0.999269783496857, -0.0382082425057888,           0.
38,           0.,           0.,           1., 0.999318480491638, -0.0369138419628143,           0.
39,           0.,           0.,           1., 0.992643654346466, -0.1210727840662,           0.
40,           0.,           0.,           1., 0.993061602115631, -0.117595113813877,           0.
41,           0.,           0.,           1., 0.993538320064545, -0.113496989011765,           0.
42,           0.,           0.,           1., 0.979779601097107, -0.200079783797264,           0.
43,           0.,           0.,           1., 0.951162695884705, -0.308690011501312,           0.
44,           0.,           0.,           1., 0.88728940486908, -0.461213052272797,           0.
45,           0.,           0.,           1., 0.978195607662201, -0.207685843110085,           0.
46,           0.,           0.,           1., 0.947525560855865, -0.319680094718933,           0.
47,           0.,           0.,           1., 0.883136928081512, -0.469115346670151,           0.
48,           0.,           0.,           1., 0.976540863513947, -0.215332090854645,           0.
49,           0.,           0.,           1., 0.944408416748047, -0.32877454161644,           0.
50,           0.,           0.,           1., 0.880379438400269, -0.474269956350327,           0.
51,           0.,           0.,           1., 0.877190768718719, 0.480142027139664,           0.
52,           0.,           0.,           1., 0.874034106731415, 0.485864609479904,           0.
53,           0.,           0.,           1., 0.939318656921387, 0.343045890331268,           0.
54,           0.,           0.,           1., 0.933214366436005, 0.359320163726807,           0.
55,           0.,           0.,           1., 0.971383988857269, 0.23751462996006,           0.
56,           0.,           0.,           1., 0.96399849653244, 0.265907675027847,           0.
57,           0.,           0.,           1., 0.980872750282288, 0.194650128483772,           0.
58,           0.,           0.,           1., 0.986462473869324, 0.16398711502552,           0.
59,           0.,           0.,           1., 0.994403064250946, 0.105653062462807,           0.
60,           0.,           0.,           1., 0.999394059181213, 0.034806102514267,           0.
61,           0.,           0.,           1., 0.999246954917908, 0.0388005748391151,           0.
62,           0.,           0.,           1., 0.999269783496857, 0.0382082462310791,           0.
63,           0.,           0.,           1., 0.999318480491638, 0.0369138605892658,           0.
64,           0.,           0.,           1., 0.992643654346466, 0.121072702109814,           0.
65,           0.,           0.,           1., 0.993061602115631, 0.117595113813877,           0.
66,           0.,           0.,           1., 0.993538320064545, 0.113497003912926,           0.
67,           0.,           0.,           1., 0.979779601097107, 0.200079798698425,           0.
68,           0.,           0.,           1., 0.951162695884705, 0.308690011501312,           0.
69,           0.,           0.,           1., 0.88728940486908, 0.461213052272797,           0.
70,           0.,           0.,           1., 0.978195607662201, 0.207685813307762,           0.
71,           0.,           0.,           1., 0.947525560855865, 0.319680094718933,           0.
72,           0.,           0.,           1., 0.883136928081512, 0.469115287065506,           0.
73,           0.,           0.,           1., 0.976540923118591, 0.215331986546516,           0.
74,           0.,           0.,           1., 0.944408476352692, 0.328774452209473,           0.
75,           0.,           0.,           1., 0.880379498004913, 0.474269896745682,           0.
76,           0.,           0.,           1., 0.986419200897217, -0.164247155189514,           0.
77,           0.,           0.,           1., 0.9808629155159, -0.194699555635452,           0.
78,           0.,           0.,           1., 0.963992536067963, -0.265929341316223,           0.
79,           0.,           0.,           1., 0.933220386505127, -0.359304517507553,           0.
80,           0.,           0.,           1., 0.874006986618042, -0.48591336607933,           0.
81,           0.,           0.,           1., 0.97123110294342, -0.238139018416405,           0.
82,           0.,           0.,           1., 0.939355909824371, -0.342943787574768,           0.
83,           0.,           0.,           1., 0.877618849277496, -0.479359060525894,           0.
84,           0.,           0.,           1., 0.881300747394562, -0.472555786371231,           0.
85,           0.,           0.,           1., 0.883490085601807, -0.468449890613556,           0.
86,           0.,           0.,           1., 0.887176990509033, -0.461429238319397,           0.
87,           0.,           0.,           1., 0.94451504945755, -0.328468233346939,           0.
88,           0.,           0.,           1., 0.947228610515594, -0.320558875799179,           0.
89,           0.,           0.,           1., 0.950817167758942, -0.309752613306046,           0.
90,           0.,           0.,           1., 0.976297378540039, -0.216433316469193,           0.
91,           0.,           0.,           1., 0.977876305580139, -0.209183931350708,           0.
92,           0.,           0.,           1., 0.979583323001862, -0.201038464903831,           0.
93,           0.,           0.,           1., 0.993508756160736, -0.113755762577057,           0.
94,           0.,           0.,           1., 0.999317348003387, -0.0369436740875244,           0.
95,           0.,           0.,           1., 0.99298769235611, -0.11821773648262,           0.
96,           0.,           0.,           1., 0.999263525009155, -0.0383723676204681,           0.
97,           0.,           0.,           1., 0.992546856403351, -0.121863588690758,           0.
98,           0.,           0.,           1., 0.999233603477478, -0.0391439683735371,           0.
99,           0.,           0.,           1., 0.999386787414551, -0.0350146815180779,           0.
100,           0.,           0.,           1., 0.994358718395233, -0.106069535017014,           0.
*Orientation, name=Ori-1, system=RECTANGULAR
Ori-1-DiscOrient
3, 0.
** ** Section: Section-1
** *Shell Section, elset=Set-1, material=Material-1, orientation=Ori-1
** 0.002, 5
*Include, input=shellsections.inp
*End Part
**  
**
** ASSEMBLY
**
*Assembly, name=Assembly
**  
*Instance, name=Part-1-1, part=Part-1
*End Instance
**  
*Nset, nset=Set-1, instance=Part-1-1
  3,  4,  8, 23, 24, 25, 26, 40, 41, 42, 43
*Elset, elset=Set-1, instance=Part-1-1
 11, 14, 17, 18, 19, 38, 41, 42, 43, 44
*Nset, nset=node_displ, instance=Part-1-1
 10,
*Elset, elset=_Surf-1_E2, internal, instance=Part-1-1
 63, 66, 86, 89, 92
*Elset, elset=_Surf-1_E3, internal, instance=Part-1-1
 67, 68, 69, 93, 94
*Elset, elset=elem_stress, instance=Part-1-1
 32,
*Surface, type=ELEMENT, name=Surf-1
_Surf-1_E2, E2
_Surf-1_E3, E3
*End Assembly
*Distribution Table, name=Ori-1-DiscOrient_Table
coord3D, coord3D
** ** 
** ** MATERIALS
** ** 
** *Material, name=Material-1
** *Elastic
**  7.5e+10, 0.3
** ----------------------------------------------------------------
** 
** STEP: Step-1
** 
*Step, name=Step-1, nlgeom=NO
*Static
1., 1., 1e-05, 1.
** 
** BOUNDARY CONDITIONS
** 
** Name: BC-1 Type: Displacement/Rotation
*Boundary
Set-1, 1, 1
Set-1, 2, 2
Set-1, 3, 3
Set-1, 4, 4
Set-1, 5, 5
Set-1, 6, 6
** 
** LOADS
** 
** Name: Load-1   Type: Shell edge load
*Dsload, follower=NO, constant resultant=YES
Surf-1, EDLD, 10., 0., 0., 1.
** 
** OUTPUT REQUESTS
** 
*Restart, write, frequency=0
** 
** FIELD OUTPUT: F-Output-1
** 
*Output, field
*Node Output
U, 
*Element Output, position=NODES, directions=YES
E, S, SE, SF, SM, SSAVG
** 
** HISTORY OUTPUT: H-Output-1
** 
*Output, history, variable=PRESELECT
*End Step
