INFO     [2024-04-17 00:01:42] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-17 00:01:42] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-17 00:01:42] _msgd.updateData :: updating current design... 
INFO     [2024-04-17 00:01:42] _structure.updateParameters :: [uh60_blade_1] updating parameters... 
INFO     [2024-04-17 00:01:42] _structure.substituteParameters :: [uh60_blade_1] substituting parameters... 
INFO     [2024-04-17 00:01:42] _structure.loadStructureMesh :: [uh60_blade_1] loading structural mesh data... 
INFO     [2024-04-17 00:01:42] rcas.readRcasInput :: reading rcas input file oml.dat... 
INFO     [2024-04-17 00:01:42] _structure.implementDomainTransformations :: [uh60_blade_1] implementing domain transformations... 
INFO     [2024-04-17 00:01:42] _structure.implementDistributionFunctions :: [uh60_blade_1] implementing distribution functions... 
INFO     [2024-04-17 00:01:42] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-04-17 00:01:42] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 00:01:42] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 00:01:42] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-04-17 00:01:42] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-04-17 00:01:42] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 00:01:42] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 00:01:42] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-04-17 00:01:42] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-04-17 00:01:43] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 00:01:43] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-04-17 00:01:43] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 00:01:43] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-17 00:01:43] _structure.discretizeDesign :: [uh60_blade_1] discretizing the design... 
INFO     [2024-04-17 00:01:43] _structure.calcParamsFromDistributions :: [uh60_blade_1] calculating parameters from distributions... 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[7.500011180138112]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:01:43] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 00:01:43] _structure.writeMeshData :: writing mesh data to file uh60_blade_1_mesh.msh... 
INFO     [2024-04-17 00:01:43] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-17 00:01:43] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-17 00:01:43] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-17 00:01:43] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:01:43] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-17 00:01:43] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:01:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-17 00:01:43] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:01:48] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-17 00:01:48] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:01:48] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-17 00:01:48] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:01:48] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-17 00:01:48] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:01:51] _structure.writeSGPropertyFile :: [uh60_blade_1] writing SG properties to file prop_calc.dat... 
INFO     [2024-04-17 00:01:51] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-17 00:02:17] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-17 00:02:17] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-17 00:02:17] _msgd.updateData :: updating current design... 
INFO     [2024-04-17 00:02:17] _structure.updateParameters :: [uh60_blade_1] updating parameters... 
INFO     [2024-04-17 00:02:17] _structure.substituteParameters :: [uh60_blade_1] substituting parameters... 
INFO     [2024-04-17 00:02:17] _structure.loadStructureMesh :: [uh60_blade_1] loading structural mesh data... 
INFO     [2024-04-17 00:02:17] rcas.readRcasInput :: reading rcas input file oml.dat... 
INFO     [2024-04-17 00:02:17] _structure.implementDomainTransformations :: [uh60_blade_1] implementing domain transformations... 
INFO     [2024-04-17 00:02:17] _structure.implementDistributionFunctions :: [uh60_blade_1] implementing distribution functions... 
INFO     [2024-04-17 00:02:17] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-04-17 00:02:17] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 00:02:17] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 00:02:17] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-04-17 00:02:17] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-04-17 00:02:17] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 00:02:17] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 00:02:17] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-04-17 00:02:17] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-04-17 00:02:17] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 00:02:17] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-04-17 00:02:17] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 00:02:17] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-17 00:02:17] _structure.discretizeDesign :: [uh60_blade_1] discretizing the design... 
INFO     [2024-04-17 00:02:17] _structure.calcParamsFromDistributions :: [uh60_blade_1] calculating parameters from distributions... 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[7.500011180138112]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[7.000016770207168]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[7.000016770207168]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[6.500022360276224]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[6.500022360276224]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[6.00002795034528]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[6.00002795034528]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[5.2000536646629385]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[5.2000536646629385]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[4.4000626087734265]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[4.4000626087734265]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.800080496994407]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.800080496994407]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.5032850972485687]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.5032850972485687]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[2.0]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 00:02:17] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 00:02:17] _structure.writeMeshData :: writing mesh data to file uh60_blade_1_mesh.msh... 
INFO     [2024-04-17 00:02:18] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-17 00:02:18] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-17 00:02:18] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-17 00:02:18] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:18] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-17 00:02:18] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:18] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-17 00:02:18] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:21] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-17 00:02:21] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:21] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-17 00:02:21] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:21] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-17 00:02:21] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:24] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-04-17 00:02:24] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:24] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-04-17 00:02:24] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:24] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-04-17 00:02:25] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:28] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-04-17 00:02:28] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:28] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-04-17 00:02:28] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:28] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-04-17 00:02:28] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:32] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-04-17 00:02:32] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:32] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-04-17 00:02:32] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-04-17 00:02:32] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:35] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-04-17 00:02:35] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:35] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-04-17 00:02:35] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:35] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-04-17 00:02:35] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:39] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-04-17 00:02:39] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:39] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-04-17 00:02:39] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:39] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-04-17 00:02:39] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:43] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-04-17 00:02:43] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:43] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-04-17 00:02:43] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-04-17 00:02:43] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:47] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-04-17 00:02:47] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:47] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-04-17 00:02:47] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:47] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-04-17 00:02:47] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:50] _structure.updateParameters :: [main_cs_set10] updating parameters... 
INFO     [2024-04-17 00:02:50] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 00:02:50] sg.runH :: [cs: main_cs_set10] running cs analysis... 
INFO     [2024-04-17 00:02:50] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 00:02:50] main.buildSGModel :: building 2D SG (prevabs): main_cs_set10... 
INFO     [2024-04-17 00:02:50] execu.run :: prevabs -i main_cs_set10.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 00:02:53] _structure.writeSGPropertyFile :: [uh60_blade_1] writing SG properties to file prop_calc.dat... 
INFO     [2024-04-17 00:02:53] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-17 17:01:22] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-17 17:01:22] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-17 17:01:22] _msgd.updateData :: updating current design... 
INFO     [2024-04-17 17:01:22] _structure.updateParameters :: [uh60_blade_1] updating parameters... 
INFO     [2024-04-17 17:01:22] _structure.substituteParameters :: [uh60_blade_1] substituting parameters... 
INFO     [2024-04-17 17:01:22] _structure.loadStructureMesh :: [uh60_blade_1] loading structural mesh data... 
INFO     [2024-04-17 17:01:22] rcas.readRcasInput :: reading rcas input file oml.dat... 
INFO     [2024-04-17 17:01:22] _structure.implementDomainTransformations :: [uh60_blade_1] implementing domain transformations... 
INFO     [2024-04-17 17:01:22] _structure.implementDistributionFunctions :: [uh60_blade_1] implementing distribution functions... 
INFO     [2024-04-17 17:01:22] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-04-17 17:01:22] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 17:01:22] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 17:01:22] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-04-17 17:01:22] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-04-17 17:01:22] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 17:01:22] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 17:01:22] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-04-17 17:01:22] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-04-17 17:01:22] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 17:01:22] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-04-17 17:01:22] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 17:01:22] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-17 17:01:22] _structure.discretizeDesign :: [uh60_blade_1] discretizing the design... 
INFO     [2024-04-17 17:01:22] _structure.calcParamsFromDistributions :: [uh60_blade_1] calculating parameters from distributions... 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[7.500011180138112]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[7.000016770207168]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[7.000016770207168]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[6.500022360276224]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[6.500022360276224]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[6.00002795034528]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:22] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[6.00002795034528]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[5.2000536646629385]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[5.2000536646629385]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[4.4000626087734265]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[4.4000626087734265]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.800080496994407]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.800080496994407]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.5032850972485687]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.5032850972485687]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[2.0]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:01:23] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 17:01:23] _structure.writeMeshData :: writing mesh data to file uh60_blade_1_mesh.msh... 
INFO     [2024-04-17 17:01:23] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-17 17:01:23] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-17 17:01:23] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-17 17:01:23] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:23] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-17 17:01:23] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:23] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-17 17:01:24] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:29] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-17 17:01:29] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:29] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-17 17:01:29] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:29] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-17 17:01:29] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:33] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-04-17 17:01:33] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:33] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-04-17 17:01:33] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:33] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-04-17 17:01:33] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:37] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-04-17 17:01:37] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:37] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-04-17 17:01:37] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:37] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-04-17 17:01:38] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:41] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-04-17 17:01:41] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:41] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-04-17 17:01:41] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:41] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-04-17 17:01:41] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:45] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-04-17 17:01:45] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:45] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-04-17 17:01:45] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:45] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-04-17 17:01:45] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:49] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-04-17 17:01:49] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:49] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-04-17 17:01:49] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:49] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-04-17 17:01:49] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:53] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-04-17 17:01:53] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:53] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-04-17 17:01:53] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:53] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-04-17 17:01:53] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:01:57] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-04-17 17:01:57] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:01:57] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-04-17 17:01:57] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:01:57] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-04-17 17:01:57] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:02:00] _structure.updateParameters :: [main_cs_set10] updating parameters... 
INFO     [2024-04-17 17:02:00] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:02:00] sg.runH :: [cs: main_cs_set10] running cs analysis... 
INFO     [2024-04-17 17:02:00] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:02:00] main.buildSGModel :: building 2D SG (prevabs): main_cs_set10... 
INFO     [2024-04-17 17:02:00] execu.run :: prevabs -i main_cs_set10.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:02:03] _structure.writeSGPropertyFile :: [uh60_blade_1] writing SG properties to file prop_calc.dat... 
INFO     [2024-04-17 17:02:03] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-04-17 17:57:20] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-04-17 17:57:20] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-04-17 17:57:20] _msgd.updateData :: updating current design... 
INFO     [2024-04-17 17:57:20] _structure.updateParameters :: [uh60_blade_1] updating parameters... 
INFO     [2024-04-17 17:57:20] _structure.substituteParameters :: [uh60_blade_1] substituting parameters... 
INFO     [2024-04-17 17:57:20] _structure.loadStructureMesh :: [uh60_blade_1] loading structural mesh data... 
INFO     [2024-04-17 17:57:20] rcas.readRcasInput :: reading rcas input file oml.dat... 
INFO     [2024-04-17 17:57:20] _structure.implementDomainTransformations :: [uh60_blade_1] implementing domain transformations... 
INFO     [2024-04-17 17:57:20] _structure.implementDistributionFunctions :: [uh60_blade_1] implementing distribution functions... 
INFO     [2024-04-17 17:57:20] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-04-17 17:57:20] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 17:57:20] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 17:57:20] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-04-17 17:57:20] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-04-17 17:57:20] distribution.loadData :: loading data (file)... 
INFO     [2024-04-17 17:57:20] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-04-17 17:57:20] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-04-17 17:57:20] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-04-17 17:57:20] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 17:57:20] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-04-17 17:57:20] distribution.loadData :: loading data (compact)... 
INFO     [2024-04-17 17:57:20] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-04-17 17:57:20] _structure.discretizeDesign :: [uh60_blade_1] discretizing the design... 
INFO     [2024-04-17 17:57:20] _structure.calcParamsFromDistributions :: [uh60_blade_1] calculating parameters from distributions... 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[7.500011180138112]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.750005590069056]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[5.3666]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[17.999798757513986]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[7.000016770207168]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[7.000016770207168]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.500008385103584]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[8.0499]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[26.999698136270972]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[6.500022360276224]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[6.500022360276224]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.250011180138112]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[10.7332]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[35.99959751502797]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[6.00002795034528]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[6.00002795034528]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.00001397517264]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[13.4165]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[44.99949689378496]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[5.2000536646629385]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[5.2000536646629385]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.6000268323314693]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[16.0998]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[53.999396272541944]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[4.4000626087734265]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.74708]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[4.4000626087734265]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.2000313043867132]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[18.7831]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[62.99929565129895]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.85975]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[3.6000715528839167]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.8000357764419583]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[21.4664]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[71.99919503005594]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.800080496994407]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[2.800080496994407]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: y = [[1.4000402484972034]] 
INFO     [2024-04-17 17:57:20] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[24.1497]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[80.99909440881292]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[2.5032850972485687]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[2.5032850972485687]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.2516425486242844]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[25.1452]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[84.3380426559536]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.73]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[2.0]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[1.0]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: x = [[26.8333]] 
INFO     [2024-04-17 17:57:21] function.__call__ :: y = [[90.0]] 
INFO     [2024-04-17 17:57:21] _structure.writeMeshData :: writing mesh data to file uh60_blade_1_mesh.msh... 
INFO     [2024-04-17 17:57:21] _msgd.analyze :: [main] going through steps... 
INFO     [2024-04-17 17:57:21] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-04-17 17:57:21] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-04-17 17:57:21] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:21] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-04-17 17:57:21] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:21] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-04-17 17:57:21] execu.run :: prevabs -i main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:25] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-04-17 17:57:25] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:25] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-04-17 17:57:25] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:25] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-04-17 17:57:25] execu.run :: prevabs -i main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:29] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-04-17 17:57:29] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:29] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-04-17 17:57:29] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:29] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-04-17 17:57:29] execu.run :: prevabs -i main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:32] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-04-17 17:57:32] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:32] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-04-17 17:57:32] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:32] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-04-17 17:57:33] execu.run :: prevabs -i main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:36] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-04-17 17:57:36] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:36] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-04-17 17:57:36] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:36] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-04-17 17:57:36] execu.run :: prevabs -i main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:40] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-04-17 17:57:40] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:40] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-04-17 17:57:40] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:40] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-04-17 17:57:40] execu.run :: prevabs -i main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:43] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-04-17 17:57:43] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:43] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-04-17 17:57:43] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:43] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-04-17 17:57:43] execu.run :: prevabs -i main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:47] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-04-17 17:57:47] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:47] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-04-17 17:57:47] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:47] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-04-17 17:57:47] execu.run :: prevabs -i main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:50] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-04-17 17:57:50] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:50] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-04-17 17:57:50] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:50] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-04-17 17:57:51] execu.run :: prevabs -i main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:53] _structure.updateParameters :: [main_cs_set10] updating parameters... 
INFO     [2024-04-17 17:57:53] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-04-17 17:57:53] sg.runH :: [cs: main_cs_set10] running cs analysis... 
INFO     [2024-04-17 17:57:53] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-04-17 17:57:53] main.buildSGModel :: building 2D SG (prevabs): main_cs_set10... 
INFO     [2024-04-17 17:57:54] execu.run :: prevabs -i main_cs_set10.xml -vabs -ver 4.1 -h 
INFO     [2024-04-17 17:57:56] _structure.writeSGPropertyFile :: [uh60_blade_1] writing SG properties to file prop_calc.dat... 
INFO     [2024-04-17 17:57:57] _msgd.writeAnalysisOut :: [main] writing output to file ... 
INFO     [2024-05-15 17:52:51] io.readMSGDInput :: reading main input main.yml... 
INFO     [2024-05-15 17:52:51] _msgd.readMDAOEvalIn :: reading mdao input... 
INFO     [2024-05-15 17:52:51] _msgd.updateData :: updating current design... 
INFO     [2024-05-15 17:52:51] _structure.updateParameters :: [uh60_blade_1] updating parameters... 
INFO     [2024-05-15 17:52:51] _structure.substituteParameters :: [uh60_blade_1] substituting parameters... 
INFO     [2024-05-15 17:52:51] _structure.loadStructureMesh :: [uh60_blade_1] loading structural mesh data... 
INFO     [2024-05-15 17:52:51] rcas.readRcasInput :: reading rcas input file oml.dat... 
INFO     [2024-05-15 17:52:51] _structure.implementDomainTransformations :: [uh60_blade_1] implementing domain transformations... 
INFO     [2024-05-15 17:52:51] _structure.implementDistributionFunctions :: [uh60_blade_1] implementing distribution functions... 
INFO     [2024-05-15 17:52:51] distribution.implement :: [airfoil] implementing parameter distribution... 
INFO     [2024-05-15 17:52:51] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:52:51] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-05-15 17:52:51] interface.loadDataFromFile :: loading data airfoilinterp... 
INFO     [2024-05-15 17:52:51] distribution.implement :: [chord] implementing parameter distribution... 
INFO     [2024-05-15 17:52:51] distribution.loadData :: loading data (file)... 
INFO     [2024-05-15 17:52:51] interface.loadDataFromFile :: loading data from oml.dat (rcas)... 
INFO     [2024-05-15 17:52:51] interface.loadDataFromFile :: loading data chord_structure... 
INFO     [2024-05-15 17:52:51] distribution.implement :: [['ply_spar_1', 'ply_front', 'ply_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:52:51] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:52:51] distribution.implement :: [['ang_front', 'ang_back']] implementing parameter distribution... 
INFO     [2024-05-15 17:52:51] distribution.loadData :: loading data (compact)... 
INFO     [2024-05-15 17:52:51] _msgd.discretize :: [main] discretizing the structure... 
INFO     [2024-05-15 17:52:51] _structure.discretizeDesign :: [uh60_blade_1] discretizing the design... 
INFO     [2024-05-15 17:52:51] _structure.calcParamsFromDistributions :: [uh60_blade_1] calculating parameters from distributions... 
INFO     [2024-05-15 17:52:51] _structure.writeMeshData :: writing mesh data to file uh60_blade_1_mesh.msh... 
INFO     [2024-05-15 17:52:51] _msgd.analyze :: [main] going through steps... 
INFO     [2024-05-15 17:52:51] sg.runH :: [step: cs analysis] running cs analysis (h)... 
INFO     [2024-05-15 17:52:51] _structure.updateParameters :: [main_cs_set1] updating parameters... 
INFO     [2024-05-15 17:52:51] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:52:51] sg.runH :: [cs: main_cs_set1] running cs analysis... 
INFO     [2024-05-15 17:52:51] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:52:51] main.buildSGModel :: building 2D SG (prevabs): main_cs_set1... 
INFO     [2024-05-15 17:52:51] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:52:52] execu.run :: prevabs -i cs\main_cs_set1.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:52:53] execu.run :: VABS cs\main_cs_set1.sg 
INFO     [2024-05-15 17:52:55] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:52:55] _structure.updateParameters :: [main_cs_set2] updating parameters... 
INFO     [2024-05-15 17:52:55] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:52:55] sg.runH :: [cs: main_cs_set2] running cs analysis... 
INFO     [2024-05-15 17:52:55] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:52:55] main.buildSGModel :: building 2D SG (prevabs): main_cs_set2... 
INFO     [2024-05-15 17:52:55] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:52:56] execu.run :: prevabs -i cs\main_cs_set2.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:52:57] execu.run :: VABS cs\main_cs_set2.sg 
INFO     [2024-05-15 17:52:59] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:52:59] _structure.updateParameters :: [main_cs_set3] updating parameters... 
INFO     [2024-05-15 17:52:59] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:52:59] sg.runH :: [cs: main_cs_set3] running cs analysis... 
INFO     [2024-05-15 17:52:59] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:52:59] main.buildSGModel :: building 2D SG (prevabs): main_cs_set3... 
INFO     [2024-05-15 17:52:59] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:00] execu.run :: prevabs -i cs\main_cs_set3.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:01] execu.run :: VABS cs\main_cs_set3.sg 
INFO     [2024-05-15 17:53:03] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:03] _structure.updateParameters :: [main_cs_set4] updating parameters... 
INFO     [2024-05-15 17:53:03] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:03] sg.runH :: [cs: main_cs_set4] running cs analysis... 
INFO     [2024-05-15 17:53:03] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:03] main.buildSGModel :: building 2D SG (prevabs): main_cs_set4... 
INFO     [2024-05-15 17:53:03] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:04] execu.run :: prevabs -i cs\main_cs_set4.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:05] execu.run :: VABS cs\main_cs_set4.sg 
INFO     [2024-05-15 17:53:07] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:07] _structure.updateParameters :: [main_cs_set5] updating parameters... 
INFO     [2024-05-15 17:53:07] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:07] sg.runH :: [cs: main_cs_set5] running cs analysis... 
INFO     [2024-05-15 17:53:07] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:07] main.buildSGModel :: building 2D SG (prevabs): main_cs_set5... 
INFO     [2024-05-15 17:53:07] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:07] execu.run :: prevabs -i cs\main_cs_set5.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:09] execu.run :: VABS cs\main_cs_set5.sg 
INFO     [2024-05-15 17:53:10] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:10] _structure.updateParameters :: [main_cs_set6] updating parameters... 
INFO     [2024-05-15 17:53:10] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:10] sg.runH :: [cs: main_cs_set6] running cs analysis... 
INFO     [2024-05-15 17:53:10] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:10] main.buildSGModel :: building 2D SG (prevabs): main_cs_set6... 
INFO     [2024-05-15 17:53:10] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:11] execu.run :: prevabs -i cs\main_cs_set6.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:12] execu.run :: VABS cs\main_cs_set6.sg 
INFO     [2024-05-15 17:53:14] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:14] _structure.updateParameters :: [main_cs_set7] updating parameters... 
INFO     [2024-05-15 17:53:14] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:14] sg.runH :: [cs: main_cs_set7] running cs analysis... 
INFO     [2024-05-15 17:53:14] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:14] main.buildSGModel :: building 2D SG (prevabs): main_cs_set7... 
INFO     [2024-05-15 17:53:14] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:14] execu.run :: prevabs -i cs\main_cs_set7.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:16] execu.run :: VABS cs\main_cs_set7.sg 
INFO     [2024-05-15 17:53:18] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:18] _structure.updateParameters :: [main_cs_set8] updating parameters... 
INFO     [2024-05-15 17:53:18] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:18] sg.runH :: [cs: main_cs_set8] running cs analysis... 
INFO     [2024-05-15 17:53:18] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:18] main.buildSGModel :: building 2D SG (prevabs): main_cs_set8... 
INFO     [2024-05-15 17:53:18] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:18] execu.run :: prevabs -i cs\main_cs_set8.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:19] execu.run :: VABS cs\main_cs_set8.sg 
INFO     [2024-05-15 17:53:22] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:22] _structure.updateParameters :: [main_cs_set9] updating parameters... 
INFO     [2024-05-15 17:53:22] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:22] sg.runH :: [cs: main_cs_set9] running cs analysis... 
INFO     [2024-05-15 17:53:22] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:22] main.buildSGModel :: building 2D SG (prevabs): main_cs_set9... 
INFO     [2024-05-15 17:53:22] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:22] execu.run :: prevabs -i cs\main_cs_set9.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:23] execu.run :: VABS cs\main_cs_set9.sg 
INFO     [2024-05-15 17:53:25] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:25] _structure.updateParameters :: [main_cs_set10] updating parameters... 
INFO     [2024-05-15 17:53:25] _structure.updateParameters :: [cs_airfoil] updating parameters... 
INFO     [2024-05-15 17:53:25] sg.runH :: [cs: main_cs_set10] running cs analysis... 
INFO     [2024-05-15 17:53:25] _structure.substituteParameters :: [cs_airfoil] substituting parameters... 
INFO     [2024-05-15 17:53:25] main.buildSGModel :: building 2D SG (prevabs): main_cs_set10... 
INFO     [2024-05-15 17:53:25] main.buildSGFromPrevabs :: building cs using prevabs... 
INFO     [2024-05-15 17:53:25] execu.run :: prevabs -i cs\main_cs_set10.xml -vabs -ver 4.1 -h 
INFO     [2024-05-15 17:53:26] execu.run :: VABS cs\main_cs_set10.sg 
INFO     [2024-05-15 17:53:28] execu.run :: VABS finished successfully 
INFO     [2024-05-15 17:53:28] _structure.writeSGPropertyFile :: [uh60_blade_1] writing SG properties to file prop_calc.dat... 
INFO     [2024-05-15 17:53:28] _msgd.writeAnalysisOut :: [main] writing output to file ... 
