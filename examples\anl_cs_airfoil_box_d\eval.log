[12:59:17] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[12:59:17] INFO     reading main input main.yml... [io.readMSGDInput]
[12:59:17] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x00000251A2BA2610>,
 'variant': 'ivabs'} [io.readMSGDInput]
[12:59:17] DEBUG    currect working directory:  [io.readMSGDInput]
[12:59:17] DEBUG    input file name: main [io.readMSGDInput]
[12:59:17] DEBUG    input file extension: .yml [io.readMSGDInput]
[12:59:17] DEBUG    output file name: main.out [io.readMSGDInput]
[12:59:17] DEBUG    variant: ivabs [io.readMSGDInput]
[12:59:17] DEBUG    version: 0.10 [io.readMSGDInput]
[12:59:17] DEBUG    _sg_assignments = [sg assignment of cs to region all (element)] [io.readInputStructure]
[12:59:17] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[12:59:17] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[12:59:17] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[12:59:17] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[12:59:17] INFO     updating current design... [_msgd.updateData]
[12:59:17] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[12:59:17] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[12:59:17] DEBUG    subsituting parameters... [container.substituteParams]
[12:59:17] DEBUG    before substitution [container.substituteParams]
[12:59:17] DEBUG    inputs: None [container.substituteParams]
[12:59:17] DEBUG    params: {} [container.substituteParams]
[12:59:17] DEBUG    after substitution [container.substituteParams]
[12:59:17] DEBUG    inputs: None [container.substituteParams]
[12:59:17] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[12:59:17] DEBUG    self._config = {} [_structure.loadStructureMesh]
[12:59:17] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[12:59:17] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[12:59:17] DEBUG    updated design (global structure): [_msgd.updateData]
[12:59:17] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': None} [_msgd.updateData]
[12:59:17] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[12:59:17] INFO     [main] discretizing the structure... [_msgd.discretize]
[12:59:17] DEBUG    structure model:
{'cs_assignment': [{'cs': 'cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': None,
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[12:59:17] DEBUG    ================ [_structure.discretizeDesign]
[12:59:17] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[12:59:17] DEBUG     [_structure.calcParamsFromDistributions]
[12:59:17] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[12:59:17] DEBUG    getting entity sets... [_structure.getEntitySets]
[12:59:17] DEBUG    sets = {} [_structure.getEntitySets]
[12:59:17] CRITICAL Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 508, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 488, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1568, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1359, in calcParamsFromDistributions
    _entity_sets = self.getEntitySets()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 818, in getEntitySets
    _point_sets = self._model.point_sets
AttributeError: 'NoneType' object has no attribute 'point_sets'
 [__main__.main]
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 508, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 488, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1568, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1359, in calcParamsFromDistributions
    _entity_sets = self.getEntitySets()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 818, in getEntitySets
    _point_sets = self._model.point_sets
AttributeError: 'NoneType' object has no attribute 'point_sets'
[12:59:17] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[13:28:50] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[13:28:50] INFO     reading main input main.yml... [io.readMSGDInput]
[13:28:50] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x000002631CE915B0>,
 'variant': 'ivabs'} [io.readMSGDInput]
[13:28:50] DEBUG    currect working directory:  [io.readMSGDInput]
[13:28:50] DEBUG    input file name: main [io.readMSGDInput]
[13:28:50] DEBUG    input file extension: .yml [io.readMSGDInput]
[13:28:50] DEBUG    output file name: main.out [io.readMSGDInput]
[13:28:50] DEBUG    variant: ivabs [io.readMSGDInput]
[13:28:50] DEBUG    version: 0.10 [io.readMSGDInput]
[13:28:50] DEBUG    _sg_assignments = [sg assignment of cs to region all (element)] [io.readInputStructure]
[13:28:50] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[13:28:50] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[13:28:50] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[13:28:50] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[13:28:50] INFO     updating current design... [_msgd.updateData]
[13:28:50] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[13:28:50] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[13:28:50] DEBUG    subsituting parameters... [container.substituteParams]
[13:28:50] DEBUG    before substitution [container.substituteParams]
[13:28:50] DEBUG    inputs: None [container.substituteParams]
[13:28:50] DEBUG    params: {} [container.substituteParams]
[13:28:50] DEBUG    after substitution [container.substituteParams]
[13:28:50] DEBUG    inputs: None [container.substituteParams]
[13:28:50] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[13:28:50] DEBUG    self._config = {} [_structure.loadStructureMesh]
[13:28:50] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[13:28:50] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[13:28:50] DEBUG    updated design (global structure): [_msgd.updateData]
[13:28:50] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': None} [_msgd.updateData]
[13:28:50] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[13:28:50] INFO     [main] discretizing the structure... [_msgd.discretize]
[13:28:50] DEBUG    structure model:
{'cs_assignment': [{'cs': 'cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': None,
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[13:28:50] DEBUG    ================ [_structure.discretizeDesign]
[13:28:50] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[13:28:50] DEBUG     [_structure.calcParamsFromDistributions]
[13:28:50] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[13:28:50] DEBUG    getting entity sets... [_structure.getEntitySets]
[13:28:50] DEBUG    sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.getEntitySets]
[13:28:50] CRITICAL Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 508, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 488, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1610, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1359, in calcParamsFromDistributions
    _entity_sets = self.getEntitySets()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 818, in getEntitySets
    _point_sets = self._model.point_sets
AttributeError: 'NoneType' object has no attribute 'point_sets'
 [__main__.main]
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\__main__.py", line 376, in main
    msgd.evaluate()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 432, in evaluate
    self.analyze()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 508, in analyze
    self.discretize()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\_msgd.py", line 488, in discretize
    structure_model.discretizeDesign(db_sg_model=self._db_sg_model)
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1610, in discretizeDesign
    self.calcParamsFromDistributions()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 1359, in calcParamsFromDistributions
    _entity_sets = self.getEntitySets()
  File "C:\Users\<USER>\work\dev\msg-design\scripts\msgd\core\_structure.py", line 818, in getEntitySets
    _point_sets = self._model.point_sets
AttributeError: 'NoneType' object has no attribute 'point_sets'
[13:28:50] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
[13:34:09] DEBUG    {'fn_main': 'main.yml', 'mode': '1', 'fn_dakota_params': '', 'fn_dakota_results': '', 'variant': 'ivabs', 'executable': 'ivabs', 'log_level_cmd': 'info', 'log_level_file': 'debug', 'log_file_name': 'eval.log', 'kwargs': {}, 'logger': <Logger msgd (DEBUG)>} [__main__.main]
[13:34:09] INFO     reading main input main.yml... [io.readMSGDInput]
[13:34:09] DEBUG    local variables:
{'fn': 'main.yml',
 'mode': '1',
 'msgd': <msgd._msgd.MSGD object at 0x000002126F6805B0>,
 'variant': 'ivabs'} [io.readMSGDInput]
[13:34:09] DEBUG    currect working directory:  [io.readMSGDInput]
[13:34:09] DEBUG    input file name: main [io.readMSGDInput]
[13:34:09] DEBUG    input file extension: .yml [io.readMSGDInput]
[13:34:09] DEBUG    output file name: main.out [io.readMSGDInput]
[13:34:09] DEBUG    variant: ivabs [io.readMSGDInput]
[13:34:09] DEBUG    version: 0.10 [io.readMSGDInput]
[13:34:09] DEBUG    _sg_assignments = [sg assignment of cs to region all (element)] [io.readInputStructure]
[13:34:09] INFO     reading mdao input... [_msgd.readMDAOEvalIn]
[13:34:09] DEBUG    mdao_tool: dakota [_msgd.readMDAOEvalIn]
[13:34:09] DEBUG    fn_dakota_params:  [_msgd.readMDAOEvalIn]
[13:34:09] DEBUG    fn_dakota_results:  [_msgd.readMDAOEvalIn]
[13:34:09] INFO     updating current design... [_msgd.updateData]
[13:34:09] DEBUG    [blade] updating parameters... [_structure.updateParameters]
[13:34:09] DEBUG    [blade] substituting parameters... [_structure.substituteParameters]
[13:34:09] DEBUG    subsituting parameters... [container.substituteParams]
[13:34:09] DEBUG    before substitution [container.substituteParams]
[13:34:09] DEBUG    inputs: None [container.substituteParams]
[13:34:09] DEBUG    params: {} [container.substituteParams]
[13:34:09] DEBUG    after substitution [container.substituteParams]
[13:34:09] DEBUG    inputs: None [container.substituteParams]
[13:34:09] INFO     [blade] loading structural mesh data... [_structure.loadStructureMesh]
[13:34:09] DEBUG    self._config = {} [_structure.loadStructureMesh]
[13:34:09] INFO     [blade] implementing domain transformations... [_structure.implementDomainTransformations]
[13:34:09] INFO     [blade] implementing distribution functions... [_structure.implementDistributionFunctions]
[13:34:09] DEBUG    updated design (global structure): [_msgd.updateData]
[13:34:09] DEBUG    {'name': 'blade', 'parameter': {}, 'dim': 1, 'builder': 'default', 'design': None} [_msgd.updateData]
[13:34:09] DEBUG    writing input file: curr_design.yml... [_msgd.writeInput]
[13:34:09] INFO     [main] discretizing the structure... [_msgd.discretize]
[13:34:09] DEBUG    structure model:
{'cs_assignment': [{'cs': 'cs', 'location': 'element', 'region': 'all'}],
 'design': {'builder': 'default',
            'design': None,
            'dim': 1,
            'name': 'blade',
            'parameter': {}},
 'model': {'config': {},
           'main_file': '',
           'prop_file': '',
           'tool': '',
           'tool_version': '',
           'type': ''},
 'name': 'blade',
 'parameter': {},
 'physics': 'elastic'} [_msgd.discretize]
[13:34:09] DEBUG    ================ [_structure.discretizeDesign]
[13:34:09] INFO     [blade] discretizing the design... [_structure.discretizeDesign]
[13:34:09] DEBUG     [_structure.calcParamsFromDistributions]
[13:34:09] INFO     [blade] calculating parameters from distributions... [_structure.calcParamsFromDistributions]
[13:34:09] DEBUG    getting entity sets... [_structure.getEntitySets]
[13:34:09] DEBUG    sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.getEntitySets]
[13:34:09] DEBUG    _point_sets = {} [_structure.getEntitySets]
[13:34:09] DEBUG    _cell_sets = {} [_structure.getEntitySets]
[13:34:09] DEBUG    _entity_sets = {'set1': {'type': 'any', 'items': [1]}} [_structure.calcParamsFromDistributions]
[13:34:09] DEBUG    ---------------- [_structure.discretizeDesign]
[13:34:09] DEBUG    all (element): [] [_structure.discretizeDesign]
[13:34:09] INFO     writing mesh data to file blade_mesh.msh... [_structure.writeMeshData]
[13:34:09] INFO     [main] going through steps... [_msgd.analyze]
[13:34:09] DEBUG    step config:
{'activate': True,
 'analysis': 'h',
 'step': 'cs analysis',
 'type': 'sg',
 'work_dir': 'cs'} [_msgd.analyze]
[13:34:09] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[13:34:09] INFO     [step: cs analysis] running cs analysis (h)... [sg.runH]
[13:34:09] DEBUG    [cs_set1] updating parameters... [_structure.updateParameters]
[13:34:09] DEBUG    [cs1] updating parameters... [_structure.updateParameters]
[13:34:09] DEBUG    [cs: cs_set1] running cs analysis... [sg.runH]
[13:34:09] DEBUG    [cs1] substituting parameters... [_structure.substituteParameters]
[13:34:09] DEBUG    subsituting parameters... [container.substituteParams]
[13:34:09] DEBUG    before substitution [container.substituteParams]
[13:34:09] DEBUG    inputs: {'base_file': 'cs_config_a.xml'} [container.substituteParams]
[13:34:09] DEBUG    params: {} [container.substituteParams]
[13:34:09] DEBUG    after substitution [container.substituteParams]
[13:34:09] DEBUG    inputs: {'base_file': 'cs_config_a.xml'} [container.substituteParams]
[13:34:09] DEBUG    building 2D SG (prevabs): cs_set1... [main.buildSGModel]
[13:34:09] DEBUG    building cs using prevabs... [main.buildSGFromPrevabs]
[13:34:09] DEBUG    fns_include = ['materials.xml', 'naca0012.dat'] [main.buildSGFromPrevabs]
[13:34:09] DEBUG    prevabs -i cs\cs_set1.xml -vabs -ver 4.1 -h [execu.run]
[13:34:10] DEBUG    _vabs._readHeader :: reading header... 
[13:34:10] DEBUG    _vabs._readMesh :: reading mesh... 
[13:34:10] DEBUG    _vabs._readMaterialRotationCombinations :: reading combinations of material and in-plane rotations... 
[13:34:10] DEBUG    _vabs._readMaterials :: reading materials... 
[13:34:10] DEBUG    execu.run :: VABS cs\cs_set1.sg 
[13:34:10] DEBUG    execu.run :: return code: 0 
[13:34:10] DEBUG    execu.run :: stdout:

 Homogenization for computing sectional properties

 The inputs are echoed in cs\cs_set1.sg.ech

 Finished reading inputs for the cross-sectional analysis.
 You can run VABS for   0 days....

 Finished constitutive modeling

 Cross-sectional properties can be found in  "cs\cs_set1.sg.K"

 Finished outputing constitutive modeling results

 VABS finished successfully

 VABS Runs for    0.171875000      Seconds.
 
[13:34:10] DEBUG    execu.run :: stderr: 
[13:34:10] DEBUG    execu.run :: VABS finished successfully 
[13:34:10] DEBUG    sg_model.data_format = vabs [sg.runH]
[13:34:10] DEBUG    step config:
{'activate': True,
 'analysis': 'd',
 'step': 'recovery',
 'type': 'sg',
 'work_dir': 'cs'} [_msgd.analyze]
[13:34:10] INFO     importing pre/post processing functions... [_analysis.importPrePostProcFuncs]
[13:34:10] INFO     [step: recovery] running cs analysis (d)... [sg.runDF]
[13:34:10] INFO     reading sectional responses... [sg.readSectionResponse]
[13:34:10] DEBUG    subsituting parameters... [container.substituteParams]
[13:34:10] DEBUG    before substitution [container.substituteParams]
[13:34:10] DEBUG    inputs: {'displacement': [1.14e-05, 1.92e-06, 0.002], 'load': [-6.960731065297227e-05, 0.00044668538601352393, 0.015870420038137607, 0.000229788237956122, 8.13244480333271e-06, -1.4022416078181857e-06]} [container.substituteParams]
[13:34:10] DEBUG    params: {} [container.substituteParams]
[13:34:10] DEBUG    after substitution [container.substituteParams]
[13:34:10] DEBUG    inputs: {'displacement': [1.14e-05, 1.92e-06, 0.002], 'load': [-6.960731065297227e-05, 0.00044668538601352393, 0.015870420038137607, 0.000229788237956122, 8.13244480333271e-06, -1.4022416078181857e-06]} [container.substituteParams]
[13:34:10] DEBUG    
_state_locase = state loc case:
locations:
cases:
  state case
case:
states:
  state: displacement ([])
  field data: 1 element data
    1: [1.14e-05, 1.92e-06, 0.002]
  state: load ([])
  field data: 1 element data
    1: [-6.960731065297227e-05, 0.00044668538601352393, 0.015870420038137607, 0.000229788237956122, 8.13244480333271e-06, -1.4022416078181857e-06] [sg.readSectionResponse]
[13:34:10] DEBUG    entity_id = 1, sg_model_name = cs_set1 [sg.runDF]
[13:34:10] DEBUG    __set_name = set1, _sg_model.name = cs_set1 [sg.runDF]
[13:34:10] DEBUG    ---------------------------------------- [sg.runDF]
[13:34:10] DEBUG    loc_id = 1, entity_id = 1, sg_model_name = cs_set1 [sg.runDF]
[13:34:10] DEBUG    [cs: cs_set1] running cs analysis... [sg.runDF]
[13:34:10] DEBUG    ---------------------------------------- [sg.runDF]
[13:34:10] DEBUG    loc: [{'structure': 'blade', 'loc_id': 1, 'locs': [1]}] [sg.runDF]
[13:34:10] DEBUG    .................... [sg.runDF]
[13:34:10] DEBUG    case_name = case1 [sg.runDF]
[13:34:10] DEBUG    _state_case = state case
case:
states:
  state: load ([])
  point data: [-6.960731065297227e-05, 0.00044668538601352393, 0.015870420038137607, 0.000229788237956122, 8.13244480333271e-06, -1.4022416078181857e-06] [sg.runDF]
[13:34:10] DEBUG    _str_load =   -6.960731065e-05     4.466853860e-04     1.587042004e-02     2.297882380e-04     8.132444803e-06    -1.402241608e-06 [sg.writeMacroStateToFile]
[13:34:10] DEBUG    building 2D SG (prevabs): cs_set1... [main.buildSGModel]
[13:34:10] DEBUG    building cs using prevabs... [main.buildSGFromPrevabs]
[13:34:10] DEBUG    fns_include = ['materials.xml', 'naca0012.dat'] [main.buildSGFromPrevabs]
[13:34:10] DEBUG    prevabs -i cs\cs_set1.xml -vabs -ver 4.1 -d [execu.run]
[13:34:10] DEBUG    copying sg files to cs\local\blade\loc1_1\cs_set1\case1... [sg.runDF]
[13:34:10] DEBUG    execu.run :: VABS cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg 2 
[13:34:13] DEBUG    execu.run :: return code: 0 
[13:34:13] DEBUG    execu.run :: stdout:

 Recovery for linear beam theory

 The inputs are echoed in cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.ech

 Finished reading inputs for the cross-sectional analysis.
 You can run VABS for   0 days....

 Finished recovery
 Recovered 3D displacement results are in cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.U
 Recovered 3D strain results are in cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.E  cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.EM  cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.EN  cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.EMN
 Recovered 3D stress results are in cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.S  cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.SM  cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.SN  cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.SMN
 Recovered average 3D stresses/strains at Gaussian points within each element are in cs\local\blade\loc1_1\cs_set1\case1\cs_set1.sg.ELE

 Finished outputing recovery results

 VABS finished successfully

 VABS Runs for     1.48437500      Seconds.
 
[13:34:13] DEBUG    execu.run :: stderr: 
[13:34:13] DEBUG    execu.run :: VABS finished successfully 
[13:34:13] INFO     [main] writing output to file ... [_msgd.writeAnalysisOut]
