set1:
  name: mainsg_set1
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - &id001 !!python/object/apply:numpy.dtype
        args:
        - f8
        - false
        - true
        state: !!python/tuple
        - 3
        - <
        - null
        - null
        - null
        - -1
        - -1
        - 0
      - !!binary |
        y8oGBAAA+D8=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set2:
  name: mainsg_set2
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        y8oGBAAAEkA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set3:
  name: mainsg_set3
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        eHgZDwAAHkA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set4:
  name: mainsg_set4
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        SEgPCQAAJUA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set5:
  name: mainsg_set5
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        GBgFAwAAK0A=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set6:
  name: mainsg_set6
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        MDAKBgCAMEA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set7:
  name: mainsg_set7
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        GBgFAwCAM0A=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set8:
  name: mainsg_set8
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        AAAAAACANkA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set9:
  name: mainsg_set9
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        6Of6/P9/OUA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
set10:
  name: mainsg_set10
  parameter: {}
  model:
    type: pl1
    tool: SwiftComp
    tool_version: '2.1'
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: lv1_layup
    parameter:
      a1: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        0M/1+f9/PEA=
      a2: 0
      a3: 0
    dim: 1
    builder: default
    design:
      symmetry: 1
      layers:
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a1
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a2
      - material: m2
        ply_thickness: 0.00015
        number_of_plies: 1
        in-plane_orientation: a3
