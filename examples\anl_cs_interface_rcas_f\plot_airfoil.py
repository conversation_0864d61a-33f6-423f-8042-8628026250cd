#!/home/<USER>/anaconda3/envs/py37/bin/python3

import json
import matplotlib.pyplot as plt
import pandas as pd

af = 'sc1095'
xy = 0

if xy == 1:
    ext = '.xy'
else:
    ext = '.dat'

filen = af + ext

if xy == 1:
    df = pd.read_csv(filen, delim_whitespace=True, header=0, skiprows=1)
else:
    df = pd.read_csv(filen, delim_whitespace=True, header=0)

df.columns = ['x', 'y']

fig, ax = plt.subplots(1, 1, figsize=(10, 7))
ax.plot(df['x'], df['y'],'-b')
ax.set_xlabel('x')
ax.set_ylabel('y')
#ax[1].legend(loc='upper right')
plt.axis('equal')
plt.savefig(af + '.png')
plt.show()
plt.close()


