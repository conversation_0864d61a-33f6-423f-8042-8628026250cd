# Configuration file for the Sphinx documentation builder.
#
# This file only contains a selection of the most common options. For a full
# list see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Path setup --------------------------------------------------------------

# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.
#
import os
import sys
import logging

logging.basicConfig(level=logging.DEBUG)

sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath(os.path.join('..', '..', 'scripts')))
# sys.path.insert(0, os.path.abspath(os.path.join('..', '..', 'scripts', 'msgd')))
logging.debug(f'sys.path\n  {sys.path}')

from _script.autodoc import generate_rst_files


def generate_keyword_rst_files(app):
    """Trigger the rst file generation before building the docs."""
    print("Generating keyword .rst files...")

    try:
        generate_rst_files()
    except Exception as e:
        raise RuntimeError(f"Error during RST generation: {e}")


def setup(app):
    """Setup the Sphinx extension."""
    app.connect('builder-inited', generate_keyword_rst_files)




logging.debug(f'dir(tags)\n  {dir(tags)}')
if (not tags.has('ivabs')) and (not tags.has('dev')):
    tags.add('msg')

logging.debug(f'tags.tags\n  {tags.tags}')

# -- Project information -----------------------------------------------------

year = '2024'

if tags.has('ivabs'):
    project = u'iVABS'
    copyright = f'{year}, Multiscale Structural Mechanics Group, Purdue University'
    author = u'Su Tian, Haodong Du, Fei Tao and Wenbin Yu'
    version = u'0.10'
    release = u'0.10'
elif tags.has('dev'):
    project = 'msg-design_dev'
    copyright = f'{year}, su tian'
    author = 'su tian'
else:
    project = 'msg-design'
    copyright = f'{year}, su tian'
    author = 'su tian'


# -- General configuration ---------------------------------------------------

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.autosummary',
    'sphinx.ext.mathjax',
    'sphinx.ext.napoleon',
    'myst_parser',
    'sphinx_design',
    # 'sphinx_gallery.gen_gallery',
    'sphinx-prompt',
    'sphinx_copybutton',
]

source_suffix = {
    '.rst': 'restructuredtext',
    '.md': 'markdown',
}

# Add any paths that contain templates here, relative to this directory.
templates_path = ['_templates']

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This pattern also affects html_static_path and html_extra_path.
exclude_patterns = [
    '_exclude/*'
]
include_patterns = ['**',]

# root_doc = 'index'

if tags.has('ivabs'):
    root_doc = 'index'
    exclude_patterns.extend([
        # 'index.rst',
        'main_msg.rst',
        'main_dev.rst',
        'dev/*',
    ])
    include_patterns.extend([
        '../../examples/*'
    ])
    # rst_epilog = """
    rst_prolog = '\n'.join([
    ".. include:: /replace_ivabs.txt",
    ".. include:: /replace.txt",
    ])

elif tags.has('dev'):
    root_doc = 'index'
    exclude_patterns.extend([
        # 'index.rst',
        'main_msg.rst',
        'main_ivabs.rst',
        'examples/*',
        'guide/*',
        'ref/beam_properties.rst',
        'ref/index.rst',
        'ref/gebt/*',
        'ref/keyword/*',
        'ref/prevabs/*',
        'ref/sg_templates/*',
        'start/index.rst',
        'start/install/*',
        'start/ivabs/*',
    ])

else:
    root_doc = 'index'
    exclude_patterns.extend([
        # 'index.rst',
        'main_ivabs.rst',
        'main_dev.rst',
        'dev/*',
        'ivabs/*'
    ])
    rst_prolog = """
    .. |msgd| replace:: MSGD
    .. |sg| replace:: SG
    .. |sg_key| replace:: ``sg``
    .. |structure gene| replace:: structure gene
    .. |structure genes| replace:: structure genes
    .. include:: /replace.txt
    """

print(f'exclude_patterns\n  {exclude_patterns}')
print(f'include_patterns\n  {include_patterns}')

language = 'en'


# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
#
# html_theme = 'sphinx_book_theme'
html_theme = 'pydata_sphinx_theme'

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
html_static_path = ['_static']

html_theme_options = {
    'logo': {
        'text': 'iVABS',
        "image_light": os.path.abspath("_static/ivabs.ico"),
        "image_dark": os.path.abspath("_static/ivabs.ico"),
    },
    'show_nav_level': 2,
    # "path_to_docs": "doc/source",
    # 'use_edit_page_button': True,
    # "use_repository_button": True,
    # "use_issues_button": True,
    # 'collapse_navigation': True,
    'navigation_depth': 4,
    "announcement": "Documentation is under construction. Please use examples as the main reference.",
}
if tags.has('ivabs'):
    html_logo = os.path.abspath("_static/ivabs.ico")
#     html_theme_options["repository_url"] = "https://github.com/wenbinyugroup/ivabs"
#     html_theme_options["repository_branch"] = "main"




# -- Options for LaTeX output ---------------------------------------------

latex_elements = {
    # The paper size ('letterpaper' or 'a4paper').
    #
    'papersize': 'letterpaper',

    # The font size ('10pt', '11pt' or '12pt').
    #
    # 'pointsize': '10pt',

    # Additional stuff for the LaTeX preamble.
    #
    # 'preamble': '',

    # Latex figure (float) alignment
    #
    # 'figure_align': 'htbp',
}

# Grouping the document tree into LaTeX files. List of tuples
# (source start file, target name, title,
#  author, documentclass [howto, manual, or own class]).
latex_documents = [
    (root_doc, 'test.tex', 'test Documentation',
     'test', 'manual'),
]

# The name of an image file (relative to this directory) to place at the top of
# the title page.
#
# latex_logo = None

# If true, show page references after internal links.
#
# latex_show_pagerefs = False

# If true, show URL addresses after external links.
#
# latex_show_urls = False

# Documents to append as an appendix to all manuals.
#
# latex_appendices = []

# If false, no module index is generated.
#
# latex_domain_indices = True




# -- Extension configuration -------------------------------------------------
if tags.has('dev'):
    autodoc_member_order = 'groupwise'
    autosummary_generate = True
elif tags.has('msg'):
    autodoc_member_order = 'groupwise'
    autosummary_generate = True

numfig = True
numfig_format = {
    'figure': 'Figure %s',
    'table': 'Table %s',
    'code-block': 'Listing %s',
    'section': 'Section'}

myst_enable_extensions = ["colon_fence",]

# sphinx_gallery_conf = {
#      'examples_dirs': '../../examples',   # path to your example scripts
#      'gallery_dirs': 'auto_examples',  # path to where to save gallery generated output
# }

if tags.has('ivabs'):
    # comments_config = {
    #     "utterances": {
    #         "repo": "wenbinyugroup/ivabs",
    #         "optional": "config",
    #     }
    # }

    html_context = {
        # "display_github": True, # Integrate GitHub
        "github_user": "wenbinyugroup", # Username
        "github_repo": "ivabs", # Repo name
        "github_version": "main", # Version
        # "conf_py_path": "/docs/source/", # Path in the checkout to the docs root
    }

    # sitemap_url_scheme = "{link}"
    # html_baseurl = 'https://wenbinyugroup.github.io/ivabs/'
