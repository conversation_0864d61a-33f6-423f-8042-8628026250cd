from __future__ import annotations

from typing import List, Dict, Optional, Union, Any

import sgio
from pydantic import BaseModel, Field


class LocationInfo(BaseModel):
    """Information about a location in the structure."""
    structure: str
    loc_id: int
    locs: List[int]


# class StateLocCase(BaseModel):
#     """Locations and cases of global/local states.

#     Parameters
#     ----------
#     locs: List[LocationInfo]
#         Track of dehomogenization location.
#         Start from the global structure and end at the upper level SG.
#     state_cases: List[sgio.StateCase]
#         List of state cases.
#     """
#     locs: List[LocationInfo] = Field(default_factory=list)
#     state_cases: List[sgio.StateCase] = Field(default_factory=list)

#     def __repr__(self) -> str:
#         lines = [
#             'state loc case:',
#             f'locations:',
#         ]
#         for loc in self.locs:
#             lines.append(f"  {loc.structure} {','.join(map(str, loc.locs))}")

#         lines.append('cases:')
#         for sc in self.state_cases:
#             lines.append(f'  {str(sc)}')

#         return '\n'.join(lines)

#     def to_dict(self) -> Dict[str, Any]:
#         """Convert the object to a dictionary."""
#         return {
#             'locs': [loc.model_dump() for loc in self.locs],
#             'state_cases': [sc.to_dict() for sc in self.state_cases]
#         }

#     def get_state_case(self, case: Dict) -> Optional[sgio.StateCase]:
#         for sc in self.state_cases:
#             if sc.case == case:
#                 return sc
#         return None

#     def get_state_cases(self, state_name: str, group_by: str = 'case') -> List[Dict]:
#         """Get state cases of a given state name."""
#         output = []
#         for sc in self.state_cases:
#             output.append({
#                 'case': sc.case,
#                 'state': sc.get_state(state_name).data
#             })
#         return output

#     def add_state_case(
#         self, 
#         state_case: Optional[sgio.StateCase] = None,
#         case_id: Optional[Any] = None, 
#         case_spec: Optional[Dict] = None,
#         state_name: Optional[str] = None, 
#         state_data: Optional[List] = None
#     ) -> None:
#         self.state_cases.append(state_case)

#     def add_state_to_case(
#         self, case:dict,
#         state_name:str, state_data:list, state_loc=None, loc_type=''
#         ):
#         _sc = self.get_state_case(case)
#         if _sc is None:
#             _sc = sgio.StateCase(case=case, states={})
#             self.state_cases.append(_sc)
#         _sc.add_state(
#             name=state_name,
#             data=state_data,
#             entity_id=state_loc,
#             loc_type=loc_type
#             )

#     def update_state_case(
#         self, entity_id=None, state_name=None,
#         use_state_cases=[], use_state_name=None
#         ):
#         # print('\n\nupdating state cases...')

#         if use_state_name is None:
#             use_state_name = state_name

#         for _sc, _sc_new in zip(self.state_cases, use_state_cases):
#             if not entity_id is None:
#                 _value = _sc_new.states[use_state_name].data[0]

#                 _sc.add_state(
#                     name=state_name,
#                     entity_id=entity_id,
#                     data=_value,
#                 )


#     def get_case_list(self):
#         case_list = []
#         for _i, _sc in enumerate(self.state_cases):
#             _case = {
#                 'id': _i+1,
#             }
#             _case.update(_sc.case)
#             case_list.append(_case)
#         return case_list

#     def get_dir_list(self):
#         dir_list = []
#         for _loc in self._locs:
#             _str_locs = '_'.join(list(map(str, _loc['locs'])))
#             dir_list.extend([
#                 _loc['structure'],
#                 f"loc{_loc['loc_id']}_{_str_locs}"
#                 ])
#         return dir_list

#     def get_state_cases_at_locs(self, locs, state_name=None):
#         """
#         A function returning state cases at the given locations.

#         Parameters
#         ----------
#         locs:list
#             List of locations.

#         Returns
#         -------
#         list
#             List of state cases at the given entities.
#             ..  code-block::

#                 [
#                     {
#                         'locs': [],
#                         'state_cases': []
#                     },
#                     ...
#                 ]
#         """
#         _state_cases_at_locs = []

#         if not isinstance(locs, list):
#             _locs = [locs,]
#         else:
#             _locs = locs

#         for _loc in _locs:
#             _state_cases_at_loc = {
#                 'locs': [_loc,],
#                 'state_cases': []
#             }
#             for _sc in self.state_cases:
#                 _sc_at_locs = _sc.at([_loc,], state_name=state_name)
#                 if not _sc_at_locs is None:
#                     _state_cases_at_loc['state_cases'].append(_sc_at_locs)

#             if len(_state_cases_at_loc['state_cases']) > 0:
#                 _state_cases_at_locs.append(_state_cases_at_loc)

#         if len(_state_cases_at_locs) == 0:
#             return None

#         if not isinstance(locs, list):
#             return _state_cases_at_locs[0]
#         else:
#             return _state_cases_at_locs





class StateLocCase():
    """Locations and cases of global/local states.

    Parameters
    ----------
    locs:list
        Track of dehomogenization location.
        Start from the global structure and end at the upper level SG.

        ..  code-block::

            [
                {
                    'structure': 'name',
                    'loc_id': 1,
                    'locs': [1, 2, 3, ...]
                },
                ...
            ]

    state_cases:list
        List of state cases.
    """
    def __init__(
        self, locs:list=[],
        state_cases:list[sgio.StateCase]=[]):
        """
        """
        self._locs:list = locs
        self.state_cases:list[sgio.StateCase] = state_cases

    @property
    def locs(self): return self._locs
    # @property
    # def state_cases(self): return self._state_cases

    def __repr__(self):
        lines = [
            'state loc case:',
            f'locations:',
        ]
        for _loc in self._locs:
            lines.append(f'  {_loc["structure"]} {",".join(list(map(str, _loc["locs"])))}')

        lines.append('cases:')
        for _sc in self.state_cases:
            lines.append(f'  {str(_sc)}')

        return '\n'.join(lines)

    def toDictionary(self, **kwargs) -> dict:
        """Convert the object to a dictionary.
        """
        _dict = {
            'locs': self._locs,
            'state_cases': [sc.toDictionary() for sc in self.state_cases]
        }
        return _dict

    def getStateCase(self, case:dict) -> sgio.StateCase:
        for _sc in self.state_cases:
            if _sc.case == case:
                return _sc
        return None

    def getStateCases(self, state_name, group_by='case') -> list:
        """Get state cases of a given state name.

        Parameters
        ----------
        state_name:str
            Name of the state.

        Returns
        -------
        list
            List of state cases.

            ..  code-block::

                [
                    {
                        'case': Case,
                        'state': State
                    },
                    ...
                ]
        """
        output = []

        for _sc in self.state_cases:
            output.append({
                'case': _sc.case,
                'state': _sc.getState(state_name).data
                })

        return output

    def addStateCase(
        self, state_case:sgio.StateCase=None,
        case_id=None, case_spec=None,
        state_name=None, state_data=None
        ):
        self.state_cases.append(state_case)

    def addStateToCase(
        self, case:dict,
        state_name:str, state_data:list, state_loc=None, loc_type=''
        ):
        _sc = self.getStateCase(case)
        if _sc is None:
            _sc = sgio.StateCase(case=case, states={})
            self.state_cases.append(_sc)
        _sc.addState(
            name=state_name,
            data=state_data,
            entity_id=state_loc,
            loc_type=loc_type
            )

    def updateStateCase(
        self, entity_id=None, state_name=None,
        use_state_cases=[], use_state_name=None
        ):
        # print('\n\nupdating state cases...')

        if use_state_name is None:
            use_state_name = state_name

        for _sc, _sc_new in zip(self.state_cases, use_state_cases):
            if not entity_id is None:
                _value = _sc_new.states[use_state_name].data[0]

                _sc.addState(
                    name=state_name,
                    entity_id=entity_id,
                    data=_value,
                )


    def getCaseList(self):
        case_list = []
        for _i, _sc in enumerate(self.state_cases):
            _case = {
                'id': _i+1,
            }
            _case.update(_sc.case)
            case_list.append(_case)
        return case_list

    def getDirList(self):
        dir_list = []
        for _loc in self._locs:
            _str_locs = '_'.join(list(map(str, _loc['locs'])))
            dir_list.extend([
                _loc['structure'],
                f"loc{_loc['loc_id']}_{_str_locs}"
                ])
        return dir_list

    def getStateCasesAtLocs(self, locs, state_name=None):
        """
        A function returning state cases at the given locations.

        Parameters
        ----------
        locs:list
            List of locations.

        Returns
        -------
        list
            List of state cases at the given entities.
            ..  code-block::

                [
                    {
                        'locs': [],
                        'state_cases': []
                    },
                    ...
                ]
        """
        _state_cases_at_locs = []

        if not isinstance(locs, list):
            _locs = [locs,]
        else:
            _locs = locs

        for _loc in _locs:
            _state_cases_at_loc = {
                'locs': [_loc,],
                'state_cases': []
            }
            for _sc in self.state_cases:
                _sc_at_locs = _sc.at([_loc,], state_name=state_name)
                if not _sc_at_locs is None:
                    _state_cases_at_loc['state_cases'].append(_sc_at_locs)

            if len(_state_cases_at_loc['state_cases']) > 0:
                _state_cases_at_locs.append(_state_cases_at_loc)

        if len(_state_cases_at_locs) == 0:
            return None

        if not isinstance(locs, list):
            return _state_cases_at_locs[0]
        else:
            return _state_cases_at_locs


