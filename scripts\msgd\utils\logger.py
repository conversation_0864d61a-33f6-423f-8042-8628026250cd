import logging

from rich.logging import <PERSON><PERSON><PERSON><PERSON>

# import msgd._global as GL<PERSON><PERSON>L
logger = logging.getLogger(__name__)


def initLogger(name, cout_level='INFO', fout_level='INFO', filename='log.txt'):
    """Initialization of a logger.

    Parameters
    ----------
    name : str
        Name of the logger.
    cout_level : {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}, optional
        Output level of logs to the screen, by default 'INFO'
    fout_level : {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}, optional
        Output level of logs to a file, by default 'INFO'
    filename : str, optional
        Name of the log file, by default 'log.txt'

    Returns
    -------
    :obj:`logging.Logger`
        A logger object.
    """
    logger = logging.getLogger(name)
    logger.setLevel('DEBUG')

    # ch = logging.StreamHandler()
    ch = RichHandler()
    ch.setLevel(cout_level.upper())
    # ch.setFormatter(CustomFormatter())
    cf = logging.Formatter(
        fmt='{message:s}',
        style='{',
        datefmt='[%X]'
    )
    ch.setFormatter(cf)
    logger.addHandler(ch)


    fh = logging.FileHandler(filename)
    # fh = RichHandler()
    fh.setLevel(fout_level.upper())
    ff = logging.Formatter(
        fmt='[{asctime}] {levelname:8s} {message} [{module}.{funcName}]',
        datefmt='%H:%M:%S', style='{'
    )
    # ff = logging.Formatter(
    #     fmt='{message:s}',
    #     style='{',
    #     datefmt='[%X]'
    # )
    fh.setFormatter(ff)
    logger.addHandler(fh)

    logger.propagate = False


    return logger




# def configLogger(logger, cout_level, fout_level, filename):
#     """Initialization of a logger.

#     Parameters
#     ----------
#     logger : Logger object
#         Logger.
#     cout_level : {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}, optional
#         Output level of logs to the screen, by default 'INFO'
#     fout_level : {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}, optional
#         Output level of logs to a file, by default 'INFO'
#     filename : str, optional
#         Name of the log file, by default 'log.txt'

#     Returns
#     -------
#     :obj:`logging.Logger`
#         A logger object.
#     """
#     logger.setLevel('DEBUG')

#     ch = logging.StreamHandler()
#     ch.setLevel(cout_level.upper())
#     ch.setFormatter(CustomFormatter())
#     logger.addHandler(ch)


#     fh = logging.FileHandler(filename)
#     fh.setLevel(fout_level.upper())
#     ff = logging.Formatter(
#         fmt='{levelname:8s} [{asctime}] {module}.{funcName} :: {message} ',
#         datefmt='%Y-%m-%d %H:%M:%S', style='{'
#     )
#     fh.setFormatter(ff)
#     logger.addHandler(fh)


#     return logger




class CustomFormatter(logging.Formatter):

    grey = "\x1b[38;20m"
    yellow = "\x1b[33;20m"
    red = "\x1b[31;20m"
    bold_red = "\x1b[31;1m"
    reset = "\x1b[0m"
    # format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)"
    format = '[{levelname}] {message}'

    FORMATS = {
        logging.DEBUG: grey + format + reset,
        logging.INFO: grey + format + reset,
        logging.WARNING: yellow + format + reset,
        logging.ERROR: red + format + reset,
        logging.CRITICAL: bold_red + format + reset
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, style='{')
        return formatter.format(record)




def logDictionary(d:dict):
    _list = ['local variables:',]
    for _k, _v in d.items():
        _list.append(f'{_k} = {_v}')
    logger.debug('\n'.join(_list))

