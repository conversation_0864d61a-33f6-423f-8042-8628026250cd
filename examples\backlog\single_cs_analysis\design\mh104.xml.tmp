<cross_section name="mh104" format="1">


  <include>
    <!-- <baseline>baselines</baseline> -->
    <!-- <layup>layups</layup> -->
    <material>materials</material>
  </include>


  <analysis>
    <model>1</model>
  </analysis>


  <general>
    <translate>-0.475  0</translate>
    <scale>1.9</scale>
    <mesh_size>{mesh_size}</mesh_size>
    <element_type>{mesh_type}</element_type>
    <track_interface>0</track_interface>
  </general>


  <baselines>

    <line name="airfoil" type="airfoil">
      <points data="file" format="1" direction="-1" header="1">mh104.dat</points>
      <flip>0</flip>
      <!-- <reverse>1</reverse> -->
    </line>

    <point name="p2t" on="airfoil" by="x2" which="top">0.0016</point>
    <point name="p3t" on="airfoil" by="x2" which="top">0.0041</point>
    <point name="p4t" on="airfoil" by="x2" which="top">0.1147</point>
    <point name="p5t" on="airfoil" by="x2" which="top">0.5366</point>
    <point name="p2b" on="airfoil" by="x2" which="bottom">0.0016</point>
    <point name="p3b" on="airfoil" by="x2" which="bottom">0.0041</point>
    <point name="p4b" on="airfoil" by="x2" which="bottom">0.1147</point>
    <point name="p5b" on="airfoil" by="x2" which="bottom">0.5366</point>

    <point name="pweb1_right">0.161 0</point>
    <point name="pweb2_right">0.511 0</point>

    <line name="line_le">
      <points>p2t:p2b</points>
    </line>
    <line name="line_seg2_top">
      <points>p3t:p2t</points>
    </line>
    <line name="line_seg3_top">
      <points>p4t:p3t</points>
    </line>
    <line name="line_seg4_top">
      <points>p5t:p4t</points>
    </line>
    <line name="line_seg2_bottom">
      <points>p2b:p3b</points>
    </line>
    <line name="line_seg3_bottom">
      <points>p3b:p4b</points>
    </line>
    <line name="line_seg4_bottom">
      <points>p4b:p5b</points>
    </line>
    <line name="line_te">
      <points>p5b:p5t</points>
    </line>

    <baseline name="line_web1">
      <point>pweb1_right</point>
      <angle>90</angle>
    </baseline>
    <baseline name="line_web2">
      <point>pweb2_right</point>
      <angle>90</angle>
    </baseline>

  </baselines>


  <layups>
    <layup name="layup1">
      <layer lamina="la_gelcoat_0000381">0</layer>
      <layer lamina="la_nexus_000051">0</layer>
      <layer lamina="la_db_frp_000053">20:18</layer>
    </layup>
    <layup name="layup2">
      <layer lamina="la_gelcoat_0000381">0</layer>
      <layer lamina="la_nexus_000051">0</layer>
      <layer lamina="la_db_frp_000053">20:18</layer>
    </layup>
    <layup name="layup3">
      <layer lamina="la_gelcoat_0000381">0</layer>
      <layer lamina="la_nexus_000051">0</layer>
      <layer lamina="la_db_frp_000053">20:33</layer>
    </layup>
    <layup name="layup4">
      <layer lamina="la_gelcoat_0000381">0</layer>
      <layer lamina="la_nexus_000051">0</layer>
      <layer lamina="la_db_frp_000053">20:17</layer>
      <layer lamina="la_ud_frp_000053">30:38</layer>
      <layer lamina="la_balsa_0003125">0</layer>
      <layer lamina="la_ud_frp_000053">30:37</layer>
      <layer lamina="la_db_frp_000053">20:16</layer>
    </layup>
    <layup name="layup5">
      <layer lamina="la_gelcoat_0000381">0</layer>
      <layer lamina="la_nexus_000051">0</layer>
      <layer lamina="la_db_frp_000053">20:17</layer>
      <layer lamina="la_balsa_0003125">0</layer>
      <layer lamina="la_db_frp_000053">0:16</layer>
    </layup>
    <layup name="layup_web">
      <layer lamina="la_ud_frp_000053">0:38</layer>
      <layer lamina="la_balsa_0003125">0</layer>
      <layer lamina="la_ud_frp_000053">0:38</layer>
    </layup>
  </layups>


  <component name="surface">
    <segment>
      <baseline>line_le</baseline>
      <layup>layup1</layup>
    </segment>
    <segment>
      <baseline>line_seg2_top</baseline>
      <layup>layup2</layup>
    </segment>
    <segment>
      <baseline>line_seg3_top</baseline>
      <layup>layup3</layup>
    </segment>
    <segment>
      <baseline>line_seg4_top</baseline>
      <layup>layup4</layup>
    </segment>
    <segment>
      <baseline>line_seg2_bottom</baseline>
      <layup>layup2</layup>
    </segment>
    <segment>
      <baseline>line_seg3_bottom</baseline>
      <layup>layup3</layup>
    </segment>
    <segment>
      <baseline>line_seg4_bottom</baseline>
      <layup>layup4</layup>
    </segment>
    <segment>
      <baseline>line_te</baseline>
      <layup>layup5</layup>
    </segment>
  </component>

  <component name="web1" depend="surface">
    <segment>
      <baseline>line_web1</baseline>
      <layup>layup_web</layup>
    </segment>
  </component>

  <component name="web2" depend="surface">
    <segment>
      <baseline>line_web2</baseline>
      <layup>layup_web</layup>
    </segment>
  </component>


</cross_section>
