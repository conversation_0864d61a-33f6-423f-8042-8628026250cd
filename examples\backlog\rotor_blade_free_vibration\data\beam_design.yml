setting:
  analysis_type: 3
  max_iteration: 100
  num_steps: 1
  num_eigens: 20

point:
  - id: 1
    coordinates: [0, 0, 0]
  - id: 2
    coordinates: [26.83, 0, 0]

member:
  - id: 1
    points: [1, 2]
    division: 32

set:
  - name: "root"
    type: 'point'
    objects: [1,]
  - name: "tip"
    type: 'point'
    objects: [2,]
  - name: 'segment1'
    type: 'member'
    objects: [1,]


condition:
  - region: 'root'
    dofs: [1, 2, 3, 4, 5, 6]
    values: [0, 0, 0, 0, 0, 0]
  - region: 'tip'
    dofs: [7, 8, 9, 10, 11, 12]
    values: [0, 0, 0, 0, 0, 0]


section_assignment:
  - region: "segment1"
    section: "cs1"

# section_property:
#   - id: 1
#     name: "cs"
#     property:
#       md1:
#         compliance_t: [
#           [1.9230769231E-08, 0, 0, 0, 0, 0],
#           [0, 0, 0, 0, 0, 0],
#           [0, 0, 0, 0, 0, 0],
#           [0, 0, 0, 2.0689655172E-05, 0, 0],
#           [0, 0, 0, 0, 5.7692307692E-06, 0],
#           [0, 0, 0, 0, 0, 2.3076923077E-05]
#         ]


time_functions:
  - id: 1
    type: 0
    entry:
      - [0, 0]
      - [1, 1]
