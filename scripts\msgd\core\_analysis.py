import logging
# from ._structure import StructureModel, DataBase
# from msgd.builder.main import buildSGModel

# import sgio

# from msgd.analysis.sg.core import SGAnalysis

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)




class AnalysisStep():
    """Base class for analysis steps.

    Parameters
    ----------
    name : str, optional
        Name of the step. Default is ''.
    activate : bool, optional
        Activate the step. Default is True.
    parameters : dict, optional
        Parameters for the step. Default is {}.
    setting : dict, optional
        Setting for the step. Default is {}.
    step_args : list, optional
        Arguments for the step. Default is [].
    step_kwargs : dict, optional
        Keyword arguments for the step. Default is {}.
    output_config : dict, optional
        Output configuration. Default is None.
    """
    step_type = ''
    def __init__(
        self, name='', activate:bool=True, parameters:dict={},
        setting:dict={}, step_args:list=[], step_kwargs:dict={},
        prepros=[], postpros=[],
        output_config={}):
        self._name = name
        self._activate = activate
        self._parameters = parameters
        self._setting = setting
        self._args = step_args
        self._kwargs = step_kwargs

        self._prepros = prepros
        self._postpros = postpros
        """
        [
            {
                'module': 'module_name',
                'function': 'function_name',
                'args': [arg1, arg2, ...]
                'kwargs': {
                    'kw1': arg1,
                    'kw2': arg2,
                    ...
                }
            },
            ...
        ]
        """

        self._prepro_funcs = []
        self._postpro_funcs = []

        self._output_config = output_config
        self._step_output = {}

    def toDictionary(self):
        """Convert the object to a dictionary.
        """
        _dict = {
            'step': self._name,
            'activate': self._activate,
            # 'setting': self._setting,
            # 'parameter': self._parameters,
            # 'args': self._args,
            # 'kwargs': self._kwargs
        }

        if self._setting:
            _dict['setting'] = self._setting
        if self._parameters:
            _dict['parameter'] = self._parameters
        if self._args:
            _dict['args'] = self._args
        if self._kwargs:
            _dict['kwargs'] = self._kwargs
        if self._output_config:
            _dict['output'] = self._output_config

        return _dict

    @property
    def name(self): return self._name
    @property
    def step_type(self): return type(self).step_type
    @property
    def activate(self): return self._activate
    @property
    def parameters(self): return self._parameters
    @property
    def setting(self): return self._setting
    @property
    def step_output(self): return self._step_output
    @property
    def step_args(self): return self._args
    @property
    def step_kwargs(self): return self._kwargs

    def importPrePostProcFuncs(self):
        """
        """
        logger.info(f'importing pre/post processing functions...')

        from msgd.utils import importFunction

        for _prepro in self._prepros:
            _func = importFunction(_prepro['module'], _prepro['function'])
            self._prepro_funcs.append(_func)

        for _postpro in self._postpros:
            _func = importFunction(_postpro['module'], _postpro['function'])
            self._postpro_funcs.append(_func)


    def run(self, **kwargs):
        ...

