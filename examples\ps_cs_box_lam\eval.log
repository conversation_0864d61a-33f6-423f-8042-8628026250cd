CRITICAL [2024-02-18 17:28:12] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:28:57] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:31:21] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:31:41] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:31:41] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:32:34] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:32:34] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:32:50] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:32:50] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:33:14] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:33:14] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:33:47] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:33:47] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:40:43] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:40:43] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:45:22] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:45:22] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:47:28] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:47:28] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-18 17:48:44] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-18 17:48:44] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-19 10:00:17] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-19 10:00:17] dkt.runDakota :: calling: dakota -i main.dakota 
CRITICAL [2024-02-19 10:14:43] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-19 10:14:43] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-02-23 15:35:03] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-23 15:35:03] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-23 15:35:04] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-02-26 15:30:43] msgd.main :: 

********************************************************************** 
CRITICAL [2024-02-26 15:30:43] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-02-26 15:30:44] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-06 14:50:52] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-06 14:50:52] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-06 14:50:52] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-06 14:51:18] msgd.main :: 

********************************************************************** 
CRITICAL [2024-03-06 14:51:18] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-06 14:51:18] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-31 17:21:32] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-31 17:21:32] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-03-31 17:22:41] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-03-31 17:22:41] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-17 18:03:30] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-17 18:03:30] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-18 17:38:46] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-18 17:38:46] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-20 15:05:10] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-20 15:05:10] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-20 15:10:20] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-20 15:10:21] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-20 15:28:49] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-20 15:28:49] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-04-20 15:35:37] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-04-20 15:35:37] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-06-14 10:02:35] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-06-14 10:02:35] dkt.runDakota :: calling: dakota -i main.dakota 
INFO     [2024-06-20 15:58:11] io.readMSGDInput :: reading main input main.yml... 
CRITICAL [2024-06-20 15:58:11] dkt.runDakota :: calling: dakota -i main.dakota 
[17:50:00] INFO     io.readMSGDInput :: reading main input main.yml... 
[17:50:00] CRITICAL dkt.runDakota :: calling: dakota -i main.dakota 
