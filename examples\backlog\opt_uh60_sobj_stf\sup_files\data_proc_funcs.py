import numpy as np
# from scipy import interpolate
import pprint
# import msgd.utils.params as mup
import msgd.utils.function as muf
import msgd.ext.rcas as mmr


# Pre-processing

# def materialId2Name(data, sname, logger, *args, **kwargs):
def materialId2Name(parameters):
    # pprt = pprint.PrettyPrinter(indent=4)
    # pprt.pprint(data)
    mdb = {
        1: 'AS4 12k/E7K8_0.0054',
        2: 'S2/SP381_0.0092',
        3: 'T650-35 12k/976_0.0052',
        4: 'T700 24K/E765_0.0056',
    }
    data[sname]['lam_spar_1'] = mdb[data[sname]['lamid_spar_1']]
    data[sname]['lam_front']  = mdb[data[sname]['lamid_front']]
    data[sname]['lam_back']   = mdb[data[sname]['lamid_back']]

    return




def materialId2Name2(data, sname, logger, *args, **kwargs):
    param_out = {}
    # pprt = pprint.PrettyPrinter(indent=4)
    # pprt.pprint(data)
    mdb = {
        1: 'AS4 12k/E7K8_0.0054',
        2: 'S2/SP381_0.0092',
        3: 'T650-35 12k/976_0.0052',
        4: 'T700 24K/E765_0.0056',
    }
    param_out['lam_spar_1'] = mdb[data['lamid_spar_1']]
    param_out['lam_front']  = mdb[data['lamid_front']]
    param_out['lam_back']   = mdb[data['lamid_back']]
    # data[sname]['lam_spar_1'] = mdb[data[sname]['lamid_spar_1']]
    # data[sname]['lam_front'] = mdb[data[sname]['lamid_front']]
    # data[sname]['lam_back'] = mdb[data[sname]['lamid_back']]
    
    return param_out


# def preProcessGeo(data, sname, logger, *args, **kwargs):
#     # Calculate embedded points
#     data[sname]['pfle1_a2'] = data[sname]['pnsmc_a2'] - data[sname]['nsmr'] - 0.005
#     data[sname]['pfle2_a2'] = data[sname]['wl_a2'] + 0.005
#     data[sname]['pfte1_a2'] = data[sname]['wt_a2'] - 0.01

#     return









# Post-processing

def dakota_postpro(data, sname, logger, *args, **kwargs):
    """ Post-process results from previous analysis steps.

    This is the last step in a Dakota evaluation.
    The goal is to calculate response/objective/constraint function values
    and store them in data['dakota']:

        data['dakota'] = {
            'descriptor1': value1,
            'descriptor2': value2,
            ...
        }
    """

    pprt = pprint.PrettyPrinter(indent=4)

    # cs_names = list(outputs['cs'].keys())
    blade_name = data['structures']['blade'][0]
    cs_names = data['structures']['cs']

    weights = kwargs['weights']
    bp_names = kwargs['beam_properties']

    # Convert data into interpolation functions
    ref_bp = kwargs['ref_properties']
    ref_bp_func_type = ref_bp['function']
    ref_bp_funcs = {}
    if ref_bp_func_type == 'interpolation':
        intp_kind = ref_bp['kind']
        try:
            ref_bp_form = ref_bp['data_form']
        except KeyError:
            ref_bp_form = 'compact'
        if ref_bp_form == 'file':
            ref_bp_fn = ref_bp['file_name']
            ref_bp_format = ref_bp['data_format']
            if ref_bp_format == 'rcas_prop':
                ref_prop = mmr.readRcasProp(ref_bp_fn)
                ref_prop_request = ref_bp['data_request']
                for bpn, bpr in zip(bp_names, ref_prop_request):
                    # print('bpn: {}, bpr: {}'.format(bpn, bpr))
                    # print(ref_prop[bpr.upper()])
                    ref_bp_funcs[bpn] = muf.InterpolationFunction(
                        ref_prop[bpr.upper()]['x'],
                        ref_prop[bpr.upper()]['y'],
                        kind=intp_kind
                    )
            # ndimx = 1
            # ref_bp_funcs = mup.loadDataCSV(ref_bp_fn, ndimx, kind=intp_kind)
    # xscale = kwargs['xscale']

    diffs = np.zeros(len(cs_names))
    mpls = np.zeros(len(cs_names))

    # Radial locations of cross-sections
    # rs = np.zeros(len(cs_names))
    rs = data[blade_name]['stations']
    for i, cs_name in enumerate(cs_names):
        cs_data = data[cs_name]

        diff = []

        for bp_name in bp_names:
            # fn = pp[0]  # function name
            # rn = pp[1]  # response name
            # cs_args = pp[2]
            rn = bp_name + '_diff'

            # Get calculated value
            bp_cal = cs_data[bp_name]
            if bp_name == 'mc2':
                bp_cal = bp_cal - cs_data['sc2']
                bp_name = bp_name + '_sc'

            # Get reference/target value
            bp_ref_name = bp_name + '_ref'
            ref_bp_func = ref_bp_funcs[bp_name]
            # print(rs[i]*xscale)
            bp_ref = float(ref_bp_func(rs[i]))
            # print(type(bp_ref).__name__)
            cs_data[bp_ref_name] = bp_ref

            r = (bp_cal - bp_ref) / bp_cal
            data['dakota']['diff_{}_{}'.format(cs_name, bp_name)] = r
            diff.append(r)

            cs_data[rn] = r


        weighted_squared_diffs = [diff[i]**2 * weights[i] for i in range(len(diff))]
        diffs[i] = sum(weighted_squared_diffs)

        # mpls[i] = cs_outputs['mu']

    diff_obj = sum(diffs)
    # outputs['final'].insert(0, ['diff', diff_obj])
    data['dakota']['diff'] = diff_obj

    # ttm = 0
    # for i in range(int(len(rs) / 2)):
    #     ttm += (mpls[2*i] + mpls[2*i+1]) / 2.0 * (rs[2*i+1] - rs[2*i])
    # ttm = ttm * inputs['rotor_radius']
    # outputs['final'].insert(1, ['total_mass_c', ttm])


    return









def calcRelDiff(data, sname, logger, *args, **kwargs):
    cs_names = list(data['structure']['css_data'].keys())

    # weights = kwargs['weights']
    bp_names = kwargs['beam_properties']

    # Convert data into interpolation functions
    ref_bp = kwargs['ref_properties']
    bp_names_ref = ref_bp['data_request']

    ref_bp_funcs = loadRefProperties(
        ref_bp['data_form'], prop_request=bp_names_ref,
        file_name=ref_bp['file_name'], file_format=ref_bp['data_format'],
        func_type=ref_bp['function'], intp_kind=ref_bp['kind']
    )


    diffs = {'gj': [], 'ei22': [], 'ei33': []}

    # Radial locations of cross-sections
    # rs = np.zeros(len(cs_names))
    rs = data['structure']['design']['section_locations']
    for i, cs_name in enumerate(cs_names):
        cs_data = data['structure']['css_data'][cs_name]

        for bp_name, bp_name_ref in zip(bp_names, bp_names_ref):
            # rn = bp_name + '_diff'

            # Get calculated value
            bp_cal = cs_data['property']['md1'][bp_name]

            # Get reference/target value
            # bp_ref_name = bp_name + '_ref'
            ref_bp_func = ref_bp_funcs[bp_name_ref]
            # print(rs[i]*xscale)
            bp_ref = float(ref_bp_func(rs[i]))
            # print(type(bp_ref).__name__)
            # cs_data[bp_ref_name] = bp_ref

            r = (bp_cal - bp_ref) / bp_cal
            # diff.append(r)
            diffs[bp_name].append(r**2)

            # cs_data[rn] = r
            data['main']['diff_{}_{}'.format(cs_name, bp_name)] = r


        # weighted_squared_diffs = [diff[i]**2 * weights[i] for i in range(len(diff))]
        # diffs[i] = sum(weighted_squared_diffs)

        # mpls[i] = cs_outputs['mu']

    # diff_obj = sum(diffs)
    # outputs['final'].insert(0, ['diff', diff_obj])
    data['main']['diff_gj'] = sum(diffs['gj'])
    data['main']['diff_eiyy'] = sum(diffs['ei22'])
    data['main']['diff_eizz'] = sum(diffs['ei33'])

    return









def loadRefProperties(
        data_form, prop_request='all', file_name='', file_format='',
        func_type=None, intp_kind='linear'):


    if data_form == 'file':
        if file_format == 'rcas_prop':
            ref_prop = mmr.readRcasProp(file_name)


    if func_type == 'interpolation':
        ref_bp_funcs = {}
        # intp_kind = ref_bp['kind']
        # try:
        #     ref_bp_form = ref_bp['data_form']
        # except KeyError:
        #     ref_bp_form = 'compact'
                # ref_prop_request = ref_bp['data_request']
        if prop_request == 'all':
            prop_request = list(ref_prop.keys())

        for bpr in prop_request:
            # print('bpn: {}, bpr: {}'.format(bpn, bpr))
            # print(ref_prop[bpr.upper()])
            ref_bp_funcs[bpr] = muf.InterpolationFunction(
                ref_prop[bpr.upper()]['x'],
                ref_prop[bpr.upper()]['y'],
                kind=intp_kind
            )
        return ref_bp_funcs

    else:
        return ref_prop

