# unit system
# mm, N, MPa

version: "0.9"


# --------------------------------------------------------------------
structure:
  name: "square_plate"
  type: null
  parameter:
    l1v1: 0
    l1v2: 90
  distribution:
    - name: a1
      function: f1
      coefficients:
        v1: l1v1
        v2: l1v2
  # coordinate_transformation:
  #   function: tf
  #   coefficient:
  #     wx: 200
  #     wy: 200
  design:
    # type: 'discrete'
    file: "plate400_s4r_10x10.inp"
    # file: "sq_1ply_cccc_s4r_20x20.inp"
    solver: "abaqus"
    section_prop_file: "shellsections.inp"
    # orient_name: 'Ori-1'
    sg_assignment:
      all: "mainsg"
  sg:
    mainsg:
      base: "lv1_layup"
      model: "md2"
      # physics: "thermoelastic"
  # model:

# --------------------------------------------------------------------
function:
  - name: 'ff_nds'
    type: 'expression'
    expression: '(v2-v1)*abs(x)+v1'
    coefficients: ['v1', 'v2']

  - name: 'tf'
    type: 'script'
    file_name: 'data_proc_funcs'
    function_name: 'trans'

  - name: "f1"
    type: "expression"
    expression: "2*(v2-v1)*abs(x)/400+v1"
    coefficients: ["v1", "v2"]

# --------------------------------------------------------------------
sg:
  - name: "lv1_layup"
    parameter:
      a1: 0
    design:
      dim: 1
      # type: 'gd1'
      type: "descriptive"
      tool: "default"
      symmetry: 2
      layers:
        - material: "m2"
          ply_thickness: 1.27
          number_of_plies: 1
          in-plane_orientation: a1

    model:
      md2:
        tool: "swiftcomp"
        version: "2.1"
        mesh_size: -1
        # k11: _k11
        # k22: _k22


  - name: "m2"
    type: "material"
    model:
      md3:
        type: "engineering"
        density: 1.0
        elasticity:
          [
            181e3, 8.96e3, 8.96e3,
            7.2e3, 7.2e3, 7.2e3,
            0.3, 0.28, 0.28
          ]
        cte: [22.5e-6, 2e-6, 2e-6]
        specific_heat: 0


# --------------------------------------------------------------------
analysis:
  physics: "thermoelastic"
  steps:
    - name: "homogenization"
      type: "sg"
      analysis: "h"
      setting:
        solver: "swiftcomp"
        # version: '2.2'
      # outputs:
      #   final:
      #     e1: "e1"
    - name: "buckling analysis"
      type: "abaqus"
      job_file: "plate400_s4r_10x10.inp"
      setting:
        timeout: 300
      args:
        - "interactive"
      post_process:
        - script: "abq_get_result.py"
          args:
            - "plate400_s4r_10x10.odb"
            - "abq_result.dat"
      step_result_file: "abq_result.dat"

# --------------------------------------------------------------------
study:
  method:
    sampling:
      sample_type:
        lhs:
      samples: 10
      seed: 1027
  variables:
    data_form: "explicit"
    list:
      - name: "l1v1"
        type: "continuous"
        bounds: [-90, 90]
      - name: "l1v2"
        type: "continuous"
        bounds: [-90, 90]
  responses:
    data_form: "explicit"
    response_functions:
      - descriptor: "eig1"
      - descriptor: "eig2"
      - descriptor: "eig3"
      - descriptor: "eig4"
      - descriptor: "eig5"
  interface:
    fork:
      parameters_file: "params.in"
      results_file: "results.out"
      file_save: on
      work_directory:
        named: "evals/eval"
        directory_tag: on
        directory_save: on
    required_files: ["model/*", "scripts/*"]
    # asynchronous:
    #   evaluation_concurrency: 10
    # failure_capture:
    #   recover: []
