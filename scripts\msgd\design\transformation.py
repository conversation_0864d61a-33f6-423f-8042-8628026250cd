"""
Structural domain transformation module

Map the physical domain to the parametric domain and vice versa.
"""
import copy
import csv
import logging

import meshio
import numpy as np

# import sgio.meshio as meshio

# import msgd._global as GLOBAL
from msgd.core._data_classes import DataBase

logger = logging.getLogger(__name__)

# from . import function as mfunc

# class Transformation():
class Domain():
    """Domain where parameter distributions are defined.

    Parameters
    ----------
    name: str
        Name of the domain.
    tf_type: str
        Type of the domain transformation.
        Choose from 'function', 'list', 'mesh'.
    """

    def __init__(
        self, name:str='', dim:int=3, tf_type:str='',
        func_name='', func_coef={},
        mesh_file:str='', mesh_format:str='',
        mesh_param:dict={},
        # node_coord_tf:dict={},
        node_map_method:str='file',
        node_map_type:str='id',
        node_map:dict={},
        node_map_file:str='', node_map_file_format:str='',
        # node_map_list:list|dict=None,
        node_map_is_label:bool=True, node_map_same_order:bool=True
        ):
        self.name = name
        self.dim = dim

        self.tf_type = tf_type

        self.trans_func = None
        """
        Function or dictionary
        """

        self._func_name = func_name
        self._func_coef = func_coef
        # self._function = None

        self._mesh_file = mesh_file
        self._mesh_format = mesh_format
        self._mesh_param = mesh_param

        # self._node_coord_tf = node_coord_tf

        self._node_map_method = node_map_method  # 'file' or 'three_nodes'
        self._node_map_type = node_map_type  # 'id' or 'coords'
        self._node_map = node_map  # map from the input
        self._node_map_file = node_map_file
        self._node_map_file_format = node_map_file_format
        # self._node_map_list = node_map_list
        self._node_map_is_label = node_map_is_label
        self._node_map_same_order = node_map_same_order

    def __repr__(self):
        text = [f'type: {self.tf_type}',]

        return '\n'.join(text)

    def toDictionary(self):
        _dict = {
            'name': self.name,
            'type': self.tf_type,
        }

        if self._type == 'function':
            _dict['function'] = self._function.name
            # _dict['coefficient'] = self._function.coefficient

        elif self._type == 'list':
            _dict['file'] = self._mesh_file
            _dict['format'] = self._mesh_format

            _dict['node_id_map'] = {
                'method': self._nid_map_method,
            }
            if self._nid_map_method == 'file':
                _dict['node_id_map']['file'] = self._nid_map_file
                _dict['node_id_map']['format'] = self._nid_map_file_format
            elif self._nid_map_method == 'three_nodes':
                _dict['node_id_map']['same_node_order'] = self._nid_map_same_order
                _dict['node_id_map']['is_id_label'] = self._nid_map_is_label
                _dict['node_id_map']['list'] = self._nid_map_list

        return _dict

    # def __call__(self, input, input_type:str='coordinate'):
    def __call__(self, coords=[], _id=0, **kwargs):
        # input = copy.deepcopy(input)

        # if self._type == 'function':
        #     if input_type.startswith('c'):
        #         transformed = self._function(input)
        # elif self._type == 'list':
        #     if input_type.startswith('i'):
        #         if not self._node_id_map is None:
        #             input = self._node_id_map[input]
        #         transformed = self._node_coord_tf[input]

        return self.transform(coords=coords, _id=_id, **kwargs)

    def transform(self, coords=[], _id=0, **kwargs):
        """
        Parameters
        ----------
        id: int
            Node id.
        coord: list
            Coordinate of the node. Shape: (num_points, num_dim)
        """

        coords_tf = None

        if self.tf_type == 'function':
            # if input_type.startswith('c'):
            coords_tf = self.trans_func(coords)

        elif self.tf_type == 'list':
            if self._node_map_type == 'id':
                # if not self._node_id_map is None:
                _id_tf = self.trans_func[_id]
                coords_tf = self._mesh_param[_id_tf]

        if len(np.shape(coords_tf)) < 2:
            coords_tf = [coords_tf,]
        coords_tf = np.array(coords_tf)[:,:self.dim]

        return coords_tf

    # def getType(self):
    #     return self._type

    # def setType(self, t):
    #     self._type = t

    # def setFunction(self, f):
    #     self._function = f

    # def setNodeCoordT(self, n):
    #     self._node_coord_tf = n

    # def setNodeIdMap(self, m):
    #     self._node_id_map = m


    def implementTransformation(
        self, db_function:DataBase, parameters={},
        structure_model=None,
        **kwargs
        ):
        logger.info(f'[{self.name}] implementing domain transformation...')

        # breakpoint()
        if self.tf_type == 'list':

            # Read the mesh data of the parametric domain
            _node_coord_param = {}

            if self._mesh_format == 'csv':
                with open(self._mesh_file, 'r') as _file:
                    _reader = csv.reader(_file)
                    for _row in _reader:
                        _nid = int(_row[0].strip())
                        # _ncoord = list(map(float, _row[1:]))
                        _ncoord = [float(_x.strip()) for _x in _row[1:]]
                        if len(_ncoord) == 2:
                            _ncoord.append(0)
                        _node_coord_param[_nid] = _ncoord

            elif self._mesh_format == 'abaqus':
                _mesh = meshio.read(self._mesh_file, 'abaqus')
                for _i, _ncoord in enumerate(_mesh.points):
                    _nid = _i + 1
                    _node_coord_param[_nid] = _ncoord

            self._mesh_param.update(_node_coord_param)
            # print('self._mesh_param')
            # print(self._mesh_param)

            # Read the node id map
            # if not _node_map_input is None:
            _node_id_map = {}
            # _node_map_method = _node_map_input.get('method', 'file')

            if self._node_map_method == 'direct':
                for _id in self._mesh_param.keys():
                    _node_id_map[_id] = _id

            elif self._node_map_method == 'file':
                if self._node_map_file_format == 'csv':
                    with open(self._node_map_file, 'r') as _file:
                        _reader = csv.reader(_file)
                        for _row in _reader:
                            _nid_from = int(_row[0].strip())
                            _nid_to = int(_row[1].strip())
                            _node_id_map[_nid_from] = _nid_to

            elif self._node_map_method == 'three_nodes':
                # mesh_physc = meshio.read(fn_mesh_physc, fmt_mesh_physc)
                mesh_physc = structure_model.mesh
                mesh_param = meshio.read(self._mesh_file, self._mesh_format)

                _list_node_id_map = createNodeMapFromThree(
                    mesh_physc=mesh_physc,
                    mesh_param=mesh_param,
                    three_node_map=self._node_map,
                    fn_node_id_map=self._node_map_file,
                    same_order=self._node_map_same_order,
                    is_id_label=self._node_map_is_label)

                for _nid_from, _nid_to in _list_node_id_map:
                    _node_id_map[_nid_from] = _nid_to

            # transf.setNodeIdMap(_node_id_map)
            self.trans_func = _node_id_map
            # print('sefl.trans_func')
            # print(self.trans_func)

            # transf.setType('list')
            # transf.setNodeCoordT(_node_coord_tf)

        elif self.tf_type == 'function':
            ...

        return





# def loadTransformation(
#     trans_input, structure_model=None, func_lib=None, params={},
#     **kwargs):
#     """
#     """

#     _name = trans_input.get('name')
#     logger.info(f'loading transformation {_name}...')

#     transf = Transformation()

#     _method = trans_input.get('method', None)

#     if _method == 'function':
#         _name = trans_input.get('function')
#         _coef = trans_input.get('coefficient', {})
#         # FIXME: loadFunction
#         # _func = mfunc.loadFunction(_name, _coef, func_lib)
#         transf.setType('function')
#         # transf.setFunction(_func)

#     elif _method == 'list':
#         _file_name = trans_input.get('file')
#         _file_format = trans_input.get('format')
#         _node_map_input = trans_input.get('node_id_map', None)

#         _node_coord_tf = {}
#         if _file_format == 'csv':
#             with open(_file_name, 'r') as _file:
#                 _reader = csv.reader(_file)
#                 for _row in _reader:
#                     _nid = int(_row[0].strip())
#                     # _ncoord = list(map(float, _row[1:]))
#                     _ncoord = [float(_x.strip()) for _x in _row[1:]]
#                     if len(_ncoord) == 2:
#                         _ncoord.append(0)
#                     _node_coord_tf[_nid] = _ncoord
#         elif _file_format == 'abaqus':
#             _mesh = meshio.read(_file_name, 'abaqus')
#             for _i, _ncoord in enumerate(_mesh.points):
#                 _nid = _i + 1
#                 _node_coord_tf[_nid] = _ncoord

#         if not _node_map_input is None:
#             _node_id_map = {}
#             _node_map_method = _node_map_input.get('method', 'file')

#             if _node_map_method == 'file':
#                 _node_map_file_name = _node_map_input.get('file')
#                 _node_map_file_format = _node_map_input.get('format')
#                 if _node_map_file_format == 'csv':
#                     with open(_node_map_file_name, 'r') as _file:
#                         _reader = csv.reader(_file)
#                         for _row in _reader:
#                             _nid_from = int(_row[0].strip())
#                             _nid_to = int(_row[1].strip())
#                             _node_id_map[_nid_from] = _nid_to

#             elif _node_map_method == 'three_nodes':
#                 _three_nodes_map = _node_map_input.get('list')
#                 _same_node_order = _node_map_input.get('same_node_order', True)
#                 _is_id_label = _node_map_input.get('is_id_label', True)
#                 _node_map_file_name = _node_map_input.get('file', '')
#                 _node_map_file_format = _node_map_input.get('format')

#                 # mesh_physc = meshio.read(fn_mesh_physc, fmt_mesh_physc)
#                 mesh_physc = structure_model.mesh
#                 mesh_param = meshio.read(_file_name, _file_format)

#                 _list_node_id_map = createNodeMapFromThree(
#                     mesh_physc, mesh_param, _three_nodes_map,
#                     fn_node_id_map=_node_map_file_name,
#                     same_order=_same_node_order, is_id_label=_is_id_label)
                
#                 for _nid_from, _nid_to in _list_node_id_map:
#                     _node_id_map[_nid_from] = _nid_to

#             transf.setNodeIdMap(_node_id_map)

#         transf.setType('list')
#         transf.setNodeCoordT(_node_coord_tf)

#     return transf




# def loadFunction(trans_input, func_lib=None, params={}, **kwargs):
#     """
#     """

#     _name = trans_input['function']
#     _coef = trans_input.get('coefficient', {})
#     _func = mfunc.loadFunction(_name, _coef, func_lib)

#     return _func





def analyzeMesh(mesh:meshio.Mesh, three_nodes):
    """

    Parameters
    ----------

    Result
    ------
    node_elements:
        From node id to element ids
        
        ..  code-block::

            {
                nid: [(etype, eid), (etype, eid), ...],
                ...
            }
    edge_elements:
        From edge (node id pair) to element ids

        ..  code-block::

            {
                (nid1, nid2): [(etype, eid), (etype, eid), ...],
                ...
            }
    """
    logger.debug('analyzing mesh...')

    logger.debug(f'three_nodes = {three_nodes}')

    node_elements = {}
    edge_elements = {}

    element_with_three_nodes = []

    for _cb in mesh.cells:
        _etype = _cb.type
        for _eid, _enodes in enumerate(_cb.data):
            _score = 0
            # logger.debug(f'{_eid}: {_enodes}')
            for _j, _nid in enumerate(_enodes):
                try:
                    node_elements[_nid].append((_etype, _eid))
                except KeyError:
                    node_elements[_nid] = []
                    node_elements[_nid].append((_etype, _eid))
                if _nid in three_nodes:
                    _score += 1

                # Add element to edge
                _nid_prev = _enodes[_j-1]
                try:
                    edge_elements[(_nid_prev, _nid)].append((_etype, _eid))
                except KeyError:
                    edge_elements[(_nid_prev, _nid)] = []
                    edge_elements[(_nid_prev, _nid)].append((_etype, _eid))

            if _score == len(three_nodes):
                element_with_three_nodes.append((_etype, _eid))

    return node_elements, edge_elements, element_with_three_nodes









def mapElementNodes(
    mesh_1, mesh_2, eid_1, eid_2, ninit_1, ninit_2,
    node_elems_1, node_elems_2, edge_elems_1, edge_elems_2,
    elem_todo_1:list, elem_todo_2:list,
    elem_done_1, elem_done_2, node_id_map, node_order=1):
    """Map element nodes.

    Parameters
    ----------
    eid_1, eid_2:
        Element id (type, index)
    ninit_1, ninit_2:
        Startiing node id for elements in mesh 1 and 2.
        These two node ids have already been stored in node_id_map.
    node_elems_1, node_elems_2:
        Map from node to elements.
    edge_elems_1, edge_elems_2:
        Map from edge to elements.
    node_order:
        Order of the nodes between the two meshes.
        1: same, -1: reversed

    Result
    -------


    """

    logger.debug(f'mapping nodes from element {eid_1} to {eid_2}...')

    elem_done_1.append(eid_1)
    elem_done_2.append(eid_2)

    enodes_1 = mesh_1.get_cells_type(eid_1[0])[eid_1[1]].tolist()
    enodes_2 = mesh_2.get_cells_type(eid_2[0])[eid_2[1]].tolist()

    logger.debug('========================================')
    logger.debug(f'len(node_id_map) = {len(node_id_map)}')
    logger.debug(f'mesh 1 element {eid_1}: {enodes_1}')
    logger.debug(f'mesh 2 element {eid_2}: {enodes_2}')
    logger.debug(f'len(elem_done_1) = {len(elem_done_1)}')
    logger.debug(f'len(elem_done_2) = {len(elem_done_2)}')

    nnode = len(enodes_1)  # Number of nodes must be the same for both elements.

    # Get index of the init node
    _nid_prev_1 = ninit_1
    _nid_prev_2 = ninit_2
    _ni_init_1 = enodes_1.index(ninit_1)
    _ni_init_2 = enodes_2.index(ninit_2)
    _ni_prev_1 = _ni_init_1
    _ni_prev_2 = _ni_init_2
    logger.debug(f'_ni_init_1={_ni_prev_1}, _nid_init_1={ninit_1}')
    logger.debug(f'_ni_init_2={_ni_prev_2}, _nid_init_2={ninit_2}')

    for _i in range(1, nnode+1):
        logger.debug('----------------------')
        # Index of the node of the map-from element
        _ni_1 = _ni_init_1 + _i
        if _ni_1 >= nnode:
            _ni_1 -= nnode

        # Index of the node of the map-to element
        if node_order == 1:
            _ni_2 = _ni_init_2 + _i
            if _ni_2 >= nnode:
                _ni_2 -= nnode
        elif node_order == -1:
            _ni_2 = _ni_init_2 - _i
            if _ni_2 < 0:
                _ni_2 += nnode

        # Get node ids
        # _nid_prev_1 = enodes_1[_ni_prev_1]
        # _nid_prev_2 = enodes_1[_ni_prev_2]
        _nid_1 = enodes_1[_ni_1]
        _nid_2 = enodes_2[_ni_2]

        logger.debug(f'_ni_prev_1={_ni_prev_1}, _nid_prev_1={_nid_prev_1}')
        logger.debug(f'_ni_prev_2={_ni_prev_2}, _nid_prev_2={_nid_prev_2}')
        logger.debug(f'_ni_1={_ni_1}, _nid_1={_nid_1}')
        logger.debug(f'_ni_2={_ni_2}, _nid_2={_nid_2}')

        # Check if nodes are already mapped
        _not_mapped = True
        for _nid_map_1, _nid_map_2 in node_id_map:
            if _nid_1 == _nid_map_1:
                _not_mapped = False
                break
        logger.debug(f'_not_mapped = {_not_mapped}')
        if _not_mapped:
            node_id_map.append([_nid_1, _nid_2])

        # Go to the next (neighboring) element
        # Find the element sharing the edge (_nid_prev--_nid)
        _edge_1 = (_nid_1, _nid_prev_1)  # For the neighboring element, the edge is in the other direction
        _edge_2 = (_nid_2, _nid_prev_2)
        logger.debug(f'_edge_1 = {_edge_1}')
        logger.debug(f'_edge_2 = {_edge_2}')

        _do_next = True
        try:
            _eid_next_1 = edge_elems_1[_edge_1][0]
            _eid_next_2 = edge_elems_2[_edge_2][0]
            logger.debug(f'_eid_next_1 = {_eid_next_1}')
            logger.debug(f'_eid_next_2 = {_eid_next_2}')
            # Check if elements are already scanned
            if _eid_next_1 in elem_done_1:
                logger.debug(f'mapping {_eid_next_1}->{_eid_next_2} has been done.')
                _do_next = False
        except KeyError:
            # No neighboring element
            logger.debug('no element for this edge')
            _do_next = False
        logger.debug(f'_do_next = {_do_next}')

        if _do_next:
            # Store next elements to a list
            _found = False
            for __eid, __nid in elem_todo_1:
                # logger.debug(f'__eid = {__eid}, _eid_next_1 = {_eid_next_1}')
                if __eid == _eid_next_1:
                    _found = True
                    break
            if _found:
                logger.debug(f'mapping {_eid_next_1}->{_eid_next_2} has been added to todo.')
            else:
                logger.debug(f'adding mapping {_eid_next_1}->{_eid_next_2} to todo.')
                elem_todo_1.append((_eid_next_1, _nid_1))
                elem_todo_2.append((_eid_next_2, _nid_2))

            # Do this function for the next pair of elements
            # mapElementNodes(
            #     mesh_1=mesh_1, mesh_2=mesh_2,
            #     eid_1=_eid_next_1, eid_2=_eid_next_2,
            #     ninit_1=_nid_1, ninit_2=_nid_2,
            #     node_elems_1=node_elems_1, node_elems_2=node_elems_2,
            #     edge_elems_1=edge_elems_1, edge_elems_2=edge_elems_2,
            #     elem_done_1=elem_done_1, elem_done_2=elem_done_2,
            #     node_id_map=node_id_map, node_order=node_order
            # )

        _ni_prev_1 = _ni_1
        _ni_prev_2 = _ni_2
        _nid_prev_1 = _nid_1
        _nid_prev_2 = _nid_2

    # Store the elements after traversing all nodes
    # elem_done_1.append(eid_1)
    # elem_done_2.append(eid_2)

    return









def travElements(
    mesh_1:meshio.Mesh, mesh_2:meshio.Mesh,
    eid_1, eid_2, nid_init_1, nid_init_2,
    node_elems_1, node_elems_2, edge_elems_1, edge_elems_2,
    node_id_map=[], node_order=1):
    """
    """

    elem_done_1 = []
    elem_done_2 = []
    elem_todo_1 = [(eid_1, nid_init_1),]
    elem_todo_2 = [(eid_2, nid_init_2),]

    _elem_counter = 0

    while len(elem_todo_1) > 0:
        _eid_1, _nid_init_1 = elem_todo_1.pop(0)
        _eid_2, _nid_init_2 = elem_todo_2.pop(0)

        _elem_counter += 1
        mapElementNodes(
            mesh_1=mesh_1, mesh_2=mesh_2,
            eid_1=_eid_1, eid_2=_eid_2,
            ninit_1=_nid_init_1, ninit_2=_nid_init_2,
            node_elems_1=node_elems_1, node_elems_2=node_elems_2,
            edge_elems_1=edge_elems_1, edge_elems_2=edge_elems_2,
            elem_todo_1=elem_todo_1, elem_todo_2=elem_todo_2,
            elem_done_1=elem_done_1, elem_done_2=elem_done_2,
            node_id_map=node_id_map, node_order=node_order
        )

        if _elem_counter % 100 == 0:
            logger.info(f'done mapping {_elem_counter} element pairs.')

    return









def createNodeMapFromThree(
    mesh_physc:meshio.Mesh, mesh_param:meshio.Mesh,
    three_node_map:list,
    fn_node_id_map:str='',
    same_order:bool=True, is_id_label:bool=True):
    """Create node id map from three nodes.

    Parameters
    ----------
    mesh_physc: meshio.Mesh
        Mesh data of the physical domain.
    mesh_param: meshio.Mesh
        Mesh data of the parametric domain.
    three_node_map: list
        List of three nodes.
    fn_node_id_map: str
        File name to store the node id map.
    same_order: bool
        True if the order of the nodes is the same.
    is_id_label: bool
        True if the node id is a label. False if it is an index.

    Result
    ------
    node_id_map: list
        List of node id pairs.
    """
    logger.info('creating node id map from three given...')

    if is_id_label:
        # Convert node id (label) to index
        _three_node_map = [[x-1 for x in _row] for _row in three_node_map]
    else:
        _three_node_map = copy.deepcopy(three_node_map)

    three_nodes_physc = [x[0] for x in _three_node_map]
    three_nodes_param = [x[1] for x in _three_node_map]
    logger.debug(f'three_nodes_physc = {three_nodes_physc}')
    logger.debug(f'three_nodes_param = {three_nodes_param}')

    # Extract more connectivity info from the mesh data

    node_elems_physc, edge_elems_physc, elem_n3_physc = analyzeMesh(
        mesh_physc, three_nodes_physc
    )
    logger.debug('node_elems_physc')
    for _nid, _e in node_elems_physc.items():
        logger.debug(f'{_nid}: {_e}')
    logger.debug('edge_elems_physc')
    for _edge, _e in edge_elems_physc.items():
        logger.debug(f'{_edge}: {_e}')
    logger.debug(f'element_with_three_nodes_physc = {elem_n3_physc}')
    elem_n3_physc = elem_n3_physc[0]

    node_elems_param, edge_elems_param, elem_n3_param = analyzeMesh(
        mesh_param, three_nodes_param
    )
    logger.debug('\nnode_elems_param')
    for _nid, _e in node_elems_param.items():
        logger.debug(f'{_nid}: {_e}')
    logger.debug('\nedge_elems_param')
    for _edge, _e in edge_elems_param.items():
        logger.debug(f'{_edge}: {_e}')
    logger.debug(f'\nelement_with_three_nodes_param = {elem_n3_param}')
    elem_n3_param = elem_n3_param[0]

    # Check the orientation
    # init_elem_nodes_physc = mesh_physc.get_cells_type(elem_n3_physc[0])[elem_n3_physc[1]].tolist()
    # init_elem_nodes_param = mesh_physc.get_cells_type(elem_n3_param[0])[elem_n3_param[1]].tolist()
    # n3_iloc_physc = [init_elem_nodes_physc.index(x) for x in three_nodes_physc]
    # n3_iloc_param = [init_elem_nodes_param.index(x) for x in three_nodes_param]
    # logger.debug(f'n3_iloc_physc = {n3_iloc_physc}')
    # logger.debug(f'n3_iloc_param = {n3_iloc_param}')

    node_id_map = copy.deepcopy(_three_node_map)
    # elem_id_traversed_physc = []
    # elem_id_traversed_param = []
    _nid_init_physc = three_nodes_physc[0]
    _nid_init_param = three_nodes_param[0]

    travElements(
        mesh_1=mesh_physc, mesh_2=mesh_param,
        eid_1=elem_n3_physc, eid_2=elem_n3_param,
        nid_init_1=_nid_init_physc, nid_init_2=_nid_init_param,
        node_elems_1=node_elems_physc, node_elems_2=node_elems_param,
        edge_elems_1=edge_elems_physc, edge_elems_2=edge_elems_param,
        node_id_map=node_id_map, node_order=1
    )

    logger.debug('\nnode_id_map')
    logger.debug(node_id_map)

    if is_id_label:
        node_id_map = [[x+1 for x in _row] for _row in node_id_map]

    if fn_node_id_map != '':
        with open(fn_node_id_map, 'w') as file:
            for _nid_physc, _nid_param in node_id_map:
                file.write(f'{_nid_physc},{_nid_param}\n')

    return node_id_map


