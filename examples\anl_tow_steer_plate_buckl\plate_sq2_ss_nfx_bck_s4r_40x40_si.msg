1

   Abaqus 2023                                  Date 28-May-2025   Time 11:57:47
   For use by PURDUE UNIVERSITY under license from Dassault Systemes or its subsidiary.

                                                                                 
                                                                                 
 STEP    1     INCREMENT     1     STEP TIME    0.00    


                        S T E P       1     C A L C U L A T I O N   O F   E I G E N V A L U E S

                                            F O R   B U C K L I N G   P R E D I C T I O N



                                                                                          

     THE SUBSPACE ITERATION METHOD IS USED FOR THIS ANALYSIS
     NUMBER OF EIGENVALUES                     10
     MAXIMUM NUMBER OF ITERATIONS                     30
     NUMBER OF VECTORS IN ITERATION                   18
     THE BUCKLING MODE CONSTRAINTS 
      ARE DEFINED AS LOAD CASE 2
                              ONLY INITIAL STRESS EFFECTS ARE INCLUDED IN THE
                              STIFFNESS MATRIX

     THIS IS A LINEAR PERTURBATION STEP.
     ALL LOADS ARE DEFINED AS CHANGE IN LOAD TO THE REFERENCE STATE

     EXTRAPOLATION WILL NOT BE USED

     CHARACTERISTIC ELEMENT LENGTH     0.100    

     DETAILS REGARDING ACTUAL SOLUTION WAVEFRONT REQUESTED

     DETAILED OUTPUT OF DIAGNOSTICS TO DATABASE REQUESTED

     PRINT OF INCREMENT NUMBER, TIME, ETC., TO THE MESSAGE FILE EVERY     1  INCREMENTS
 
	SYMMETRIC DIRECT SPARSE SOLVER RUNNING ON
 	1 HOST:        1 MPI RANK  x 1 THREAD
 	NUMBER OF EQUATIONS:     2646
 	NUMBER OF RHS:           1
 	NUMBER OF FLOPS:         3.237e+07

     CHECK POINT  START OF SOLVER     

     CHECK POINT  END OF SOLVER       

       ELAPSED USER TIME (SEC)      =   0.0000    
       ELAPSED SYSTEM TIME (SEC)    =   0.0000    
       ELAPSED TOTAL CPU TIME (SEC) =   0.0000    
       ELAPSED WALLCLOCK TIME (SEC) =            0
 
     ITERATION 1
      1       6.945506e+01     
      2       1.217682e+02     
      3       2.176302e+02     
      4       2.648585e+02     
      5       3.993504e+02     
      6       5.822974e+02     
      7       7.065621e+02     
      8       7.733044e+02     
      9       9.453200e+02     
      10      1.119044e+03     
      11      1.186113e+03     
      12      1.299879e+03     
      13      1.567247e+03     
      14      1.640682e+03     
      15      1.764183e+03     
      16      2.036560e+03     
      17      2.419794e+03     
      18      2.703153e+03     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 0
 
     ITERATION 2
      1       3.390380e+01     
      2       7.084547e+01     
      3       1.175088e+02     
      4       1.383721e+02     
      5       1.926741e+02     
      6       2.109343e+02     
      7       2.837485e+02     
      8       3.177066e+02     
      9       3.299829e+02     
      10      3.700218e+02     
      11      4.399913e+02     
      12      5.430206e+02     
      13      6.003472e+02     
      14      6.672938e+02     
      15      6.997440e+02     
      16      8.362184e+02     
      17      1.084161e+03     
      18      1.112723e+03     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 0
 
     ITERATION 3
      1       3.387275e+01     
      2       7.049297e+01     
      3       1.163619e+02     
      4       1.372764e+02     
      5       1.883092e+02     
      6       1.939949e+02     
      7       2.632529e+02     
      8       2.811266e+02     
      9       3.015439e+02     
      10      3.226440e+02     
      11      3.956966e+02     
      12      4.935228e+02     
      13      5.164223e+02     
      14      5.636913e+02     
      15      6.040572e+02     
      16      6.557877e+02     
      17      7.894449e+02     
      18      8.770070e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 0
 
     ITERATION 4
      1       3.387270e+01     
      2       7.049069e+01     
      3       1.163252e+02     
      4       1.372560e+02     
      5       1.880656e+02     
      6       1.929294e+02     
      7       2.604240e+02     
      8       2.784013e+02     
      9       2.977151e+02     
      10      3.168380e+02     
      11      3.887011e+02     
      12      4.823148e+02     
      13      5.018732e+02     
      14      5.299565e+02     
      15      5.673402e+02     
      16      5.918876e+02     
      17      6.492636e+02     
      18      8.006701e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 0
 
     ITERATION 5
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880486e+02     
      6       1.928479e+02     
      7       2.600280e+02     
      8       2.780866e+02     
      9       2.969742e+02     
      10      3.158659e+02     
      11      3.868034e+02     
      12      4.779060e+02     
      13      4.959222e+02     
      14      5.184235e+02     
      15      5.384212e+02     
      16      5.719588e+02     
      17      5.838179e+02     
      18      7.407745e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 2
 
     ITERATION 6
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880474e+02     
      6       1.928415e+02     
      7       2.599717e+02     
      8       2.780395e+02     
      9       2.968135e+02     
      10      3.156851e+02     
      11      3.862103e+02     
      12      4.751121e+02     
      13      4.923493e+02     
      14      5.142597e+02     
      15      5.260681e+02     
      16      5.432140e+02     
      17      5.697966e+02     
      18      6.805922e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 4
 
     ITERATION 7
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880473e+02     
      6       1.928410e+02     
      7       2.599640e+02     
      8       2.780320e+02     
      9       2.967767e+02     
      10      3.156507e+02     
      11      3.860282e+02     
      12      4.733728e+02     
      13      4.904612e+02     
      14      5.125856e+02     
      15      5.198701e+02     
      16      5.300235e+02     
      17      5.581856e+02     
      18      6.303829e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 5
 
     ITERATION 8
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880473e+02     
      6       1.928409e+02     
      7       2.599629e+02     
      8       2.780308e+02     
      9       2.967683e+02     
      10      3.156442e+02     
      11      3.859757e+02     
      12      4.724786e+02     
      13      4.895504e+02     
      14      5.118309e+02     
      15      5.161949e+02     
      16      5.252150e+02     
      17      5.427093e+02     
      18      6.019881e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 6
 
     ITERATION 9
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880473e+02     
      6       1.928409e+02     
      7       2.599628e+02     
      8       2.780306e+02     
      9       2.967665e+02     
      10      3.156429e+02     
      11      3.859615e+02     
      12      4.720603e+02     
      13      4.891247e+02     
      14      5.114348e+02     
      15      5.142526e+02     
      16      5.233538e+02     
      17      5.286055e+02     
      18      5.901219e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 8
 
     ITERATION 10
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880473e+02     
      6       1.928409e+02     
      7       2.599628e+02     
      8       2.780306e+02     
      9       2.967661e+02     
      10      3.156427e+02     
      11      3.859578e+02     
      12      4.718627e+02     
      13      4.889338e+02     
      14      5.111547e+02     
      15      5.133224e+02     
      16      5.198006e+02     
      17      5.226604e+02     
      18      5.849889e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 9
 
     ITERATION 11
      1       3.387270e+01     
      2       7.049068e+01     
      3       1.163239e+02     
      4       1.372554e+02     
      5       1.880473e+02     
      6       1.928409e+02     
      7       2.599628e+02     
      8       2.780306e+02     
      9       2.967661e+02     
      10      3.156426e+02     
      11      3.859568e+02     
      12      4.717684e+02     
      13      4.888542e+02     
      14      5.108611e+02     
      15      5.128873e+02     
      16      5.153963e+02     
      17      5.222569e+02     
      18      5.824376e+02     
 
     NUMBER OF EIGENVALUES CONVERGED UPTO THIS ITERATION = 10


          THE ANALYSIS HAS BEEN COMPLETED



     ANALYSIS SUMMARY:
     TOTAL OF          1  INCREMENTS
                       0  CUTBACKS IN AUTOMATIC INCREMENTATION
                       1  ITERATIONS INCLUDING CONTACT ITERATIONS IF PRESENT
                       1  PASSES THROUGH THE EQUATION SOLVER OF WHICH 
                       1  INVOLVE MATRIX DECOMPOSITION, INCLUDING
                       0  DECOMPOSITION(S) OF THE MASS MATRIX
                       1  REORDERING OF EQUATIONS TO MINIMIZE WAVEFRONT
                       0  ADDITIONAL RESIDUAL EVALUATIONS FOR LINE SEARCHES
                       0  ADDITIONAL OPERATOR EVALUATIONS FOR LINE SEARCHES
                       0  WARNING MESSAGES DURING USER INPUT PROCESSING
                       0  WARNING MESSAGES DURING ANALYSIS
                       0  ANALYSIS WARNINGS ARE NUMERICAL PROBLEM MESSAGES
                       0  ANALYSIS WARNINGS ARE NEGATIVE EIGENVALUE MESSAGES
                       0  ERROR MESSAGES



     JOB TIME SUMMARY
       USER TIME (SEC)      =     0.40    
       SYSTEM TIME (SEC)    =     0.10    
       TOTAL CPU TIME (SEC) =     0.50    
       WALLCLOCK TIME (SEC) =            1
       MEMORY PEAK (GB)     =            0
