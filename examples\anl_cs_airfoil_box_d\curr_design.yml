version: '0.10'
structure:
  name: blade
  parameter: {}
  model:
    type: ''
    tool: ''
    tool_version: ''
    main_file: ''
    prop_file: ''
    config: {}
  design:
    name: blade
    parameter: {}
    dim: 1
    builder: default
    design: null
  cs_assignment:
  - region: all
    location: element
    cs: cs
  physics: elastic
functions: []
cs:
- name: cs1
  parameter: {}
  dim: 2
  builder: prevabs
  design:
    base_file: cs_config_a.xml
analysis:
  steps:
  - step: cs analysis
    activate: true
    type: sg
    analysis: h
    work_dir: cs
  - step: recovery
    activate: true
    type: sg
    analysis: d
    work_dir: cs
