version: '0.10'
structure:
  name: blade1
  parameter: {}
  model:
    type: ''
    tool: gebt
    tool_version: ''
    main_file: beam_design.yml
    prop_file: ''
    config: {}
  design:
    name: blade1
    parameter:
      lyr_ang_sta1: 0
      lyr_ang_sta2: 90
      lyr_ply: 6
    dim: 1
    builder: default
    design: {}
    distribution:
    - name: lyr_ang
      type: float
      data_form: explicit
      data:
      - coordinate: 0
        value: 0
      - coordinate: 10
        value: 90
  cs_assignment:
  - region: segment1
    location: element
    cs: main_cs
  physics: elastic
functions:
- name: f_interp
  type: float
  kind: linear
  fill_value: extrapolate
cs:
- name: box
  parameter:
    w: 0.953
    h: 0.53
    lyr_ang: 0
    lyr_ply: 6
  dim: 2
  builder: prevabs
  design:
    base_file: box.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    type: sg
    analysis: h
    work_dir: cs
  - step: beam analysis
    activate: true
    output:
    - value: all
      to: file
      file_name: global_force_moment.csv
    - value:
      - u1
      - u2
      - u3
      name:
      - u1_tip
      - u2_tip
      - u3_tip
      to: main
      entity: point
      entity_name: tip
    - value: mass
    type: gebt
  - step: cs recovery
    activate: true
    output:
      file_name: strength_ratio.yml
      file_format: yml
      value:
      - sr_min
    type: sg
    analysis: fi
    work_dir: cs
