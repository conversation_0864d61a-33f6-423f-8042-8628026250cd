import logging
import os

import sgio
import yaml

# import msgd._global as GLOBAL


# import typing
# import msgd.model.sg as msg
# import msgd.model.beam as mmb
# import msgd.ext.gebt.beam as mgb
import msgd.utils as mutils
from .beam import (
    GEBTBeam,
    # GEBTBeamMember,
    GEBTCondition,
    GEBTTimeFunction,
    GEBTNodeResult,
    GEBTBeamResult
    )

logger = logging.getLogger(__name__)



# Reader


def readGEBTIn(fn_in:str, fmt:str='') -> GEBTBeam:
    """ Read GEBT input file.

    Parameters
    ----------
    fn_in : str
        File name of the GEBT input.
    fmt : str
        Format of the input file. Default is ''.

    Returns
    -------
    GEBTBeam:
        GEBT beam object.
    """

    logger.info(f'reading the GEBT beam file {fn_in}...')

    if fmt == '':
        fn, fmt = os.path.splitext(fn_in)
        fmt = fmt[1:]

    if fmt == 'yml':
        return readGEBTInYml(fn_in)

    elif fmt == 'txt':
        return readGEBTInTxt(fn_in)




def readGEBTInYml(fn) -> GEBTBeam:
    """
    """

    # logger.debug(f'local variables:\n{mutils.convertDict2Str(locals())}')

    if isinstance(fn, str):

        logger.info('loading file {}...'.format(fn))

        with open(fn, 'r', encoding='utf-8') as fo:
            raw_input = yaml.safe_load(fo)

        fn_dir, fn_base = os.path.split(fn)
        fn_base, fn_ext = os.path.splitext(fn_base)
        logger.debug('fn_dir  = {}'.format(fn_dir))
        logger.debug('fn_base = {}'.format(fn_base))
        logger.debug('fn_ext  = {}'.format(fn_ext))

        _name = fn_base

    elif isinstance(fn, dict):

        raw_input = fn
        _name = raw_input.get('name', '')

    logger.debug(f'raw input:\n{mutils.convertDict2Str(raw_input)}')


    beam = GEBTBeam(name=_name)


    # Read analysis configurations
    # ----------------------------
    beam.analysis = raw_input.get('analysis', 1)
    beam.max_iteration = raw_input.get('max_iteration', 1)
    beam.num_steps = raw_input.get('num_steps', 1)
    if beam.analysis == 3:
        beam.num_eigens = raw_input.get('num_eigens', 20)

    # Read velocities
    _velocity = raw_input.get('velocity', {})
    beam.angular_velocity = _velocity.get('angular', [0, 0, 0])
    beam.linear_velocity = _velocity.get('linear', [0, 0, 0])


    # Read geometry
    # -------------
    scaling = raw_input.get('scaling', 1.0)

    # Read points
    points = raw_input.get('point', [])
    for point in points:
        _coords = [scaling * c for c in point['coordinates']]
        beam.addPoint(pid=point['id'], coords=_coords)

    # Read members
    members = raw_input['member']
    for member in members:
        beam.addMember(mid=member['id'], points=member['points'])
        # bmemb_id += 1
        # _id = member_input['id']
        # bmemb = GEBTBeamMember()
        # bmemb.member_id = _id
        # bmemb.points = member_input['points']
        # # bmemb.css = member_input['sections']
        # bmemb.num_divisions = member_input['division']

        # beam.members[_id] = bmemb

    # Read sets
    sets = raw_input.get('set', [])
    for _set in sets:
        _name = _set['name']
        _class = _set['type']
        _items = _set['items']
        if _class == 'point':
            beam.point_sets[_name] = _items
        elif _class == 'member':
            beam.member_sets[_name] = _items


    # Read conditions
    # ---------------
    conditions = raw_input.get('condition', [])
    for _cond in conditions:
        _ids = []
        _region = _cond['region']

        if not (isinstance(_region, list) or isinstance(_region, tuple)):
            _region = [_region,]

        _type = _cond.get('type', None)

        for _r in _region:
            if _r in beam.point_sets.keys():
                _ids = _ids + beam.point_sets[_r]
                if not _type:
                    _type = 'point'
            elif _r in beam.member_sets.keys():
                _ids = _ids + beam.member_sets[_r]
                if not _type:
                    _type = 'member'

        for _id in _ids:
            beam.addCondition(loc_type=_type, loc_id=_id, cond_input=_cond)


    # Read distributed functions
    # --------------------------
    distr_funcs = raw_input.get('distr_function', [])
    for _df in distr_funcs:
        _id = _df['id']
        _coefs = _df['coefs']
        beam.addDistributedFunction(dfid=_id, coefs=_coefs)

    # Read sectional properties
    # -------------------------
    sgs = raw_input.get('section_property', [])
    for sg in sgs:
        # bp = sgio.BeamProperty()
        # ms = sgio.MaterialSection(smdim=1)
        # bp = sgio.BeamModel()
        ms = sgio.TimoshenkoBeamModel()

        _id = sg['id']
        ms.name = sg['name']
        _props = sg['property']['md1']
        print(_props)

        _mass = []
        if 'mass' in _props.keys():
            _mass = _props['mass']
        else:
            _mass = [
                [_props[f'ms1{j+1}'] for j in range(6)],
                [_props[f'ms2{j+1}'] for j in range(6)],
                [_props[f'ms3{j+1}'] for j in range(6)],
                [_props[f'ms4{j+1}'] for j in range(6)],
                [_props[f'ms5{j+1}'] for j in range(6)],
                [_props[f'ms6{j+1}'] for j in range(6)],
            ]
        ms.mass = _mass

        _cmpl_t = []
        if 'compliance_t' in _props.keys():
            _cmpl_t = _props['compliance_t']
        else:
            _cmpl_t = [
                [_props[f'cmp1{j+1}'] for j in range(6)],
                [_props[f'cmp2{j+1}'] for j in range(6)],
                [_props[f'cmp3{j+1}'] for j in range(6)],
                [_props[f'cmp4{j+1}'] for j in range(6)],
                [_props[f'cmp5{j+1}'] for j in range(6)],
                [_props[f'cmp6{j+1}'] for j in range(6)],
            ]
        ms.cmpl = _cmpl_t

        # ms.constitutive = bp

        beam.sections[_id] = ms
        beam.bp_name_id[ms.name] = _id


    # Read SG assignments
    # -------------------
    sg_assignments = raw_input.get('section_assignment', [])
    for _sg_assign in sg_assignments:
        _ids = []

        _region = _sg_assign['region']
        if not (isinstance(_region, list) or isinstance(_region, tuple)):
            _region = [_region,]

        _type = _sg_assign.get('type', None)

        _section = _sg_assign['section']
        if not (isinstance(_section, list) or isinstance(_section, tuple)):
            _section = [_section, _section]
        _section = [beam.bp_name_id[_name] for _name in _section]

        for _r in _region:
            if _r in beam.point_sets.keys():
                _ids = _ids + beam.point_sets[_r]
                if not _type:
                    _type = 'point'
            elif _r in beam.member_sets.keys():
                # print(beam.member_sets[_r])
                _ids = _ids + beam.member_sets[_r]
                if not _type:
                    _type = 'member'

        print(f'ids = {_ids}')

        for _id in _ids:
            if _type == 'member':
                print(beam.members[_id])
                beam.assignMemberSections(mid=_id, sid0=_section[0], sid1=_section[1])
                # beam.members[_id] = _section


    # Read meshing
    # ------------
    meshing = raw_input.get('meshing', {})
    memb_divs = meshing.get('member_division', [])
    for _memb_div in memb_divs:
        _id = _memb_div['member']
        _div = _memb_div['division']
        beam.setMemberDivisions(mid=_id, nd=_div)


    return beam









def readGEBTInTxt(fn_gebt_in: str) -> GEBTBeam:
    """Read GEBT input.

    Parameters
    ----------
    fn_gebt_in : str
        File name of the GEBT input.

    Returns
    -------
    msgpi.ms.beam.Beam
        Beam object constructed from the data.
    """
    beam = GEBTBeam()

    # results = {}
    lines = []

    with open(fn_gebt_in, 'r') as fin:
        for ln, line in enumerate(fin):
            line = line.strip()
            if line == '':
                continue
            else:
                lines.append(line.split('#')[0].strip())

    # for line in lines:
    #     print(line)

    li = 0

    line1 = list(map(int, lines[li].split()))
    beam.analysis = line1[0]
    beam.max_iteration = line1[1]
    beam.num_steps = line1[2]
    if beam.analysis > 0:
        # Read rorating velocities
        li += 1
        beam.angular_velocity = list(map(float, lines[li].split()))
        li += 1
        beam.av_tf = list(map(int, lines[li].split()))
        li += 1
        beam.linear_velocity = list(map(float, lines[li].split()))
        li += 1
        beam.lv_tf = list(map(int, lines[li].split()))
        if beam.analysis == 3:
            li += 1
            beam.num_eigens = int(lines[li])

    li += 1

    line2 = list(map(int, lines[li].split()))
    nkp = line2[0]
    nmemb = line2[1]
    ncond_pt = line2[2]
    nmate = line2[3]
    nframe = line2[4]
    ncond_mb = line2[5]
    ndistr = line2[6]
    ntimefun = line2[7]
    ncurv = line2[8]
    li += 1

    # Read keypoints
    for i in range(nkp):
        p = lines[li].strip().split()[:4]
        beam.points[int(p[0])] = list(map(float, p[1:]))
        li += 1


    # Read members
    for i in range(nmemb):
        data = list(map(int, lines[li].strip().split()[:8]))
        # bm = GEBTBeamMember()
        _mid = data[0]
        _points = data[1:3]
        _css = data[3:5]
        _frame_id = data[5]
        _num_divisions = data[6]
        _curv_id = data[7]

        beam.addMember(mid=_mid, points=_points)
        beam.assignMemberSections(mid=_mid, sid0=_css[0], sid1=_css[1])
        beam.assignMemberLocalFrame(mid=_mid, fid=_frame_id)
        beam.setMemberDivisions(mid=_mid, nd=_num_divisions)
        beam.assignMemberInitCurvature(mid=_mid, cid=_curv_id)
        # beam.members[data[0]] = bm
        li += 1


    # Read point conditions
    for i in range(ncond_pt):
        pc = GEBTCondition()
        pc.object_id = int(lines[li])
        li += 1
        pc.dofs = list(map(int, lines[li].split()))
        li += 1
        pc.values = list(map(float, lines[li].split()))
        li += 1
        pc.time_funcs = list(map(int, lines[li].split()))
        li += 1
        pc.followers = list(map(int, lines[li].split()))
        beam.point_conditions.append(pc)
        li += 1

    # Read sectional properties
    for i in range(nmate):
        # ms = sgio.BeamProperty()
        ms = sgio.TimoshenkoBeamModel()
        ms_id = int(lines[li])
        li += 1
        # Read compliance matrix
        _cmpl = []
        for j in range(6):
            _cmpl.append(
                list(map(float, lines[li].split()))
            )
            li += 1
        ms.cmpl = _cmpl
        # Read mass matrix
        if beam.analysis != 0:
            _mass = []
            for j in range(6):
                _mass.append(
                    list(map(float, lines[li].split()))
                )
                li += 1
            ms.mass = _mass

        beam.sections[ms_id] = ms

    # Read local frames
    for i in range(nframe):
        frame_id = int(lines[li].strip())
        c = []
        for j in range(3):
            li += 1
            c.append(list(map(float, lines[li].split())))
        li += 1
        beam.frames[frame_id] = c

    # Read member conditions
    for i in range(ncond_mb):
        mc = GEBTCondition()
        mc.object_id = int(lines[li])
        li += 1
        mc.dofs = list(map(int, lines[li].split()))
        li += 1
        mc.values = list(map(float, lines[li].split()))
        li += 1
        mc.time_funcs = list(map(int, lines[li].split()))
        li += 1
        mc.followers = list(map(int, lines[li].split()))
        beam.member_conditions.append(mc)
        li += 1

    # Read distributed load functions
    for i in range(ndistr):
        _id = int(lines[li])
        li += 1
        _coefs = list(map(float, lines[li].split()))
        li += 1
        beam.distr_loads[_id] = _coefs

    # Read initial curvatures and twist
    for i in range(ncurv):
        pass

    # Read time functions
    if (ntimefun > 0) or (beam.analysis == 2):
        beam.sim_range = list(map(float, lines[li].split()))
        li += 1
        for i in range(ntimefun):
            _id = int(lines[li])
            li += 1
            tf = GEBTTimeFunction(_id)
            tf.type = int(lines[li])
            li += 1
            tf.time_range = list(map(float, lines[li].split()))
            li += 1
            _nentries = int(lines[li])
            li += 1
            for j in range(_nentries):
                _entry = list(map(float, lines[li].split()))
                tf.entries.append(_entry)
                li += 1
            beam.time_functions.append(tf)

    return beam









def readGEBTOutNode(lines, node_type, node_id, analysis=0, method=0):
    """Read GEBT nodal results.

    Parameters
    ----------
    lines : list(str)
        File name of the GEBT output.
    beam : msgpi.ms.beam.Beam
        Beam object.

    Returns
    -------
    list of lists of floats
        Table of result numbers for this node.
    """

    out = []
    for l in lines:
        out += list(map(float, l.strip().split()))

    if method == 0:
        return out
    elif method == 1:
        node_result = GEBTNodeResult()
        node_result.type = node_type
        node_result.id = node_id
        node_result.coord = out[0:3]
        node_result.u = out[3:6]
        node_result.r = out[6:9]
        node_result.f = out[9:12]
        node_result.m = out[12:15]
        if node_type == 'element' and analysis > 0:
            node_result.p = out[15:18]
            node_result.h = out[18:21]
        return node_result









def readGEBTOutBeam(lines, i, beam:GEBTBeam):
    """Read the result of the whole beam for a single step or eigenvalue
    """
    # print('readGEBTOutBeam')

    beam_result = GEBTBeamResult()

    # i = 0
    pid = 0
    mid = 0
    while i < len(lines):
        line = lines[i].strip()
        if (line == '') or ('===' in line) or ('---' in line):
            i += 1
            # continue
        elif 'Point' in line:
            # print('- reading point results...')
            pid += 1
            logger.debug(f'reading point {pid} result...')
            # Read point result
            # out = readGEBTOutNode(lines[i + 2:i + 5])
            # results_step['point'].append(out)
            node_result = readGEBTOutNode(
                lines[i + 2:i + 5], 'point', pid,
                analysis=beam.analysis, method=1
            )
            beam_result.points.append(node_result)
            i += 5
            # points_results.append(out)
            # continue
        elif 'Member' in line:
            # print('- reading member results...')
            # Read member result
            mid += 1
            logger.debug(f'reading memebr {mid} result...')
            nelem = beam.member_divisions[mid]  # number of divisions
            i += 2
            member_out = []
            eid = 0
            for j in range(nelem):
                # print('  - reading element results...')
                eid += 1
                if beam.analysis == 0:
                    # Static
                    # out = readGEBTOutNode(lines[i:i + 3])
                    elem_result = readGEBTOutNode(
                        lines[i:i + 3], 'element', mid,
                        analysis=beam.analysis, method=1
                    )
                    i += 4
                else:
                    # Dynamic
                    # out = readGEBTOutNode(lines[i:i + 4])
                    elem_result = readGEBTOutNode(
                        lines[i:i + 4], 'element', mid,
                        analysis=beam.analysis, method=1
                    )
                    i += 5
                # member_out.append(out)
                member_out.append(elem_result)
            # results_step['member'].append(member_out)
            beam_result.members.append(member_out)
            # members_results.append(member_out)
            # if mid == nmemb:
            #     results.append(results_step)
            # continue
        else:
            i += 1

        if mid == len(beam.members):
            break

    return beam_result









def readGEBTOutStatic(fn_gebt_out, beam:GEBTBeam):
    """Read GEBT results of static analysis.

    Parameters
    ----------
    fn_gebt_out : str
        File name of the GEBT output.
    beam : GEBTBeam
        Beam object.

    Returns
    -------
    """
    flag_analysis = beam.analysis
    nstep = beam.num_steps
    nkp = len(beam.points)
    nmemb = len(beam.members)

    results = []  # for all steps
    points_results = []
    members_results = []

    # lines = []

    with open(fn_gebt_out, 'r') as fin:
        all_lines = fin.readlines()

    i = 0
    sid = 0  # at least one step
    mid = 0
    while i < len(all_lines):
        line = all_lines[i].strip()
        if (line == '') or ('===' in line) or ('---' in line):
            i += 1
            continue
        elif ('Step' in line) or ((nstep == 1) and (sid == 0)):
            sid += 1  # step number
            results_step = {'point': [], 'member': []}  # point results, member results
            mid = 0
            i += 1
            continue
        elif 'Point' in line:
            # Read point result
            out = readGEBTOutNode(all_lines[i + 2:i + 5])
            i += 5
            results_step['point'].append(out)
            # points_results.append(out)
            continue
        elif 'Member' in line:
            # Read member result
            mid += 1
            nelem = beam.members[mid].num_divisions  # number of divisions
            i += 2
            member_out = []
            for j in range(nelem):
                if flag_analysis == 0:
                    # Static
                    out = readGEBTOutNode(all_lines[i:i + 3])
                    i += 4
                else:
                    # Dynamic
                    out = readGEBTOutNode(all_lines[i:i + 4])
                    i += 5
                member_out.append(out)
            results_step['member'].append(member_out)
            # members_results.append(member_out)
            if mid == nmemb:
                results.append(results_step)
            continue
        i += 1

    return results









def readGEBTOutEigen(fn_gebt_out, beam:GEBTBeam):
    """Read GEBT results of eigen analysis.

    Parameters
    ----------
    fn_gebt_out : str
        File name of the GEBT output.
    beam : :msgpi.ms.beam.Beam
        Beam object.

    Returns
    -------
    list, list
        Steady results, eigen results

        Eigen results: `[eigenvalues, eigenvectors]`

        eigenvalues = `[[eva11, eva12], [eva21, eva22], ...]`

        eigenvectors = `[eve1, eve2, ...]`

        for each eigenvector evei: `[point results, member results]`

        for each point result: `[[x11, x12, x13, u11, u12, u13, t11, t12, t13], ...]`

        for each memebr result: `[[[x11, x12, x13, u11, u12, u13, t11, t12, t13], [], ...], ...]`
    """

    # if len(gebtin) == 0:
    #     gebtin = readGEBTIn(gebtin_name)
    # gebtout_name = gebtin_name + '.out'

    nstep = beam.num_steps
    nkp = len(beam.points)
    nmemb = len(beam.members)
    members_in = beam.members
    # members_in = gebtin['members']
    # print 'nmemb =', nmemb

    # evei = [p1, m11, m12, ..., m1n, p2]
    # p1/m1i = [x1, x2, x3, t1, t2, t3]

    with open(fn_gebt_out, 'r') as fin:
        all_lines = fin.readlines()




    # Read steady state results

    results_steady = []  # for all steps
    points_results = []
    members_results = []

    end_of_steady = False

    i = 0
    sid = 0  # at least one step
    mid = 0
    # while i < len(all_lines):
    while not end_of_steady:
        line = all_lines[i].strip()
        if (line == '') or ('===' in line) or ('---' in line):
            i += 1
            continue
        elif ('Step' in line) or ((nstep == 1) and (sid == 0)):
            sid += 1  # step number
            results_step = {'point': [], 'member': []}  # point results, member results
            mid = 0
            i += 1
            continue
        elif 'Point' in line:
            # Read point result
            out = readGEBTOutNode(all_lines[i + 2:i + 5])
            i += 5
            results_step['point'].append(out)
            # points_results.append(out)
            continue
        elif 'Member' in line:
            # Read member result
            mid += 1
            nelem = beam.members[mid].num_divisions  # number of divisions
            i += 2
            member_out = []
            for j in range(nelem):
                out = readGEBTOutNode(all_lines[i:i + 4])
                i += 5
                member_out.append(out)
            results_step['member'].append(member_out)
            # members_results.append(member_out)
            if mid == nmemb:
                results_steady.append(results_step)
                end_of_steady = True
            continue
        i += 1




    # Read eigen results
    all_lines = all_lines[i:]

    eva = []  # Eigenvalues: [[eva11, eva12], [eva21, eva22], ...]
    eve = []  # Eigenvectors: [eve1, eve2, ...]

    eig = 0
    i = 0
    block = ''
    while i < len(all_lines):
        # print i
        line = all_lines[i].strip()
        if line == '':
            i += 1
            continue
        if 'Eigenvalue' in line:
            eig += 1
            # print 'eigenvalue:', eig
            eva.append(list(map(float, all_lines[i + 1].split())))
            evei = [[], []]
            # store all Point results for an Eigenvalue temporarily
            # evei_p = []  # = [[x11, x12, x13, u11, u12, u13, t11, t12, t13], ...]
            # store all Member results for an Eigenvalue temporarily
            # evei_m = []  # = [[[x11, x12, x13, u11, u12, u13, t11, t12, t13], [], ...], ...]
            mid = 0  # Member id
            pid = 0
            i += 2
            continue
        if eig > 0:
            if 'Point' in line:
                pid += 1
                out = readGEBTOutNode(all_lines[i + 2:i + 5])
                # pr = utl.textToMatrix(all_lines[i + 2:i + 5])  # point results
                # x, u, t = pr[0], pr[1][:3], pr[1][3:]
                # [x11, x12, x13, u11, u12, u13, t11, t12, t13]
                # eve_p.append(pr[0] + pr[1])
                i += 5
                evei[0].append(out)
                continue
            elif 'Member' in line:
                # Read member result
                mid += 1
                nelem = beam.members[mid].num_divisions  # number of divisions
                # nelem = members_in[mid - 1][-2]  # number of divisions
                i += 2
                evei_m = []
                for j in range(nelem):
                    out = readGEBTOutNode(all_lines[i:i + 4])
                    i += 5
                    evei_m.append(out)
                evei[1].append(evei_m)
                # members_results.append(member_out)
                if mid == nmemb:
                    eve.append(evei)
                continue
        i += 1

    results_eigen = [eva, eve]

    return results_steady, results_eigen









def readGEBTOut(fn_gebt_out, beam:GEBTBeam, method=1):
    """Read GEBT results.

    Parameters
    ----------
    fn_gebt_out : str
        File name of the GEBT output.
    beam : GEBTBeam
        Beam object

    Returns
    -------
    """

    logger.info(f'reading gebt output {fn_gebt_out}...')

    flag_analysis = beam.analysis
    nstep = beam.num_steps
    # nkp = len(beam.points)
    # nmemb = len(beam.members)

    if method == 0:
        if flag_analysis <= 1:
            return readGEBTOutStatic(fn_gebt_out, beam)
        elif flag_analysis == 3:
            return readGEBTOutEigen(fn_gebt_out, beam)

    elif method == 1:
        with open(fn_gebt_out, 'r') as fin:
            all_lines = fin.readlines()

        i = 0

        # Read results of all steps
        sid = 0  # at least one step
        while i < len(all_lines):
            line = all_lines[i].strip()

            if (line == '') or ('===' in line) or ('---' in line):
                i += 1
                continue
            elif ('Step' in line) or ((nstep == 1) and (sid == 0)):
                sid += 1  # step number
                # print('step', sid)
                logger.debug(f'reading step {sid} output...')
                # results_step = {'point': [], 'member': []}  # point results, member results
                beam_result = readGEBTOutBeam(all_lines, i, beam)
                beam.results_steps.append(beam_result)
                i += 1
                continue
            else:
                i += 1
            
            if sid == beam.num_steps:
                break

        # Read results of all eigenvalues
        if beam.analysis == 3:
            # eva = []  # Eigenvalues: [[eva11, eva12], [eva21, eva22], ...]
            eig = 0
            while i < len(all_lines):
                line = all_lines[i].strip()

                if (line == '') or ('===' in line) or ('---' in line):
                    i += 1
                    continue
                elif 'Eigenvalue' in line:
                    eig += 1
                    logger.debug(f'reading eigen result {eig}/{beam.num_eigens}...')
                    # print 'eigenvalue:', eig
                    # eva.append(list(map(float, all_lines[i + 1].split())))
                    eva = list(map(float, all_lines[i + 1].split()))
                    # evei = [[], []]
                    # store all Point results for an Eigenvalue temporarily
                    # evei_p = []  # = [[x11, x12, x13, u11, u12, u13, t11, t12, t13], ...]
                    # store all Member results for an Eigenvalue temporarily
                    # evei_m = []  # = [[[x11, x12, x13, u11, u12, u13, t11, t12, t13], [], ...], ...]
                    # mid = 0  # Member id
                    # pid = 0
                    beam_result = readGEBTOutBeam(all_lines, i, beam)
                    beam.results_eigen.append([eva, beam_result])
                    i += 1
                    continue
                else:
                    i += 1

                if eig == beam.num_eigens:
                    break

            # if beam.analysis < 3:
            #     #
            # elif beam.analysis == 3:

    return
