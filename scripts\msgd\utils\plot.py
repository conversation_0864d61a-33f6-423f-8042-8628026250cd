# import csv
import logging
import os

import numpy as np
import sgio
import yaml
# import msgpi.sg.io as msi
# import msgd.ext.rcas as mmr
# import msgd.utils.function as muf
# import msgd.utils.math as mum
# import msgd.utils.logger as mul

# import msgd._global as GLOBAL

logger = logging.getLogger(__name__)



def plotSGenome(
    structure_name, fn_out='', out_fmt='gmsh22',
    sg_tool='vabs', tool_version='4', model='BM1', sgdir='.'
    ):
    """
    """

    fn_discrete_sg = f'_{structure_name}_discrete_sg_assigns.yml'
    fn_structure_mesh = f'{structure_name}_mesh.msh'

    if not fn_out:
        fn_out = f'{structure_name}_sgenome.msh'

    with open(fn_discrete_sg, 'r') as file:
        discrete_sg = yaml.load(file, Loader=yaml.FullLoader)

    # print(discrete_sg)


    structure_mesh = sgio.meshio.read(fn_structure_mesh, file_format='gmsh')
    points = structure_mesh.points
    cells = structure_mesh.cells
    # print(points)


    # First region
    _region = discrete_sg[0]
    _region_id = _region['region']
    _region_type = _region['location']
    if _region_type == 'node':
        _fn = os.path.join(sgdir, f"{discrete_sg[0]['sg_model']}.sg")
        sgenome = sgio.read(
            fn=_fn,
            file_format=sg_tool,
            format_version=tool_version,
            model=model)

        # Move the SG to the global location
        _loc = points[_region_id-1]
        sgenome.mesh.points += np.array(_loc)

    # elif _loc == 'element_node':


    for _i in range(1, len(discrete_sg)):
        # print(f'========== {_i} ==========')
        _region = discrete_sg[_i]
        _region_id = _region['region']
        _region_type = _region['location']

        if _region_type == 'node':
            _fn = os.path.join(sgdir, f"{discrete_sg[_i]['sg_model']}.sg")
            sg2 = sgio.read(
                fn=_fn,
                file_format=sg_tool,
                format_version=tool_version,
                model=model)

            # Move the SG to the global location
            _loc = points[_region_id-1]
            sg2.mesh.points += np.array(_loc)

            # Combine the SGs
            sgenome = sgio.combineSG(sgenome, sg2)


    sgio.write(sgenome, fn_out, out_fmt, mesh_only=True)

    return



# def plotBeamCrossSections(fn, fmt='gmsh'):

#     # logger = mul.initLogger(__name__)

#     # fn = 'blade_cs.msh'

#     # Read data
#     # =========
#     print()
#     logger.info('- reading data...')

#     # Read rcas data

#     rcas_data = mmr.GalaxyBladeData()
#     rcas_data.oml = mmr.readRcasOml('oml.dat')
#     twist = rcas_data.getAeroSegTwist(which='in')
#     shear = rcas_data.getAeroSegShear(which='in')
#     ftwist = muf.InterpolationFunction(twist['x'], twist['y'], kind='previous')
#     fshear = muf.InterpolationFunction(shear['x'], shear['y'], kind='previous')

#     # print(twist)
#     # return


#     # Read cs data

#     xscale = 1
#     yscale = 1
#     zscale = 1

#     sgs = []
#     nnode = 0
#     nelem = 0
#     head_rows = 1
#     ref_center = 'tc'
#     with open('cs_loc_filenames.csv', 'r') as fo:
#         cs_loc_filenames = csv.reader(fo)
#         for i, row in enumerate(cs_loc_filenames):
#             if i < head_rows:
#                 continue
#             # print(row)
#             sgi = {}
#             sgi['fn_cs'] = row[0]
#             sgi['x'] = float(row[1]) * xscale
#             sgi['y'] = float(row[2]) * yscale
#             sgi['z'] = float(row[3]) * zscale

#             # print('- reading cross-section input {0}...'.format(sgi['fn_cs']))
#             logger.info('- reading cross-section input {0}...'.format(sgi['fn_cs']))
#             sg = sgio.read(sgi['fn_cs'], 'vabs', '4', 1)
#             bp = sgio.readOutput(sgi['fn_cs'], 'vabs', 1, analysis='h')
#             sgi['sg'] = sg
#             sgi['bp'] = bp
#             sgi['twist'] = ftwist(sgi['x'])
#             sgi['shear'] = fshear(sgi['x'])
#             sgi['nnode'] = len(sg.nodes)
#             sgi['nelem'] = len(sg.elements)

#             nnode += sgi['nnode']
#             nelem += sgi['nelem']

#             sgs.append(sgi)


#     # Plus blade nodes/elements
#     nnode += len(sgs)
#     nelem += len(sgs) - 1


#     # print('nnode', nnode)
#     # print('nelem', nelem)
#     logger.info('nnode = {}'.format(nnode))
#     logger.info('nelem = {}'.format(nelem))


#     # Transformation
#     # ==============
#     print()
#     logger.info('- transforming...')
#     for i, sgi in enumerate(sgs):
#         sg = sgi['sg']
#         # print('    - {0}'.format(sg.name))
#         logger.info('    - {0}'.format(sg.name))

#         # Transformation
#         t0 = [0, -sgi['bp'].get(ref_center+'y'), -sgi['bp'].get(ref_center+'z')]
#         sa = sgi['shear']
#         R = [
#             [np.cos(sa), -np.sin(sa), 0],
#             [np.sin(sa),  np.cos(sa), 0],
#             [0, 0, 1]
#         ]
#         t1 = [sgi['x'], sgi['y'], sgi['z']]

#         for nid, ncoord in sg.nodes.items():
#             v = [0, ncoord[0], ncoord[1]]
#             v = mum.transformRigid3D(v, t=t0)
#             v = mum.transformRigid3D(v, R=R, t=t1)
#             sg.nodes[nid] = v
#         sg.spdim = 3
#         sgs[i]['sg'] = sg



#     # Write Gmsh files
#     # ==================
#     # print('- writing msh file {0}...'.format(fn))
#     print()
#     logger.info('- writing msh file {0}...'.format(fn))
#     with open(fn, 'w') as fo:
#         fo.write('$MeshFormat\n')
#         fo.write('2.2  0  8\n')
#         fo.write('$EndMeshFormat\n')
#         fo.write('\n')

#         # Write nodes
#         # print('  - writing nodes...')
#         logger.info('  - writing nodes...')
#         fo.write('$Nodes\n')
#         fo.write(str(nnode)+'\n')

#         nid_begin = 1

#         # Cross-sections
#         for sgi in sgs:
#             sg = sgi['sg']
#             # print('    - {0}'.format(sg.name))
#             logger.info('    - {0}'.format(sg.name))

#             # Transformation
#             # sa = sgi['shear']
#             # R = [
#             #     [np.cos(sa), -np.sin(sa), 0],
#             #     [np.sin(sa),  np.cos(sa), 0],
#             #     [0, 0, 1]
#             # ]
#             # t = [sgi['x'], sgi['y'], sgi['z']]
#             # try:
#             #     t[1] -= sgi['bp'].get(ref_center+'y')
#             #     t[2] -= sgi['bp'].get(ref_center+'z')
#             # except:
#             #     pass

#             # for nid, ncoord in sg.nodes.items():
#             #     v0 = [0, ncoord[0], ncoord[1]]
#             #     v1 = mum.transformRigid3D(v0, R, t)
#             #     sg.nodes[nid] = v1
#             #     sg.spdim = 3

#             sg.writeGmshNodes(fo, nid_begin)
#             nid_begin += sgi['nnode']

#         # Blade ref line
#         for sgi in sgs:
#             fo.write('{} {} {} {}\n'.format(nid_begin, sgi['x'], sgi['y'], sgi['z']))
#             nid_begin += 1


#         fo.write('$EndNodes\n')
#         fo.write('\n')


#         # Write Elements
#         # print('  - writing elements')
#         logger.info('  - writing elements')
#         fo.write('$Elements\n')
#         fo.write(str(nelem)+'\n')

#         nid_begin = 1
#         eid_begin = 1

#         # Cross-sections
#         for sgi in sgs:
#             # print('    - {0}'.format(sgi['sg'].name))
#             logger.info('    - {0}'.format(sgi['sg'].name))
#             sgi['sg'].writeGmshElements(fo, nid_begin, eid_begin)
#             nid_begin += sgi['nnode']
#             eid_begin += sgi['nelem']

#         # Blade ref line
#         for i in range(len(sgs)-1):
#             fo.write('{} {}'.format(eid_begin, 1))
#             fo.write(' 2 0 0')
#             fo.write(' {} {}\n'.format(nid_begin, nid_begin+1))
#             nid_begin += 1
#             eid_begin += 1

#         fo.write('$EndElements\n')
#         fo.write('\n')

