---
description: 
globs: 
alwaysApply: false
---
# SG Library Structure

The SG Library is the core component of the MSG Design framework, providing functionality for structure genome calculations and material modeling.

## Directory Structure

- [sg_library/](mdc:sg_library): Main library directory
  - [cs_templates/](mdc:sg_library/cs_templates): Cross-section templates
  - [material_database_us_ft.xml](mdc:sg_library/material_database_us_ft.xml): Material properties database
  - [run.py](mdc:sg_library/run.py): Main execution script
  - [test.yml](mdc:sg_library/test.yml): Test configuration

## Key Components

### Material Database
The [material_database_us_ft.xml](mdc:sg_library/material_database_us_ft.xml) contains material properties and configurations used in the analysis. This file is essential for:
- Material property definitions
- Unit system specifications (US/FT)
- Material behavior models

### Cross-Section Templates
The [cs_templates/](mdc:sg_library/cs_templates) directory contains predefined cross-section templates for:
- Common structural shapes
- Composite layups
- Custom cross-section definitions

### Test Configuration
The [test.yml](mdc:sg_library/test.yml) file provides:
- Test case configurations
- Example input parameters
- Validation scenarios

