import pytest
import numpy as np
from meshio import CellBlock
from msgd.core._helpers import get_element_list


class TestGetElementList:
    """Test suite for the get_element_list function."""

    def test_empty_input(self):
        """Test with empty cells and cell_data_etags."""
        cells = []
        cell_data_etags = []

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == []
        assert etag_to_index == {}

    def test_single_cell_block_lines(self):
        """Test with a single cell block containing line elements."""
        cells = [
            CellBlock('line', [[0, 1], [1, 2], [2, 3]])
        ]
        cell_data_etags = [
            np.array([1, 2, 3])
        ]

        expected_list = [
            ('line', [0, 1]),  # Element tag 1 at index 0
            ('line', [1, 2]),  # Element tag 2 at index 1
            ('line', [2, 3])   # Element tag 3 at index 2
        ]
        expected_mapping = {
            1: 0,
            2: 1,
            3: 2
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_single_cell_block_triangles(self):
        """Test with a single cell block containing triangle elements."""
        cells = [
            CellBlock('triangle', [[0, 1, 2], [1, 2, 3], [2, 3, 4]])
        ]
        cell_data_etags = [
            np.array([101, 102, 103])
        ]

        expected_list = [
            ('triangle', [0, 1, 2]),  # Element tag 101 at index 0
            ('triangle', [1, 2, 3]),  # Element tag 102 at index 1
            ('triangle', [2, 3, 4])   # Element tag 103 at index 2
        ]
        expected_mapping = {
            101: 0,
            102: 1,
            103: 2
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_multiple_cell_blocks(self):
        """Test with multiple cell blocks of different element types."""
        cells = [
            CellBlock('line', [[0, 1], [1, 2]]),
            CellBlock('triangle', [[0, 1, 2], [1, 2, 3]]),
            CellBlock('quad', [[0, 1, 2, 3]])
        ]
        cell_data_etags = [
            np.array([1, 2]),
            np.array([101, 102]),
            np.array([201])
        ]

        expected_list = [
            ('line', [0, 1]),           # Element tag 1 at index 0
            ('line', [1, 2]),           # Element tag 2 at index 1
            ('triangle', [0, 1, 2]),    # Element tag 101 at index 2
            ('triangle', [1, 2, 3]),    # Element tag 102 at index 3
            ('quad', [0, 1, 2, 3])      # Element tag 201 at index 4
        ]
        expected_mapping = {
            1: 0,
            2: 1,
            101: 2,
            102: 3,
            201: 4
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_element_tag_sequential_positioning(self):
        """Test that elements are positioned sequentially regardless of tag values."""
        cells = [
            CellBlock('line', [[0, 1], [1, 2]])
        ]
        cell_data_etags = [
            np.array([5, 3])  # Non-sequential element tags
        ]

        # Elements should be placed sequentially, not based on tag values
        expected_list = [
            ('line', [0, 1]),  # Element tag 5 at index 0
            ('line', [1, 2])   # Element tag 3 at index 1
        ]
        expected_mapping = {
            5: 0,  # Element tag 5 maps to index 0
            3: 1   # Element tag 3 maps to index 1
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_single_element_per_block(self):
        """Test with single elements in multiple blocks."""
        cells = [
            CellBlock('line', [[0, 1]]),
            CellBlock('triangle', [[0, 1, 2]]),
            CellBlock('quad', [[0, 1, 2, 3]])
        ]
        cell_data_etags = [
            np.array([1]),
            np.array([101]),
            np.array([201])
        ]

        expected_list = [
            ('line', [0, 1]),
            ('triangle', [0, 1, 2]),
            ('quad', [0, 1, 2, 3])
        ]
        expected_mapping = {
            1: 0,
            101: 1,
            201: 2
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_large_node_indices(self):
        """Test with large node indices."""
        cells = [
            CellBlock('triangle', [[1000, 1001, 1002], [2000, 2001, 2002]])
        ]
        cell_data_etags = [
            np.array([5001, 5002])
        ]

        expected_list = [
            ('triangle', [1000, 1001, 1002]),
            ('triangle', [2000, 2001, 2002])
        ]
        expected_mapping = {
            5001: 0,
            5002: 1
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_mismatched_lengths_error(self):
        """Test error handling when cells and cell_data_etags have mismatched lengths."""
        cells = [
            CellBlock('line', [[0, 1], [1, 2]])
        ]
        cell_data_etags = [
            np.array([1, 2, 3])  # More element tags than elements
        ]

        with pytest.raises(ValueError, match="Mismatch between number of elements"):
            get_element_list(cells, cell_data_etags)

    def test_empty_cell_block(self):
        """Test with an empty cell block."""
        cells = [
            CellBlock('line', [])
        ]
        cell_data_etags = [
            np.array([])
        ]

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == []
        assert etag_to_index == {}

    def test_different_cell_data_types(self):
        """Test with different types of cell_data_etags (list vs numpy array)."""
        cells = [
            CellBlock('line', [[0, 1], [1, 2]])
        ]
        cell_data_etags = [
            [1, 2]  # Regular list instead of numpy array
        ]

        expected_list = [
            ('line', [0, 1]),
            ('line', [1, 2])
        ]
        expected_mapping = {
            1: 0,
            2: 1
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping

    def test_return_type_and_structure(self):
        """Test that the return type and structure match the specification."""
        cells = [
            CellBlock('triangle', [[0, 1, 2]])
        ]
        cell_data_etags = [
            np.array([2])  # Element tag 2
        ]

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        # Check that we get a tuple with two elements
        assert isinstance(element_list, list)
        assert isinstance(etag_to_index, dict)

        # Check element list structure
        assert len(element_list) == 1
        assert all(isinstance(item, tuple) for item in element_list)
        assert all(len(item) == 2 for item in element_list)
        assert all(isinstance(item[0], str) for item in element_list)  # element type
        assert all(isinstance(item[1], list) for item in element_list)  # node IDs

        # Check mapping structure
        assert len(etag_to_index) == 1
        assert all(isinstance(k, int) for k in etag_to_index.keys())  # tags are integers
        assert all(isinstance(v, int) for v in etag_to_index.values())  # indices are integers

        # Check specific content
        assert element_list[0] == ('triangle', [0, 1, 2])
        assert etag_to_index[2] == 0

    def test_node_id_preservation(self):
        """Test that node IDs are preserved correctly as integers."""
        cells = [
            CellBlock('triangle', [[10, 20, 30]])
        ]
        cell_data_etags = [
            np.array([100])
        ]

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list[0][1] == [10, 20, 30]
        assert all(isinstance(nid, int) for nid in element_list[0][1])
        assert etag_to_index[100] == 0

    def test_docstring_example(self):
        """Test the exact example from the docstring."""
        cells = [
            CellBlock('triangle', [[0, 1, 2], [1, 2, 3]]),
            CellBlock('quad', [[11, 12, 13, 14], [15, 16, 17, 18]])
        ]
        cell_data_etags = [
            [1, 2],
            [101, 102]
        ]

        expected_list = [
            ('triangle', [0, 1, 2]),
            ('triangle', [1, 2, 3]),
            ('quad', [11, 12, 13, 14]),
            ('quad', [15, 16, 17, 18]),
        ]
        expected_mapping = {
            1: 0,
            2: 1,
            101: 2,
            102: 3
        }

        element_list, etag_to_index = get_element_list(cells, cell_data_etags)

        assert element_list == expected_list
        assert etag_to_index == expected_mapping


if __name__ == "__main__":
    pytest.main([__file__])
