version: '0.10'
structure:
  name: blade1
  parameter: {}
  model:
    type: ''
    tool: gebt
    tool_version: ''
    main_file: beam_design.yml
    prop_file: ''
    config: {}
  design:
    name: blade1
    parameter:
      L: 10
      w_root: 1.5
      w_tip: 0.5
    dim: 1
    builder: default
    design: null
    distribution:
    - name: w
      type: float
      xscale: 10
      data_form: compact
      data: '0,1.5

        1,0.5

        '
  cs_assignment:
  - region: segment1
    location: element_node
    cs: main_cs
  physics: elastic
functions:
- name: f_interp_linear
  type: float
  kind: linear
  fill_value: extrapolate
cs:
- name: cs1
  parameter:
    w: 1
    t: 0.1
    lam_lyr_1: la_mat_1
    ang_lyr_1: 0
    gms: 0.01
  dim: 2
  builder: prevabs
  design:
    base_file: rect.xml.tmp
analysis:
  steps:
  - step: cs analysis
    activate: true
    type: sg
    analysis: h
    work_dir: cs
  - step: beam analysis
    activate: true
    output:
      value:
      - eig1
      - eig2
      - eig3
      - eig4
      - eig5
      - eig6
      - eig7
      - eig8
      - eig9
      - eig10
    type: gebt
