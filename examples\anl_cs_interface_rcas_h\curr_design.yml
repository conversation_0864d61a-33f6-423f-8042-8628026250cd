version: '0.10'
structure:
  name: uh60_blade_1
  parameter: {}
  model:
    type: ''
    tool: rcas
    tool_version: ''
    main_file: oml.dat
    prop_file: ''
    config:
      node_id_start: 12
  design:
    name: uh60_blade_1
    parameter:
      radius: 26.8333
      ang_front_r0: 0
      ang_front_r1: 90
      ang_back_r0: 0
      ang_back_r1: 90
      r_ply: 0.5
      ply_spar_l1_root: 8
      ply_spar_l1_r1: 6
      ply_spar_l1_tip: 2
      ply_front_root: 4
      ply_front_r1: 3
      ply_front_tip: 1
      ply_back_root: 4
      ply_back_r1: 3
      ply_back_tip: 1
    dim: 1
    builder: default
    design: null
    distribution:
    - name: airfoil
      type: str
      data_form: file
    - name: chord
      type: float
      data_form: file
    - name:
      - ply_spar_1
      - ply_front
      - ply_back
      type: int
      xscale: 26.8333
      data_form: compact
      data: '0.1,8,4,4

        0.5,6,3,3

        1.0,2,1,1

        '
    - name:
      - ang_front
      - ang_back
      type: int
      xscale: 26.8333
      data_form: compact
      data: '0,0,0

        1,90,90

        '
  cs_assignment:
  - region: all
    location: node
    cs: main_cs
  physics: elastic
functions:
- name: f_interp_prev
  type: float
  kind: previous
  fill_value: extrapolate
- name: f_interp_linear
  type: float
  kind: linear
  fill_value: extrapolate
cs:
- name: cs_airfoil
  parameter:
    cs_template: airfoil_gbox_uni.xml.tmp
    mdb_name: material_database_us_ft
    airfoil: sc1095.dat
    airfoil_point_order: -1
    chord: 1.73
    lam_spar_1: T300 15k/976_0.0053
    lam_cap: Aluminum 8009_0.01
    lam_front: AS4 12k/E7K8
    lam_back: AS4 12k/E7K8
    ply_spar_1: 0
    ang_front: 0
    ang_back: 0
    ply_front: 1
    ply_back: 1
    mat_nsm: lead
    rnsm: 0.001
    mat_fill_front: Rohacell 70
    mat_fill_back: Plascore PN2-3/16OX3.0
    mat_fill_te: AS4 12k/E7K8
    gms: 0.002
    fms: 0.04
  dim: 2
  builder: prevabs
  design:
    base_file: cs_template
analysis:
  steps:
  - step: cs analysis
    activate: true
    output:
      file_name: prop_calc.dat
      file_format: rcas
      value:
      - mu
      - gyry
      - gyrz
      - ea
      - gj
      - eiyy
      - eizz
      - mcy
      - mcz
      - tcy
      - tcz
    type: sg
    analysis: h
    work_dir: cs
